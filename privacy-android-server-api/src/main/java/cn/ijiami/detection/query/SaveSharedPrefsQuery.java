package cn.ijiami.detection.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@ApiModel(value = "保存个人隐私信息")
public class SaveSharedPrefsQuery {
    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "下载文件路径")
    private List<String> filePaths;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<String> getFilePaths() {
        return filePaths;
    }

    public void setFilePaths(List<String> filePaths) {
        this.filePaths = filePaths;
    }
}
