package cn.ijiami.detection.query.task;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosLogQuery.java
 * @Description 请求ios缓存的动态日志
 * @createTime 2022年09月13日 17:07:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IosLogQuery {

    @ApiModelProperty("权限名列表，用,分割 例如：获取IDFV,获取手机用户名称")
    private List<String> types;

    @ApiModelProperty(value = "行为阶段，传多个是用,分割 例如 1,2,3")
    private List<Integer> behaviorStage;

    @ApiModelProperty(value = "是否属于个人信息相关 1 是， 0 否，，传多个是用,分割 例如 0,1")
    private List<Integer> isPersonal;

    @ApiModelProperty("任务id")
    private Long taskId;

}
