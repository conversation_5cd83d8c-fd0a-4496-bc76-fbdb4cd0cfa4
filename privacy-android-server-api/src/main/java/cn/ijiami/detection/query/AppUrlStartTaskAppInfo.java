package cn.ijiami.detection.query;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppUrlStartTaskAppInfo.java
 * @Description 应用url启动检测任务应用信息
 * @createTime 2024年05月24日 18:20:00
 */
@Data
public class AppUrlStartTaskAppInfo {

    @ApiModelProperty(value = "app包地址", required = true)
    private String appUrl;

    @ApiModelProperty(value = "logo地址")
    private String logoUrl;

    @ApiModelProperty(value = "ios的app有appId时需要传", example = "1544045905")
    private String appId;

    @ApiModelProperty(value = "app包md5", example = "bc6813bdd24c142e5ff001c3613f7c77", required = true)
    private String md5;

    @ApiModelProperty(value = "app名", example = "微信", required = true)
    private String name;

    @ApiModelProperty(value = "app版本", example = "1.0.1", required = true)
    private String version;

    @ApiModelProperty(value = "是否包含壳，ios应用包按照实际情况传，android应用包传false")
    private Boolean hasShell = false;

    @ApiModelProperty(value = "app包名", example = "com.xxx.app", required = true)
    private String packageName;

    @ApiModelProperty(value = "app包体积大小，单位MB", example = "40.35")
    private String size;

    @ApiModelProperty(value = "app分类id", example = "1")
    private List<Long> classifyIdList;

}
