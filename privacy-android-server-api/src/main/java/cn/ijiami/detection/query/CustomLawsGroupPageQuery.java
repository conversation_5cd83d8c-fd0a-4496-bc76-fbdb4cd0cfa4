package cn.ijiami.detection.query;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomLawsGroupPageQuery.java
 * @Description 自定义法规列表查询
 * @createTime 2023年12月27日 11:25:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomLawsGroupPageQuery extends BaseEntity {

    @ApiModelProperty(value = "查询的名字")
    private String name;

    @ApiModelProperty(value = "查询的规则所属平台，1 android 2 ios 4 微信小程序 5 支付宝小程序")
    private Integer terminalType;

}
