package cn.ijiami.detection.query;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionPageQuery.java
 * @Description 检测配置查询
 * @createTime 2023年06月21日 15:16:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetectionPageQuery extends BaseEntity {

    @ApiModelProperty(value = "角色名或用户名", example = "aaa")
    private String name;

    @ApiModelProperty(value = "排序类型 1 创建时间  2 有效截止日期")
    private Integer sortType;

    @ApiModelProperty(value = "排序升降 1升序  2降序")
    private Integer sortOrder;

}
