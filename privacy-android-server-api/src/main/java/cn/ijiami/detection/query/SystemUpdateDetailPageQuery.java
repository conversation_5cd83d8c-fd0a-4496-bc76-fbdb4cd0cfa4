package cn.ijiami.detection.query;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SystemUpdateDetailPageQuery.java
 * @Description 系统更新查询
 * @createTime 2024年11月15日 12:07:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SystemUpdateDetailPageQuery extends BaseEntity {

    @ApiModelProperty(value = "工具或系统更新id", example = "1")
    private Long updateId;

}
