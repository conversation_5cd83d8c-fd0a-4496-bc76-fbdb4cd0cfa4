package cn.ijiami.detection.query;

import cn.ijiami.detection.entity.TAction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 行为函数 query对象
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "ActionQuery", description = "行为函数 query对象")
public class ActionQuery extends TAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "行为函数", hidden = false)
	private String actionFunction;

	public String getActionFunction() {
		return actionFunction;
	}

	public void setActionFunction(String actionFunction) {
		this.actionFunction = actionFunction;
	}
}
