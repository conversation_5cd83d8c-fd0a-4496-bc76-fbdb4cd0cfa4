package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:保存法规对象
 *
 * @Author:lyl
 * @Date:2023/11/30 10:47
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "DetalLawsQuery",description = "保存法规及明细对象")
@Data
public class DetailLawsQuery extends BaseEntity {
    /**
     * Description:法规名称
     *
     * @Author:lyl
     * @Date:2023/11/30 10:35
     */
    @ApiModelProperty(value = "法规名称,必须是全部名称,必传项")
    private String lawName;

    @ApiModelProperty(value = "法规id,此字段仅用于修改,新增时非必传")
    private Long id;
    /**
     * Description:检测项明细
     *
     * @Author:lyl
     * @Date:2023/11/30 10:53
     */
    @ApiModelProperty(value = "检测项明细")
    private TPrivacyCheck privacyCheck;

    @ApiModelProperty(value = "新检测项明细，用于修改时传入新修改的参数")
    private TPrivacyCheck newPrivacyCheck;
    /**
     * Description:规则，1.仅保存  2.保存并发布
     *
     * @Author:lyl
     * @Date:2023/11/30 10:52
     */
    @ApiModelProperty(value = "保存规则：1.仅保存 2.保存并发布")
    private Integer rule;

    @ApiModelProperty(value = "终端类型 1.android 2.ios 4.微信小程序 5.支付宝小程序")
    private Integer terminalType;

    @ApiModelProperty(value = "1发布,2下架")
    private Integer pushStatus;
}
