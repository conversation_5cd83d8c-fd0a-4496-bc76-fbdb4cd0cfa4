package cn.ijiami.detection.query;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.AITrainImageCategoryEnum;
import cn.ijiami.detection.enums.AITrainImageStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AITrainImageVo.java
 * @Description ai训练图片
 * @createTime 2024年03月06日 17:15:00
 */
@Data
public class AITrainImageVo {

    @ApiModelProperty(value = "图片id")
    private Long id;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "类型，1 按钮", hidden = false)
    private AITrainImageCategoryEnum category;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = false)
    private String createUserName;

    @ApiModelProperty(value = "状态，0 待训练 1 已删除 2 已导出", hidden = false)
    private AITrainImageStatusEnum status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
