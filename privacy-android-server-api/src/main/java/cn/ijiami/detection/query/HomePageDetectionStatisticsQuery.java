package cn.ijiami.detection.query;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDetectionStatisticsQuery.java
 * @Description 首页检测统计查询
 * @createTime 2024年05月15日 10:51:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomePageDetectionStatisticsQuery {

    @ApiModelProperty(value = "包名列表", hidden = false, example = "[\"com.tencent\", \"com.qq\"]")
    private List<String> packageNameList;

    @ApiModelProperty(value = "所属终端", hidden = false, example = "1")
    private Integer terminalType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;


}
