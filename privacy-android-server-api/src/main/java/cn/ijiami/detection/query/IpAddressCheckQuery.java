package cn.ijiami.detection.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IPAdressCheckQuery.java
 * @Description
 * @createTime 2021年11月19日 14:40:00
 */

@ApiModel(description = "IP拨测query对象")
@Data
public class IpAddressCheckQuery {

    @ApiModelProperty(value = "通讯传输行为id")
    private Long outsideId;

    @ApiModelProperty(value = "通知id")
    private String notificationId;
}
