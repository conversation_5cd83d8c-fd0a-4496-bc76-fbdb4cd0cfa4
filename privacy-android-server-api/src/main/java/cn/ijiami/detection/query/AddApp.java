package cn.ijiami.detection.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AddApp.java
 * @Description 添加app
 * @createTime 2025年02月18日 15:19:00
 */
@Data
public class AddApp {

    @NotBlank
    @ApiModelProperty(value = "应用名", required = true)
    private String appName;

    @NotBlank
    @ApiModelProperty(value = "应用包名", required = true)
    private String packageName;

    @ApiModelProperty(value = "上传成功的隐私文件id")
    private String appPrivacyPolicyFileId;

    @ApiModelProperty(value = "上传成功的第三方sdk列表文件id")
    private String thirdPartySharedListFileId;

}
