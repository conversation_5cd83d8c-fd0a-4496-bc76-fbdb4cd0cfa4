package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReviewFinish.java
 * @Description 复核完成
 * @createTime 2024年03月06日 16:28:00
 */
@Data
@ApiModel("深度检测完成")
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeepDetectionFinish {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "只保存个人隐私相关行为")
    private Boolean isOnlySavePersonalBehavior;

}
