package cn.ijiami.detection.service.api;

import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.detection.message.enums.MessageNotificationEnum;
import net.sf.json.JSONObject;

/**
 * 消息推送接口
 *
 * <AUTHOR>
 */
public interface ISendMessageService {

    /**
     * 推送结果消息
     *
     * @param message
     * @return
     */
    boolean sendMessage(String message);

    /**
     * 推送广播消息
     *
     * @param message 消息内容
     * @param task 任务信息
     */
    void sendBroadcast(String message, TTask task);

    /**
     * 发送任务进度
     * @param typeEnum
     * @param progress
     * @param task
     * @param delayMillis 延迟发送时间
     */
    void sendTaskProgressBroadcast(BroadcastMessageTypeEnum typeEnum, int progress, TTask task, long delayMillis);


    /**
     * 发送任务状态
     * @param typeEnum
     * @param describe
     * @param task
     * @param delayMillis 延迟发送时间
     */
    void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task, long delayMillis);

    /**
     * 发送任务进度
     * @param typeEnum
     * @param progress
     * @param task
     */
    void sendTaskProgressBroadcast(BroadcastMessageTypeEnum typeEnum, int progress, TTask task);

    /**
     * 发送任务状态广播，所有用户都能收到
     * @param typeEnum
     * @param describe
     * @param task
     */
    void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task);

    /**
     * 发送任务状态广播，任务用户才能收到
     * @param typeEnum
     * @param describe
     * @param task
     */
    void sendTaskStatusMessage(BroadcastMessageTypeEnum typeEnum, String describe, TTask task);

    /**
     * 发送扫码检测消息
     * @param message
     * @param userId
     * @param notificationId
     */
    void sendScanQRCodeMessage(String message, Long userId, String notificationId);

    /**
     * 发送ip拨测完成消息
     * @param message
     * @param userId
     * @param notificationId
     */
    void sendIpAddressCheckMessage(String message, Long userId, String notificationId);

    /**
     * 发送系统通知消息给用户，前端调用接口去拉取具体的消息
     * @param message
     * @param userId
     * @param notificationId
     */
    void sendSystemNoticeMessage(String message, Long userId, String notificationId);

    /**
     * 发送任务日志
     * @param message
     * @param data
     */
    void sendTaskDynamicLogMessage(String message, DynamicTaskContext data);

    /**
     * 发送任务日志
     * @param json
     * @param data
     */
    void sendTaskDynamicLogMessage(JSONObject json, DynamicTaskContext data);

    /**
     * 发送网络日志
     * @param json
     * @param data
     */
    void sendTaskNetLogMessage(JSONObject json, DynamicTaskContext data);

    /**
     * 发送检测任务传感器日志给前端
     * @param json
     * @param data 任务数据
     */
    void sendTaskSensorLogMessage(JSONObject json, DynamicTaskContext data);

    /**
     * 发送通知
     * 已经废弃，会导致前端多个标签页都收到消息
     * @param notificationType
     * @param type
     * @param message
     * @param userId
     * @return
     */
    @Deprecated
    boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, Long userId);

    /**
     * 已经废弃，会导致前端多个标签页都收到消息
     * @param notificationType
     * @param type
     * @param message
     * @param title
     * @param userId
     * @param hiddenEnum
     * @return
     */
    @Deprecated
    boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, String title, Long userId, HiddenEnum hiddenEnum);

    /**
     * 已经废弃，会导致前端多个标签页都收到消息
     * @param notificationType
     * @param type
     * @param message
     * @param title
     * @param userId
     * @param hiddenEnum
     * @param topicName
     * @return
     */
    @Deprecated
    boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, String title, Long userId, HiddenEnum hiddenEnum, String topicName);


    boolean sendNoticeToBrowserTab(MessageNotificationEnum notificationType, String type, String title, Long userId, String notificationId);

    /**
     * 用户有可能多开标签页，这个方法能发送通知到指定用户的某个标签页上
     * @param notificationType
     * @param type
     * @param message
     * @param title
     * @param userId
     * @param hiddenEnum
     * @param notificationId
     * @return
     */
    boolean sendNoticeToBrowserTab(MessageNotificationEnum notificationType, String type, String message,String title, Long userId, HiddenEnum hiddenEnum, String notificationId);

    /**
     * 发送砸壳进度更新通知
     * @param message
     * @param userId
     * @param notificationId
     */
    void sendShellSmashingProgressMessage(String message, Long userId, String notificationId);
}
