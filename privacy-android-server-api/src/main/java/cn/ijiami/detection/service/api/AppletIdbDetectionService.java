package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.DetectionDeviceVO;
import cn.ijiami.detection.entity.TUserUseDevice;
import net.sf.json.JSONObject;

public interface AppletIdbDetectionService {

    void updateAppletDynamicFromStomp(JSONObject message, String messageStr);

    void autoAnalyticalWechatLog(Long taskId, String data);

    void autoAnalyticalAlipayLog(Long taskId, String logText);

    /**
     * 深度检测获取占用设备
     *
     * @param userId
     * @return
     */
    DetectionDeviceVO getUserDevice(Long userId, Integer terminalType);

    TUserUseDevice findAppletUseDevicesByUserId(Long userId, Integer terminalType);
}
