package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.detection.ScreenshotImage;
import cn.ijiami.detection.entity.*;

import java.util.List;
import java.util.Map;

/**
 * 将截图入库的方法抽出来单独写一个服务类
 * @date  2021/6/24 16:01:00
 */
public interface IScreenshotImageService {

    /**
     * 获取检测时的手动截图数据
     * @param taskId
     * @return
     */
    List<ScreenshotImage> getManualScreenshotImage(Long taskId);

    /**
     * 安卓与ios快速检测检测截图入库，用于应用行为截图
     * @param taskId  任务id
     * @param screenshotMap 截图时间搓跟filekey的map
     * @param tPrivacyActionNougatList 应用行为实体
     */
    void saveDynamicBehaviorImg(Long taskId, Map<Long,String> screenshotMap,List<TPrivacyActionNougat> tPrivacyActionNougatList);

    /**
     * 安卓与ios快速检测截图入库，用于通信行为
     * @param taskId
     * @param screenshotMap
     * @param tPrivacyOutsideAddressList
     */
    void saveImgOfOutsideData(Long taskId, Map<Long,String> screenshotMap,List<TPrivacyOutsideAddress> tPrivacyOutsideAddressList);

    /**
     * 安卓与ios快速检测截图入库，用于传输个人信息行为截图
     * @param taskId 任务id
     * @param screenshotMap 截图时间搓与filekey
     * @param tPrivacySensitiveWordList  传输个人信息行为实体
     */
    void saveDynamicBehaviorImgSensitiveWord(Long taskId, Map<Long,String> screenshotMap, List<TPrivacySensitiveWord> tPrivacySensitiveWordList);

    /**
     * 安卓与ios快速检测截图入库，用于存储个人信息行为
     * @param taskId
     * @param screenshotMap
     * @param tPrivacySharedPrefsList
     */
    void saveImgOfSharedData(Long taskId, Map<Long,String> screenshotMap, List<TPrivacySharedPrefs> tPrivacySharedPrefsList);


    /**
     * 安卓与ios深度检测动态截图入库，用于应用行为手动截图
     *
     * @param taskId                   任务id
     * @param tPrivacyActionNougatList 应用行为实体
     * @param screenshotImages         手动截图的数据
     */
    void saveManualBehaviorImg(Long taskId, List<TPrivacyActionNougat> tPrivacyActionNougatList, List<ScreenshotImage> screenshotImages, Map<Long, TActionNougat> actionNougatMap);

    /**
     * 安卓与ios深度检测动态截图入库，用于通信行为手动截图
     * @param taskId  任务id
     * @param tPrivacyOutsideAddressList 通信行为实体
     * @param screenshotImages 手动截图的数据
     */
    void saveManualBehaviorImgOutside(Long taskId,List<TPrivacyOutsideAddress> tPrivacyOutsideAddressList,List<ScreenshotImage> screenshotImages);

    /**
     * 安卓与ios深度检测动态截图入库，用于传输个人信息行为手动截图
     * @param taskId  任务id
     * @param tPrivacySensitiveWordList 传输个人信息行为实体
     * @param screenshotImages 手动截图的数据
     */
    void saveManualBehaviorImgSensitiveWord(Long taskId,List<TPrivacySensitiveWord> tPrivacySensitiveWordList,List<ScreenshotImage> screenshotImages);

    /**
     * 安卓与ios深度检测动态截图入库，用于存储个人信息行为手动截图
     * @param taskId  任务id
     * @param tPrivacySharedPrefsList 存储个人信息实体
     * @param screenshotImages 手动截图的数据
     */
    void saveManualBehaviorImgShared(Long taskId,List<TPrivacySharedPrefs> tPrivacySharedPrefsList,List<ScreenshotImage> screenshotImages);


    /**
     * ios快速检测保存截图图片并返回时间搓与图片key的map集合
     * @param screenshotPath
     * @return
     */
    Map<Long, String> savePictureAndReturnMap(String screenshotPath);

    /**
     * 安卓快速检测保存截图图片并返回时间搓与图片key的map集合
     *
     * @param screenshotPath
     * @return
     */
    Map<Long, String> saveScreenPictureReturnMap(String screenshotPath);


    void deleteByTaskId(Long taskId);
}
