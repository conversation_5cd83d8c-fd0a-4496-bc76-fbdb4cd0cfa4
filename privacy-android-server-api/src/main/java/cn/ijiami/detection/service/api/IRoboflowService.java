package cn.ijiami.detection.service.api;

import org.springframework.web.multipart.MultipartFile;

public interface IRoboflowService {
	
	/**
	 * 图片上传
	 * @param apiKey
	 * @param datasetName
	 * @param data
	 * @return
	 */
	public String roboflowUploadImage(String apiKey,String datasetName,MultipartFile data);
	
	/**
	 * 图片上传-本地图片
	 * @param apiKey
	 * @param datasetName
	 * @param imagePath
	 * @return
	 */
	public String roboflowUploadImageLocal(String imagePath);
	
	/**
	 * 导出数据集
	 * @param workspace
	 * @param project
	 * @param version
	 * @param format
	 * @param apiKey
	 * @return
	 */
	public String exprotData(String workspace,String project,String version, String format,String apiKey);
}
