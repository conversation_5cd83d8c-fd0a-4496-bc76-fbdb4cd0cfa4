package cn.ijiami.detection.service.api;

import cn.ijiami.detection.query.ThreePartyAuth;

import java.io.IOException;
import java.io.UnsupportedEncodingException;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 第三方授权服务
 *
 * <AUTHOR>
 * @date 2020/3/27 10:50
 **/
public interface IThreePartyAuthService {

	/**
	 * 三方授权登录接口
	 * 
	 * @param threePartyAuth 接口参数
	 * @param request 
	 * @param response http response
	 * @return 
	 * @throws UnsupportedEncodingException 
	 * @throws IOException 
	 */
	void auth(ThreePartyAuth threePartyAuth, HttpServletRequest request, HttpServletResponse response);
}
