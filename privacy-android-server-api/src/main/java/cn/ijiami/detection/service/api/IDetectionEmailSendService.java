package cn.ijiami.detection.service.api;

import cn.ijiami.detection.entity.TEmailSend;

import java.util.List;

/**
 * 邮件发送服务
 * 
 * <AUTHOR>
 *
 */

public interface IDetectionEmailSendService {

	/**
	 * 批量保存邮件发送信息
	 * 
	 * @param emailSendList
	 * @return
	 */
	public boolean batchSave(List<TEmailSend> emailSendList);

	/**
	 * 批量更新
	 * 
	 * @param emailSendList
	 * @return
	 */
	public boolean batchUpdate(List<TEmailSend> emailSendList);

	/**
	 * 发送邮件（批量）
	 * 
	 * @param emailSendList 邮件内容信息集合
	 * @param receiveAddr   收件人地址集合
	 * @return
	 */
	public boolean sendEmail(List<TEmailSend> emailSendList, List<String> receiveAddr);
}
