package cn.ijiami.detection.service.api;

import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.entity.TIosFrameworkLibrary;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import com.ocpframework.sdk.detection.vo.SdkApiVO;

import java.util.List;
import java.util.Map;

public interface IosActionExecutorService {

    void setNougatExecutor(ActionExecutor actionExecutor, String stackInfo, String executableName,
                           String appName, String packageName,
                           List<TIosFrameworkLibrary> libraryList);

    void setNougatExecutor(ActionExecutor actionExecutor, String stackInfo, String executableName,
                           String appName, String packageName, Map<String, SdkApiVO> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                           List<TIosFrameworkLibrary> libraryList);

}
