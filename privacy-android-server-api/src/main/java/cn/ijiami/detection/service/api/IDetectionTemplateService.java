package cn.ijiami.detection.service.api;

import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.VO.DetectionTemplateTypeVO;
import cn.ijiami.detection.VO.DetectionTemplateVO;
import cn.ijiami.detection.entity.TDetectionTemplate;
import cn.ijiami.detection.query.DetectionTemplateQuery;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

/**
 * 检测模版接口类
 * 
 * <AUTHOR>
 *
 */
public interface IDetectionTemplateService {
	/**
	 * 添加检测模版
	 * 
	 * @param detectionTemplateVO
	 * @return
	 * @throws IjiamiApplicationException
	 */
	TDetectionTemplate addDetectionTemplate(DetectionTemplateVO detectionTemplateVO) throws IjiamiApplicationException;

	/**
	 * 删除检测模版
	 * 
	 * @param id
	 * @return
	 * @throws IjiamiApplicationException
	 */
	int deleteDetectionTemplateById(Long templateId) throws IjiamiApplicationException;

	/**
	 * 查询检测模版集合并分页
	 * 
	 * @param detectionTemplate
	 * @return
	 */
	PageInfo<TDetectionTemplate> findDetectionTemplateByPage(DetectionTemplateQuery detectionTemplateQuery);

	/**
	 * 查看详情
	 * 
	 * @param Id
	 * @return
	 * @throws IjiamiApplicationException
	 */
	PageInfo<DetectionItemVO> findDetectionTemplateDetail(Long templateId) throws IjiamiApplicationException;
	
	/**
	 * 查询模板信息
	 * @param templateId
	 * @return
	 */
	TDetectionTemplate findById(Long templateId);
	
	List<TDetectionTemplate> getTemplateList(DetectionTemplateQuery detectionTemplateQuery);
	
	PageInfo<DetectionTemplateTypeVO> findDetectionTemplateVoByPage(DetectionTemplateQuery detectionTemplateQuery);
}
