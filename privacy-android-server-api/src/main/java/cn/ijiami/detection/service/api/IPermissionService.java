package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.UserPermissionVO;
import cn.ijiami.detection.entity.TPermission;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 铭感权限接口类
 * 
 * <AUTHOR>
 *
 */
public interface IPermissionService {
	/**
	 * 根据敏感权限对象查询用户敏感权限并分页
	 * 
	 * @param permission
	 * @return
	 */
	PageInfo<UserPermissionVO> findPermissionByPage(TPermission permission);

	/**
	 * 查询用户的权限
	 * 
	 * @param userId
	 * @return List<UserPermissionVO>
	 */
	List<TPermission> findAllPermission();

	String findPermissions();
}
