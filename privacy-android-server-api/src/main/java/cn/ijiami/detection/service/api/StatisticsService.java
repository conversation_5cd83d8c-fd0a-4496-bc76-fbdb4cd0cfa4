package cn.ijiami.detection.service.api;

import cn.ijiami.detection.android.client.dto.AssetsDetectionDTO;
import cn.ijiami.detection.android.client.dto.AssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticalSummaryDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDetailDTO;
import cn.ijiami.detection.VO.statistics.DetectionStatistics;
import cn.ijiami.detection.VO.statistics.*;
import cn.ijiami.detection.android.client.param.AssetsDetailsParam;
import cn.ijiami.detection.android.client.param.AssetsInfoParam;
import cn.ijiami.detection.android.client.param.AssetsStatisticsParam;
import cn.ijiami.detection.query.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface StatisticsService {

    AssetsStatisticalSummaryDTO assetsStatisticalSummary(AssetsStatisticsParam query);

    AssetsStatisticsDTO assetsStatistics(AssetsStatisticsParam query);

    List<AssetsDetectionDTO> detectionTopList(AssetsStatisticsParam query);

    PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(AssetsDetailsParam query);

    List<AssetsStatisticsDetailDTO> assetsDetailsAll(Long userId, AssetsStatisticsParam query);

    List<AssetsTask> assetsTaskAll(Long userId, AssetsStatisticsParam query);

    DetectionStatistics detectionStatistics(Long userId, DetectionStatisticsQuery query);

    PageInfo<DetectionStatisticsDetail> detectionDetailsByPage(Long userId, DetectionDetailsQuery query);

    DetectionStatisticsLawItem detectionLawItem(Long userId, DetectionLawItemQuery query, Integer pageSize);

    PageInfo<AssetsInfoDTO> detectionLawItemAssetsByPage(Long userId, AssetsInfoParam query);

    PageInfo<SdkStatisticsDetail> sdkDetailsByPage(Long userId, SdkDetailsQuery query);

    DetectFalsePositivesStatistics detectFalsePositivesStatistics(Long userId, DetectionStatisticsQuery query);

    PageInfo<DetectFalsePositivesDetail> detectFalsePositivesByPage(Long userId, DetectionDetailsQuery query);

    DetectFalsePositivesLawItem detectFalsePositivesLawItem(Long userId, DetectionLawItemQuery query, Integer pageSize);

    PageInfo<DetectFalsePositivesAssetsInfo> detectFalsePositivesLawItemAssetsByPage(Long userId, AssetsInfoParam query);

    List<DetectFalsePositivesReportAssetsInfo> findDetectFalsePositivesReport(Long userId, DetectionStatisticsQuery query);

    HomePageDetectionStatistics homePageDetectionStatistics(Long userId, HomePageDetectionStatisticsQuery query);

    void deleteByTaskId(Long taskId);

    void deleteByAssetsId(Long assetsId);
}