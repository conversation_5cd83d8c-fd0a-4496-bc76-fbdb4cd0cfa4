package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.ApiTaskTagInfoVO;
import cn.ijiami.detection.VO.TaskTagInfoVO;
import cn.ijiami.detection.query.APITaskTagQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface ITaskTagService {

    TaskTagInfoVO getTaskTagList(Long taskId);

    void saveTaskTag(Long taskId, String message, List<Long> tagIdList);

    PageInfo<ApiTaskTagInfoVO> findTaskTagByPage(APITaskTagQuery query);
}
