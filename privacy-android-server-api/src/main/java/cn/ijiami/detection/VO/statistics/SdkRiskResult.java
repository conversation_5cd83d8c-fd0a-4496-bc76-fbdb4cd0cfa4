package cn.ijiami.detection.VO.statistics;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkRiskResult.java
 * @Description sdk风险查询
 * @createTime 2022年07月08日 16:14:00
 */
@Data
public class SdkRiskResult {

    /**
     * sdkid
     */
    private Long sdkId;

    /**
     * sdk名称
     */
    private String sdkName;

    /**
     * sdk包名
     */
    private String sdkPackage;

    /**
     * 厂家
     */
    private String sdkManufacturer;

    /**
     * 风险数
     */
    private Integer riskCount;

    private String lawName;

    private Integer lawId;

}
