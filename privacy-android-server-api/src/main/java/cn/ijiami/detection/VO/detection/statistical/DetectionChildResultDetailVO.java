package cn.ijiami.detection.VO.detection.statistical;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.IpDetailVO;
import cn.ijiami.detection.VO.detection.SensitiveWordFilePathVO;
import cn.ijiami.detection.entity.TDetectionItem;

/**
 * 报告检测结果详情分析子VO
 * 
 * <AUTHOR>
 *
 */
public class DetectionChildResultDetailVO extends TDetectionItem {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	// 等级
	private String gradeName;
	// 状态
	private int status;
	// 检测结果
	private String result;

	// 结果描述
	private String resultDetail;

	// 检测文件总数
	private String total_num;

	// 安全文件总数
	private String safe_num;

	// 风险文件总数
	private String unsafe_num;

	private boolean is_dynamic = false;
	// 详情列表(有文件列表、代码列表返回)
	private List<SensitiveWordFilePathVO> detailList = new ArrayList<SensitiveWordFilePathVO>();

	// 详情列表2(文件列表字符串)
	private List<String> detail2List = new ArrayList<String>();

	// 资源文件中包含APK
	private List<BaseMessageVO> apkBaseList = new ArrayList<BaseMessageVO>();

	// ip数据
	private List<IpDetailVO> ipList = new ArrayList<IpDetailVO>();
	
	public String getResult() {
		return result;	
	}

	public String getResultDetail() {
		return resultDetail;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public void setResultDetail(String resultDetail) {
		this.resultDetail = resultDetail;
	}

	public String getTotal_num() {
		return total_num;
	}

	public String getSafe_num() {
		return safe_num;
	}

	public String getUnsafe_num() {
		return unsafe_num;
	}

	public void setTotal_num(String total_num) {
		this.total_num = total_num;
	}

	public void setSafe_num(String safe_num) {
		this.safe_num = safe_num;
	}

	public void setUnsafe_num(String unsafe_num) {
		this.unsafe_num = unsafe_num;
	}

	public List<SensitiveWordFilePathVO> getDetailList() {
		return detailList;
	}

	public List<String> getDetail2List() {
		return detail2List;
	}

	public void setDetailList(List<SensitiveWordFilePathVO> detailList) {
		this.detailList = detailList;
	}

	public void setDetail2List(List<String> detail2List) {
		this.detail2List = detail2List;
	}

	public boolean isIs_dynamic() {
		return is_dynamic;
	}

	public void setIs_dynamic(boolean is_dynamic) {
		this.is_dynamic = is_dynamic;
	}

	public String getGradeName() {
		return gradeName;
	}

	public void setGradeName(String gradeName) {
		this.gradeName = gradeName;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public List<BaseMessageVO> getApkBaseList() {
		return apkBaseList;
	}

	public void setApkBaseList(List<BaseMessageVO> apkBaseList) {
		this.apkBaseList = apkBaseList;
	}

	public List<IpDetailVO> getIpList() {
		return ipList;
	}

	public void setIpList(List<IpDetailVO> ipList) {
		this.ipList = ipList;
	}

}
