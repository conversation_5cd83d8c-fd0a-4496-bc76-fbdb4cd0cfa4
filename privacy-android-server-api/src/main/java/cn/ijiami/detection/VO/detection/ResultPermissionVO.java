package cn.ijiami.detection.VO.detection;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 应用权限检测返回值
 * 
 * <AUTHOR>
 *
 */
public class ResultPermissionVO {

	// 权限名称
	@JSONField(alternateNames = "permission_name")
	private String permissionName;

	// 描述
	@JSONField(alternateNames = "permission_describe")
	private String permissionDescribe;

	// 权限等级
	@JSONField(alternateNames = "permission_grade")
	private String permissionGrade;

	// 是否敏感
	private String isSensitive = "否";

	// 是否滥用
	private String isAbuse = "否";

	//等级编号
	private Integer gradeNo;
	public Integer getGradeNo() {
		return gradeNo;
	}

	public void setGradeNo(Integer gradeNo) {
		this.gradeNo = gradeNo;
	}


	public String getPermissionName() {
		return permissionName;
	}

	public void setPermissionName(String permissionName) {
		this.permissionName = permissionName;
	}

	public String getPermissionDescribe() {
		return permissionDescribe;
	}

	public void setPermissionDescribe(String permissionDescribe) {
		this.permissionDescribe = permissionDescribe;
	}

	public String getPermissionGrade() {
		return permissionGrade;
	}

	public void setPermissionGrade(String permissionGrade) {
		this.permissionGrade = permissionGrade;
	}

	public String getIsSensitive() {
		return isSensitive;
	}

	public String getIsAbuse() {
		return isAbuse;
	}

	public void setIsSensitive(String isSensitive) {
		this.isSensitive = isSensitive;
	}

	public void setIsAbuse(String isAbuse) {
		this.isAbuse = isAbuse;
	}

}
