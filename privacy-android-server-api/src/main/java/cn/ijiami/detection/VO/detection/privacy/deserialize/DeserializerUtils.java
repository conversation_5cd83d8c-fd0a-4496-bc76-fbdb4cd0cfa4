package cn.ijiami.detection.VO.detection.privacy.deserialize;

import cn.ijiami.detection.bean.ResultContent;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeserializerUtils.java
 * @Description 解析工具
 * @createTime 2023年05月08日 18:01:00
 */
@Slf4j
public class DeserializerUtils {
	
	private static final String JSON_CONVERT_FAILURE_LOG = "jsonConvertToBean failure={}";

    public static <T> T toBean(JsonNode p, TypeReference<T> typeRef) {
        ObjectMapper objectMapper = makeMapper();
        try {
            String text = p.toString();
            return objectMapper.readValue(text, typeRef);
        } catch (IOException e) {
            log.info(JSON_CONVERT_FAILURE_LOG, e.getMessage());
            return null;
        }
    }

    public static <T> T toBean(String json, TypeReference<T> typeRef) {
        ObjectMapper objectMapper = makeMapper();
        try {
            return objectMapper.readValue(json, typeRef);
        } catch (IOException e) {
            log.info(JSON_CONVERT_FAILURE_LOG, e.getMessage());
            return null;
        }
    }

    public static <T> T toBean(String json, Class<T> clazz) {
        ObjectMapper objectMapper = makeMapper();
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.info(JSON_CONVERT_FAILURE_LOG, e.getMessage());
            return null;
        }
    }

    private static ObjectMapper makeMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.enable(JsonParser.Feature.ALLOW_SINGLE_QUOTES);
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        return objectMapper;
    }

}
