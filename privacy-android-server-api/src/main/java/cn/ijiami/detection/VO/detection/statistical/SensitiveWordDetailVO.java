package cn.ijiami.detection.VO.detection.statistical;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.VO.detection.SensitiveWordVO;

/**
 * 敏感词汇检测详情VO
 * 
 * <AUTHOR>
 *
 */
public class SensitiveWordDetailVO {

	// 敏感词统计vo
	private SensitiveWordCountVO sensitiveWordCountVO;

	// 敏感词汇集合
	private List<SensitiveWordVO> sensitiveWordVOList = new ArrayList<SensitiveWordVO>();

	public SensitiveWordCountVO getSensitiveWordCountVO() {
		return sensitiveWordCountVO;
	}

	public void setSensitiveWordCountVO(SensitiveWordCountVO sensitiveWordCountVO) {
		this.sensitiveWordCountVO = sensitiveWordCountVO;
	}

	public List<SensitiveWordVO> getSensitiveWordVOList() {
		return sensitiveWordVOList;
	}

	public void setSensitiveWordVOList(List<SensitiveWordVO> sensitiveWordVOList) {
		this.sensitiveWordVOList = sensitiveWordVOList;
	}
}
