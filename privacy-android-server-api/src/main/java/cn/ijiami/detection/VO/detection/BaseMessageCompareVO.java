package cn.ijiami.detection.VO.detection;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import cn.ijiami.detection.VO.PrivacyCategoryVO;
import cn.ijiami.detection.query.FieldCompare;
import io.swagger.annotations.ApiModelProperty;

/**
 * 基本信息vo
 *
 * <AUTHOR>
 */
public class BaseMessageCompareVO {

    // 应用名称
    @ApiModelProperty(value ="应用名称")
    private FieldCompare<String> appName;

    // 包名
    @ApiModelProperty(value ="包名")
    private FieldCompare<String> packageName;

    // apk 大小
    @ApiModelProperty(value ="apk 大小")
    private FieldCompare<String> apkSize;

    // 版本
    @ApiModelProperty(value ="版本")
    private FieldCompare<String> versionName;

    private FieldCompare<String> targetSdkVersion;

    private FieldCompare<String> minSdkVersion;

    // 文件MD5
    @ApiModelProperty(value ="文件MD5")
    @JSONField(alternateNames = "apkMD5")
    private FieldCompare<String> apkMd5;

    // 签名MD5
    @ApiModelProperty(value ="签名MD5")
    private FieldCompare<String> signMd5;

    // 签名
    @ApiModelProperty(value ="签名")
    private FieldCompare<String> signDetail;

    // 是否加固
    @ApiModelProperty(value ="是否加固")
    private FieldCompare<String> encryptDetail;

    //apk文件路径
    @ApiModelProperty(value ="apk文件路径")
    private FieldCompare<String> filePath;

    /**
     * 所属终端
     */
    @ApiModelProperty(value ="所属终端")
    private FieldCompare<Integer> terminalType;

    //签名详情（后面新增）
    @ApiModelProperty(value ="签名详情")
    private FieldCompare<List<String>> signDetailList;

    //热更新SDK
    @ApiModelProperty(value ="热更新SDK")
    private FieldCompare<String> hotUpdateSdks;

    //应用图片
    @ApiModelProperty(value ="应用图片")
    private FieldCompare<String> apkLogo;

    @ApiModelProperty(value = "检测耗时")
    private String apkDetectionTime;

    @ApiModelProperty(value = "检测开始时间")
    private String apkDetectionStarttime;

    //源文件名称
    @ApiModelProperty(value = "源文件名称")
    private FieldCompare<String> sourceFileName;
    
    @ApiModelProperty(value = "SHA-1值")
    @JSONField(alternateNames = "sha_1")
    private FieldCompare<String> sha1;

    @ApiModelProperty(value = "SHA-256值")
    @JSONField(alternateNames = "sha_256")
    private FieldCompare<String> sha256;
    
    @ApiModelProperty(value = "设备型号")
    private FieldCompare<String> model;
    
    @ApiModelProperty(value = "设备版本")
    private FieldCompare<String> version;
    
    @ApiModelProperty(value = "检测法规勾选")
    private FieldCompare<String> detectionLaws;

    @ApiModelProperty(value = "业务功能分类")
    private FieldCompare<PrivacyCategoryVO> privacyCategory;


    public FieldCompare<String> getServiceCategory() {
        return serviceCategory;
    }

    public void setServiceCategory(FieldCompare<String> serviceCategory) {
        this.serviceCategory = serviceCategory;
    }

    public FieldCompare<String> getAccountSubject() {
        return accountSubject;
    }

    public void setAccountSubject(FieldCompare<String> accountSubject) {
        this.accountSubject = accountSubject;
    }

    public FieldCompare<String> getAppId() {
        return appId;
    }

    public void setAppId(FieldCompare<String> appId) {
        this.appId = appId;
    }

    public FieldCompare<String> getOriginalId() {
        return originalId;
    }

    public void setOriginalId(FieldCompare<String> originalId) {
        this.originalId = originalId;
    }

    public FieldCompare<String> getPrivacyData() {
        return privacyData;
    }

    public void setPrivacyData(FieldCompare<String> privacyData) {
        this.privacyData = privacyData;
    }

    public FieldCompare<String> getStatement() {
        return statement;
    }

    public void setStatement(FieldCompare<String> statement) {
        this.statement = statement;
    }

    public FieldCompare<String> getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(FieldCompare<String> updateTime) {
        this.updateTime = updateTime;
    }

    public FieldCompare<String> getPlugins() {
        return plugins;
    }

    public void setPlugins(FieldCompare<String> plugins) {
        this.plugins = plugins;
    }

    public FieldCompare<String> getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(FieldCompare<String> serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    /**
     * 小程序服务类目
     */
    @ApiModelProperty(value = "小程序服务类目")
    private FieldCompare<String> serviceCategory;

    /**
     * 小程序账号主体
     */
    @ApiModelProperty(value = "小程序账号主体")
    private FieldCompare<String> accountSubject;

    /**
     * 小程序原始id
     */
    @ApiModelProperty(value = "小程序appId")
    private FieldCompare<String> appId;

    /**
     * 小程序原始id
     */
    @ApiModelProperty(value = "小程序原始id")
    private FieldCompare<String> originalId;

    /**
     * 小程序服务隐私及数据提示
     */
    @ApiModelProperty(value = "小程序服务隐私及数据提示")
    private FieldCompare<String> privacyData;

    /**
     * 服务声明
     */
    @ApiModelProperty(value = "小程序服务声明")
    private FieldCompare<String> statement;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "小程序更新时间")
    private FieldCompare<String> updateTime;

    /**
     * 引用插件
     */
    @ApiModelProperty(value = "小程序引用插件")
    private FieldCompare<String> plugins;

    /**
     * 授权服务商
     */
    @ApiModelProperty(value = "授权服务商")
    private FieldCompare<String> serviceProvider;

    public FieldCompare<String> getAppName() {
        return appName;
    }

    public void setAppName(FieldCompare<String> appName) {
        this.appName = appName;
    }

    public FieldCompare<String> getPackageName() {
        return packageName;
    }

    public void setPackageName(FieldCompare<String> packageName) {
        this.packageName = packageName;
    }

    public FieldCompare<String> getApkSize() {
        return apkSize;
    }

    public void setApkSize(FieldCompare<String> apkSize) {
        this.apkSize = apkSize;
    }

    public FieldCompare<String> getVersionName() {
        return versionName;
    }

    public void setVersionName(FieldCompare<String> versionName) {
        this.versionName = versionName;
    }

    public FieldCompare<String> getTargetSdkVersion() {
        return targetSdkVersion;
    }

    public void setTargetSdkVersion(FieldCompare<String> targetSdkVersion) {
        this.targetSdkVersion = targetSdkVersion;
    }

    public FieldCompare<String> getMinSdkVersion() {
        return minSdkVersion;
    }

    public void setMinSdkVersion(FieldCompare<String> minSdkVersion) {
        this.minSdkVersion = minSdkVersion;
    }

    public FieldCompare<String> getApkMd5() {
        return apkMd5;
    }

    public void setApkMd5(FieldCompare<String> apkMd5) {
        this.apkMd5 = apkMd5;
    }

    public FieldCompare<String> getSignMd5() {
        return signMd5;
    }

    public void setSignMd5(FieldCompare<String> signMd5) {
        this.signMd5 = signMd5;
    }

    public FieldCompare<String> getSignDetail() {
        return signDetail;
    }

    public void setSignDetail(FieldCompare<String> signDetail) {
        this.signDetail = signDetail;
    }

    public FieldCompare<String> getEncryptDetail() {
        return encryptDetail;
    }

    public void setEncryptDetail(FieldCompare<String> encryptDetail) {
        this.encryptDetail = encryptDetail;
    }

    public FieldCompare<String> getFilePath() {
        return filePath;
    }

    public void setFilePath(FieldCompare<String> filePath) {
        this.filePath = filePath;
    }

    public FieldCompare<Integer> getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(FieldCompare<Integer> terminalType) {
        this.terminalType = terminalType;
    }

    public FieldCompare<List<String>> getSignDetailList() {
        return signDetailList;
    }

    public void setSignDetailList(FieldCompare<List<String>> signDetailList) {
        this.signDetailList = signDetailList;
    }

    public FieldCompare<String> getHotUpdateSdks() {
        return hotUpdateSdks;
    }

    public void setHotUpdateSdks(FieldCompare<String> hotUpdateSdks) {
        this.hotUpdateSdks = hotUpdateSdks;
    }

    public FieldCompare<String> getApkLogo() {
        return apkLogo;
    }

    public void setApkLogo(FieldCompare<String> apkLogo) {
        this.apkLogo = apkLogo;
    }

    public String getApkDetectionTime() {
        return apkDetectionTime;
    }

    public void setApkDetectionTime(String apkDetectionTime) {
        this.apkDetectionTime = apkDetectionTime;
    }

    public String getApkDetectionStarttime() {
        return apkDetectionStarttime;
    }

    public void setApkDetectionStarttime(String apkDetectionStarttime) {
        this.apkDetectionStarttime = apkDetectionStarttime;
    }

    public FieldCompare<String> getSourceFileName() {
        return sourceFileName;
    }

    public void setSourceFileName(FieldCompare<String> sourceFileName) {
        this.sourceFileName = sourceFileName;
    }

    public FieldCompare<String> getSha1() {
        return sha1;
    }

    public void setSha1(FieldCompare<String> sha1) {
        this.sha1 = sha1;
    }

    public FieldCompare<String> getSha256() {
        return sha256;
    }

    public void setSha256(FieldCompare<String> sha256) {
        this.sha256 = sha256;
    }

    public FieldCompare<String> getModel() {
        return model;
    }

    public void setModel(FieldCompare<String> model) {
        this.model = model;
    }

    public FieldCompare<String> getVersion() {
        return version;
    }

    public void setVersion(FieldCompare<String> version) {
        this.version = version;
    }

    public FieldCompare<String> getDetectionLaws() {
        return detectionLaws;
    }

    public void setDetectionLaws(FieldCompare<String> detectionLaws) {
        this.detectionLaws = detectionLaws;
    }

    public FieldCompare<PrivacyCategoryVO> getPrivacyCategory() {
        return privacyCategory;
    }

    public void setPrivacyCategory(FieldCompare<PrivacyCategoryVO> privacyCategory) {
        this.privacyCategory = privacyCategory;
    }
}
