package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TPrivacyActionNougat;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/4 14:18
 */
@Data
public class CountActionNougatVO implements Serializable {

    private static final long serialVersionUID = 7903359683299087164L;
    private List<TPrivacyActionNougat> privacyActionNougats = new ArrayList<>();

    private Integer allSum = 0;

    private Integer riskSum = 0;

    private String pieChartImageBase64 = "";
}
