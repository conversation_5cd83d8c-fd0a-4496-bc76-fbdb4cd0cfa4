package cn.ijiami.detection.VO.statistics;

import cn.ijiami.detection.VO.LawDetectResult;
import cn.ijiami.detection.enums.LawResultStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesItemDetail.java
 * @Description 误报详情
 * @createTime 2023年11月13日 10:17:00
 */
@Data
public class DetectFalsePositivesReportItem implements LawDetectResult {

    private Long resultId;

    private Long lawsItemParentId;

    private String lawsItemParentName;

    private String lawsItemName;

    private String aliasSuggestion;

    private String aliasName;

    private String suggestion;

    private String conclusion;

    private String sceneTitle;

    private LawResultStatusEnum originalResultStatus;

    private LawResultStatusEnum resultStatus;

    private String markDescription;

    @Override
    public void setName(String name) {
        this.lawsItemName = name;
    }

    @Override
    public String getName() {
        return lawsItemName;
    }
}
