package cn.ijiami.detection.VO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName Check.java
 * @Description 是否允许发起检测
 * @createTime 2023年06月21日 17:27:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckStartTaskVO {

    @ApiModelProperty("要启动检测任务的资产id")
    private List<Long> assetsIds;

}
