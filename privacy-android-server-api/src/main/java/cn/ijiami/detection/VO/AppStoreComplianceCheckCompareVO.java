package cn.ijiami.detection.VO;

import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.query.FieldCompare;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppStoreComplianceCheckCompareVO.java
 * @Description 应用商店专项检测差异报告
 * @createTime 2024年07月16日 18:12:00
 */
@Data
public class AppStoreComplianceCheckCompareVO {

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("ios隐私SDK信息-未声明SDK列表差异")
    private List<SdkVO> undeclaredDiffList;

    @ApiModelProperty("苹果商店上架隐私类api列表差异")
    private List<AppStorePrivacyApiVO> appStorePrivacyApiDiffList;

    @ApiModelProperty("检测详情列表差异")
    private List<TPrivacyPolicyResult> checkDiffList;

    @ApiModelProperty(value = "ios隐私SDK信息-未声明SDK")
    private List<SdkVO> undeclaredList;

    @ApiModelProperty("苹果商店上架隐私类api列表")
    private List<AppStorePrivacyApiVO> appStorePrivacyApiList;

    @ApiModelProperty(value = "应用商店上架专项检测")
    private List<TPrivacyPolicyResult> checkList;

}
