package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/2/11 16:02
 */
public class CountSharedPrefsNameVO implements Serializable {

    private static final long serialVersionUID = -1627391752214614209L;
    private Long taskId;

    private Long typeId;

    @ApiModelProperty(value = "个人信息")
    private String name;

    @ApiModelProperty(value = "敏感词")
    private String sensitiveWord;

    @ApiModelProperty(value = "APP次数")
    private Integer appCount;

    @ApiModelProperty(value = "SDK次数")
    private Integer sdkCount;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAppCount() {
        return appCount;
    }

    public void setAppCount(Integer appCount) {
        this.appCount = appCount;
    }

    public Integer getSdkCount() {
        return sdkCount;
    }

    public void setSdkCount(Integer sdkCount) {
        this.sdkCount = sdkCount;
    }

    public String getSensitiveWord() {
        return sensitiveWord;
    }

    public void setSensitiveWord(String sensitiveWord) {
        this.sensitiveWord = sensitiveWord;
    }
}
