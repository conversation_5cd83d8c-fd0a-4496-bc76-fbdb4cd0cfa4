package cn.ijiami.detection.VO;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/3/21 15:52
 */
public class PrivacyActionNougatVO implements Serializable {
    private static final long serialVersionUID = -8482967997211301743L;

    private String actionName;

    private String actionPermissionAlias;

    private Integer sensitive;

    private Integer appCount;

    private Integer sdkCount;

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getActionPermissionAlias() {
        return actionPermissionAlias;
    }

    public void setActionPermissionAlias(String actionPermissionAlias) {
        this.actionPermissionAlias = actionPermissionAlias;
    }

    public Integer getSensitive() {
        return sensitive;
    }

    public void setSensitive(Integer sensitive) {
        this.sensitive = sensitive;
    }

    public Integer getAppCount() {
        return appCount;
    }

    public void setAppCount(Integer appCount) {
        this.appCount = appCount;
    }

    public Integer getSdkCount() {
        return sdkCount;
    }

    public void setSdkCount(Integer sdkCount) {
        this.sdkCount = sdkCount;
    }
}
