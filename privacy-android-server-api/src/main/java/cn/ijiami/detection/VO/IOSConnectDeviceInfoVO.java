package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@ToString
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IOSConnectDeviceInfoVO {


    @NotNull(message = "连接地址不能为空")
    @ApiModelProperty(value = "连接地址")
    private String socketUrl;

    @ApiModelProperty(value = "扫码的设备id")
    private String deviceId;

    @NotNull(message = "连接id不能为空")
    @ApiModelProperty(value = "连接id，从二维码扫码信息里获取")
    private String connectionId;

}
