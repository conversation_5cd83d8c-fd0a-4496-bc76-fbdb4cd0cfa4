package cn.ijiami.detection.VO;

import lombok.Data;

@Data
public class CookieTokenVO {

    /**
     * access_token : eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_nrqHnkIblkZgiLCJwZXJtaXNzaW9uQ29kZSI6IjAwMS0wMDEifV0sImF0dHJpYnV0ZXMiOm51bGwsInRlbmFudElkIjowLCJjb21wYW55SWQiOm51bGwsImRlcGFydG1lbnRJZCI6bnVsbCwiaXNEZWZhdWx0IjpudWxsLCJhY2NvdW50Tm9uTG9ja2VkIjp0cnVlLCJjcmVkZW50aWFsc05vbkV4cGlyZWQiOnRydWUsImFjY291bnROb25FeHBpcmVkIjp0cnVlLCJlbmFibGVkIjp0cnVlLCJ1c2VybmFtZSI6InpoYW5nbGluZmVuZyJ9LCJhdXRob3JpdGllcyI6WyJ4aXRvbmdndWFubGl5dWFuIl0sImp0aSI6Ijk0MzllMDFkLTNkMzktNGI0Mi1hZGM2LTI1YjMxMzZhYWUyNiIsImNsaWVudF9pZCI6ImFkbWluIiwic3RhdHVzIjoiMjAwIn0.SdxsPIYuou2QDzbVGi7hGEMiSCbUjpyzBYUjBfx3IHq3Tx5ovhlKDAs5Y-KRZugdXlGOfBNVRq5QE442rNXXdW5ZlRYzDx81-35xWsDk29KR9YHi1TwVi45YAMOUBYVcxqyY2zBTFcicD1N4qGNNvejVXqfNAne-NqcW4t9DC-_X43tG07mRivM7P4Lyf-7kEe9Tq8SUm0EC4hb-hlkKd7D4bATmWseXMVykYXEypUmzUjgSIYFSZhMN0dBNQRns6PR71g_f_LL_W6SP-023c6Gq_PKCRx-FrtgRi88869q14ReEVQ3DSe7o33mkTSKzy5TbvWQVJsxpYxkq0KUSnA
     * token_type : bearer
     * refresh_token : ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
     * scope : read write
     * clientIp : **************
     * message : oauth success
     * status : 200
     * jti : 9439e01d-3d39-4b42-adc6-25b3136aae26
     * cookiepath : /
     */

    private String accessToken;
    private String tokenType;
    private String refreshToken;
    private String scope;
    private String clientIp;
    private String message;
    private String status;
    private String jti;
    private String cookiepath;
    private Integer expiresIn;
    private String tenantId;
    private Long getTokenDataStr;
    private String userName;

}
