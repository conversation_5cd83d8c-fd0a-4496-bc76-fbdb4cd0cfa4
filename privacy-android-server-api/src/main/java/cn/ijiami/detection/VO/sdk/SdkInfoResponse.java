package cn.ijiami.detection.VO.sdk;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkInfoResponse.java
 * @Description 更新sdk响应信息
 * @createTime 2022年08月25日 11:26:00
 */
@NoArgsConstructor
@Data
public class SdkInfoResponse {

    private Boolean success;
    private String message;
    private Integer code;
    private ResultDTO result;
    private Long timestamp;

    @ToString
    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        private Integer pageSize;
        private Integer currentPage;
        private Object ascs;
        private Object descs;
        private String version;
        private List<SdkDTO> addList;
        private List<SdkDTO> updateList;
        private List<SdkDTO> deleteList;

        @ToString
        @NoArgsConstructor
        @Data
        public static class SdkDTO {
            private String sdkName;
            private String sdkVersion;
            private String packageName;
            private String funcClassify;
            private String description;
            private String companyName;
            private String companyAddress;
            private String companyWebSiteUrl;
            private String functionalId;
            private String sdkUrl;
            private TerminalType terminalType;
            private String md5;
            private String compileVersion;
            private String minVersion;
            private String targetVersion;
            private String websiteUrl;
            private String sdkCode;
            private String groupId;
            private String artifactId;
            private Integer hotfix;
            private String privacyUrl;
            private List<String> sdkPermissionList;
            private List<String> sdkPackageList;

            @NoArgsConstructor
            @Data
            public static class TerminalType {
                private Integer value;
                private String code;
                private String name;
            }
        }
    }
}
