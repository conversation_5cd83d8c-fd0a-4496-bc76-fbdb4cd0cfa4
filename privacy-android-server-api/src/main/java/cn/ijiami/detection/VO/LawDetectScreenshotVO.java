package cn.ijiami.detection.VO;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawDetectScreenshotVO.java
 * @Description 法规截图
 * @createTime 2024年09月10日 11:56:00
 */
@Data
public class LawDetectScreenshotVO implements Serializable {

	private static final long serialVersionUID = 4842908665114837141L;

	private String base64;

    private Integer height;

    private Integer width;

    public LawDetectScreenshotVO(String base64, Integer height, Integer width) {
        this.base64 = base64;
        this.height = height;
        this.width = width;
    }
}
