package cn.ijiami.detection.VO.statistics;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesReportlaw.java
 * @Description 误报详情
 * @createTime 2023年11月13日 14:33:00
 */
@Data
public class DetectFalsePositivesReportLaw {

    private String userName;

    private Integer lawId;

    private Date detectionTime;

    private List<DetectFalsePositivesReportItem> itemList;

}
