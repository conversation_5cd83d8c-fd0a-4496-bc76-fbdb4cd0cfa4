package cn.ijiami.detection.VO;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "第三方结果推送内容")
public class StfDeviceTypeVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
     * 设备型号
     */
	@ApiModelProperty(value = "设备型号")
    private List<String> model;

    /**
     * 操作系统版本
     */
	@ApiModelProperty(value = "操作系统版本")
    private List<String> version;

	public List<String> getVersion() {
		return version;
	}

	public void setVersion(List<String> version) {
		this.version = version;
	}

	public List<String> getModel() {
		return model;
	}

	public void setModel(List<String> model) {
		this.model = model;
	}

    
}
