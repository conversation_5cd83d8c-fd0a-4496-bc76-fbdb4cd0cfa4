package cn.ijiami.detection.VO;

import cn.ijiami.detection.VO.detection.BaseMessageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "appletExtraInfoVO",description = "小程序基本信息")
public class AppletExtraInfoVO implements Serializable {

    private static final long serialVersionUID = -1122334455L;

    /**
     * 小程序APPID
     */
    @ApiModelProperty(value = "小程序APPID")
    private String appId;

    /**
     * 小程序名称
     */
    @ApiModelProperty(value = "小程序名称")
    private String nickname;

    /**
     * 小程序服务类目
     */
    @ApiModelProperty(value = "小程序服务类目")
    private String serviceCategory;

    /**
     * 小程序账号主体
     */
    @ApiModelProperty(value = "小程序账号主体")
    private String accountSubject;

    /**
     * 小程序原始id
     */
    @ApiModelProperty(value = "小程序原始id")
    private String originalId;

    /**
     * 小程序服务隐私及数据提示
     */
    @ApiModelProperty(value = "小程序服务隐私及数据提示")
    private String privacyData;

    /**
     * 服务声明
     */
    @ApiModelProperty(value = "小程序服务声明")
    private String statement;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "小程序更新时间")
    private String updateTime;

    /**
     * 引用插件
     */
    @ApiModelProperty(value = "小程序引用插件id")
    private List<String> pluginAppIds;

    /**
     * 引用插件
     */
    @ApiModelProperty(value = "小程序引用插件")
    private String plugins;

    /**
     * 授权服务商
     */
    @ApiModelProperty(value = "授权服务商")
    private String serviceProvider;

    /**
     * sdk列表
     */
    @ApiModelProperty(value = "sdk列表")
    private List<String> sdkList;

}
