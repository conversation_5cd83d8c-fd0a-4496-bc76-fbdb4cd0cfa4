package cn.ijiami.detection.VO;

import cn.ijiami.detection.enums.stf.StfDeviceBatteryHealthEnum;
import cn.ijiami.detection.enums.stf.StfDeviceBatteryStatusEnum;
import cn.ijiami.detection.enums.stf.StfDevicePlatformEnum;
import cn.ijiami.detection.enums.stf.StfDeviceStatusEnum;
import cn.ijiami.detection.enums.stf.StfNetworkTypeEnum;

/**
 * stf设备信息
 *
 */
public class StfDeviceInfo {

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备序列号
     */
    private String deviceSerial;

    /**
     * 远程连接地址
     */
    private String remoteConnectUrl;

    /**
     * 监控socketUrl
     */
    private String webSocketUrl;

    /**
     * 设备状态
     */
    private StfDeviceStatusEnum status;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * CPU架构
     */
    private String cpuPlatform;

    /**
     * 屏幕宽度
     */
    private Integer displayWidth;

    /**
     * 屏幕高度
     */
    private Integer displayHeight;

    /**
     * 位置
     */
    private String locationName;

    /**
     * 网络
     */
    private StfNetworkTypeEnum networkType;

    /**
     * imsi
     */
    private String imsi;

    /**
     * imei
     */
    private String imei;

    /**
     * phoneNumber
     */
    private String phoneNumber;

    /**
     * remark
     */
    private String remark;

    /**
     * 平台
     */
    private StfDevicePlatformEnum platformType;

    /**
     * 电池健康状态
     */
    private StfDeviceBatteryHealthEnum batteryHealth;

    /**
     * 电量
     */
    private Float batteryScale;

    /**
     * 电池状态(是否充电)
     */
    private StfDeviceBatteryStatusEnum batteryStatus;

    /**
     * 温度
     */
    private Float batteryTemp;

    /**
     * 来源
     */
    private String source;

    /**
     * 生产商
     */
    private String manufacturer;

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getRemoteConnectUrl() {
        return remoteConnectUrl;
    }

    public void setRemoteConnectUrl(String remoteConnectUrl) {
        this.remoteConnectUrl = remoteConnectUrl;
    }

    public String getWebSocketUrl() {
        return webSocketUrl;
    }

    public void setWebSocketUrl(String webSocketUrl) {
        this.webSocketUrl = webSocketUrl;
    }

    public StfDeviceStatusEnum getStatus() {
        return status;
    }

    public void setStatus(StfDeviceStatusEnum status) {
        this.status = status;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getCpuPlatform() {
        return cpuPlatform;
    }

    public void setCpuPlatform(String cpuPlatform) {
        this.cpuPlatform = cpuPlatform;
    }

    public Integer getDisplayWidth() {
        return displayWidth;
    }

    public void setDisplayWidth(Integer displayWidth) {
        this.displayWidth = displayWidth;
    }

    public Integer getDisplayHeight() {
        return displayHeight;
    }

    public void setDisplayHeight(Integer displayHeight) {
        this.displayHeight = displayHeight;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public StfNetworkTypeEnum getNetworkType() {
        return networkType;
    }

    public void setNetworkType(StfNetworkTypeEnum networkType) {
        this.networkType = networkType;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public StfDevicePlatformEnum getPlatformType() {
        return platformType;
    }

    public void setPlatformType(StfDevicePlatformEnum platformType) {
        this.platformType = platformType;
    }

    public StfDeviceBatteryHealthEnum getBatteryHealth() {
        return batteryHealth;
    }

    public void setBatteryHealth(StfDeviceBatteryHealthEnum batteryHealth) {
        this.batteryHealth = batteryHealth;
    }

    public Float getBatteryScale() {
        return batteryScale;
    }

    public void setBatteryScale(Float batteryScale) {
        this.batteryScale = batteryScale;
    }

    public StfDeviceBatteryStatusEnum getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(StfDeviceBatteryStatusEnum batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public Float getBatteryTemp() {
        return batteryTemp;
    }

    public void setBatteryTemp(Float batteryTemp) {
        this.batteryTemp = batteryTemp;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
}