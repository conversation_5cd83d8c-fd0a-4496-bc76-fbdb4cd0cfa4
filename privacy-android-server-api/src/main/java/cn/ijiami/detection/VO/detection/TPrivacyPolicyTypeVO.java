package cn.ijiami.detection.VO.detection;

import java.io.Serializable;
import java.util.List;

import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeAPIVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import io.swagger.annotations.ApiModelProperty;

public class TPrivacyPolicyTypeVO implements Serializable {
    /**
     * 主键
     */
//    @ApiModelProperty(value = "主键")
//    private Long id;

    /**
     * 类别
     */
//    @ApiModelProperty(value = "类别")
//    private String typeName;

    @ApiModelProperty(value = "法律法规类型 1.自评估指南 2. GB/T 35273 3. 工信部337号文")
    private Integer type;

    @ApiModelProperty(value = "法律法规名称")
    private String lawName;
    
    private List<PrivacyPolicyTypeAPIVO> lawItemList;

    private static final long serialVersionUID = 1L;


	public List<PrivacyPolicyTypeAPIVO> getLawItemList() {
		return lawItemList;
	}

	public void setLawItemList(List<PrivacyPolicyTypeAPIVO> lawItemList) {
		this.lawItemList = lawItemList;
	}

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getLawName() {
        return lawName;
    }

    public void setLawName(String lawName) {
        this.lawName = lawName;
    }
}

