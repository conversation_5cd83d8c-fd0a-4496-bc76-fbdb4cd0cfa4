package cn.ijiami.detection.VO;

/**
 * <AUTHOR>
 * @description
 * @date 2019-03-26 10:55
 */
public class PrivacyPermissionCountVO implements Comparable<PrivacyPermissionCountVO> {

    private PermissionVO permission;
    private int a;
    private int b;
    private int c;

    public PermissionVO getPermission() {
        return permission;
    }

    public void setPermission(PermissionVO permission) {
        this.permission = permission;
    }

    public int getA() {
        return a;
    }

    public void setA(int a) {
        this.a = a;
    }

    public int getB() {
        return b;
    }

    public void setB(int b) {
        this.b = b;
    }

    public int getC() {
        return c;
    }

    public void setC(int c) {
        this.c = c;
    }

    @Override
    public int compareTo(PrivacyPermissionCountVO o) {
        return o.getPermission().getType().compareTo(this.getPermission().getGrade());
    }
}
