package cn.ijiami.detection.VO.detection.privacy;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-05-28 09:50
 */
@Data
public class PrivacyPolicyVO implements Serializable {

    private static final long serialVersionUID = 2389081719597345629L;
    
    @ApiModelProperty(value = "是否满足")
    private Boolean result;

    @ApiModelProperty(value = "整改建议")
    private String suggestion;
}
