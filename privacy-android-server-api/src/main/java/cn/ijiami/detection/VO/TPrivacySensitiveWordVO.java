package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "TPrivacySensitiveWordVO", description="传输个人信息VO")
public class TPrivacySensitiveWordVO implements Serializable {

    private static final long serialVersionUID = -2538258744125656452L;

    @ApiModelProperty(value = "传输个人信息分页列表")
    private PageInfo<TPrivacySensitiveWord> pageInfo;

    public PageInfo<TPrivacySensitiveWord> getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo<TPrivacySensitiveWord> pageInfo) {
        this.pageInfo = pageInfo;
    }
}
