package cn.ijiami.detection.VO;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChunkFileVO.java
 * @Description 分片数据
 * @createTime 2021年12月24日 19:21:00
 */
@Data
public class CheckChunkFileVO {


//    @ApiModelProperty(value = "是否上传完成")
//    private boolean isCompleted = false;
    @ApiModelProperty(value = "是否上传完成")
    private boolean completed = false;

//    @ApiModelProperty(value = "资源详情，checkChunkFile接口isCompleted=true的情况下才会有")
//    private TAssets assets;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    @ApiModelProperty(value = "分片文件总数")
    private Integer chunkTotal;

    @ApiModelProperty(value = "已经上传成功的分片列表")
    private List<ChunkFile> chunkFileList;

    @ApiModelProperty(value = "分片上传状态，1 上传成功，2 上传失败，单个重新上传，3 上传失败，所有重新上传")
    private Integer uploadChunkStatus;

    @ApiModelProperty(value = "当前上传分片文件md5")
    private String uploadChunkMd5;

    @Data
    public static class ChunkFile {

        @ApiModelProperty(value = "文件md5")
        private String chunkMd5;

        @ApiModelProperty(value = "分片文件序号")
        private Integer chunkNumber;

        @ApiModelProperty(value = "分片文件大小")
        private Long chunkSize;

    }

}
