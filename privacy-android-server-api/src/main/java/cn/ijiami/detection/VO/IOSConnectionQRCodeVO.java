package cn.ijiami.detection.VO;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class IOSConnectionQRCodeVO {

    @J<PERSON>NField(name = "apiPath")
    private String url;

    @JSONField(name = "connectionId")
    private String connectionId;

    @JSONField(name = "sa")
    private String frpsIp;

    @JSONField(name = "sp")
    private String frpsPort;

    @JSONField(name = "ca")
    private String frpcUrl;

    @JSONField(name = "cp")
    private String frpcPort;
    //连接类型-frp、wss(websocket直连接)
    @JSONField(name = "type")
    private String type;
    
    //wss类型连接-IOS扫码idb连接地址
    @JSONField(name = "idbConnectUrl")
    private String idbConnectUrl;
    //wss类型连接-IOS扫码web连接地址
    @JSONField(name = "webConnectUrl")
    private String webConnectUrl;

}
