package cn.ijiami.detection.VO.detection.wechat;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/3/6 14:41
 */
@ApiModel
public class WechatDetectionResultVO {

    @ApiModelProperty(value = "微信公众号/小程序名称")
    private String name;

    @ApiModelProperty(value = "微信号")
    private String wechatId;

    @ApiModelProperty(value = "资产id")
    private Long assetsId;

    @ApiModelProperty(value = "账号主体")
    private String accountSubject;

    @ApiModelProperty(value = "检测分数")
    private String score;

    @ApiModelProperty(value = "检测耗时")
    private String detectionTime;

    @ApiModelProperty(value = "开始检测时间")
    private Date detectionStartTime;

    @ApiModelProperty(value = "检测策略")
    private String strategyName;

    @ApiModelProperty(value = "检测进度")
    private Double process;

    @ApiModelProperty(value = "危险检测项统计")
    private DetectionResultCount detectionResultCount;

    @ApiModelProperty(value = "检测结果统计")
    private List<DetectionResultDetail> detectionResultDetails;

    @ApiModelProperty(value = "微信公众号菜单")
    private JSONArray wechatMenus;

    @ApiModelProperty(value = "检测结果详情")
    private List<DetectionItemResult> detectionItems;

    @ApiModelProperty(value = "文字检测结果")
    private List<TxtDetectionResult> txtDetectionResults;

    @ApiModelProperty(value = "图片检测结果")
    private List<ImageDetectionResult> imageDetectionResults;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWechatId() {
        return wechatId;
    }

    public void setWechatId(String wechatId) {
        this.wechatId = wechatId;
    }

    public String getAccountSubject() {
        return accountSubject;
    }

    public void setAccountSubject(String accountSubject) {
        this.accountSubject = accountSubject;
    }

    public JSONArray getWechatMenus() {
        return wechatMenus;
    }

    public void setWechatMenus(JSONArray wechatMenus) {
        this.wechatMenus = wechatMenus;
    }

    public List<DetectionItemResult> getDetectionItems() {
        return detectionItems;
    }

    public void setDetectionItems(List<DetectionItemResult> detectionItems) {
        this.detectionItems = detectionItems;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public List<DetectionResultDetail> getDetectionResultDetails() {
        return detectionResultDetails;
    }

    public void setDetectionResultDetails(List<DetectionResultDetail> detectionResultDetails) {
        this.detectionResultDetails = detectionResultDetails;
    }

    public DetectionResultCount getDetectionResultCount() {
        return detectionResultCount;
    }

    public void setDetectionResultCount(DetectionResultCount detectionResultCount) {
        this.detectionResultCount = detectionResultCount;
    }

    public String getDetectionTime() {
        return detectionTime;
    }

    public void setDetectionTime(String detectionTime) {
        this.detectionTime = detectionTime;
    }

    public Date getDetectionStartTime() {
        return detectionStartTime;
    }

    public void setDetectionStartTime(Date detectionStartTime) {
        this.detectionStartTime = detectionStartTime;
    }

    public String getStrategyName() {
        return strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public Double getProcess() {
        return process;
    }

    public void setProcess(Double process) {
        this.process = process;
    }

    public List<TxtDetectionResult> getTxtDetectionResults() {
        return txtDetectionResults;
    }

    public void setTxtDetectionResults(List<TxtDetectionResult> txtDetectionResults) {
        this.txtDetectionResults = txtDetectionResults;
    }

    public List<ImageDetectionResult> getImageDetectionResults() {
        return imageDetectionResults;
    }

    public void setImageDetectionResults(List<ImageDetectionResult> imageDetectionResults) {
        this.imageDetectionResults = imageDetectionResults;
    }

    public Long getAssetsId() {
        return assetsId;
    }

    public void setAssetsId(Long assetsId) {
        this.assetsId = assetsId;
    }
}
