package cn.ijiami.detection.VO;

import java.io.Serializable;
import java.util.Date;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

/**
 * 行为信息详情
 *
 * <AUTHOR>
 * @date 2020-12-26 22:17
 */
public class LawActionDetailVO implements Serializable {

    private static final long serialVersionUID = -1929630803643767941L;

    private Long    id;
    /**
     * 法规字典唯一编码（关联t_privacy_laws_result）
     */
    private String itemNo;
    /**
     * 行为id
     */
    private Long actionId;
    /**
     * 行为别名
     */
    private String actionName;
    /**
     * 行为别名
     */
    private String actionAlias;
    /**
     * 权限信息
     */
    private String permission;
    /**
     * 权限信息简写
     */
    private String permissionAlias;
    /**
     * 触发频次
     */
    private Integer triggerNum;
    /**
     * 主体类型 1APP 2SDK
     */
    private Integer executorType;
    /**
     * 主体
     */
    private String  executor;
    /**
     * 是否与个人信息相关
     */
    private Boolean personal;
    /**
     * 触发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date    triggerTime;
    
    private Long intervalTime; //时间间隔
    
    private String timeUnit;  //时间单位
    
    @ApiModelProperty(value = "行为阶段   授权前行为、前台运行行为、后台运行行为")
    private BehaviorStageEnum behaviorStage;
    
    @ApiModelProperty(value = "相关的隐私文本")
    private String[] privacyPolicySnippet;
    
    private String keyWords; 
    
    private String behaviorStageName;
    
    @ApiModelProperty(value = "是否相关的隐私文本 1是")
    private Integer privacyPolicy;
    
    @ApiModelProperty(value = "归属地")
    private String attributively;
    @ApiModelProperty(value = "ip地址")
    private String ip;
    @ApiModelProperty(value = "代码片段")
    private String code;
    @ApiModelProperty(value = "域名")
    private String host;
    @ApiModelProperty(value = "端口")
    private String port;
    @ApiModelProperty(value = "cookie")
    private String cookie;
    @ApiModelProperty(value = "是否为境外IP")
    private Integer outside;

    private Integer dataType;

    private String packageName;
    private String detailsData;
    private String stackInfo;
    private String jniStackInfo;

    private String apiName;

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getAttributively() {
		return attributively;
	}

	public void setAttributively(String attributively) {
		this.attributively = attributively;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getCookie() {
		return cookie;
	}

	public void setCookie(String cookie) {
		this.cookie = cookie;
	}

	public Integer getPrivacyPolicy() {
		return privacyPolicy;
	}

	public void setPrivacyPolicy(Integer privacyPolicy) {
		this.privacyPolicy = privacyPolicy;
	}

	public String getBehaviorStageName() {
		return behaviorStageName;
	}

	public void setBehaviorStageName(String behaviorStageName) {
		this.behaviorStageName = behaviorStageName;
	}

	public String[] getPrivacyPolicySnippet() {
		return privacyPolicySnippet;
	}

	public void setPrivacyPolicySnippet(String[] privacyPolicySnippet) {
		this.privacyPolicySnippet = privacyPolicySnippet;
	}

	public String getKeyWords() {
		return keyWords;
	}

	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}

	public BehaviorStageEnum getBehaviorStage() {
		return behaviorStage;
	}

	public void setBehaviorStage(BehaviorStageEnum behaviorStage) {
		this.behaviorStage = behaviorStage;
	}

	public String getTimeUnit() {
		return timeUnit;
	}

	public void setTimeUnit(String timeUnit) {
		this.timeUnit = timeUnit;
	}

	public Long getIntervalTime() {
		return intervalTime;
	}

	public void setIntervalTime(Long intervalTime) {
		this.intervalTime = intervalTime;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getActionAlias() {
        return actionAlias;
    }

    public void setActionAlias(String actionAlias) {
        this.actionAlias = actionAlias;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getPermissionAlias() {
        return permissionAlias;
    }

    public void setPermissionAlias(String permissionAlias) {
        this.permissionAlias = permissionAlias;
    }

    public Integer getTriggerNum() {
        return triggerNum;
    }

    public void setTriggerNum(Integer triggerNum) {
        this.triggerNum = triggerNum;
    }

    public Integer getExecutorType() {
        return executorType;
    }

    public void setExecutorType(Integer executorType) {
        this.executorType = executorType;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public Date getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    public Boolean getPersonal() {
        return personal;
    }

    public void setPersonal(Boolean personal) {
        this.personal = personal;
    }

    @Override
    public String toString() {
        return "LawActionDetailVO{" + "id=" + id + ", itemNo='" + itemNo + '\'' + ", actionId=" + actionId + ", actionAlias='" + actionAlias + '\''
                + ", permission='" + permission + '\'' + ", permissionAlias='" + permissionAlias + '\'' + ", triggerNum=" + triggerNum + ", executorType="
                + executorType + ", executor='" + executor + '\'' + ", personal=" + personal + ", triggerTime=" + triggerTime + '}';
    }

    public Integer getOutside() {
        return outside;
    }

    public void setOutside(Integer outside) {
        this.outside = outside;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getDetailsData() {
        return detailsData;
    }

    public void setDetailsData(String detailsData) {
        this.detailsData = detailsData;
    }

    public String getStackInfo() {
        return stackInfo;
    }

    public void setStackInfo(String stackInfo) {
        this.stackInfo = stackInfo;
    }

    public String getJniStackInfo() {
        return jniStackInfo;
    }

    public void setJniStackInfo(String jniStackInfo) {
        this.jniStackInfo = jniStackInfo;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }
}
