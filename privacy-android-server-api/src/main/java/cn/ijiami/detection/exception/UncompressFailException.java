package cn.ijiami.detection.exception;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UncompressFailException.java
 * @Description 解压失败
 * @createTime 2024年10月22日 17:14:00
 */
public class UncompressFailException extends RuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public UncompressFailException() {
    }

    public UncompressFailException(String message) {
        super(message);
    }

    public UncompressFailException(String message, Throwable cause) {
        super(message, cause);
    }

    public UncompressFailException(Throwable cause) {
        super(cause);
    }

    public UncompressFailException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
