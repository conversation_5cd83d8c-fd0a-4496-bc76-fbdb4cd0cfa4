package cn.ijiami.detection.exception;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DownloadZipFailException.java
 * @Description 下载压缩包失败
 * @createTime 2024年10月22日 17:19:00
 */
public class DownloadZipFailException extends RuntimeException {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public DownloadZipFailException() {
    }

    public DownloadZipFailException(String message) {
        super(message);
    }

    public DownloadZipFailException(String message, Throwable cause) {
        super(message, cause);
    }

    public DownloadZipFailException(Throwable cause) {
        super(cause);
    }

    public DownloadZipFailException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
