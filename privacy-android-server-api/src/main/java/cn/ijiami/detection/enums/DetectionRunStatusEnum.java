package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 检测运行状态枚举
 * 
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DetectionRunStatusEnum implements BaseValueEnum {
	DETECTION_STOP(1, "检测中断"), DETECTION_ERROR(2, "检测异常"), DETECTION_FINISH(3, "检测完成");

	private int value;

	private String name;

	private DetectionRunStatusEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	@JsonCreator
	public static DetectionRunStatusEnum getItem(int value) {
		for (DetectionRunStatusEnum status : values()) {
			if (status.getValue() == value) {
				return status;
			}
		}
		return null;
	}

	@Override
	public int itemValue() {

		return value;
	}

	@Override
	public String itemName() {
		return name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public void setName(String name) {
		this.name = name;
	}

}
