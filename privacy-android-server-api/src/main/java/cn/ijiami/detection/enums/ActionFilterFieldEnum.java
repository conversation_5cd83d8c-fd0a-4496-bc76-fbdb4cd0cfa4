package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ActionFilterFieldEnum implements BaseValueEnum{

	stackInfo(1, "堆栈方式判定过滤"), detailsData(2, "数据详情方式过滤");

    private int value;
    // 名称
    private String name;

    private ActionFilterFieldEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static ActionFilterFieldEnum getItem(int value) {
        for (ActionFilterFieldEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
