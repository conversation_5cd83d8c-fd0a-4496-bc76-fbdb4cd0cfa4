package cn.ijiami.detection.enums;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PrivacyStatusEnum implements BaseValueEnum {

    YES(1, "是"), NO(0, "否");

    private final int value;

    private final String name;

    PrivacyStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static PrivacyStatusEnum getItem(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (PrivacyStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public static PrivacyStatusEnum getItemByName(String name) {
        for (PrivacyStatusEnum item : values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }


    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
