package cn.ijiami.detection.enums;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum IdbStagedDataEnum implements BaseValueEnum {

    NONE(0, "无阶段数据", null, null, ""),

    BEHAVIOR_GRANT(1, "授权前阶段", BehaviorStageEnum.BEHAVIOR_GRANT, DynamicAutoSubStatusEnum.BEHAVIOR_GRANT,
            "behavior_info_01.json,hostip_01.txt,capture_info_01.txt,shared_prefs_01,capture_info_01,capture_info_01.xls"),
    REFUSE(2, "授权拒绝阶段", BehaviorStageEnum.BEHAVIOR_FRONT, DynamicAutoSubStatusEnum.REFUSE,
            "behavior_info_02.json,hostip_02.txt,capture_info_02.txt,shared_prefs_02,capture_info_02,capture_info_02.xls"),
    AGREE(3, "授权同意阶段", BehaviorStageEnum.BEHAVIOR_FRONT, DynamicAutoSubStatusEnum.AGREE,
            "behavior_info_03.json,hostip_03.txt,capture_info_03.txt,shared_prefs_03,capture_info_03,capture_info_03.xls"),
    BEHAVIOR_FRONT(4, "前台阶段", BehaviorStageEnum.BEHAVIOR_FRONT, DynamicAutoSubStatusEnum.BEHAVIOR_FRONT,
            "behavior_info_04.json,hostip_04.txt,capture_info_04.txt,shared_prefs_04,capture_info_04,capture_info_04.xls"),
    SHAKING(5, "摇一摇阶段", BehaviorStageEnum.BEHAVIOR_FRONT, DynamicAutoSubStatusEnum.SHAKING,
            "behavior_info_05.json,hostip_05.txt,capture_info_05.txt,shared_prefs_05,capture_info_05,capture_info_05.xls"),
    BEHAVIOR_GROUND(6, "后台阶段", BehaviorStageEnum.BEHAVIOR_GROUND, DynamicAutoSubStatusEnum.BEHAVIOR_GROUND,
            "behavior_info_06.json,hostip_06.txt,capture_info_06.txt,shared_prefs_06,capture_info_06,capture_info_06.xls"),
    BEHAVIOR_EXIT(7, "退出阶段", BehaviorStageEnum.BEHAVIOR_EXIT, DynamicAutoSubStatusEnum.BEHAVIOR_EXIT,
            "behavior_info_07.json,hostip_07.txt,capture_info_07.txt,shared_prefs_07,capture_info_07,capture_info_07.xls");

    // 值
    private final int value;

    // 名称
    private final String name;

    private final BehaviorStageEnum behaviorStage;

    private final DynamicAutoSubStatusEnum taskSubStatus;

    private final String fileNames;

    IdbStagedDataEnum(int value, String name, BehaviorStageEnum behaviorStage, DynamicAutoSubStatusEnum taskSubStatus, String filePaths) {
        this.value = value;
        this.name = name;
        this.behaviorStage = behaviorStage;
        this.taskSubStatus = taskSubStatus;
        this.fileNames = filePaths;
    }

    @JsonCreator
    public static IdbStagedDataEnum getItem(int value) {
        for (IdbStagedDataEnum item : values()) {
            if (item.itemValue() == value) {
                return item;
            }
        }
        return null;
    }


    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public BehaviorStageEnum getBehaviorStage() {
        return behaviorStage;
    }

    public String getFileNames() {
        return fileNames;
    }
}
