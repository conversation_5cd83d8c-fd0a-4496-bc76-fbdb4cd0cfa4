package cn.ijiami.detection.enums;

public enum HarmonyManualTypeEnum {

    DOWNLOAD_APP(0, "下载app"),
    INSTALL_APP(2, "安装app"),
    STARTUP(3, "启动app"),
    USER_INTERRUPTED(4, "手动中断"),
    BEHAVIOR(5, "动态检测实时日志"),
    NET(6, "网络日志"),
    APP_UNINSTALL_COMPLETE(7, "app卸载完成"),
    SCREENSHOT_DATA(9, "截图数据"),
    DETECTION_RUNNING(10, "检测中"),
    CLEAR_LOG(14, "清除日志");

    public final int value;
    public final String description;

    HarmonyManualTypeEnum(int value, String name) {
        this.value = value;
        this.description = name;
    }
}
