package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 使用次数排序枚举
 * 
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum UseCountSortEnum implements BaseValueEnum {
	DESC(1, "降序"), ASC(2, "升序");

	// 值
	private int value;

	// 名称
	private String name;

	private UseCountSortEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	@JsonCreator
	public static UseCountSortEnum getItem(int value) {
		for (UseCountSortEnum item : values()) {
			if (item.getValue() == value) {
				return item;
			}
		}
		return null;
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public int itemValue() {
		return value;
	}

	@Override
	public String itemName() {
		return name;
	}

}
