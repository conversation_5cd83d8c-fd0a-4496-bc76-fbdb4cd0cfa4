package cn.ijiami.detection.enums;

public enum ProductFileEnum {

    COMMON(1),
    PERSONAL_ACTION(2),
    SDK(3),
    IDB(4),
    LAW_164(5),
    LAW_191(6),
    MAC_IDB(7);

    public final int category;

    ProductFileEnum(int category) {
        this.category = category;
    }

    public static ProductFileEnum findByCategory(int category) {
        for (ProductFileEnum fileIdEnum:values()) {
            if (fileIdEnum.category == category) {
                return fileIdEnum;
            }
        }
        return null;
    }

}
