package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionDynamicType.java
 * @Description 动态检测类型
 * @createTime 2021年11月24日 15:28:00
 */
public enum DetectionDynamicType implements BaseValueEnum {

    ANDROID_DEEP(1, "深度检测"),
    ANDROID_LAW(2, "法规检测"),
    ANDROID_FAST(3, "快速检测"),
    WECHAT_APPLET_DEEP(4, "深度检测"),
    WECHAT_APPLET_LAW(5, "法规检测"),
    WECHAT_APPLET_FAST(6, "快速检测"),
    ALIPAY_APPLET_STATIC(7, "静态检测"),
    ALIPAY_APPLET_DEEP(8, "深度检测"),
    ALIPAY_APPLET_LAW(9, "法规检测"),
    ALIPAY_APPLET_FAST(10, "快速检测"),
    WECHAT_APPLET_STATIC(11, "静态检测"),
    HARMONY_FAST(12, "快速检测"),
    HARMONY_DEEP(13, "深度检测"),
    HARMONY_LAW(14, "法规检测");

    // 值
    private int value;

    // 名称
    private String name;

    DetectionDynamicType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DetectionDynamicType getItem(int value) {
        for (DetectionDynamicType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
