package cn.ijiami.detection.enums;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TaskDataStatusEnum implements BaseValueEnum {

    INIT(1, "等待解析"),
    ANALYZEING(2, "解析中"),
    SUCCESS(3, "解析完成"),
    FAILURE(4, "解析失败");

    private final int value;
    private final String name;

    TaskDataStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
