package cn.ijiami.detection.enums.ai;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AIModule implements BaseValueEnum {

    LAW(1, "权限检测模块"),
    PERMISSION(2, "权限检测模块"),
    ACTION(3, "应用行为模块"),
    PERSONAL_INFO_RISK(4, "个人信息风险"),
    LAW_INFO_RISK(5, "合规风险");


    private final int value;
    private final String name;

    AIModule(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
