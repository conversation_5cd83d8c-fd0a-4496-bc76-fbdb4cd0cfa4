package cn.ijiami.detection.enums;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Objects;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AiClientTypeEnum implements BaseValueEnum {

    ENABLE(0, "启用"),
    DISABLE(1, "禁用");

    private final int value;
    private final String desc;

    AiClientTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static AiClientTypeEnum getItem(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (AiClientTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return desc;
    }
}
