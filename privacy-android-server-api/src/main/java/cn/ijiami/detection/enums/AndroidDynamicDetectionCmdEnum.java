package cn.ijiami.detection.enums;

public enum AndroidDynamicDetectionCmdEnum {

    RUNNING(1, "检测进行中"),
    STOP(2, "检测停止"),
    SCREENSHOT(4, "截图数据"),
    FINISH(6, "检测结束"),
    STATIC_DETECTION_RESULT(20, "静态检测结果"),
    AI_DETECT_LOGIN(29, "ai检测前的登录通知");

    public Integer value;
    public String description;

    AndroidDynamicDetectionCmdEnum(int value, String name) {
        this.value = value;
        this.description = name;
    }
}
