package cn.ijiami.detection.enums;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SystemUpdateStatusEnum.java
 * @Description 系统更新状态
 * @createTime 2024年11月18日 12:39:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SystemUpdateStatusEnum implements BaseValueEnum {

    NEWEST(1, "最新"),
    OLD(2, "旧版"),
    UPDATING(3, "更新中"),
    UPDATE_FAILURE(4, "更新失败"),
    ROLLING_BACK(5, "回滚中"),
    ROLLBACK_FAILURE(6, "回滚失败");

    private final int value;
    private final String name;

    SystemUpdateStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static SystemUpdateStatusEnum getItem(int value) {
        for (SystemUpdateStatusEnum item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
