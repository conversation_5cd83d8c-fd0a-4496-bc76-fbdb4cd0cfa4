package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 单个分片上传的状态
 */
public enum UploadChunkStatus implements BaseValueEnum {

    SUCCESS(1, "上传成功"), UPLOAD_ERROR(2, "上传失败"), MERGE_ERROR(3, "合并失败");

    // 值
    private final int value;

    // 名称
    private final String name;

    UploadChunkStatus(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static UploadChunkStatus getItem(int value) {
        for (UploadChunkStatus item : values()) {
            if (item.itemValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
