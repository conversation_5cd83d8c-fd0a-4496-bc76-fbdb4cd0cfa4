package cn.ijiami.detection.idb.hitshell;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StopIosHitShellRequest.java
 * @Description 停止ios砸壳
 * @createTime 2022年01月17日 15:12:00
 */
@Data
public class StopIosHitShellRequest {

    @JsonProperty(value = "requestParam")
    private Params requestParam;

    @Data
    public static class Params {

        @JsonProperty(value = "businessId")
        private String businessId;

        @JsonProperty(value = "deviceID")
        private String deviceId;

        @JsonProperty(value = "cmdType")
        private Integer cmdType;
    }

}
