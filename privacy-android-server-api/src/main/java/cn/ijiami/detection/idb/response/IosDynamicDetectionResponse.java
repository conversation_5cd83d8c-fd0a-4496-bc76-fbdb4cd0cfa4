package cn.ijiami.detection.idb.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosDynamicDetectionResponse.java
 * @Description IosDynamicDetectionResponse
 * @createTime 2022年01月17日 15:12:00
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class IosDynamicDetectionResponse extends BaseIdbResponse {



}
