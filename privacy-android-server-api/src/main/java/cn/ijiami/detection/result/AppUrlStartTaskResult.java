package cn.ijiami.detection.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppUrlStartTaskResult.java
 * @Description 应用url启动检测任务返回
 * @createTime 2024年05月24日 18:08:00
 */
@Data
public class AppUrlStartTaskResult {

    @ApiModelProperty(value = "业务id")
    private String businessId;

    @ApiModelProperty(value = "app资产id")
    private Long assetsId;

}
