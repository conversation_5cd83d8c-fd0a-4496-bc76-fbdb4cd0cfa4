package cn.ijiami.detection.result;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppIdResult.java
 * @Description ios应用的appid查询
 * @createTime 2024年05月29日 11:11:00
 */
@Data
public class AppIdResult {

    private String appId;

    public static AppIdResult build(String appId) {
        AppIdResult result = new AppIdResult();
        result.setAppId(appId);
        return result;
    }

}
