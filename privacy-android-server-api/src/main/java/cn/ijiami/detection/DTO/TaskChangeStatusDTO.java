package cn.ijiami.detection.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@ApiModel("任务状态修改")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskChangeStatusDTO implements Serializable {

    private static final long serialVersionUID = -5842078960152819676L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务类型 1:动态/深度检测  2:法规检测 3: 复核检测")
    private Integer type;

    @ApiModelProperty(value = "状态 true启动 false中断")
    private Boolean status;

    @ApiModelProperty(value = "设备序列号")
    private String deviceSerial;
    
    private Long userId;

    public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }
}
