package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "法规检测项明细表")
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "t_privacy_check")
public class TPrivacyCheck implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "对应法规类型主键id")
    @Column(name = "type_id")
    private Long typeId;

    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    @ApiModelProperty(value = "检测项")
    @Column(name = "check_name")
    private String checkName;

    @ApiModelProperty(value = "评估标准")
    @Column(name = "notes")
    private String notes;

    @ApiModelProperty(value = "是否满足")
    @Column(name = "result")
    private Boolean result;

    @ApiModelProperty(value = "整改建议")
    @Column(name = "suggestion")
    private String suggestion;

    @ApiModelProperty(value = "参考规范")
    @Column(name = "reference")
    private String reference;

    @ApiModelProperty(value = "测试要点")
    @Column(name = "test_point")
    private String testPoint;

    @ApiModelProperty(value = "测试方法")
    @Column(name = "test_method")
    private String testMethod;

    @ApiModelProperty(value = "评估结果")
    @Column(name = "test_result")
    private String testResult;

    @ApiModelProperty(value = "终端类型 1.Android 2.ios")
    @Column(name = "terminal_type")
    private Integer terminalType;

    @ApiModelProperty(value = "正则表达式关键字")
    @Column(name = "key_word")
    private String keyWord;

    @ApiModelProperty(value = "状态：1.未发布 2.已发布")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty(value = "是否删除：0.未删除 1.已删除")
    @Column(name = "is_del")
    private Integer isDel;

    @ApiModelProperty(value = "创建人")
    @Column(name = "create_user_id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    @Column(name = "update_user_id")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "检测项类别展示排序字段")
    @Column(name = "type_sort")
    private Integer typeSort;

    @ApiModelProperty(value = "检测项展示排序字段")
    @Column(name = "check_sort")
    private Integer checkSort;

    @ApiModelProperty(value = "缓存标志 0正式数据 1缓存可物理删除")
    @Column(name = "is_cache")
    private Integer isCache;

    @ApiModelProperty(value = "旧检测项id，用于检测项更新时清理旧检测项状态")
    @Column(name = "old_privacy_id")
    private Long oldPrivacyId;

    @ApiModelProperty(value = "是否展示 1展示 0不展示")
    @Column(name = "is_show")
    private Integer isShow;

    private static final long serialVersionUID = 1L;

    public Integer getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getTestPoint() {
        return testPoint;
    }

    public void setTestPoint(String testPoint) {
        this.testPoint = testPoint;
    }

    public String getTestResult() {
        return testResult;
    }

    public void setTestResult(String testResult) {
        this.testResult = testResult;
    }

    public String getTestMethod() {
        return testMethod;
    }

    public void setTestMethod(String testMethod) {
        this.testMethod = testMethod;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getTypeSort() {
        return typeSort;
    }

    public void setTypeSort(Integer typeSort) {
        this.typeSort = typeSort;
    }

    public Integer getCheckSort() {
        return checkSort;
    }

    public void setCheckSort(Integer checkSort) {
        this.checkSort = checkSort;
    }

    public Integer getIsCache() {
        return isCache;
    }

    public void setIsCache(Integer isCache) {
        this.isCache = isCache;
    }

    public Long getOldPrivacyId() {
        return oldPrivacyId;
    }

    public void setOldPrivacyId(Long oldPrivacyId) {
        this.oldPrivacyId = oldPrivacyId;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    @Override
    public String toString() {
        return "TPrivacyCheck{" +
                "id=" + id +
                ", typeId=" + typeId +
                ", typeName='" + typeName + '\'' +
                ", checkName='" + checkName + '\'' +
                ", notes='" + notes + '\'' +
                ", result=" + result +
                ", suggestion='" + suggestion + '\'' +
                ", reference='" + reference + '\'' +
                ", testPoint='" + testPoint + '\'' +
                ", testMethod='" + testMethod + '\'' +
                ", testResult='" + testResult + '\'' +
                ", terminalType=" + terminalType +
                ", keyWord='" + keyWord + '\'' +
                ", status=" + status +
                ", isDel=" + isDel +
                ", createUserId=" + createUserId +
                ", createTime=" + createTime +
                ", updateUserId=" + updateUserId +
                ", updateTime=" + updateTime +
                ", typeSort=" + typeSort +
                ", checkSort=" + checkSort +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()){
        	return false; // 如果obj为null或者obj不是Person类的实例，返回false
        } 
        TPrivacyCheck check = (TPrivacyCheck) obj; // 将obj强制转换为Person类的实例
        return Objects.equals(typeName,check.typeName) && Objects.equals(checkName,check.checkName)
                && Objects.equals(suggestion,check.suggestion) && Objects.equals(notes,check.notes)
                && Objects.equals(testPoint,check.testPoint) && Objects.equals(testMethod,check.testMethod);// 比较两个对象的属性是否相同
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(typeName, checkName, suggestion, notes, testPoint, testMethod);
    }
}

