package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.LawResultMarkStatusEnum;
import cn.ijiami.detection.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 检测结果标记
 * <AUTHOR>
 *
 */
@Table(name = "t_privacy_result_mark")
public class TPrivacyResultMark implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
	private Long id;
	
	@Column(name = "b_id")
    @ApiModelProperty(value = "业务ID")
	private Long bId;
	
	
	@Column(name = "result_type")
    @ApiModelProperty(value = "数据类型 1.传输个人信息  2.存储个人信息  3.164号文法规 4. 191号文法规 5. 35273号文 6. 41391号文")
    private LawResultTypeEnum resultType;
	
	
	@Column(name = "result_status")
    @ApiModelProperty(value = "结果（1 存在风险 2 未发现风险） | 1正确    2误判")
    private LawResultMarkStatusEnum resultStatus;

	@Column(name = "risk_level")
	@ApiModelProperty(value = "风险等级 1：低、2：中、3：高")
	private LawResultRiskLevelEnum riskLevel;

	@Column(name = "suggestion")
    @ApiModelProperty(value = "整改建议")
	private String suggestion;

	@Column(name = "conclusion")
	@ApiModelProperty(value = "结论")
	private  String conclusion;

	@ApiModelProperty(value = "截图文件key列表")
	private List<String> screenshotFileKeyList;

	@Column(name = "description")
    @ApiModelProperty(value = "备注说明")
	private String description;
	
	 /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getbId() {
		return bId;
	}

	public void setbId(Long bId) {
		this.bId = bId;
	}

	public LawResultTypeEnum getResultType() {
		return resultType;
	}

	public void setResultType(LawResultTypeEnum resultType) {
		this.resultType = resultType;
	}

	public LawResultMarkStatusEnum getResultStatus() {
		return resultStatus;
	}

	public void setResultStatus(LawResultMarkStatusEnum resultStatus) {
		this.resultStatus = resultStatus;
	}

	public String getSuggestion() {
		return suggestion;
	}

	public void setSuggestion(String suggestion) {
		this.suggestion = suggestion;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}


	public LawResultRiskLevelEnum getRiskLevel() {
		return riskLevel;
	}

	public void setRiskLevel(LawResultRiskLevelEnum riskLevel) {
		this.riskLevel = riskLevel;
	}

	public String getConclusion() {
		return conclusion;
	}

	public void setConclusion(String conclusion) {
		this.conclusion = conclusion;
	}

	public List<String> getScreenshotFileKeyList() {
		return screenshotFileKeyList;
	}

	public void setScreenshotFileKeyList(List<String> screenshotFileKeyList) {
		this.screenshotFileKeyList = screenshotFileKeyList;
	}
}
