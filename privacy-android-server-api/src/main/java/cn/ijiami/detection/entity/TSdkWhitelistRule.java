package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "t_sdk_whitelist_rule")
public class TSdkWhitelistRule implements Serializable {

    private static final long serialVersionUID = -6966330211988912060L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "package_name")
    private String packageName;

    @Column(name = "pattern")
    private String pattern;

    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime = new Date();

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime = new Date();

}
