package cn.ijiami.detection.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TPrivacyResultMarkImage.java
 * @Description 法规误报标记的图片
 * @createTime 2024年03月05日 15:06:00
 */
@Data
@Table(name = "t_privacy_result_mark_image")
public class TPrivacyResultMarkImage {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @Column(name = "mark_id")
    @ApiModelProperty(value = "标记ID")
    private Long markId;

    @Column(name = "detail_id")
    @ApiModelProperty(value = "检测项结果中的图片数据id")
    private Long detailId;

    @Column(name = "file_key")
    @ApiModelProperty(value = "文件key")
    private String fileKey;

}
