package cn.ijiami.detection.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TPrivacyActionNougatExtend.java
 * @Description 动态行为检测数据扩展表, 数据量大的字段拆分到这里
 * @createTime 2023年07月20日 17:58:00
 */
@Data
@ApiModel(value = "动态行为检测数据扩展表")
@Table(name = "t_privacy_action_nougat_extend")
public class TPrivacyActionNougatExtend {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "行为检测数据id")
    @Column(name = "nougat_id")
    private Long nougatId;

    @ApiModelProperty(value = "jni函数调用栈")
    @Column(name = "jni_stack_info")
    private String jniStackInfo;

    @ApiModelProperty(value = "触发行为的api名称")
    @Column(name = "api_name")
    private String apiName;

    public static TPrivacyActionNougatExtend make(TPrivacyActionNougat nougat) {
        TPrivacyActionNougatExtend extend = new TPrivacyActionNougatExtend();
        extend.setNougatId(nougat.getId());
        extend.setJniStackInfo(nougat.getJniStackInfo());
        extend.setApiName(nougat.getApiName());
        return extend;
    }

}
