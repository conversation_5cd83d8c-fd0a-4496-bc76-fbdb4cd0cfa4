package cn.ijiami.detection.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "TBroadcast",description = "静态广播实体类")
@Table(name = "t_broadcast")
public class TBroadcast implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "广播名称")
    @Column(name = "code")
    private String code;

    @ApiModelProperty(value = "广播描述")
    @Column(name = "description")
    private String description;
    
//    @ApiModelProperty(value = "广播接收路径")
//    @Column(name = "receiver_package")
//    private String receiverPackage;
//    
//    @ApiModelProperty(value = "资产ID")
//    @Column(name = "assets_id")
//    private Long assetsId;
}
