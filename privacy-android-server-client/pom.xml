<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.ijiami.framework</groupId>
		<artifactId>ijiami-framework</artifactId>
		<version>2.6.8-SNAPSHOT</version>
	</parent>
	<groupId>cn.ijiami.detection</groupId>
	<artifactId>privacy-android-server-client</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>privacy-android-server-client</name>
	<description>android检测服务客户端</description>
	
	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	
	<dependencies>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<version>2.1.3.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.5.22</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-common</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-mybatis</artifactId>
		</dependency>
	</dependencies>
	
	<!-- ijiami私服配置 - 用于下载依赖 -->
	<repositories>
		<repository>
			<id>ijiami_server</id>
			<url>http://172.10.3.192:8081/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	
	<!-- ijiami插件 maven库配置 - 用于下载插件 -->
	<pluginRepositories>
		<pluginRepository>
			<id>ijiami_server</id>
			<url>http://172.10.3.192:8081/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
	
	<!-- 发布配置 - 用于发布到私服供其他微服务引用 -->
	<distributionManagement>
		<repository>
			<id>releases</id>
			<name>Local Nexus Repository</name>
			<url>http://172.10.3.192:8081/nexus/content/repositories/releases</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>Local Nexus Repository</name>
			<url>http://172.10.3.192:8081/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>
	
	<build>
		<plugins>
			<!-- 编译插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>8</source>
					<target>8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			
			<!-- 源码打包插件 - 便于其他项目调试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<!-- Javadoc 打包插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<charset>UTF-8</charset>
					<docencoding>UTF-8</docencoding>
					<failOnError>false</failOnError>
					<doclint>none</doclint>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<!-- 部署插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
			</plugin>
		</plugins>
	</build>
</project>