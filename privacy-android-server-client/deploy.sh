#!/bin/bash

# Privacy Android Server Client 部署脚本
# 用于将客户端包发布到私服

echo "开始部署 privacy-android-server-client..."

# 检查是否在正确的目录
if [ ! -f "pom.xml" ]; then
    echo "错误：请在 privacy-android-server-client 目录下执行此脚本"
    exit 1
fi

# 检查 pom.xml 中的 artifactId
if ! grep -q "privacy-android-server-client" pom.xml; then
    echo "错误：当前目录不是 privacy-android-server-client 项目"
    exit 1
fi

# 清理并编译
echo "正在清理和编译..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码"
    exit 1
fi

# 运行测试（如果有的话）
echo "正在运行测试..."
mvn test

if [ $? -ne 0 ]; then
    echo "测试失败，请检查代码"
    exit 1
fi

# 打包
echo "正在打包..."
mvn package -DskipTests

if [ $? -ne 0 ]; then
    echo "打包失败"
    exit 1
fi

# 部署到私服
echo "正在部署到私服..."
mvn deploy -DskipTests

if [ $? -eq 0 ]; then
    echo "部署成功！"
    echo "其他微服务现在可以通过以下依赖引用："
    echo ""
    echo "<dependency>"
    echo "    <groupId>cn.ijiami.detection</groupId>"
    echo "    <artifactId>privacy-android-server-client</artifactId>"
    echo "    <version>1.0-SNAPSHOT</version>"
    echo "</dependency>"
    echo ""
else
    echo "部署失败，请检查网络连接和私服配置"
    exit 1
fi 