package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacySharedPrefsDTO.java
 * @Description 存储个人信息DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("存储个人信息DTO")
public class PrivacySharedPrefsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "存储键")
    private String key;

    @ApiModelProperty(value = "存储值")
    private String value;

    @ApiModelProperty(value = "存储类型")
    private String type;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "是否敏感")
    private Boolean sensitive;

    @ApiModelProperty(value = "敏感类型")
    private String sensitiveType;
}
