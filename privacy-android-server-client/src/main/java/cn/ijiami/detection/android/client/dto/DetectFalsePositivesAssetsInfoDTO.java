package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesAssetsInfoDTO.java
 * @Description 检测误报资产信息DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesAssetsInfoDTO {

    @ApiModelProperty(value = "资产名称", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;

    @ApiModelProperty(value = "本条误报次数", example = "1")
    private Integer currentDetectFalsePositivesCount;

    @ApiModelProperty(value = "上传人员", example = "管理员")
    private String userName;

    @ApiModelProperty(value = "版本", example = "1.0.0")
    private String version;

    @ApiModelProperty(value = "应用logo")
    private String logo;
}
