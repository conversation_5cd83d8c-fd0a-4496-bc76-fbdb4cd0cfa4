package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UpdateOrAddAssetsDTO.java
 * @Description 更新或添加资产DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "UpdateOrAddAssetsDTO", description = "更新或添加资产DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateOrAddAssetsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产ID")
    private Long assetsId;

    @ApiModelProperty(value = "文件ID")
    private String fileId;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "操作状态")
    private Integer status;

    @ApiModelProperty(value = "操作信息")
    private String message;
}
