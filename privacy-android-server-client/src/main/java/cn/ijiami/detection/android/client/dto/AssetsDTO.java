package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsDTO.java
 * @Description 资产DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "AssetsDTO", description = "资产DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsDTO implements Serializable {
    private static final long serialVersionUID = 1886046121202609094L;

    @ApiModelProperty(value = "资产ID 主键", example = "1")
    private Long id;

    @ApiModelProperty(value = "资产名称", example = "连连看")
    private String name;

    @ApiModelProperty(value = "版本", example = "1.0.0")
    private String version;

    @ApiModelProperty(value = "包名", example = "com.example.app")
    private String pakage;

    @ApiModelProperty(value = "图标", example = "icon.png")
    private String logo;

    @ApiModelProperty(value = "所属分类", example = "1")
    private Long category;

    @ApiModelProperty(value = "资产大小(MB)", example = "1.3")
    private String size;

    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "最后一次检测得分", example = "90")
    private String detectionScore;

    @ApiModelProperty(value = "最后一次检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastDetectionTime;

    @ApiModelProperty(value = "风险等级", example = "高")
    private String riskLevel;

    @ApiModelProperty(value = "终端类型", example = "1")
    private Integer terminalType;

    @ApiModelProperty(value = "所属平台", example = "detection")
    private String platform;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "更新人")
    private Long updateUserId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "MD5值")
    private String md5;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "应用签名")
    private String signature;

    @ApiModelProperty(value = "加密公司")
    private String encryptCompany;

    @ApiModelProperty(value = "IOS testflight地址")
    private String testflightUrl;

    @ApiModelProperty(value = "是否需要重签名-符号恢复 1需要 2不需要")
    private Integer isNeedSign;

    @ApiModelProperty(value = "obb数据包路径")
    private String obbDataPath;

    @ApiModelProperty(value = "隐私政策文件路径")
    private String privacyPolicyPath;

    @ApiModelProperty(value = "第三方共享清单文件路径")
    private String thirdPartySharedListPath;

    @ApiModelProperty(value = "二维码图片路径")
    private String qrcodeImagePath;
}
