package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskQueryParam.java
 * @Description 检测任务列表查询参数对象
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "TaskQueryParam", description = "检测任务列表查询参数对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "终端枚举（1.安卓 2.ios 3.微信公众号 4.微信小程序 5.文本 6.图片）为空则查询全部")
    private Integer terminalTypeEnum;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "资产名称")
    private String appName;

    @ApiModelProperty(value = "1.快速检测 2.深度检测 5. ai智能检测")
    private Integer detectionType;

    @ApiModelProperty(value = "1. 全部任务 2.普通任务 3.函数过滤任务 4.自定义法规任务， 默认为2")
    private Integer taskType = 2;

    @ApiModelProperty(value = "任务id集合", hidden = true)
    private List<String> ids = new ArrayList<>();

    @ApiModelProperty(value = "检测状态 1检测中 2已完成")
    private Integer status;

    @ApiModelProperty(value = "排序类型 1静态检测  2深度/动态 3法规检测")
    private Integer sortType;

    @ApiModelProperty(value = "排序升降 1升序  2降序")
    private Integer sortOrder;

    @ApiModelProperty(value = "静态检测状态 等待1、检测中2、中断3、成功4")
    private Integer staticStatus;

    @ApiModelProperty(value = "动态/深度检测状态  等待1、检测中2、中断3、成功4")
    private Integer dynamicStatus;

    @ApiModelProperty(value = "法规检测状态 等待1、检测中2、中断3、成功4")
    private Integer lawStatus;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize = 10;
}
