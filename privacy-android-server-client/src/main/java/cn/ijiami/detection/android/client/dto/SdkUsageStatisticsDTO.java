package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkUsageStatisticsDTO.java
 * @Description SDK使用统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class SdkUsageStatisticsDTO {

    @ApiModelProperty(value = "sdk名称", example = "微信SDK")
    private String sdkName;

    @ApiModelProperty(value = "sdk包名", example = "com.tencent.mm.sdk")
    private String sdkPackageName;

    @ApiModelProperty(value = "调用次数", example = "1")
    private Integer count;
}
