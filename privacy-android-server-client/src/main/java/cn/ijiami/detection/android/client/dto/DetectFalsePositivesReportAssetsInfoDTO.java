package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesReportAssetsInfoDTO.java
 * @Description 检测误报报告资产信息DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesReportAssetsInfoDTO {

    @ApiModelProperty(value = "资产名称", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "版本", example = "1.0.0")
    private String version;

    @ApiModelProperty(value = "包名", example = "com.tencent.mm")
    private String packageName;

    @ApiModelProperty(value = "法规列表")
    private List<DetectFalsePositivesReportLawDTO> lawList;
}
