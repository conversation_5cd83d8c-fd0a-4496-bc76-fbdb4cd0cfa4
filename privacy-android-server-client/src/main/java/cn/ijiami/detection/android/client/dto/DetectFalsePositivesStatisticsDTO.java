package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesStatisticsDTO.java
 * @Description 检测误报统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesStatisticsDTO {

    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "164法规误报统计")
    private LawFalsePositivesStatisticsDTO law164Statistics;

    @ApiModelProperty(value = "191法规误报统计")
    private LawFalsePositivesStatisticsDTO law191Statistics;

    @ApiModelProperty(value = "35273法规误报统计")
    private LawFalsePositivesStatisticsDTO law35273Statistics;

    @ApiModelProperty(value = "41391法规误报统计")
    private LawFalsePositivesStatisticsDTO law41391Statistics;
}
