package cn.ijiami.detection.android.client.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * Privacy Android Server Client 配置类
 * 
 * 其他微服务可以通过 @Import(PrivacyAndroidServerClientConfig.class) 
 * 或者在启动类上添加 @EnableFeignClients(basePackages = "cn.ijiami.detection.android.client.api")
 * 来启用 Feign 客户端
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableFeignClients(basePackages = "cn.ijiami.detection.android.client.api")
public class PrivacyAndroidServerClientConfig {
    
    // 可以在这里添加其他配置，比如自定义的 Feign 配置等
    
} 