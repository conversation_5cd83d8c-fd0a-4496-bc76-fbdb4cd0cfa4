package cn.ijiami.detection.android.client.dto;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatisticsLawItemDTO.java
 * @Description 检测统计法规条文DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectionStatisticsLawItemDTO {

    @ApiModelProperty(value = "法规条文id", example = "1")
    private Long lawItemId;

    @ApiModelProperty(value = "法规名称", example = "1. 未公开收集使用规则")
    private String lawName;

    @ApiModelProperty(value = "法规问题列表")
    private List<DetailDTO> lawDescriptionList;

    @Data
    public static class DetailDTO {

        @ApiModelProperty(value = "法规子条文id", example = "1")
        private Long lawChildItemId;

        @ApiModelProperty(value = "法规描述", example = "在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则")
        private String lawDescription;

        @ApiModelProperty(value = "存在风险", example = "1")
        private Integer nonComplianceCount;

        @ApiModelProperty(value = "APP资产列表")
        private PageInfo<AssetsInfoDTO> assetsList;
    }
}
