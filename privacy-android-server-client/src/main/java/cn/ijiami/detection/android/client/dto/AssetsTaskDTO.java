package cn.ijiami.detection.android.client.dto;

import cn.ijiami.detection.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.DynamicLawStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsTaskDTO.java
 * @Description 资产任务DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class AssetsTaskDTO {

    @ApiModelProperty(value = "资产名称", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "版本", example = "1.0.0")
    private String version;

    @ApiModelProperty(value = "创建用户名", example = "admin")
    private String createUserName;

    @ApiModelProperty(value = "资产创建时间")
    private Date assetsCreateTime;

    @ApiModelProperty(value = "资产大小", example = "100MB")
    private String assetsSize;

    @ApiModelProperty(value = "包名", example = "com.tencent.mm")
    private String packageName;

    @ApiModelProperty(value = "检测时间")
    private Date detectionTime;

    @ApiModelProperty(value = "检测类型", example = "1")
    private Integer detectionType;

    @ApiModelProperty(value = "静态检测状态")
    private DetectionStatusEnum staticStatus;

    @ApiModelProperty(value = "法规检测状态")
    private DynamicLawStatusEnum lawStatus;

    @ApiModelProperty(value = "动态检测状态")
    private DynamicAutoStatusEnum dynamicStatus;
}
