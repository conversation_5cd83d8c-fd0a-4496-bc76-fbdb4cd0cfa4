package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsStatisticalSummaryDTO.java
 * @Description 应用统计和检测数
 * @createTime 2025年05月15日 17:34:00
 */
@Data
public class AssetsStatisticalSummaryDTO {

    @ApiModelProperty(value = "应用资产统计")
    private AssetsStatisticsDTO assetsStatistics;

    @ApiModelProperty(value = "检测数top列表")
    private List<AssetsDetectionDTO> detectionTopList;

}
