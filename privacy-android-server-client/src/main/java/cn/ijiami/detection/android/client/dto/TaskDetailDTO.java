package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDetailDTO.java
 * @Description 任务详情DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "任务详情DTO")
public class TaskDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "线程ID")
    private String threadId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    private String packageName;

    @ApiModelProperty(value = "版本号")
    private String versionName;

    @ApiModelProperty(value = "文件MD5")
    private String apkMd5;

    @ApiModelProperty(value = "签名MD5")
    private String signMd5;

    @ApiModelProperty(value = "静态检测进度")
    private Double progress = 0.00;

    @ApiModelProperty(value = "动态检测进度")
    private Double dynamicProgress = 0.00;

    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date uploadTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "终端类型(1:android,2:ios)")
    private Integer terminalType;

    @ApiModelProperty(value = "检测类型")
    private Integer detectionType;

    @ApiModelProperty(value = "静态任务排队序号")
    private Integer staticTaskSort;

    @ApiModelProperty(value = "动态任务排队序号")
    private Integer dynamicTaskSort;

    @ApiModelProperty(value = "数据路径")
    private String dataPath;

    @ApiModelProperty(value = "原下载路径")
    private String shellIpaPath;

    @ApiModelProperty(value = "脱壳包下载路径")
    private String dumpFilePath;

    @ApiModelProperty(value = "app包下载路径")
    private String appFilePath;

    @ApiModelProperty(value = "是否已读")
    private Integer isRead;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "检测版本")
    private String detectionVersion;

    @ApiModelProperty(value = "是否砸壳")
    private Integer isHavePacker;

    @ApiModelProperty(value = "法规类型代码")
    private String lawTypeCode;
}
