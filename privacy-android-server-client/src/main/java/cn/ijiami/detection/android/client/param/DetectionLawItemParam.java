package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionLawItemParam.java
 * @Description 检测法规条文查询参数
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectionLawItemParam {

    @ApiModelProperty(value = "法规条文id", example = "1")
    private Long lawItemId;

    @ApiModelProperty(value = "根据应用名搜索", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "用户名", example = "abc")
    private String userName;

    @ApiModelProperty(value = "所属终端", example = "1")
    private Integer terminalType;

    @ApiModelProperty(value = "开始日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;
}
