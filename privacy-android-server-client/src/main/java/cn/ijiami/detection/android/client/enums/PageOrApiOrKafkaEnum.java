package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PageOrApiOrKafkaEnum implements BaseValueEnum {
    IS_PAGE(0,"页面发起任务"),IS_API(1,"API接口发起任务"),IS_KAFKA(2,"kafka发起任务");

    private int value;

    private String name;

    PageOrApiOrKafkaEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static PageOrApiOrKafkaEnum getItem(int value) {
        for (PageOrApiOrKafkaEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
