package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppDetailsDTO.java
 * @Description 应用详情DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "AppDetailsDTO", description = "应用详情DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppDetailsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    private String packageName;

    @ApiModelProperty(value = "版本名")
    private String versionName;

    @ApiModelProperty(value = "版本号")
    private String versionCode;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "应用大小")
    private Long appSize;

    @ApiModelProperty(value = "最小SDK版本")
    private String minSdkVersion;

    @ApiModelProperty(value = "目标SDK版本")
    private String targetSdkVersion;

    @ApiModelProperty(value = "编译SDK版本")
    private String compileSdkVersion;

    @ApiModelProperty(value = "应用签名")
    private String signature;

    @ApiModelProperty(value = "开发者")
    private String developer;

    @ApiModelProperty(value = "应用描述")
    private String description;
}
