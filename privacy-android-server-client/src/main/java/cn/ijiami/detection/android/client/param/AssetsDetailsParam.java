package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsDetailsParam.java
 * @Description 检测详情查询
 * @createTime 2025年05月15日 17:34:00
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsDetailsParam extends BaseEntity {

    @ApiModelProperty(value = "用户id", hidden = false, example = "abc")
    private Long userId;

    @ApiModelProperty(value = "资产名", hidden = false, example = "abc")
    private String assetsName;

    @ApiModelProperty(value = "检测类型", hidden = false, example = "检测类型 1.快速检测 2.深度检测 不传则返回全部")
    private Integer detectionType;

    @ApiModelProperty(value = "所属终端, 1 Android, 2 iOS, 4 小程序", hidden = false, example = "1")
    private Integer terminalType;


    @ApiModelProperty(value = "检测任务状态", hidden = false, example = "1 全部完成 2 全部中断 3 仅完成静态检测 4 完成静态+动态检测")
    private Integer detectionStatus;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public Integer getDetectionType() {
        return detectionType;
    }

    public void setDetectionType(Integer detectionType) {
        this.detectionType = detectionType;
    }

    public Integer getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }

    public Integer getDetectionStatus() {
        return detectionStatus;
    }

    public void setDetectionStatus(Integer detectionStatus) {
        this.detectionStatus = detectionStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
