package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatisticsDTO.java
 * @Description 检测统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectionStatisticsDTO {

    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "164合规问题统计")
    private LawStatisticsDTO law164Statistics;

    @ApiModelProperty(value = "191合规问题统计")
    private LawStatisticsDTO law191Statistics;

    @ApiModelProperty(value = "35273合规问题统计")
    private LawStatisticsDTO law35273Statistics;

    @ApiModelProperty(value = "41391合规问题统计")
    private LawStatisticsDTO law41391Statistics;

    @ApiModelProperty(value = "SDK使用情况统计")
    private List<SdkUsageStatisticsDTO> sdkUsageStatisticsList;
}
