package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseQueryParam.java
 * @Description 基础查询参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "BaseQueryParam", description = "基础查询参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseQueryParam {

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer rows;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    @ApiModelProperty(value = "排序方向")
    private String orderDirection;
}
