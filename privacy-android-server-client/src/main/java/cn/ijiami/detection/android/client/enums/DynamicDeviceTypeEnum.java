package cn.ijiami.detection.android.client.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * @Description 动态检测设备类型
 * <AUTHOR>
 * @Date 2020/8/13
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicDeviceTypeEnum implements BaseValueEnum {

    DYNAMIC_DEVICE_CLOUD(1, "云手机"),
    DYNAMIC_DEVICE_SANDBOX(2, "沙箱手机");

    // 值
    private int value;

    // 名称
    private String name;

    DynamicDeviceTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DynamicDeviceTypeEnum getItem(int value) {
        for (DynamicDeviceTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }


    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
