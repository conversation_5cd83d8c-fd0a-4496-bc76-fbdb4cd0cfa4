package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsDetectionDTO.java
 * @Description 检测数统计
 * @createTime 2025年05月15日 17:34:00
 */
@Data
public class AssetsDetectionDTO {

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "检测次数", hidden = false, example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

}
