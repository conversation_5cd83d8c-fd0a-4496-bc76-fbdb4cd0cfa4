package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChunkUploadFileDTO.java
 * @Description 分块上传文件DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "ChunkUploadFileDTO", description = "分块上传文件DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChunkUploadFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "文件MD5")
    private String fileMd5;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "分块总数")
    private Integer chunkTotal;

    @ApiModelProperty(value = "已上传分块数")
    private Integer uploadedChunks;

    @ApiModelProperty(value = "上传状态")
    private Integer uploadStatus;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
}
