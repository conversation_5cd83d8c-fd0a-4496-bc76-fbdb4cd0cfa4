package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.param.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsServiceApi.java
 * @Description 资产服务API
 * @createTime 2025年06月03日 18:39:00
 */
@Api("资产管理")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface AssetsServiceApi {

    @ApiOperation("新增资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/insert"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    Integer insert(@Valid @RequestBody AssetsDTO assets);

    @ApiOperation("查询资产列表集合")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/findAssetsListPage"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    AssetsListPageDTO findAssetsListPage(@Valid @RequestBody AssetsDTO assets);

    @ApiOperation("解析APK")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/analysisApk"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    AssetsDTO analysisApk(@Valid @RequestBody AnalysisApkParam param);

    @ApiOperation("解析APK带返回状态")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/analysisApkWithResponse"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    BaseResponseDTO<AssetsDTO> analysisApkWithResponse(@Valid @RequestBody AnalysisApkParam param);

    @ApiOperation("查询资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/findTassets"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    AssetsDTO findTassets(@Valid @RequestBody AssetsDTO assets);

    @ApiOperation("根据ID获取资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/getAssetsById"},
            produces = {"application/json"}
    )
    AssetsDTO getAssetsById(@RequestParam("assetsId") Long assetsId);

    @ApiOperation("保存或更新资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/saveOrUpdate"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    AssetsDTO saveOrUpdate(@Valid @RequestBody AssetsDTO assets);

    @ApiOperation("批量保存或更新资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/saveOrUpdateList"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void saveOrUpdateList(@Valid @RequestBody SaveOrUpdateListParam param);

    @ApiOperation("通过文件批量保存或更新资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/saveOrUpdateListByFileVO"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void saveOrUpdateListByFileVO(@Valid @RequestBody SaveOrUpdateListByFileParam param);

    @ApiOperation("检查分块文件")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/checkChunkFile"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    CheckChunkFileDTO checkChunkFile(@Valid @RequestBody CheckChunkFileParam param);

    @ApiOperation("停止URL上传")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/stopUrlUpload"},
            produces = {"application/json"}
    )
    void stopUrlUpload(@RequestParam("url") String url, @RequestParam("userId") Long userId);

    @ApiOperation("停止分块上传")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/stopChunkUpload"},
            produces = {"application/json"}
    )
    void stopChunkUpload(@RequestParam("md5") String md5, @RequestParam("userId") Long userId);

    @ApiOperation("删除资产")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/delete"},
            produces = {"application/json"}
    )
    void delete(@RequestParam("id") Long id, @RequestParam("userId") Long userId);

    @ApiOperation("删除APK")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/deleteApk"},
            produces = {"application/json"}
    )
    void deleteApk(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    @ApiOperation("获取资产文件包")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/getFileByAssetsId"},
            produces = {"application/json"}
    )
    Map<String, String> getFileByAssetsId(@RequestParam("assetsId") Long assetsId);

    @ApiOperation("下载脱壳包")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/getDumpByAssetsId"},
            produces = {"application/json"}
    )
    String getDumpByAssetsId(@RequestParam("assetsId") Long assetsId);

    @ApiOperation("清理分块文件")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/chunkClean"},
            produces = {"application/json"}
    )
    void chunkClean();

    @ApiOperation("获取应用签名信息")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/extractAppSignature"},
            produces = {"application/json"}
    )
    String extractAppSignature(@RequestParam("appFilePath") String appFilePath);

    @ApiOperation("查询分析队列")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/queryAnalysisQueue"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    List<ChunkUploadFileDTO> queryAnalysisQueue(@Valid @RequestBody BaseQueryParam baseQuery);

    @ApiOperation("保存OBB数据")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/saveObb"},
            produces = {"application/json"}
    )
    void saveObb(@RequestParam("assetsId") Long assetsId, 
                 @RequestParam("obbPath") String obbPath, 
                 @RequestParam("fileId") String fileId, 
                 @RequestParam("userId") Long userId);

    @ApiOperation("批量添加应用")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/batchAddApp"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void batchAddApp(@Valid @RequestBody BatchAddAppParam param);

    @ApiOperation("设置资产文件")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/assets/setAssetsFile"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void setAssetsFile(@Valid @RequestBody SetAssetsFileParam param);
}
