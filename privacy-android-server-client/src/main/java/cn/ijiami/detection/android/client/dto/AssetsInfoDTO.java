package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsInfo.java
 * @Description 资产详情
 * @createTime 2025年05月15日 17:34:00
 */
public class AssetsInfoDTO {

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "检测次数", hidden = false, example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "本条次数", example = "1")
    private Integer currentNonComplianceCount;

    @ApiModelProperty(value = "上传人员", example = "管理员")
    private String userName;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

    @ApiModelProperty(value = "应用logo", hidden = false, example = "L2RlZmF1bHQvaW1hZ2VzLzE2NTY5MTk2NTY1NTnmgYvniLHorrAucG5n")
    private String logo;

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public Integer getDetectionCount() {
        return detectionCount;
    }

    public void setDetectionCount(Integer detectionCount) {
        this.detectionCount = detectionCount;
    }

    public Integer getNonComplianceTotalCount() {
        return nonComplianceTotalCount;
    }

    public void setNonComplianceTotalCount(Integer nonComplianceTotalCount) {
        this.nonComplianceTotalCount = nonComplianceTotalCount;
    }

    public Integer getCurrentNonComplianceCount() {
        return currentNonComplianceCount;
    }

    public void setCurrentNonComplianceCount(Integer currentNonComplianceCount) {
        this.currentNonComplianceCount = currentNonComplianceCount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }
}
