package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskProgressParam.java
 * @Description 动态检测、合规检测更新进度参数类
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "动态检测、合规检测更新进度参数类")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskProgressParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "检测类型1.动态检测2.法规检测")
    private Integer type;

    @ApiModelProperty(value = "1.中断动态检测2.完成动态检测,开始检测不需要该参数")
    private Integer status;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Long userId;
}
