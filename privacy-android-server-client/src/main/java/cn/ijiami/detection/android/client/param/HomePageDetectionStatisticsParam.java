package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDetectionStatisticsParam.java
 * @Description 首页检测统计查询参数
 * @createTime 2025年01月15日 10:00:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomePageDetectionStatisticsParam {

    @ApiModelProperty(value = "所属终端", example = "1")
    private Integer terminalType;

    @ApiModelProperty(value = "开始日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;

    @ApiModelProperty(value = "包名列表")
    private List<String> packageNameList;
}
