package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsListItemDTO.java
 * @Description 资产列表项DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "AssetsListItemDTO", description = "资产列表项DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsListItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产ID")
    private Long id;

    @ApiModelProperty(value = "资产名称")
    private String name;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "包名")
    private String packageName;

    @ApiModelProperty(value = "图标")
    private String logo;

    @ApiModelProperty(value = "资产大小(MB)")
    private String size;

    @ApiModelProperty(value = "检测次数")
    private Integer detectionCount;

    @ApiModelProperty(value = "最后一次检测得分")
    private String detectionScore;

    @ApiModelProperty(value = "最后一次检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastDetectionTime;

    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "终端类型")
    private Integer terminalType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "MD5值")
    private String md5;
}
