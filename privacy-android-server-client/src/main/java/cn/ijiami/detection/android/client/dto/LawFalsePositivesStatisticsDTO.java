package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawFalsePositivesStatisticsDTO.java
 * @Description 法规误报统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class LawFalsePositivesStatisticsDTO {

    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;

    @ApiModelProperty(value = "法规条文列表")
    private List<DetectFalsePositivesLawDTO> lawItemList;

    @ApiModelProperty(value = "应用问题列表")
    private List<DetectFalsePositivesAppDTO> appIssueList;

    @Data
    public static class DetectFalsePositivesLawDTO {
        @ApiModelProperty(value = "法规条文id", example = "1")
        private Long lawItemId;

        @ApiModelProperty(value = "法规条文名称", example = "未公开收集使用规则")
        private String lawItemName;

        @ApiModelProperty(value = "误报数", example = "1")
        private Integer detectFalsePositivesCount;
    }

    @Data
    public static class DetectFalsePositivesAppDTO {
        @ApiModelProperty(value = "资产id", example = "1")
        private Long assetsId;

        @ApiModelProperty(value = "资产名称", example = "微信")
        private String assetsName;

        @ApiModelProperty(value = "误报数", example = "1")
        private Integer detectFalsePositivesCount;

        @ApiModelProperty(value = "版本", example = "1.0.0")
        private String version;
    }
}
