package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppInfoParam.java
 * @Description 应用信息参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "AppInfoParam", description = "应用信息参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppInfoParam {

    @ApiModelProperty(value = "应用名称", required = true)
    @NotBlank(message = "应用名称不能为空")
    private String appName;

    @ApiModelProperty(value = "包名", required = true)
    @NotBlank(message = "包名不能为空")
    private String packageName;

    @ApiModelProperty(value = "版本名")
    private String versionName;

    @ApiModelProperty(value = "版本号")
    private String versionCode;

    @ApiModelProperty(value = "应用路径")
    private String appPath;

    @ApiModelProperty(value = "图标路径")
    private String iconPath;

    @ApiModelProperty(value = "应用大小")
    private Long appSize;

    @ApiModelProperty(value = "终端类型")
    private Integer terminalType;
}
