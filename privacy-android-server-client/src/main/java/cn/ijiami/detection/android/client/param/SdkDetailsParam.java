package cn.ijiami.detection.android.client.param;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkDetailsParam.java
 * @Description SDK详情查询参数
 * @createTime 2025年01月15日 10:00:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SdkDetailsParam extends BaseEntity {

    @ApiModelProperty(value = "所属终端, 1 Android, 2 iOS")
    private Integer terminalType;

    @ApiModelProperty(value = "根据sdk名搜索", example = "微信")
    private String sdkName;

    @ApiModelProperty(value = "资产名", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "用户名", example = "abc")
    private String userName;

    @ApiModelProperty(value = "法规检测类型 1. 164检测 2. 191检测 不传则返回全部", example = "1")
    private Integer lawType;

    @ApiModelProperty(value = "开始日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;
}
