package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsStatisticsDTO.java
 * @Description 资产统计
 * @createTime 2025年05月15日 17:34:00
 */
@Data
public class AssetsStatisticsDTO {

    /**
     * 所属终端
     */
    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Integer totalCount;

    /**
     * 本周新增
     */
    @ApiModelProperty(value = "本周新增")
    private Integer incrementThisWeek;

    /**
     * 本月新增
     */
    @ApiModelProperty(value = "本月新增")
    private Integer incrementThisMonth;

    /**
     * 本年新增
     */
    @ApiModelProperty(value = "本年新增")
    private Integer incrementThisYear;

}
