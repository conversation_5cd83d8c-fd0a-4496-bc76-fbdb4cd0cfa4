package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesDetailDTO.java
 * @Description 检测误报详情DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesDetailDTO {

    @ApiModelProperty(value = "资产名称", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "资产版本列表")
    private List<VersionDTO> versionList;

    @Data
    public static class VersionDTO {

        @ApiModelProperty(value = "版本", example = "1.0.0")
        private String version;

        @ApiModelProperty(value = "检测记录")
        private List<DetectionDTO> detectionList;
    }

    @Data
    public static class DetectionDTO {

        @ApiModelProperty(value = "检测时间", example = "2022-07-01 09:55:27")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date detectionTime;

        @ApiModelProperty(value = "误报总数", example = "1")
        private Integer detectFalsePositivesCount;

        @ApiModelProperty(value = "误报内容列表")
        private List<DetectFalsePositivesContentDTO> detectFalsePositivesContentList;
    }

    @Data
    public static class DetectFalsePositivesContentDTO {

        @ApiModelProperty(value = "法规名称", example = "1. 未公开收集使用规则")
        private String lawName;

        @ApiModelProperty(value = "法规描述")
        private List<String> lawDescriptionList;
    }
}
