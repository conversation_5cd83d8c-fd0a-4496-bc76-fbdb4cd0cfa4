package cn.ijiami.detection.android.client.api;

import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.param.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 *
 */
@Api("统计")
@FeignClient(value = "${ijiami-cloud-privacy-android-server-name:privacy-android-server}")
public interface StatisticsServiceApi {

    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsStatisticalSummary"},
            produces = {"application/json"}
    )
    AssetsStatisticalSummaryDTO assetsStatisticalSummary(@Valid @RequestBody AssetsStatisticsParam param);

    @ApiOperation("启动任务接口")
    @PostMapping(
            value = {"${ijiami-cloud-privacy-android-server:/privacy-detection}/api/detection/statistics/assetsDetailsByPage"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(@Valid @RequestBody AssetsDetailsParam param);

}
