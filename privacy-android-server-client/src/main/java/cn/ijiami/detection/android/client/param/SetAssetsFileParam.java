package cn.ijiami.detection.android.client.param;

import cn.ijiami.detection.android.client.dto.AssetsDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SetAssetsFileParam.java
 * @Description 设置资产文件参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "SetAssetsFileParam", description = "设置资产文件参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetAssetsFileParam {

    @ApiModelProperty(value = "资产信息", required = true)
    @NotNull(message = "资产信息不能为空")
    private AssetsDTO assets;

    @ApiModelProperty(value = "隐私政策文件ID")
    private String appPrivacyPolicyFileId;

    @ApiModelProperty(value = "第三方共享清单文件ID")
    private String thirdPartySharedListFileId;

    @ApiModelProperty(value = "二维码文件ID")
    private String qrcodeFileId;
}
