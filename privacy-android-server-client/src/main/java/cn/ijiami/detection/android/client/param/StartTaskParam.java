package cn.ijiami.detection.android.client.param;

import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.android.client.enums.PageOrApiOrKafkaEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 检测任务创建query对象
 */
@ApiModel(value = "StartTaskParam", description = "开始检测任务对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class StartTaskParam {

    @ApiModelProperty(value = "检测模板id")
    private Long templateId;

    @ApiModelProperty(value = "资产id")
    private List<Long> assetsIds;

    @ApiModelProperty(value = "用户id", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "安卓动态/法规检测设备类型,1云手机2沙箱手机")
    private DynamicDeviceTypeEnum dynamicDeviceTypeEnum;

    /**
     * 用来区分前端tab（快速、深度）页
     */
    @ApiModelProperty(value = "检测类型1.快速检测 2.深度检测 5 ai检测")
    private Integer detectionType;
    
    
    private String bussinessId;
    
    private String callbackUrl;

    private String appId;

    @ApiModelProperty(value = "区分页面、接口、kafka消费   0页面，1API接口，2kafka",hidden = true)
    private PageOrApiOrKafkaEnum pageOrApiOrKafkaEnum;
    
    @ApiModelProperty(value = "选择检测的法规(多个用逗号分隔  1,2,5,6)")
    private String laws;
    @ApiModelProperty(value = "设备型号 SandBox V2.921")
    private String model;
    @ApiModelProperty(value = "设备版本 8.1.0")
    private String version;

    @ApiModelProperty(value = "行为函数过滤集合id")
    private Long actionFilterGroupId;

    @ApiModelProperty(value = "自定义法规集id")
    private Long customLawsGroupId;

    public String getLaws() {
		return laws;
	}

	public void setLaws(String laws) {
		this.laws = laws;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public PageOrApiOrKafkaEnum getPageOrApiOrKafkaEnum() {
        return pageOrApiOrKafkaEnum;
    }

    public void setPageOrApiOrKafkaEnum(PageOrApiOrKafkaEnum pageOrApiOrKafkaEnum) {
        this.pageOrApiOrKafkaEnum = pageOrApiOrKafkaEnum;
    }

    public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public Long getTemplateId() {
        return templateId;
    }

    public String getBussinessId() {
		return bussinessId;
	}

	public void setBussinessId(String bussinessId) {
		this.bussinessId = bussinessId;
	}

	public Integer getDetectionType() {
        return detectionType;
    }

    public void setDetectionType(Integer detectionType) {
        this.detectionType = detectionType;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public List<Long> getAssetsIds() {
        return assetsIds;
    }

    public void setAssetsIds(List<Long> assetsIds) {
        this.assetsIds = assetsIds;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public DynamicDeviceTypeEnum getDynamicDeviceTypeEnum() {
        return dynamicDeviceTypeEnum;
    }

    public void setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum dynamicDeviceTypeEnum) {
        this.dynamicDeviceTypeEnum = dynamicDeviceTypeEnum;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Long getActionFilterGroupId() {
        return actionFilterGroupId;
    }

    public void setActionFilterGroupId(Long actionFilterGroupId) {
        this.actionFilterGroupId = actionFilterGroupId;
    }

    public Long getCustomLawsGroupId() {
        return customLawsGroupId;
    }

    public void setCustomLawsGroupId(Long customLawsGroupId) {
        this.customLawsGroupId = customLawsGroupId;
    }
}
