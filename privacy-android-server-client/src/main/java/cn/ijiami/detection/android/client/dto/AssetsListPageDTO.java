package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsListPageDTO.java
 * @Description 资产列表分页DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "AssetsListPageDTO", description = "资产列表分页DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsListPageDTO implements Serializable {
    private static final long serialVersionUID = -750353209095494329L;

    @ApiModelProperty(value = "全部应用数量")
    private Integer allAppSize;

    @ApiModelProperty(value = "Android应用数量")
    private Integer androidAppSize;

    @ApiModelProperty(value = "IOS应用数量")
    private Integer iosAppSize;

    @ApiModelProperty(value = "微信小程序数量")
    private Integer wechatAppletSize;

    @ApiModelProperty(value = "支付宝小程序数量")
    private Integer alipayAppletSize;

    @ApiModelProperty(value = "鸿蒙应用数量")
    private Integer harmonySize;

    @ApiModelProperty(value = "资产分页列表")
    private PageInfo<AssetsListItemDTO> pageInfoAssets;
}
