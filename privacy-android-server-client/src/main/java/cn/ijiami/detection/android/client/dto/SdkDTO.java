package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkDTO.java
 * @Description 第三方SDK信息DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("第三方SDK信息DTO")
public class SdkDTO implements Serializable {

    private static final long serialVersionUID = 7453227714287101091L;

    @ApiModelProperty(value = "SDK ID")
    private Long id;

    @ApiModelProperty(value = "SDK名称")
    private String sdkName;

    @ApiModelProperty(value = "SDK版本")
    private String sdkVersion;

    @ApiModelProperty(value = "包名")
    private String packageName;

    @ApiModelProperty(value = "功能分类")
    private String funcClassify;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    @ApiModelProperty(value = "公司网站")
    private String companyWebSiteUrl;

    @ApiModelProperty(value = "权限列表")
    private List<PermissionDTO> permissions = new ArrayList<>();

    @ApiModelProperty(value = "境外地址列表")
    private List<PrivacyOutsideAddressDTO> outsideAddresses = new ArrayList<>();

    @ApiModelProperty(value = "SDK行为分析")
    private List<PrivacyActionNougatDTO> actionNougatList = new ArrayList<>();

    @ApiModelProperty(value = "存储个人信息")
    private List<PrivacySharedPrefsDTO> sharedPrefs = new ArrayList<>();

    @ApiModelProperty(value = "与个人信息相关行为（单位：种）")
    private Integer isPersonalActionNougatCount;

    @ApiModelProperty(value = "与个人信息相关权限（单位：个）")
    private Integer isPersonalPermissionCount;

    @ApiModelProperty(value = "通信境外IP（单位：个）")
    private Integer outsideIpCount;

    @ApiModelProperty(value = "是否为ios解析PrivacyInfo.xcprivacy文件得到数据")
    private boolean xcprivacy;
}
