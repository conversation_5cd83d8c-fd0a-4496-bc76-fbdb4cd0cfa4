package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawStatisticsDTO.java
 * @Description 法规统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class LawStatisticsDTO {

    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "法规条文列表")
    private List<LawItemDTO> lawItemList;

    @ApiModelProperty(value = "应用问题列表")
    private List<RiskResultDTO> appIssueList;

    @Data
    public static class LawItemDTO {
        @ApiModelProperty(value = "法规条文id", example = "1")
        private Long lawItemId;

        @ApiModelProperty(value = "法规条文名称", example = "未公开收集使用规则")
        private String lawItemName;

        @ApiModelProperty(value = "不合规数量", example = "1")
        private Integer nonComplianceCount;
    }

    @Data
    public static class RiskResultDTO {
        @ApiModelProperty(value = "资产id", example = "1")
        private Long assetsId;

        @ApiModelProperty(value = "资产名称", example = "微信")
        private String assetsName;

        @ApiModelProperty(value = "风险数量", example = "1")
        private Integer riskCount;

        @ApiModelProperty(value = "版本", example = "1.0.0")
        private String version;
    }
}
