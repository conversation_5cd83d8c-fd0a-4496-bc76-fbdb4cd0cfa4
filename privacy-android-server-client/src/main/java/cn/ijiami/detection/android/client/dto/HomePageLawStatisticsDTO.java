package cn.ijiami.detection.android.client.dto;

import cn.ijiami.detection.enums.StatisticsLawType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageLawStatisticsDTO.java
 * @Description 首页法规统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class HomePageLawStatisticsDTO {

    @ApiModelProperty(value = "法规类型和名称，1 164号文, 2 191号文, 3 35273法规, 4 41391法规", example = "1")
    private StatisticsLawType lawType;

    @ApiModelProperty(value = "检测次数", example = "1")
    private Integer detectionTotalCount;

    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;
}
