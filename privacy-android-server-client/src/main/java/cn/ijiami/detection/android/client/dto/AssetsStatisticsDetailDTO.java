package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsStatisticsDetailDTO.java
 * @Description 资产统计详情
 * @createTime 2025年05月15日 17:34:00
 */
public class AssetsStatisticsDetailDTO {

    @ApiModelProperty(value = "唯一id，前端展示要用", hidden = false, example = "1231231")
    private String id;

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "资产id", hidden = false, example = "1231231")
    private Long assetsId;

    @ApiModelProperty(value = "检测次数", hidden = false, example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "快速检测次数", hidden = false, example = "1")
    private Integer fastDetectionCount;

    @ApiModelProperty(value = "深度检测次数", hidden = false, example = "1")
    private Integer deepDetectionCount;

    @ApiModelProperty(value = "快速检测完成次数", hidden = false, example = "1")
    private Integer fastDetectionCompletions;

    @ApiModelProperty(value = "深度检测完成次数", hidden = false, example = "1")
    private Integer deepDetectionCompletions;

    @ApiModelProperty(value = "快速检测失败次数", hidden = false, example = "1")
    private Integer fastDetectionFailures;

    @ApiModelProperty(value = "深度检测失败次数", hidden = false, example = "1")
    private Integer deepDetectionFailures;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

    @ApiModelProperty(value = "最近一次检测时间", hidden = false, example = "2022-07-01 09:55:27")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastDetectionTime;

    @ApiModelProperty(value = "静态检测耗时", hidden = false, example = "40min")
    private String staticDetectDuration;

    @ApiModelProperty(value = "动态检测耗时", hidden = false, example = "20min")
    private String dynamicDetectDuration;

    @ApiModelProperty(value = "资产版本列表", hidden = false, example = "")
    private List<AssetsStatisticsDetailDTO> versionList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public Long getAssetsId() {
        return assetsId;
    }

    public void setAssetsId(Long assetsId) {
        this.assetsId = assetsId;
    }

    public Integer getDetectionCount() {
        return detectionCount;
    }

    public void setDetectionCount(Integer detectionCount) {
        this.detectionCount = detectionCount;
    }

    public Integer getFastDetectionCount() {
        return fastDetectionCount;
    }

    public void setFastDetectionCount(Integer fastDetectionCount) {
        this.fastDetectionCount = fastDetectionCount;
    }

    public Integer getDeepDetectionCount() {
        return deepDetectionCount;
    }

    public void setDeepDetectionCount(Integer deepDetectionCount) {
        this.deepDetectionCount = deepDetectionCount;
    }

    public Integer getFastDetectionCompletions() {
        return fastDetectionCompletions;
    }

    public void setFastDetectionCompletions(Integer fastDetectionCompletions) {
        this.fastDetectionCompletions = fastDetectionCompletions;
    }

    public Integer getDeepDetectionCompletions() {
        return deepDetectionCompletions;
    }

    public void setDeepDetectionCompletions(Integer deepDetectionCompletions) {
        this.deepDetectionCompletions = deepDetectionCompletions;
    }

    public Integer getFastDetectionFailures() {
        return fastDetectionFailures;
    }

    public void setFastDetectionFailures(Integer fastDetectionFailures) {
        this.fastDetectionFailures = fastDetectionFailures;
    }

    public Integer getDeepDetectionFailures() {
        return deepDetectionFailures;
    }

    public void setDeepDetectionFailures(Integer deepDetectionFailures) {
        this.deepDetectionFailures = deepDetectionFailures;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Date getLastDetectionTime() {
        return lastDetectionTime;
    }

    public void setLastDetectionTime(Date lastDetectionTime) {
        this.lastDetectionTime = lastDetectionTime;
    }

    public String getStaticDetectDuration() {
        return staticDetectDuration;
    }

    public void setStaticDetectDuration(String staticDetectDuration) {
        this.staticDetectDuration = staticDetectDuration;
    }

    public String getDynamicDetectDuration() {
        return dynamicDetectDuration;
    }

    public void setDynamicDetectDuration(String dynamicDetectDuration) {
        this.dynamicDetectDuration = dynamicDetectDuration;
    }

    public List<AssetsStatisticsDetailDTO> getVersionList() {
        return versionList;
    }

    public void setVersionList(List<AssetsStatisticsDetailDTO> versionList) {
        this.versionList = versionList;
    }
}
