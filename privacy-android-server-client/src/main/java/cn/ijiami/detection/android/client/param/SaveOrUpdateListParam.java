package cn.ijiami.detection.android.client.param;

import cn.ijiami.detection.android.client.dto.AssetsDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SaveOrUpdateListParam.java
 * @Description 批量保存或更新参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "SaveOrUpdateListParam", description = "批量保存或更新参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveOrUpdateListParam {

    @ApiModelProperty(value = "资产列表", required = true)
    @NotEmpty(message = "资产列表不能为空")
    private List<AssetsDTO> assetsList;

    @ApiModelProperty(value = "终端类型", required = true)
    @NotNull(message = "终端类型不能为空")
    private Integer terminalType;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
