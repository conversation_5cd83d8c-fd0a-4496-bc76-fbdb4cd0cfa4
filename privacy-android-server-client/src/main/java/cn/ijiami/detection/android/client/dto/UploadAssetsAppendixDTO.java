package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UploadAssetsAppendixDTO.java
 * @Description 上传资产附件DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "UploadAssetsAppendixDTO", description = "上传资产附件DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadAssetsAppendixDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件ID")
    private String fileId;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "上传状态")
    private Integer uploadStatus;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
}
