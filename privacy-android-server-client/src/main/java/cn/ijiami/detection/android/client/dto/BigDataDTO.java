package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BigDataDTO.java
 * @Description 大数据DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("大数据DTO")
public class BigDataDTO implements Serializable {
    private static final long serialVersionUID = -6906212185693103819L;

    @ApiModelProperty(value = "MD5值")
    private String md5;

    @ApiModelProperty(value = "是否有风险")
    private Boolean risk = true;

    @ApiModelProperty(value = "检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date detectionTime;
}
