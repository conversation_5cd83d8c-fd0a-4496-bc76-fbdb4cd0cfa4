package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionStatisticsParam.java
 * @Description 检测统计查询参数
 * @createTime 2025年01月15日 10:00:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetectionStatisticsParam {

    @ApiParam(value = "标签页通知id，用来区分给哪个标签页发消息")
    private String notificationId;

    @ApiModelProperty(value = "用户名", example = "abc")
    private String userName;

    @ApiModelProperty(value = "资产名", example = "abc")
    private String assetsName;

    @ApiModelProperty(value = "所属终端", example = "1")
    private Integer terminalType;

    @ApiModelProperty(value = "法规类型", example = "法规类型1. 164检测 2. 191检测 3. 35273检测 4. 41391检测")
    private Integer lawType;

    @ApiModelProperty(value = "开始日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;
}
