package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyOutsideAddressDTO.java
 * @Description 境外地址DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("境外地址DTO")
public class PrivacyOutsideAddressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    @ApiModelProperty(value = "域名")
    private String domain;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "地区")
    private String region;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "ISP")
    private String isp;

    @ApiModelProperty(value = "组织")
    private String org;

    @ApiModelProperty(value = "是否境外")
    private Boolean isOutside;

    @ApiModelProperty(value = "访问次数")
    private Integer accessCount;
}
