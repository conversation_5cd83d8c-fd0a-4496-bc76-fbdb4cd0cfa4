package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BatchAddAppParam.java
 * @Description 批量添加应用参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "BatchAddAppParam", description = "批量添加应用参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchAddAppParam {

    @ApiModelProperty(value = "应用列表", required = true)
    @NotEmpty(message = "应用列表不能为空")
    private List<AppInfoParam> appList;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
