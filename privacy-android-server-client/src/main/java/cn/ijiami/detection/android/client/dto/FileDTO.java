package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FileDTO.java
 * @Description 文件DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "FileDTO", description = "文件DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件扩展名")
    private String fileExtName;

    @ApiModelProperty(value = "文件键")
    private String fileKey;

    @ApiModelProperty(value = "相对路径")
    private String relativePath;

    @ApiModelProperty(value = "参数")
    private Map<String, Object> params;
}
