package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseResponseDTO.java
 * @Description 基础响应DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "BaseResponseDTO", description = "基础响应DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseResponseDTO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "数据")
    private T data;
}
