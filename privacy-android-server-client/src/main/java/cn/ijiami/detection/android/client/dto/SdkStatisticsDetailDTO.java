package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkStatisticsDetailDTO.java
 * @Description SDK统计详情DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class SdkStatisticsDetailDTO {

    @ApiModelProperty(value = "sdk名称", example = "微信SDK")
    private String sdkName;

    @ApiModelProperty(value = "sdk包名", example = "com.tencent.mm.sdk")
    private String sdkPackageName;

    @ApiModelProperty(value = "厂家", example = "腾讯")
    private String manufacturer;

    @ApiModelProperty(value = "合规风险总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "涉及应用问题")
    private List<String> lawItemList;
}
