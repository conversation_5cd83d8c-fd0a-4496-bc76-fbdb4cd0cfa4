package cn.ijiami.detection.android.client.param;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionDetailsParam.java
 * @Description 检测详情查询参数
 * @createTime 2025年01月15日 10:00:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetectionDetailsParam extends BaseEntity {

    @ApiModelProperty(value = "法规id", example = "1")
    private Integer lawId;

    @ApiModelProperty(value = "根据应用名搜索", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "用户名", example = "abc")
    private String userName;

    @ApiModelProperty(value = "开始日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束日期", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;
}
