package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDetectionStatisticsDTO.java
 * @Description 首页检测统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class HomePageDetectionStatisticsDTO {

    @ApiModelProperty(value = "检测应用数，单个应用查询时为检测应用版本数", example = "1")
    private Integer detectionTotalCount;

    @ApiModelProperty(value = "快速检测次数", example = "1")
    private Integer fastDetectionCount;

    @ApiModelProperty(value = "深度检测次数", example = "1")
    private Integer deepDetectionCount;

    @ApiModelProperty(value = "AI检测次数", example = "1")
    private Integer aIDetectionCount;

    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "每日检测统计列表")
    private List<HomePageDailyStatisticsDTO> dailyStatisticsList;

    @ApiModelProperty(value = "法规统计列表")
    private List<HomePageLawStatisticsDTO> lawStatisticsList;
}
