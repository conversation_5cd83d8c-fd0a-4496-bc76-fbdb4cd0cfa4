package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CheckChunkFileParam.java
 * @Description 检查分块文件参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "CheckChunkFileParam", description = "检查分块文件参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckChunkFileParam {

    @ApiModelProperty(value = "文件MD5", required = true)
    @NotBlank(message = "文件MD5不能为空")
    private String fileMd5;

    @ApiModelProperty(value = "源文件名", required = true)
    @NotBlank(message = "源文件名不能为空")
    private String sourceFileName;

    @ApiModelProperty(value = "分块总数", required = true)
    @NotNull(message = "分块总数不能为空")
    private Integer chunkTotal;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
