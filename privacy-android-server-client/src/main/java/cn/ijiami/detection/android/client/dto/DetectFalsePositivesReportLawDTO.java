package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesReportLawDTO.java
 * @Description 检测误报报告法规DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesReportLawDTO {

    @ApiModelProperty(value = "用户名", example = "admin")
    private String userName;

    @ApiModelProperty(value = "法规id", example = "1")
    private Integer lawId;

    @ApiModelProperty(value = "检测时间")
    private Date detectionTime;

    @ApiModelProperty(value = "条目列表")
    private List<DetectFalsePositivesReportItemDTO> itemList;
}
