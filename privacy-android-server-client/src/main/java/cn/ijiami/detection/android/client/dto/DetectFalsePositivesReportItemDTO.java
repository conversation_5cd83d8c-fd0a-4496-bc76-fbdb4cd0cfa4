package cn.ijiami.detection.android.client.dto;

import cn.ijiami.detection.enums.LawResultStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesReportItemDTO.java
 * @Description 检测误报报告条目DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class DetectFalsePositivesReportItemDTO {

    @ApiModelProperty(value = "结果id", example = "1")
    private Long resultId;

    @ApiModelProperty(value = "法规条文父级id", example = "1")
    private Long lawsItemParentId;

    @ApiModelProperty(value = "法规条文父级名称", example = "未公开收集使用规则")
    private String lawsItemParentName;

    @ApiModelProperty(value = "法规条文名称", example = "在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则")
    private String lawsItemName;

    @ApiModelProperty(value = "别名建议")
    private String aliasSuggestion;

    @ApiModelProperty(value = "别名")
    private String aliasName;

    @ApiModelProperty(value = "建议")
    private String suggestion;

    @ApiModelProperty(value = "结论")
    private String conclusion;

    @ApiModelProperty(value = "场景标题")
    private String sceneTitle;

    @ApiModelProperty(value = "原始结果状态")
    private LawResultStatusEnum originalResultStatus;

    @ApiModelProperty(value = "结果状态")
    private LawResultStatusEnum resultStatus;

    @ApiModelProperty(value = "标记描述")
    private String markDescription;
}
