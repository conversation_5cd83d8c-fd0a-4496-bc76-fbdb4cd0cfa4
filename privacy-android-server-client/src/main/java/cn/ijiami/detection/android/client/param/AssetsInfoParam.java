package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsInfoParam.java
 * @Description 资产详情查询
 * @createTime 2025年05月15日 17:34:00
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsInfoParam extends BaseEntity {

    @ApiModelProperty(value = "法规子条文id", example = "1")
    private Long lawChildItemId;

    @ApiModelProperty(value = "资产名", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "用户名", hidden = false, example = "abc")
    private String userName;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;

    public Long getLawChildItemId() {
        return lawChildItemId;
    }

    public void setLawChildItemId(Long lawChildItemId) {
        this.lawChildItemId = lawChildItemId;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
