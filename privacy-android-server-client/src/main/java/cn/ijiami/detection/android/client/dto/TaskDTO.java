package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskParam.java
 * @Description 任务实体DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("任务实体DTO")
public class TaskParam implements Serializable {

    @ApiModelProperty(value = "任务ID 主键")
    private Long taskId;

    @ApiModelProperty(value = "线程ID")
    private String threadId;

    @ApiModelProperty(value = "检测详情ID")
    private String apkDetectionDetailId;

    @ApiModelProperty(value = "资产id")
    private Long assetsId;

    @ApiModelProperty(value = "所属平台")
    private String platform;

    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "静态检测状态")
    private Integer taskTatus;

    @ApiModelProperty(value = "动态检测状态")
    private Integer dynamicStatus;

    @ApiModelProperty(value = "动态检测子状态")
    private Integer dynamicSubStatus;

    @ApiModelProperty(value = "完整检测状态")
    private Integer dynamicManualStatus;

    @ApiModelProperty(value = "法规检测状态")
    private Integer dynamicLawStatus;

    @ApiModelProperty(value = "法规检测子状态")
    private Integer dynamicLawSubStatus;

    @ApiModelProperty(value = "复核检测状态")
    private Integer reviewStatus;

    @ApiModelProperty(value = "是否完成检测")
    private Integer detectComplete;

    @ApiModelProperty(value = "是否合规")
    private Boolean isSafe;

    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date taskStarttime;

    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date taskEndtime;

    @ApiModelProperty(value = "静态检测时长")
    private Long staticDetectDuration;

    @ApiModelProperty(value = "动态检测时长")
    private Long dynamicDetectDuration;

    @ApiModelProperty(value = "法规检测时长")
    private Long lawDetectDuration;

    @ApiModelProperty(value = "检测类型（快速、深度）")
    private Integer detectionType;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "APK的MD5")
    private String md5;

    @ApiModelProperty(value = "数据路径")
    private String dataPath;

    @ApiModelProperty(value = "动态检测开始时间")
    private Date dynamicStarttime;

    @ApiModelProperty(value = "法规检测开始时间")
    private Date lawStarttime;

    @ApiModelProperty(value = "复核检测开始时间")
    private Date reviewStarttime;

    @ApiModelProperty(value = "动态检测异常描述")
    private String description;

    @ApiModelProperty(value = "静态检测排队序号")
    private Integer staticTaskSort;

    @ApiModelProperty(value = "动态/法规检测排队序号")
    private Integer dynamicTaskSort;

    @ApiModelProperty(value = "动态/法规检测设备类型")
    private Integer dynamicDeviceType;

    @ApiModelProperty(value = "云手机序号(内嵌手机地址id)")
    private String deviceSerial;

    @ApiModelProperty(value = "云手机硬件序号")
    private String deviceHardwareSerial;

    @ApiModelProperty(value = "云手机序号远程连接地址")
    private String deviceRemoteConnectUrl;

    @ApiModelProperty(value = "云手机token")
    private String stfToken;

    @ApiModelProperty(value = "任务修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    @ApiModelProperty(value = "检测时长")
    private String detectTimeLength;

    @ApiModelProperty(value = "version")
    private Long version;
}
