package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDailyStatisticsDTO.java
 * @Description 首页每日统计DTO
 * @createTime 2025年01月15日 10:00:00
 */
@Data
public class HomePageDailyStatisticsDTO {

    @ApiModelProperty(value = "检测应用数", example = "1")
    private Integer detectionTotalCount;

    @ApiModelProperty(value = "快速检测次数", example = "1")
    private Integer fastDetectionCount;

    @ApiModelProperty(value = "深度检测次数", example = "1")
    private Integer deepDetectionCount;

    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "日期", example = "2017-07-18")
    private String detectionDate;
}
