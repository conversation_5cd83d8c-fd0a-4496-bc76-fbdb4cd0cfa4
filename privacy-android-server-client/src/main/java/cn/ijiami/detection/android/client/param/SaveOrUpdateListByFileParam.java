package cn.ijiami.detection.android.client.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SaveOrUpdateListByFileParam.java
 * @Description 通过文件批量保存或更新参数
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "SaveOrUpdateListByFileParam", description = "通过文件批量保存或更新参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SaveOrUpdateListByFileParam {

    @ApiModelProperty(value = "文件列表", required = true)
    @NotEmpty(message = "文件列表不能为空")
    private List<FileParam> fileVOList;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
