package cn.ijiami.detection.android.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CheckChunkFileDTO.java
 * @Description 检查分块文件DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel(value = "CheckChunkFileDTO", description = "检查分块文件DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckChunkFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件MD5")
    private String fileMd5;

    @ApiModelProperty(value = "源文件名")
    private String sourceFileName;

    @ApiModelProperty(value = "分块总数")
    private Integer chunkTotal;

    @ApiModelProperty(value = "已上传的分块数")
    private Integer uploadedChunks;

    @ApiModelProperty(value = "是否上传完成")
    private Boolean uploadComplete;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "上传状态")
    private Integer uploadStatus;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
}
