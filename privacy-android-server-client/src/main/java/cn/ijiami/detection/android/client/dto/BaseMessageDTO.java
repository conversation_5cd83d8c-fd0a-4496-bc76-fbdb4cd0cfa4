package cn.ijiami.detection.android.client.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseMessageDTO.java
 * @Description 基本信息DTO
 * @createTime 2025年06月03日 18:39:00
 */
@Data
@ApiModel("基本信息DTO")
public class BaseMessageDTO implements Serializable {

    private static final long serialVersionUID = -1667693858186932209L;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    private String packageName;

    @ApiModelProperty(value = "apk 大小")
    private String apkSize;

    @ApiModelProperty(value = "版本")
    private String versionName;

    @ApiModelProperty(value = "平台地址，导出报告时需要显示")
    private String platformHost;

    @ApiModelProperty(value = "目标SDK版本")
    private String targetSdkVersion;

    @ApiModelProperty(value = "最小SDK版本")
    private String minSdkVersion;

    @ApiModelProperty(value = "文件MD5")
    @JSONField(alternateNames = "apkMD5")
    private String apkMd5;

    @ApiModelProperty(value = "签名MD5")
    private String signMd5;

    @ApiModelProperty(value = "签名")
    private String signDetail;

    @ApiModelProperty(value = "是否加固")
    private String encryptDetail;

    @ApiModelProperty(value = "apk文件路径")
    private String filePath;

    @ApiModelProperty(value = "所属终端")
    private Integer terminalType;

    @ApiModelProperty(value = "签名详情")
    private List<String> signDetailList;

    @ApiModelProperty(value = "热更新SDK")
    private String hotUpdateSdks;

    @ApiModelProperty(value = "应用图片")
    private String apkLogo;

    @ApiModelProperty(value = "检测耗时")
    private String apkDetectionTime;

    @ApiModelProperty(value = "检测开始时间")
    private String apkDetectionStarttime;

    @ApiModelProperty(value = "源文件名称")
    private String sourceFileName;

    @ApiModelProperty(value = "SHA-1值")
    @JSONField(alternateNames = "sha_1")
    private String sha1;

    @ApiModelProperty(value = "SHA-256值")
    @JSONField(alternateNames = "sha_256")
    private String sha256;

    @ApiModelProperty(value = "设备型号")
    private String model;

    @ApiModelProperty(value = "设备版本")
    private String version;

    @ApiModelProperty(value = "检测法规勾选")
    private String detectionLaws;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "系统版本")
    private String systemVersion;

    @ApiModelProperty(value = "小程序服务类目")
    private String serviceCategory;

    @ApiModelProperty(value = "小程序账号主体")
    private String accountSubject;

    @ApiModelProperty(value = "小程序appId")
    private String appId;

    @ApiModelProperty(value = "小程序原始id")
    private String originalId;

    @ApiModelProperty(value = "小程序服务隐私及数据提示")
    private String privacyData;

    @ApiModelProperty(value = "小程序服务声明")
    private String statement;

    @ApiModelProperty(value = "小程序更新时间")
    private String updateTime;

    @ApiModelProperty(value = "小程序引用插件")
    private String plugins;

    @ApiModelProperty(value = "授权服务商")
    private String serviceProvider;
}
