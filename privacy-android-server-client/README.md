# Privacy Android Server Client

## 概述

`privacy-android-server-client` 是一个独立的工具包，提供了调用 privacy-android-server 服务的 Feign 客户端接口。

## 功能特性

- 提供 Feign 客户端接口定义
- 包含完整的请求参数和响应 DTO
- 支持微服务间调用
- 可独立发布和引用

## 使用方式

### 1. 作为依赖引入

在其他微服务项目的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>cn.ijiami.detection</groupId>
    <artifactId>privacy-android-server-client</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

### 2. 启用 Feign 客户端

#### 方式一：使用配置类（推荐）

在 Spring Boot 启动类上导入配置：

```java
@Import(PrivacyAndroidServerClientConfig.class)
@SpringBootApplication
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

#### 方式二：直接启用 Feign

在 Spring Boot 启动类上添加注解：

```java
@EnableFeignClients(basePackages = "cn.ijiami.detection.android.client.api")
@SpringBootApplication
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

### 3. 配置服务名称和路径

在 `application.yml` 或 `application.properties` 中配置：

```yaml
# 服务名称配置
ijiami-cloud-privacy-android-server-name: privacy-android-server

# 服务路径配置（可选，默认为 /privacy-detection）
ijiami-cloud-privacy-android-server: /privacy-detection
```

### 4. 使用客户端接口

```java
@Service
public class YourService {
    
    @Autowired
    private DetectionTaskServiceApi detectionTaskServiceApi;
    
    @Autowired
    private StatisticsServiceApi statisticsServiceApi;
    
    public void startDetectionTask(StartTaskParam param) {
        detectionTaskServiceApi.startTask(param);
    }
    
    public Boolean stopDetectionTask(Long taskId) {
        return detectionTaskServiceApi.stopTask(taskId);
    }
    
    public AssetsStatisticalSummaryDTO getStatistics(AssetsStatisticsParam param) {
        return statisticsServiceApi.assetsStatisticalSummary(param);
    }
}
```

## API 接口说明

### DetectionTaskServiceApi

- `startTask(StartTaskParam param)`: 启动检测任务
- `stopTask(Long taskId)`: 停止检测任务

### StatisticsServiceApi

- `assetsStatisticalSummary(AssetsStatisticsParam param)`: 获取资产统计摘要
- `assetsDetailsByPage(AssetsDetailsParam param)`: 分页获取资产详情

## 发布说明

### 发布到私服

#### 方式一：使用部署脚本（推荐）

**Linux/Mac:**
```bash
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

#### 方式二：手动发布

```bash
# 编译并发布到私服
mvn clean deploy

# 仅发布（跳过测试）
mvn clean deploy -DskipTests
```

### 版本管理

- 开发版本：`1.0-SNAPSHOT`
- 正式版本：`1.0.0`

## 注意事项

1. 确保目标服务 `privacy-android-server` 已启动并注册到服务注册中心
2. 配置正确的服务名称和路径
3. 确保网络连通性和权限配置正确
4. 建议在生产环境使用正式版本而非 SNAPSHOT 版本

## 依赖说明

本工具包依赖以下主要组件：

- Spring Cloud OpenFeign
- Swagger Annotations
- Spring Boot Validation
- Jackson Annotations
- ijiami-framework-common
- ijiami-framework-mybatis 