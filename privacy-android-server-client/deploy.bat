@echo off
chcp 65001 >nul

REM Privacy Android Server Client 部署脚本
REM 用于将客户端包发布到私服

echo 开始部署 privacy-android-server-client...

REM 检查是否在正确的目录
if not exist "pom.xml" (
    echo 错误：请在 privacy-android-server-client 目录下执行此脚本
    pause
    exit /b 1
)

REM 检查 pom.xml 中的 artifactId
findstr /c:"privacy-android-server-client" pom.xml >nul
if errorlevel 1 (
    echo 错误：当前目录不是 privacy-android-server-client 项目
    pause
    exit /b 1
)

REM 清理并编译
echo 正在清理和编译...
call mvn clean compile
if errorlevel 1 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

REM 运行测试（如果有的话）
echo 正在运行测试...
call mvn test
if errorlevel 1 (
    echo 测试失败，请检查代码
    pause
    exit /b 1
)

REM 打包
echo 正在打包...
call mvn package -DskipTests
if errorlevel 1 (
    echo 打包失败
    pause
    exit /b 1
)

REM 部署到私服
echo 正在部署到私服...
call mvn deploy -DskipTests
if errorlevel 0 (
    echo 部署成功！
    echo 其他微服务现在可以通过以下依赖引用：
    echo.
    echo ^<dependency^>
    echo     ^<groupId^>cn.ijiami.detection^</groupId^>
    echo     ^<artifactId^>privacy-android-server-client^</artifactId^>
    echo     ^<version^>1.0-SNAPSHOT^</version^>
    echo ^</dependency^>
    echo.
) else (
    echo 部署失败，请检查网络连接和私服配置
    pause
    exit /b 1
)

pause 