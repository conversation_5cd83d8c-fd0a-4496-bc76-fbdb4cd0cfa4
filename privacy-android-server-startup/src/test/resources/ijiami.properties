##default path
ijiami.base.default.path=E:/zywa/ijiami/

##default path
ijiami.framework.default.path=E:/zywa/ijiami/
ijiami.document.path=E:/zywa/ijiami/document
##\u6388\u6743\u670D\u52A1IP
ijiami.oauth.server.ip=localhost

##\u6388\u6743\u670D\u52A1\u7AEF\u53E3
ijiami.oauth.server.port=8080

##\u6388\u6743\u670D\u52A1\u4E0A\u4E0B\u6587
ijiami.oauth.server.contextPath=/detection

#verifyCode
ijiami.verifyCode.isVerify=false

ijiami.framework.isMultiClient=false

ijiami.tools.path=E:/zywa/tools

#default path
ijiami.framework.file.path=E:/zywa/ijiami/files
ijiami.report.root.path=E:/zywa/ijiami/report/
detection.tools.dynamic_path=E:/zywa/ijiami/dynamic_file/

ijiami.apk.download.path=https://************/apk
ijiami.dynamic.data.download.path=https://************/dynamic/

#ipa\u5305\u8131\u58F3\u76F8\u5173\u914D\u7F6E
ijiami.shell.tool.path=E:/zywa/ijiami/ios/shell/shellTools.out
ijiami.shell.server.task=/task
ijiami.shell.server.stop=/taskStop
ijiami.shell.server.callback.path=https://************/detection/common/shell/callback
ijiami.shell.server.num=1
ijiami.shell.server.fail.second=360

#xxl-job\u76F8\u5173\u670D\u52A1\u914D\u7F6E
xxl.job.url=http://************:8888/xxl-job-admin
xxl.detection.url=http://************:8081/xxl-detection
xxl.detection.engine.version=v1.2
xxl.job.android.handler=3
xxl.job.android.dynamic.handler=7
xxl.job.ios.handler=2

ijiami.mmdb.path=E:/zywa/ijiami/mmdb/GeoLite2-City.mmdb
#\u6B64\u9879\u914D\u7F6E\u53EA\u9488\u5BF9Windows\u670D\u52A1\u5668\uFF0CLinux\u4E0D\u9700\u8981\u914D\u7F6E
ijiami.wkhtmltopdf.path=

ijiami.report.page.url=http://************:8081/jareport/words

#\u68C0\u6D4B\u5B8C\u6210\u662F\u5426\u5C06MD5\u503C\u63A8\u9001\u7ED9\u5927\u6570\u636E\u4E2D\u5FC3
ijiami.bigdata.callback.switch=false
ijiami.bigdata.callback.url=http://***********:8001/assets-handler/assetshandler/privacyCallback

#\u68C0\u6D4B\u5B8C\u6210\u662F\u5426\u56DE\u8C03\u91CD\u5E86\u79FB\u52A8\u63A5\u53E3
ijiami.chongqin.callback.switch=false
ijiami.chongqin.callback.url=http://weibingtie.xicp.net/msbdcp-chongqing-server/common/monitor/app/receive_privacy_data

#spring task config
ijiami.stask.file.baseDirName=E:/apk/APK
ijiami.stask.cron=0 0 0 1/1 * ?
ijiami.stask.searchSize=30

# email address config (split ",")
ijiami.email.addr.config=<EMAIL>,<EMAIL>,<EMAIL>
ijiami.email.isOpen=true

# task thread num
task.thread.num=6

# login error num
ijiami.manager.login.error.limit=5
# disable time
ijiami.manager.login.error.minutes=10
#authorize config
ijiami.manager.authorization=false
ijiami.manager.role.id.cantEmpty=zh_CN


# \u7B2C\u4E09\u65B9\u767B\u5F55\u76F8\u5173\u914D\u7F6E
ijiami.server.remoteHost=************
ijiami.server.tokenHost=https://************
ijiami.server.redirectHost=https://************

#\u52A8\u6001\u4EFB\u52A1\u8D85\u65F6\u5173\u95ED
task.service.cron=0/59 * * * * ?
#\u52A8\u6001\u4EFB\u52A1\u8D85\u65F6\u5173\u95ED24\u5C0F\u65F6
task.service.cron1=0/59 * * * * ?
#\u7248\u672C\u53F7
detection_version=2.3

# \u9632\u76D7\u94FE\u767D\u540D\u5355\u914D\u7F6E
ijiami.custom.referer.filter=false
ijiami.custom.referer.white.host=************
ijiami.custom.referer.white.api=/threeparty/auth/auth,/#/login,/oauth/token,/bigdata/listDocumentId,/bigdata/getDetectedResult,/common/shell/callback

#\u9ED8\u8BA4\u5BC6\u7801
ijiami.manager.default.password=ijiami123456
## \u6587\u4EF6\u4E0A\u4F20\u9650\u5236 \u591A\u4E2A\u7528\u9017\u53F7\u5206\u9694\uFF0C\u914D\u7F6E\u7ED3\u5C3E\u4E5F\u9700\u8981\u6709\u9017\u53F7
ijiami.framework.uploadFile.filterSuffix=.asp,.php,.jsp,.asa
## \u624B\u673A\u53F7\u9A8C\u8BC1
ijiami.framework.mobilephone.regexp=^$|^(13[0-9]|14[5|7]|15[0|1|2|3|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\\d{8}$
## \u5BC6\u7801\u89C4\u5219
ijiami.framework.password.regexp=^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\\w\\s]).{8,32}$
## \u7528\u6237\u540D\u79F0\u89C4\u5219
ijiami.framework.userName.regexp=^[a-zA-Z0-9]{4,20}$
#
ijiami.manager.accessTokenTimeOut=1800
#
ijiami.manager.refreshTokenTimeOut=3600
## \u5F00\u542F\u63A5\u53E3\u7B7E\u540D
ijiami.framework.sign.auth=false
## \u4E0D\u9700\u8981\u7B7E\u540D\u7684\u63A5\u53E3
ijiami.framework.sign.filter.urlPattern=/error,/oauth/token,/api/enc/base/download,/common/verifyCode/generateImage,/WebSocket,/common/verifyCode/generateKey/,/api/file/viewFile,/api/client,/api/privacy/download/auto/report,/api/privacy/download/result,/api/privacy/downloadReport,/common/shell/callback,api/assets/urlUpload,

# stf #
ijiami.stf.url=https://************:7100
ijiami.stf.token=40fce61634204bdca6dd0234cb6f9e06583f59c0bade4cf99aa0bd0823a7f799
ijiami.stf.console=https://************:8083

# \u8FDC\u7A0B\u5DE5\u5177\u914D\u7F6E #
# \u672C\u5730\u5DE5\u5177\u8FD8\u662F\u8FDC\u7A0B\u5DE5\u5177
ijiami.remote.tool.status=true
# \u8FDC\u7A0B\u5DE5\u5177socket\u5730\u5740
ijiami.remote.tool.url=wss://172.10.3.189
# idbçç±»åï¼0 åæº 1 å¤æº
ijiami.remote.tool.category=0
# éæå½æ°åææå¡å°å
ijiami.codeScanner.server=http://************:8083/ocp-code-profiling-server
# éæå½æ°åæç»æåè°å°å
ijiami.staticFunctionAnalyse.callback=https://ijmuat.zywa.com.cn/callback/staticFunctionAnalysis
# è±å£³åä¸è½½urlåç¼ï¼ä¸è¬å¡«æ¬æºå°å
ijiami.download.dump.prefix=http://localhost:8082/detection/api/client/downloadDump/
# appåä¸è½½urlåç¼ï¼ä¸è¬å¡«æ¬æºå°å
ijiami.download.app.prefix=http://localhost:8082/detection/api/client/downloadApp/
# iosäºææºéç½®
ijiami.ios.remote.tool.url=ws://*************:9037
ijiami.ios.remote.tool.status=true
ijiami.ios.remote.tool.uploadUrl=https://ijmuat.zywa.com.cn/detection
# iosäºææºæ°é
ijiami.ios.cloud.phone.num=1
# iosæ«ç æ£æµçéç½®
ijiami.ios.connection.qrcode.frps.ip=************
ijiami.ios.connection.qrcode.frps.port=7000
ijiami.ios.connection.qrcode.frpc.url=https://uatiosfrp.zywa.com.cn
ijiami.ios.connection.qrcode.frpc.port.range=9000-9200
ijiami.ios.connection.callback=https://************/detection/thirdParty/callback/iosDevice
ijiami.ios.connection.qrcode.expire=300000
# ocrè¯·æ±å°å
ijiami.ocr.url=http://127.0.0.1:9527/ocr?file=
point_10107_agree=åæ|æ¿æ|åè®¸|å¥½ç|æ¥å|å¯ä»¥
point_10107_refuse=ä¸åæ|æä¸åæ|æä¸ä½¿ç¨|éåº|åæ¶|ä»æµè§|æç»|æ¾å¼ä½¿ç¨|ä¸ä½¿ç¨
# äºææºåç½®éè®¯å½
ijiami.cloud.phone.addressBook={"\u5f20\u4e09":"18292129631","\u674e\u56db":"13290123308","\u738b\u4e94":"18029245533","\u5c0f\u4e59":"14919338026","\u5c0f\u8f9b":"15322964951"}
# éç§ææ¬è¯­ä¹è¯å«æå¡å¨å°å
policy.identify.url=http://***********:30003/policy
# éç§ææ¬çæå°ææ¬å¤§å°
ijiami.privacyPolicy.mimTextSize=8
# å½åæºå¨æ¯å¦ååä»»å¡
ijiami.detection.taskSort=false
# iosçsdkåºè·¯å¾
ijiami.sdk.library.path=E:/zywa/ijiami/ios/tools/sdk_library.db
# idçæå¨éªè±ç®æ³çæºå¨id
ijiami.snowflake.worker.id=1

#å¤§æ°æ®å¹³å°
ijiami.bigdata.api_url=http://************:30502/armp
ijiami.bigdata.username=PRIVACY
ijiami.bigdata.password=privacy123
ijiami.bigdata.token_name=privacyToken

# Androidæ°æ®ååprivacyUrlç®å½æ°æ®æ¯å¦éç§æ¿ç­çå¹éè§å
ijiami.privacy.url.regexp=policies|privacy|policy|terms|sdk|agreement|service|rule
# Androidæ°æ®ååprivacyUrlåprivacyHtmlç®å½æ°æ®æ¯å¦éç§æ¿ç­ææ¬çå¹éè§å
ijiami.privacy.html.regexp=\u005c\u0053\u002b\u0028\u9690\u79c1\u653f\u7b56\u007c\u9690\u79c1\u6743\u653f\u7b56\u007c\u9690\u79c1\u534f\u8bae\u007c\u9690\u79c1\u6761\u6b3e\u007c\u7528\u6237\u6761\u6b3e\u007c\u7528\u6237\u534f\u8bae\u007c\u670d\u52a1\u534f\u8bae\u007c\u6743\u9650\u0029\u007c\u0028\u0073\u0064\u006b\u005b\u005c\u0073\u005c\u0053\u005d\u002a\u0029\u007b\u0033\u002c\u007d
# ipæ¨æµæå¡å°å
ijiami.networkCheck.server=http://***********:8084/ocp-network-server
# ipæ¨æµåçç»æåè°å°åï¼ä¸è¬å¡«æ¬æº
ijiami.networkCheck.callback=http://localhost:8082/detection/thirdParty/callback/ipAddressCheck
# ç¨æ·androidäºææºä½¿ç¨ä¸ªæ°éå¶
ijiami.detection.userAndroidDevicesLimit=2
# ç¨æ·iosäºææºä½¿ç¨ä¸ªæ°éå¶
ijiami.detection.userIosDevicesLimit=2
# åä¸ªæä»¶ä¸ä¼ å¤§å°éå¶ï¼åä½M
ijiami.upload.maximumFileSize=1000
# åçæä»¶å¤§å°ï¼åä½M
ijiami.upload.chunkFileSize=50
# æ¹éä¸ä¼ appä¸ªæ°éå¶
ijiami.upload.multipleUploadLimit=10
# ocræå¡å°å
ijiami.ocr.server=http://***********:30006/ocp-tomatoesocr-server
ijiami.tensorflow.checkboxModelPath=E:/zywa/ijiami/ios/tools/tensorflow/save/checkbox
ijiami.tensorflow.checkedModelPath=E:/zywa/ijiami/ios/tools/tensorflow/save/checked
#sdkæ´æ°æ°æ®æº
ijiami.sdk.server=http://************:38083/odp-manager-server
detection.result.url.prefix=https://abd.ijiami.cn:4442/
ijiami.framework.rootPath=E:/zywa/ijiami/ios/masm