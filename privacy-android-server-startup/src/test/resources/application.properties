##application name
application.name=privacy-detection
#server.contextPath=/detection
server.servlet.contextPath=/detection
server.port=8080
##spring database configuration
#spring.datasource.url=************************************************************************************************************************************************************************
#spring.datasource.username=privacy_dev_user
#spring.datasource.password=privacy_dev_m4NDSkaY3xpK
spring.datasource.url=**************************************************************************************************************************************************************************************
spring.datasource.username=privacy_test_user
spring.datasource.password=privacy_testm4NDSkaY3xpK
#spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
## \u76D1\u63A7druid\u7684\u8FDE\u63A5\u6CC4\u6F0F\uFF0C\u53EA\u5728\u6D4B\u8BD5\u73AF\u5883\u4E2D\u4F7F\u7528
spring.datasource.druid.removeAbandoned=true
spring.datasource.druid.removeAbandonedTimeout=1800
spring.datasource.druid.logAbandoned=true
spring.datasource.druid.filters=stat,wall,slf4j
spring.datasource.druid.filter.slf4j.enabled=true
spring.datasource.druid.useGlobalDataSourceStat=true
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false

# Druid\u76D1\u63A7\u63A7\u5236\u53F0\u914D\u7F6E \u5F00\u59CB\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u53EA\u5728\u6D4B\u8BD5\u73AF\u5883\u4E2D\u4F7F\u7528
# \u662F\u5426\u542F\u7528StatViewServlet\u9ED8\u8BA4\u503Cfalse
spring.datasource.druid.stat-view-servlet.enabled=true
# \u8BF7\u6C42\u8DEF\u5F84
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.reset-enable=false
# \u8BBE\u7F6E\u767B\u5F55\u7528\u6237
spring.datasource.druid.stat-view-servlet.login-username=detection
# \u8BBE\u7F6E\u767B\u5F55\u5BC6\u7801
spring.datasource.druid.stat-view-servlet.login-password=ijiamitest
# StatViewServlet\u914D\u7F6E \u7ED3\u675F


spring.redis.enabled=true
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=500
spring.redis.lettuce.pool.max-active=2000
spring.redis.lettuce.pool.max-wait=10000
spring.redis.timeout=5000

spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=master@password
spring.cache.type=REDIS


spring.mvc.pathmatch.matching-strategy=ant_path_matcher
ijiami.framework.swagger.enabled=true
ijiami.framework.swagger.dockets.detection.groupName=detection
ijiami.framework.swagger.dockets.detection.title=°®¼ÓÃÜÒÆ¶¯Ó¦ÓÃ¸öÈËÐÅÏ¢°²È«¼ì²âÏµÍ³½Ó¿ÚÎÄµµ
ijiami.framework.swagger.dockets.detection.description=°®¼ÓÃÜÒÆ¶¯Ó¦ÓÃ¸öÈËÐÅÏ¢°²È«¼ì²âÏµÍ³½Ó¿ÚÎÄµµ
ijiami.framework.swagger.dockets.detection.version=3.0
#swaagerÉ¨ÃèµÄ°üÃû  xx;xx;
ijiami.framework.swagger.dockets.detection.basePackage=cn.ijiami
ijiami.framework.swagger.dockets.detection.contactInfo.name=¸ü¶à°®¼ÓÃÜÏà¹ØÎÄÕÂÇë¹Ø×¢£ºhttp://blog.ijiami.com/
ijiami.framework.swagger.dockets.detection.contactInfo.url=http://127.0.0.1:8080/detection/doc.html
ijiami.framework.swagger.dockets.detection.contactInfo.email=<EMAIL>


##use frame
security.headers.frame=true
##time format
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

spring.thymeleaf.cache=false

##mybatis configuration

# \u90AE\u4EF6\u57FA\u672C\u914D\u7F6E
spring.mail.host=smtphz.qiye.163.com
spring.mail.port=994
spring.mail.username=<EMAIL>
spring.mail.password=Jub7jsP2sKdPFQeq
spring.mail.protocol=smtp
spring.mail.default-encoding=utf-8
# \u5982\u679C\u662F\u4F01\u4E1A\u90AE\u7BB1\u9700\u8981\u6DFB\u52A0\u4EE5\u4E0B\u914D\u7F6E
spring.mail.properties.mail.smtp.ssl.enable=true
spring.mail.properties.mail.imap.ssl.socketFactory.fallback=false
spring.mail.properties.mail.smtp.ssl.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

spring.http.multipart.maxFileSize=1000Mb
spring.http.multipart.maxRequestSize=1000Mb

mybatis.type-handlers-package=cn.ijiami.framework.mybatis,cn.ijiami.manager.handler,cn.ijiami.detection.handler,cn.ijiami.detection
mybatis.mapper-locations=classpath*:cn/ijiami/**/mapper/*.xml

mybatis-plus.type-handlers-package=cn.ijiami.framework.mybatis,cn.ijiami.manager.handler,cn.ijiami.detection.handler,cn.ijiami.detection
mybatis-plus.mapper-locations=classpath*:cn/ijiami/**/mapper/*.xml

#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mapper.enum-as-simple-type=true

##jsp View configuration
spring.mvc.view.prefix=/WEB-INF/views/
spring.mvc.view.suffix=.jsp
## ijiami-data springCloud
feign.hystrix.enabled = false
#i18n
spring.messages.basename=i18n/messages
#\u670D\u52A1\u540D
spring.application.name=privacy-detection-web-cloud-server
# \u57FA\u7840\u5E73\u53F01.4 \u7248\u672C\u6CE8\u518C\u4E2D\u5FC3
#eureka.client.service-url.defaultZone=http://************:8761/ijiami-cloud-register/eureka/
eureka.client.service-url.defaultZone=http://***********:30005/ijiami-cloud-register/eureka/
#\u57FA\u7840\u5E73\u53F0 1.3.2 \u6CE8\u518C\u4E2D\u5FC3\u914D\u7F6E
#\u6CE8\u518C\u4E2D\u5FC3ip
#ijiami.server.register.host=************
#\u6CE8\u518C\u4E2D\u5FC3\u7AEF\u53E3
#ijiami.server.register.port=8761
#\u6CE8\u518C\u4E2D\u5FC3\u4E0A\u4E0B\u6587
#ijiami.server.register.context=/ijiami-cloud-register
#\u4FDD\u8BC1\u540C\u4E00\u4E2Atomcat\u90E8\u7F72\u591A\u4E2A\u9879\u76EE\u65F6\u4E0D\u4F1A\u51B2\u7A81
spring.jmx.default-domain=detection-web
ijiami-cloud-privacy-detection-server-name=privacy-detection-tools-cloud-server
ijiami-cloud-privacy-detection-server-context=/privacy-detection
ijiami-cloud-message-server-name=message-cloud-server
ijiami-cloud-message-server-context=/ijiami-message
#mongodb config
#spring.data.mongodb.uri=mongodb://************:37017/privacy-detection
spring.data.mongodb.uri=*********************************************************************************************
#spring.data.mongodb.uri=*******************************************************************************************
#spring.data.mongodb.uri=mongodb://************:37017/privacy-detection-test2
#fastDFS config
connect_timeout=60
network_timeout=1200
charset=UTF-8
http.tracker_http_port=80
http.anti_steal_token=no
http.secret_key=FastDFS1234567890
tracker_server=***********:22122
fastDFS.ip=http://***********
#\u00A0\u9759\u6001\u51FD\u6570\u5206\u6790apk\u4E0B\u8F7D\u7684\u5730\u5740
fastDFS.intranet.ip = http://***********
ip.callbackurl=http://************/detection/common/shell/callback

ribbon.ReadTimeout= 120000


spring.kafka.bootstrap-servers=************:9092
spring.kafka.producer.retries=0
spring.kafka.producer.batch-size=16384
spring.kafka.producer.buffer-memory=33554432
spring.kafka.consumer.group-id=test-consumer-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.auto-commit-interval=5000
spring.kafka.consumer.max-poll-records=5
spring.kafka.listener.ack-mode=MANUAL_IMMEDIATE
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

spring.main.allow-circular-references=true
spring.main.allow-bean-definition-overriding=true

# \u63A7\u5236\u53F0\u7AEF\u53E3\u53F7 15672
#spring.rabbitmq.host=************
#spring.rabbitmq.port=5672
#spring.rabbitmq.username=detection_dev
#spring.rabbitmq.password=detection_dev
#spring.rabbitmq.virtual-host=detection_dev
# zipkin #
ijiami.cloud.trace.enabled=false
ijiami.logCrtl.config.path=/zywa/logcrtl
ijiami.framework.sign.suffix.pattern=.js,html,.htm,.vue,.css,.png,.jpg,.jpeg,.gif,.jsp,.zip,.rar,.so,.word,.excel,.pdf,.aar,.jar,.apk,.ipa,
ijiami.manager.subject=\u7231\u52A0\u5BC6\u79FB\u52A8\u5E94\u7528\u4E2A\u4EBA\u4FE1\u606F\u5B89\u5168\u68C0\u6D4B\u5E73\u53F0\u2014\u2014\u91CD\u7F6E\u5BC6\u7801
# \u6807\u8BB0\u6D88\u606F\u5904\u7406\u65B9\u5F0F
message.server.switch=redis
ijiami.detection.quartz=false
#\u5F00\u542F\u591A\u7AEF\u767B\u5F55,\u9ED8\u8BA4\u662Ffalse
loginMulti.enable=true
#ÔÊÐí¿çÓò
#ijiami.header.Access-Control-Allow-Origin=http://localhost:8989
ijiami.header.map.Access-Control-Allow-Origin=*

spring.jackson.mapper.USE_ANNOTATIONS=false
spring.jackson.deserialization.FAIL_ON_UNKNOWN_PROPERTIES=false
project.ignoreNullWsContainer = true

