package cn.ijiami.detection.web.application;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import tk.mybatis.spring.annotation.MapperScan;

/**
 * SpringBoot 启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
@ComponentScan(basePackages = {"cn.ijiami.*"})
@MapperScan("cn.ijiami.**.mapper")
@ServletComponentScan("cn.ijiami.*")
@EnableFeignClients(basePackages = {"cn.ijiami.base", "cn.ijiami.detection", "cn.ijiami"})
@EnableDiscoveryClient
@EnableScheduling
@EnableAsync
public class DetectionWebApplication extends SpringBootServletInitializer {
    private static final Logger LOG = LoggerFactory.getLogger(DetectionWebApplication.class);
    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        LOG.info("---------------爱加密移动应用安全检测Android检测服务 启动中----------------");
        super.onStartup(servletContext);
    }

    public static void main(String[] args) {
        SpringApplication.run(DetectionWebApplication.class, args);
        LOG.info("---------------爱加密移动应用安全检测Android检测服务 启动成功----------------");
    }
}
