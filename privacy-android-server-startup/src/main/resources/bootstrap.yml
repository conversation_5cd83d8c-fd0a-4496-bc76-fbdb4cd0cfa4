spring:
  application:
      name: privacy-android-server-client
  profiles:
    active: dev
  cloud:
      nacos:
        config:
            server-addr: 172.10.3.44:8848
            namespace: 62bc2379-67b0-4055-8291-4db0fa7a9432
            username: privacy_dev2
            password: privacy_dev2
            context-path: /nacos
            file-extension: yaml # 指定配置文件扩展名
            extension-configs:
              - data-id: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
                group: DEFAULT_GROUP # 默认分组
                refresh: true
        discovery:
            server-addr: 172.10.3.44:8848
            namespace: 62bc2379-67b0-4055-8291-4db0fa7a9432
            username: nacos
            password: HaoQinfeng123
            context-path: /nacos
  config:
    import: nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

management:
  endpoints:
    web:
      exposure:
        include: '*' # 暴露所有 Actuator 端点，便于服务发现 