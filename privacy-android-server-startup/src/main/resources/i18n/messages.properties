ijiami.manager.common.notSupport=ä¸æ¯æçæä½
ijiami.manager.resource.NotNull=èµæºä¿¡æ¯ä¸è½ä¸ºç©º
ijiami.manager.user.nameHasExist=ç¨æ·åå·²å­å¨
ijiami.manager.user.notExist=ç¨æ·ä¸å­å¨
ijiami.manager.user.id.notExist=ç¨æ·idä¸å­å¨
ijiami.manager.user.input.id=è¯·è¾å¥ç¨æ·id
ijiami.manager.user.input.oldPassword=è¯·è¾å¥åå§å¯ç 
ijiami.manager.user.input.newPassword=è¯·è¾å¥æ°å¯ç 
ijiami.manager.user.input.confirmationPassword=è¯·è¾å¥ç¡®è®¤å¯ç 
ijiami.manager.user.input.notMatchconfirmPassword=æ°å¯ç ä¸ç¡®è®¤å¯ç ä¸ä¸è´
ijiami.manager.user.input.notMatchPassword=æ°å¯ç ä¸æ§å¯ç ä¸è½ä¸æ ·
ijiami.manager.password.auth.failed=åå§å¯ç æ ¡éªå¤±è´¥
ijiami.manager.role.id.cantEmpty=è§è²idä¸è½ä¸ºç©º




AbstractAccessDecisionManager.accessDenied=Access is denied
AbstractLdapAuthenticationProvider.emptyPassword=Empty Password
AbstractSecurityInterceptor.authenticationNotFound=An Authentication object was not found in the SecurityContext
AbstractUserDetailsAuthenticationProvider.badCredentials=Bad credentials
AbstractUserDetailsAuthenticationProvider.credentialsExpired=User credentials have expired
AbstractUserDetailsAuthenticationProvider.disabled=User is disabled
AbstractUserDetailsAuthenticationProvider.expired=User account has expired
AbstractUserDetailsAuthenticationProvider.locked=User account is locked
AbstractUserDetailsAuthenticationProvider.onlySupports=Only UsernamePasswordAuthenticationToken is supported
AccountStatusUserDetailsChecker.credentialsExpired=User credentials have expired
AccountStatusUserDetailsChecker.disabled=User is disabled
AccountStatusUserDetailsChecker.expired=User account has expired
AccountStatusUserDetailsChecker.locked=User account is locked
AclEntryAfterInvocationProvider.noPermission=Authentication {0} has NO permissions to the domain object {1}
AnonymousAuthenticationProvider.incorrectKey=The presented AnonymousAuthenticationToken does not contain the expected key
BindAuthenticator.badCredentials=Bad credentials
BindAuthenticator.emptyPassword=Empty Password
CasAuthenticationProvider.incorrectKey=The presented CasAuthenticationToken does not contain the expected key
CasAuthenticationProvider.noServiceTicket=Failed to provide a CAS service ticket to validate
ConcurrentSessionControlAuthenticationStrategy.exceededAllowed=Maximum sessions of {0} for this principal exceeded
DigestAuthenticationFilter.incorrectRealm=Response realm name {0} does not match system realm name of {1}
DigestAuthenticationFilter.incorrectResponse=Incorrect response
DigestAuthenticationFilter.missingAuth=Missing mandatory digest value for 'auth' QOP; received header {0}
DigestAuthenticationFilter.missingMandatory=Missing mandatory digest value; received header {0}
DigestAuthenticationFilter.nonceCompromised=Nonce token compromised {0}
DigestAuthenticationFilter.nonceEncoding=Nonce is not encoded in Base64; received nonce {0}
DigestAuthenticationFilter.nonceExpired=Nonce has expired/timed out
DigestAuthenticationFilter.nonceNotNumeric=Nonce token should have yielded a numeric first token; but was {0}
DigestAuthenticationFilter.nonceNotTwoTokens=Nonce should have yielded two tokens but was {0}
DigestAuthenticationFilter.usernameNotFound=Username {0} not found
JdbcDaoImpl.noAuthority=User {0} has no GrantedAuthority
JdbcDaoImpl.notFound=User {0} not found
LdapAuthenticationProvider.badCredentials=Bad credentials
LdapAuthenticationProvider.credentialsExpired=User credentials have expired
LdapAuthenticationProvider.disabled=User is disabled
LdapAuthenticationProvider.expired=User account has expired
LdapAuthenticationProvider.locked=User account is locked
LdapAuthenticationProvider.emptyUsername=Empty username not allowed
LdapAuthenticationProvider.onlySupports=Only UsernamePasswordAuthenticationToken is supported
PasswordComparisonAuthenticator.badCredentials=Bad credentials
PersistentTokenBasedRememberMeServices.cookieStolen=Invalid remember-me token (Series/token) mismatch. Implies previous cookie theft attack.
ProviderManager.providerNotFound=No AuthenticationProvider found for {0}
RememberMeAuthenticationProvider.incorrectKey=The presented RememberMeAuthenticationToken does not contain the expected key
RunAsImplAuthenticationProvider.incorrectKey=The presented RunAsUserToken does not contain the expected key
SubjectDnX509PrincipalExtractor.noMatching=No matching pattern was found in subjectDN: {0}
SwitchUserFilter.noCurrentUser=No current user associated with this request
SwitchUserFilter.noOriginalAuthentication=Could not find original Authentication object
