ijiami.manager.common.notSupport=ä¸æ¯æçæä½
ijiami.manager.resource.NotNull=èµæºä¿¡æ¯ä¸è½ä¸ºç©º
ijiami.manager.user.nameHasExist=ç¨æ·åå·²å­å¨
ijiami.manager.user.notExist=ç¨æ·ä¸å­å¨
ijiami.manager.user.id.notExist=ç¨æ·idä¸å­å¨
ijiami.manager.user.input.id=è¯·è¾å¥ç¨æ·id
ijiami.manager.user.input.oldPassword=è¯·è¾å¥åå§å¯ç 
ijiami.manager.user.input.newPassword=è¯·è¾å¥æ°å¯ç 
ijiami.manager.user.input.confirmationPassword=è¯·è¾å¥ç¡®è®¤å¯ç 
ijiami.manager.user.input.notMatchconfirmPassword=æ°å¯ç ä¸ç¡®è®¤å¯ç ä¸ä¸è´
ijiami.manager.user.input.notMatchPassword=æ°å¯ç ä¸æ§å¯ç ä¸è½ä¸æ ·
ijiami.manager.password.auth.failed=åå§å¯ç æ ¡éªå¤±è´¥
ijiami.manager.role.id.cantEmpty=è§è²idä¸è½ä¸ºç©º
ijiami.manager.user.input.checkPwd=æ°å¯ç é¿åº¦è³å°ä¸º8ä½ï¼æå¤§ä¸º20ä½ï¼åå«æ°å­ å°åå­æ¯ å¤§åå­æ¯ ç¹æ®ç¬¦å·ä»»æä¸¤ç§åä»¥ä¸

ijiami.base.common.notSupport=ä¸æ¯æçæä½
ijiami.base.about.idNotNull=å³äºæä»¬IDä¸è½ä¸ºç©º
ijiami.base.dictionary.codeHasExist=å­å¸ç¼ç å·²å­å¨
ijiami.base.icon.keyNotNull=å¾æ Keyä¸è½ä¸ºç©º
ijiami.base.file.notexist=æä»¶ä¸å­å¨
ijiami.base.file.isexception=æä»¶å¼å¸¸



#ç»å½æ ¡éªæç¤ºè¯­
#ä¸åè®¸è®¿é®
AbstractAccessDecisionManager.accessDenied=ä¸åè®¸è®¿é®
#åçå­è¯
AbstractLdapAuthenticationProvider.emptyPassword=åçå­è¯
#æªå¨SecurityContextä¸­æ¥æ¾å°è®¤è¯å¯¹è±¡
AbstractSecurityInterceptor.authenticationNotFound=æªå¨SecurityContextä¸­æ¥æ¾å°è®¤è¯å¯¹è±¡
#è´¦å·æå¯ç éè¯¯
AbstractUserDetailsAuthenticationProvider.badCredentials=è´¦å·æå¯ç éè¯¯
#ç¨æ·å­è¯å·²è¿æ
AbstractUserDetailsAuthenticationProvider.credentialsExpired=ç¨æ·å­è¯å·²è¿æ
#è´¦å·å·²è¢«ç¦ç¨,è¯·èç³»ç®¡çå
AbstractUserDetailsAuthenticationProvider.disabled=è´¦å·å·²è¢«ç¦ç¨;è¯·èç³»ç®¡çå
#è´¦å·å·²è¿æ,è¯·èç³»ç®¡çå
AbstractUserDetailsAuthenticationProvider.expired=è´¦å·å·²è¿æ;è¯·èç³»ç®¡çå
#è´¦å·å·²è¢«éå®,è¯·10åéååå°è¯
AbstractUserDetailsAuthenticationProvider.locked=è´¦å·å·²è¢«éå®;è¯·10åéååå°è¯

AbstractUserDetailsAuthenticationProvider.onlySupports=ä»ä»æ¯æUsernamePasswordAuthenticationToken
AccountStatusUserDetailsChecker.credentialsExpired=ç¨æ·å­è¯å·²è¿æ
AccountStatusUserDetailsChecker.disabled=ç¨æ·å·²å¤±æ
AccountStatusUserDetailsChecker.expired=ç¨æ·å¸å·å·²è¿æ
AccountStatusUserDetailsChecker.locked=ç¨æ·å¸å·å·²è¢«éå®
AclEntryAfterInvocationProvider.noPermission=ç»å®çAuthenticationå¯¹è±¡({0})æ ¹æ¬æ æææ§é¢åå¯¹è±¡({1})
AnonymousAuthenticationProvider.incorrectKey=å±ç¤ºçAnonymousAuthenticationTokenä¸å«æé¢æçkey
BindAuthenticator.badCredentials=è´¦å·æå¯ç éè¯¯
BindAuthenticator.emptyPassword=åçå­è¯
CasAuthenticationProvider.incorrectKey=å±ç¤ºçCasAuthenticationTokenä¸å«æé¢æçkey
CasAuthenticationProvider.noServiceTicket=æªè½å¤æ­£ç¡®æä¾å¾éªè¯çCASæå¡ç¥¨æ ¹
ConcurrentSessionControlAuthenticationStrategy.exceededAllowed=å·²ç»è¶è¿äºå½åä¸»ä½({0})è¢«åè®¸çæå¤§ä¼è¯æ°é
DigestAuthenticationFilter.incorrectRealm=ååºç»æä¸­çRealmåå­({0})åç³»ç»æå®çRealmåå­({1})ä¸å»å
DigestAuthenticationFilter.incorrectResponse=éè¯¯çååºç»æ
DigestAuthenticationFilter.missingAuth=éæ¼äºéå¯¹'auth' QOPçãå¿é¡»ç»å®çæè¦åå¼; æ¥æ¶å°çå¤´ä¿¡æ¯ä¸º{0}
DigestAuthenticationFilter.missingMandatory=éæ¼äºå¿é¡»ç»å®çæè¦åå¼; æ¥æ¶å°çå¤´ä¿¡æ¯ä¸º{0}
DigestAuthenticationFilter.nonceCompromised=Nonceä»¤çå·²ç»å­å¨é®é¢äºï¼{0}
DigestAuthenticationFilter.nonceEncoding=Nonceæªç»è¿Base64ç¼ç ; ç¸åºçnonceåå¼ä¸º {0}
DigestAuthenticationFilter.nonceExpired=Nonceå·²ç»è¿æ/è¶æ¶
DigestAuthenticationFilter.nonceNotNumeric=Nonceä»¤ççç¬¬1é¨ååºè¯¥æ¯æ°å­ï¼ä½ç»æå´æ¯{0}
DigestAuthenticationFilter.nonceNotTwoTokens=Nonceåºè¯¥ç±ä¸¤é¨ååå¼ææï¼ä½ç»æå´æ¯{0}
DigestAuthenticationFilter.usernameNotFound=ç¨æ·å{0}æªæ¾å°
JdbcDaoImpl.noAuthority=æ²¡æä¸ºç¨æ·{0}æå®è§è²
JdbcDaoImpl.notFound=æªæ¾å°ç¨æ·{0}
LdapAuthenticationProvider.badCredentials=è´¦å·æå¯ç éè¯¯
LdapAuthenticationProvider.credentialsExpired=ç¨æ·å­è¯å·²è¿æ
LdapAuthenticationProvider.disabled=ç¨æ·å·²å¤±æ
LdapAuthenticationProvider.expired=ç¨æ·å¸å·å·²è¿æ
LdapAuthenticationProvider.locked=ç¨æ·å¸å·å·²è¢«éå®
LdapAuthenticationProvider.emptyUsername=ç¨æ·åä¸åè®¸ä¸ºç©º
LdapAuthenticationProvider.onlySupports=ä»ä»æ¯æUsernamePasswordAuthenticationToken
PasswordComparisonAuthenticator.badCredentials=è´¦å·æå¯ç éè¯¯
#PersistentTokenBasedRememberMeServices.cookieStolen=Invalid remember-me token (Series/token) mismatch. Implies previous cookie theft attack.
ProviderManager.providerNotFound=æªæ¥æ¾å°éå¯¹{0}çAuthenticationProvider
RememberMeAuthenticationProvider.incorrectKey=å±ç¤ºRememberMeAuthenticationTokenä¸å«æé¢æçkey
RunAsImplAuthenticationProvider.incorrectKey=å±ç¤ºçRunAsUserTokenä¸å«æé¢æçkey
SubjectDnX509PrincipalExtractor.noMatching=æªå¨subjectDN\: {0}ä¸­æ¾å°å¹éçæ¨¡å¼
SwitchUserFilter.noCurrentUser=ä¸å­å¨å½åç¨æ·
SwitchUserFilter.noOriginalAuthentication=ä¸è½å¤æ¥æ¾å°ååçå·²è®¤è¯å¯¹è±¡