ijiami.manager.common.notSupport=not support
ijiami.manager.resource.NotNull=resource entity can't be null
ijiami.manager.user.nameHasExist=user has exist
ijiami.manager.user.notExist=user not exist
ijiami.manager.user.id.notExist=user id not exist
ijiami.manager.user.input.id=Please input user ID
ijiami.manager.user.input.oldPassword=Please input old password
ijiami.manager.user.input.newPassword=Please input new password
ijiami.manager.user.input.confirmationPassword=Please input your confirmation password
ijiami.manager.user.input.notMatchconfirmPassword=The new password is inconsistent with the confirmation password
ijiami.manager.user.input.notMatchPassword=The new password does not match the old one
ijiami.manager.password.auth.failed=old password authentication failed
ijiami.manager.role.id.cantEmpty=role id Can't be empty


ijiami.base.common.notSupport=not support
ijiami.base.about.idNotNull=about us id not be null
ijiami.base.dictionary.codeHasExist=dictionary code has exist
ijiami.base.icon.keyNotNull=icon key not be null
ijiami.base.file.notexist=file not exist
ijiami.base.file.isexception=file is exception




AbstractAccessDecisionManager.accessDenied=Access is denied
AbstractLdapAuthenticationProvider.emptyPassword=Empty Password
AbstractSecurityInterceptor.authenticationNotFound=An Authentication object was not found in the SecurityContext
AbstractUserDetailsAuthenticationProvider.badCredentials=Bad credentials
AbstractUserDetailsAuthenticationProvider.credentialsExpired=User credentials have expired
AbstractUserDetailsAuthenticationProvider.disabled=User is disabled
AbstractUserDetailsAuthenticationProvider.expired=User account has expired
AbstractUserDetailsAuthenticationProvider.locked=User account is locked
AbstractUserDetailsAuthenticationProvider.onlySupports=Only UsernamePasswordAuthenticationToken is supported
AccountStatusUserDetailsChecker.credentialsExpired=User credentials have expired
AccountStatusUserDetailsChecker.disabled=User is disabled
AccountStatusUserDetailsChecker.expired=User account has expired
AccountStatusUserDetailsChecker.locked=User account is locked
AclEntryAfterInvocationProvider.noPermission=Authentication {0} has NO permissions to the domain object {1}
AnonymousAuthenticationProvider.incorrectKey=The presented AnonymousAuthenticationToken does not contain the expected key
BindAuthenticator.badCredentials=Bad credentials
BindAuthenticator.emptyPassword=Empty Password
CasAuthenticationProvider.incorrectKey=The presented CasAuthenticationToken does not contain the expected key
CasAuthenticationProvider.noServiceTicket=Failed to provide a CAS service ticket to validate
ConcurrentSessionControlAuthenticationStrategy.exceededAllowed=Maximum sessions of {0} for this principal exceeded
DigestAuthenticationFilter.incorrectRealm=Response realm name {0} does not match system realm name of {1}
DigestAuthenticationFilter.incorrectResponse=Incorrect response
DigestAuthenticationFilter.missingAuth=Missing mandatory digest value for 'auth' QOP; received header {0}
DigestAuthenticationFilter.missingMandatory=Missing mandatory digest value; received header {0}
DigestAuthenticationFilter.nonceCompromised=Nonce token compromised {0}
DigestAuthenticationFilter.nonceEncoding=Nonce is not encoded in Base64; received nonce {0}
DigestAuthenticationFilter.nonceExpired=Nonce has expired/timed out
DigestAuthenticationFilter.nonceNotNumeric=Nonce token should have yielded a numeric first token; but was {0}
DigestAuthenticationFilter.nonceNotTwoTokens=Nonce should have yielded two tokens but was {0}
DigestAuthenticationFilter.usernameNotFound=Username {0} not found
JdbcDaoImpl.noAuthority=User {0} has no GrantedAuthority
JdbcDaoImpl.notFound=User {0} not found
LdapAuthenticationProvider.badCredentials=Bad credentials
LdapAuthenticationProvider.credentialsExpired=User credentials have expired
LdapAuthenticationProvider.disabled=User is disabled
LdapAuthenticationProvider.expired=User account has expired
LdapAuthenticationProvider.locked=User account is locked
LdapAuthenticationProvider.emptyUsername=Empty username not allowed
LdapAuthenticationProvider.onlySupports=Only UsernamePasswordAuthenticationToken is supported
PasswordComparisonAuthenticator.badCredentials=Bad credentials
PersistentTokenBasedRememberMeServices.cookieStolen=Invalid remember-me token (Series/token) mismatch. Implies previous cookie theft attack.
ProviderManager.providerNotFound=No AuthenticationProvider found for {0}
RememberMeAuthenticationProvider.incorrectKey=The presented RememberMeAuthenticationToken does not contain the expected key
RunAsImplAuthenticationProvider.incorrectKey=The presented RunAsUserToken does not contain the expected key
SubjectDnX509PrincipalExtractor.noMatching=No matching pattern was found in subjectDN: {0}
SwitchUserFilter.noCurrentUser=No current user associated with this request
SwitchUserFilter.noOriginalAuthentication=Could not find original Authentication object




