<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.ijiami.detection</groupId>
		<artifactId>privacy-android-server</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	<groupId>cn.ijiami.detection</groupId>
	<artifactId>privacy-android-server-impl</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>privacy-android-server-impl</name>
	<description>检测接口服务实现</description>
	<dependencies>
		<!-- fastdfs -->
		<dependency>
			<groupId>org.csource</groupId>
			<artifactId>fastdfs-client-java</artifactId>
			<version>1.27-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>

		<dependency>
			<groupId>com.github.tobato</groupId>
			<artifactId>fastdfs-client</artifactId>
			<version>1.26.6</version>
		</dependency>
		
		<dependency>
		  <groupId>cn.ijiami.framework</groupId>
		  <artifactId>ijiami-framework-utils</artifactId>
		</dependency>

        <dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-redis</artifactId>
		</dependency>
		
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-utils</artifactId>
		</dependency>

		<!-- 项目模块依赖 -->
		<!-- api组件 -->
		<dependency>
			<groupId>cn.ijiami.detection</groupId>
			<artifactId>privacy-android-server-api</artifactId>
			<version>1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-data-commons</artifactId>
					<groupId>org.springframework.data</groupId>
				</exclusion>
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 报表服务组件 (微服务情况下，只需api组件) -->
		<dependency>
			<groupId>cn.ijiami.report</groupId>
			<artifactId>ijiami-report-service-api</artifactId>
			<version>${ijiami-framework.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.report</groupId>
			<artifactId>ijiami-report-service-impl</artifactId>
			<version>${ijiami-framework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ant</groupId>
			<artifactId>ant</artifactId>
			<version>1.10.5</version>
		</dependency>
		<!-- httpclient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
		</dependency>
		<dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-utils</artifactId>
		</dependency>
        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>2.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
		
		<!-- ios 检测工具依赖 linux版-->
		<dependency>
		  <groupId>cn.ijiami.msmp</groupId>
		  <artifactId>msmp-ios-detection-tool</artifactId>
		  <version>2.1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.plist</groupId>
			<artifactId>dd-plist</artifactId>
			<version>1.16</version>
		</dependency>
		<dependency>
			<groupId>cn.ijiami</groupId>
			<artifactId>z-csp-device</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!-- xml解析 -->
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		<!-- SFTP -->
		<dependency>
		    <groupId>com.jcraft</groupId>
		    <artifactId>jsch</artifactId>
		    <version>0.1.54</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.dongliu/apk-parser -->
		<dependency>
		    <groupId>net.dongliu</groupId>
		    <artifactId>apk-parser</artifactId>
		    <version>2.6.10</version>
		</dependency>
		<!--		二维码生成相关 开始-->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.3.0</version>
		</dependency>
		<!--		二维码生成相关 结束-->
		<!--		webSocket Client 相关 开始-->
		<dependency>
			<groupId>org.java-websocket</groupId>
			<artifactId>Java-WebSocket</artifactId>
			<version>1.5.2</version>
		</dependency>
		<!--		webSocket Client 结束-->
		<dependency>
		   <groupId>com.qcloud</groupId>
		   <artifactId>cos_api</artifactId>
		   <version>5.6.8</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId> 
			<artifactId>jsoup</artifactId> 
			<version>1.11.3</version>
		</dependency>
		<!--		opencc4j 相关 开始-->
		<dependency>
			<groupId>com.github.houbb</groupId>
			<artifactId>opencc4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
		<!--		opencc4j 相关 结束-->
		
		<!--		tensorflow 相关 开始-->
		<dependency>
			<groupId>org.tensorflow</groupId>
			<artifactId>tensorflow</artifactId>
			<version>1.15.0</version>
		</dependency>
		<!--		tensorflow 相关 结束-->
		<!-- PDF识别为txt内容 -->
		<dependency>
		    <groupId>com.itextpdf</groupId>
		    <artifactId>itextpdf</artifactId>
		    <version>5.5.13</version>
		 </dependency>
		 
		 <!-- v2签名信息校验 -->
		 <!-- <dependency>
			<groupId>com.bihe0832.CheckAndroidSignature</groupId>
			<artifactId>CheckAndroidSignature</artifactId>
			<version>2.0</version>
		 </dependency> -->
		 
		<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-java -->
		<!-- <dependency>
		    <groupId>org.seleniumhq.selenium</groupId>
		    <artifactId>selenium-java</artifactId>
		    <version>4.3.0</version>
		</dependency> -->
		
		<!-- ip2region IP库 -->
		<dependency>
		    <groupId>org.lionsoul</groupId>
		    <artifactId>ip2region</artifactId>
		    <version>2.6.5</version>
		</dependency>
		
		<!-- 签名v1~v4签名判定 -->
		 <dependency>
		  <groupId>com.android.apksig</groupId>
		  <artifactId>apksigner</artifactId>
		  <version>1.1</version>
		</dependency>
		
		<!-- httpclient -->
		<dependency>
		    <groupId>commons-httpclient</groupId>
		    <artifactId>commons-httpclient</artifactId>
		    <version>3.1</version>
		</dependency>
		
		<dependency>
            <groupId>cn.ijiami.framework</groupId>
            <artifactId>ijiami-framework-apk</artifactId>
       </dependency>
       
       <dependency>
            <groupId>cn.ijiami.framework</groupId>
            <artifactId>ijiami-framework-ipa</artifactId>
            <exclusions>
            	<exclusion>
            		<groupId>cn.ijiami</groupId>
  					<artifactId>IJMSecurityDetection_IOS</artifactId>
            	</exclusion>
            </exclusions>
       </dependency>
       
       <dependency>
		  <groupId>cn.ijiami.framework</groupId>
		  <artifactId>ijiami-framework-email</artifactId>
		</dependency>
		<dependency>
		  <groupId>cn.ijiami.framework</groupId>
		  <artifactId>ijiami-framework-file</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>		
		<dependency>
		    <groupId>commons-net</groupId>
		    <artifactId>commons-net</artifactId>
		    <version>3.5</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
		<dependency>
		    <groupId>com.google.code.gson</groupId>
		    <artifactId>gson</artifactId>
		    <version>2.8.9</version>
		</dependency>
		
		<dependency>
		  <groupId>com.google.guava</groupId>
		  <artifactId>guava</artifactId>
		  <version>20.0</version>
		</dependency>

		<!-- android 解压、反编译工具依賴 开始-->
		<dependency>
			<groupId>org.apktool</groupId>
			<artifactId>apktool-lib</artifactId>
			<version>2.6.0</version>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.detection</groupId>
			<artifactId>android-detection-decompiler</artifactId>
			<version>1.7-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.detection</groupId>
			<artifactId>detection-common-utils</artifactId>
			<version>1.7-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- android 解压、反编译工具依賴 结束-->
		<dependency>
            <groupId>android.content.res.APKParser</groupId>
            <artifactId>APKParser-base</artifactId>
            <version>2.0.1</version>
        </dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.3.2</version>
				<configuration>
					<configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
					<overwrite>true</overwrite>
					<verbose>true</verbose>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.mysql</groupId>
						<artifactId>mysql-connector-j</artifactId>
						<version>${mysql8.version}</version>
					</dependency>
					<dependency>
						<groupId>tk.mybatis</groupId>
						<artifactId>mapper</artifactId>
						<version>3.4.0</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>