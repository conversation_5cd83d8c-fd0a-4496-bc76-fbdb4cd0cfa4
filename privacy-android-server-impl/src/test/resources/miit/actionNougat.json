[{"actionId": 10001, "actionName": "获取设备类型", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 1, "sensitive": true}, {"actionId": 10002, "actionName": "获取设备IMEI", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 2, "sensitive": true}, {"actionId": 10003, "actionName": "获取设备IMSI", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 3, "sensitive": true}, {"actionId": 10004, "actionName": "获取SIM卡的ICCID", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 4, "sensitive": true}, {"actionId": 10005, "actionName": "获取电话号码", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 5, "sensitive": true}, {"actionId": 10006, "actionName": "监听通话状态", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 6, "sensitive": true}, {"actionId": 10007, "actionName": "自动接通电话", "actionPermission": "android.permission.CALL_PHONE", "actionPermissionAlias": "CALL_PHONE", "id": 7, "sensitive": true}, {"actionId": 10008, "actionName": "自动挂断电话", "actionPermission": "android.permission.CALL_PHONE", "actionPermissionAlias": "CALL_PHONE", "id": 8, "sensitive": true}, {"actionId": 11001, "actionName": "开始录音", "actionPermission": "android.permission.RECORD_AUDIO", "actionPermissionAlias": "RECORD_AUDIO", "id": 9, "sensitive": true}, {"actionId": 12001, "actionName": "获取定位", "actionPermission": "android.permission.ACCESS_COARSE_LOCATION", "actionPermissionAlias": "ACCESS_COARSE_LOCATION", "id": 10, "sensitive": true}, {"actionId": 12002, "actionName": "使用GPS获取定位", "actionPermission": "android.permission.ACCESS_FINE_LOCATION", "actionPermissionAlias": "ACCESS_FINE_LOCATION", "id": 11, "sensitive": true}, {"actionId": 12003, "actionName": "使用网络获取定位", "actionPermission": "android.permission.ACCESS_COARSE_LOCATION", "actionPermissionAlias": "ACCESS_COARSE_LOCATION", "id": 12, "sensitive": true}, {"actionId": 12004, "actionName": "监听定位", "actionPermission": "android.permission.ACCESS_COARSE_LOCATION", "actionPermissionAlias": "ACCESS_COARSE_LOCATION", "id": 13, "sensitive": true}, {"actionId": 12005, "actionName": "监听GPS定位", "actionPermission": "android.permission.ACCESS_FINE_LOCATION", "actionPermissionAlias": "ACCESS_FINE_LOCATION", "id": 14, "sensitive": true}, {"actionId": 12006, "actionName": "监听网络定位", "actionPermission": "android.permission.ACCESS_COARSE_LOCATION", "actionPermissionAlias": "ACCESS_COARSE_LOCATION", "id": 15, "sensitive": true}, {"actionId": 13001, "actionName": "发送短信", "actionPermission": "android.permission.SEND_SMS", "actionPermissionAlias": "SEND_SMS", "id": 16, "sensitive": true}, {"actionId": 13002, "actionName": "发送长短信", "actionPermission": "android.permission.SEND_SMS", "actionPermissionAlias": "SEND_SMS", "id": 17, "sensitive": true}, {"actionId": 13003, "actionName": "发送数据短信", "actionPermission": "android.permission.SEND_SMS", "actionPermissionAlias": "SEND_SMS", "id": 18, "sensitive": true}, {"actionId": 13004, "actionName": "发送彩信", "actionPermission": "android.permission.SEND_SMS", "actionPermissionAlias": "SEND_SMS", "id": 19, "sensitive": true}, {"actionId": 13005, "actionName": "接收短信", "actionPermission": "android.permission.RECEIVE_SMS", "actionPermissionAlias": "RECEIVE_SMS", "id": 20, "sensitive": true}, {"actionId": 14001, "actionName": "查询短信", "actionPermission": "android.permission.READ_SMS", "actionPermissionAlias": "READ_SMS", "id": 21, "sensitive": true}, {"actionId": 14002, "actionName": "添加短信", "id": 22, "sensitive": true}, {"actionId": 14003, "actionName": "修改短信", "id": 23, "sensitive": true}, {"actionId": 14004, "actionName": "删除短信", "id": 24, "sensitive": true}, {"actionId": 14005, "actionName": "查询通话记录", "actionPermission": "android.permission.READ_CALL_LOG", "actionPermissionAlias": "READ_CALL_LOG", "id": 25, "sensitive": true}, {"actionId": 14006, "actionName": "添加通话记录", "actionPermission": "android.permission.WRITE_CALL_LOG", "actionPermissionAlias": "WRITE_CALL_LOG", "id": 26, "sensitive": true}, {"actionId": 14007, "actionName": "修改通话记录", "actionPermission": "android.permission.WRITE_CALL_LOG", "actionPermissionAlias": "WRITE_CALL_LOG", "id": 27, "sensitive": true}, {"actionId": 14008, "actionName": "删除通话记录", "actionPermission": "android.permission.WRITE_CALL_LOG", "actionPermissionAlias": "WRITE_CALL_LOG", "id": 28, "sensitive": true}, {"actionId": 14009, "actionName": "查询通讯录", "actionPermission": "android.permission.READ_CONTACTS", "actionPermissionAlias": "READ_CONTACTS", "id": 29, "sensitive": true}, {"actionId": 14010, "actionName": "添加通讯录记录", "actionPermission": "android.permission.WRITE_CONTACTS", "actionPermissionAlias": "WRITE_CONTACTS", "id": 30, "sensitive": true}, {"actionId": 14011, "actionName": "修改通讯录记录", "actionPermission": "android.permission.WRITE_CONTACTS", "actionPermissionAlias": "WRITE_CONTACTS", "id": 31, "sensitive": true}, {"actionId": 14012, "actionName": "删除通讯录记录", "actionPermission": "android.permission.WRITE_CONTACTS", "actionPermissionAlias": "WRITE_CONTACTS", "id": 32, "sensitive": true}, {"actionId": 14013, "actionName": "查询日历事件", "actionPermission": "android.permission.READ_CALENDAR", "actionPermissionAlias": "READ_CALENDAR", "id": 33, "sensitive": true}, {"actionId": 14014, "actionName": "添加日历事件", "actionPermission": "android.permission.WRITE_CALENDAR", "actionPermissionAlias": "WRITE_CALENDAR", "id": 34, "sensitive": true}, {"actionId": 14015, "actionName": "修改日历事件", "actionPermission": "android.permission.WRITE_CALENDAR", "actionPermissionAlias": "WRITE_CALENDAR", "id": 35, "sensitive": true}, {"actionId": 14016, "actionName": "删除日历事件", "actionPermission": "android.permission.WRITE_CALENDAR", "actionPermissionAlias": "WRITE_CALENDAR", "id": 36, "sensitive": true}, {"actionId": 15001, "actionName": "打开摄像头", "actionPermission": "android.permission.CAMERA", "actionPermissionAlias": "CAMERA", "id": 37, "sensitive": true}, {"actionId": 16001, "actionName": "获取ROOT权限", "id": 38, "sensitive": false}, {"actionId": 16002, "actionName": "动态加载SO文件", "id": 39, "sensitive": false}, {"actionId": 16003, "actionName": "获取系统日志", "actionPermission": "android.permission.READ_LOGS", "actionPermissionAlias": "READ_LOGS", "id": 40, "sensitive": true}, {"actionId": 16004, "actionName": "屏幕截图", "actionPermission": "android.permission.READ_FRAME_BUFFER", "actionPermissionAlias": "READ_FRAME_BUFFER", "id": 41, "sensitive": true}, {"actionId": 16005, "actionName": "执行SHELL命令", "id": 42, "sensitive": false}, {"actionId": 17001, "actionName": "新建文件", "actionPermission": "android.permission.WRITE_EXTERNAL_STORAGE", "actionPermissionAlias": "WRITE_EXTERNAL_STORAGE", "id": 43, "sensitive": true}, {"actionId": 17002, "actionName": "删除文件", "actionPermission": "android.permission.WRITE_EXTERNAL_STORAGE", "actionPermissionAlias": "WRITE_EXTERNAL_STORAGE", "id": 44, "sensitive": true}, {"actionId": 17003, "actionName": "创建目录", "actionPermission": "android.permission.WRITE_EXTERNAL_STORAGE", "actionPermissionAlias": "WRITE_EXTERNAL_STORAGE", "id": 45, "sensitive": true}, {"actionId": 17004, "actionName": "修改文件名", "actionPermission": "android.permission.WRITE_EXTERNAL_STORAGE", "actionPermissionAlias": "WRITE_EXTERNAL_STORAGE", "id": 46, "sensitive": true}, {"actionId": 17005, "actionName": "打开文件读取流", "actionPermission": "android.permission.READ_EXTERNAL_STORAGE", "actionPermissionAlias": "READ_EXTERNAL_STORAGE", "id": 47, "sensitive": true}, {"actionId": 17006, "actionName": "打开文件写入流", "actionPermission": "android.permission.WRITE_EXTERNAL_STORAGE", "actionPermissionAlias": "WRITE_EXTERNAL_STORAGE", "id": 48, "sensitive": true}, {"actionId": 18001, "actionName": "socket连接请求", "actionPermission": "android.permission.INTERNET", "actionPermissionAlias": "INTERNET", "id": 49, "sensitive": false}, {"actionId": 18002, "actionName": "http连接请求", "actionPermission": "android.permission.INTERNET", "actionPermissionAlias": "INTERNET", "id": 50, "sensitive": false}, {"actionId": 19001, "actionName": "加载插件", "id": 51, "sensitive": false}, {"actionId": 20001, "actionName": "获取计步传感器数据", "actionPermission": "android.permission.BODY_SENSORS", "actionPermissionAlias": "BODY_SENSORS", "id": 52, "sensitive": true}, {"actionId": 21001, "actionName": "关闭后台进程", "actionPermission": "android.permission.KILL_BACKGROUND_PROCESSES", "actionPermissionAlias": "KIL<PERSON>_BACKGROUND_PROCESSES", "id": 53, "sensitive": true}, {"actionId": 21002, "actionName": "调用系统相机", "id": 54, "sensitive": true}, {"actionId": 22001, "actionName": "后台运行时立即退出", "actionPermission": "android.permission.SET_ALWAYS_FINISH", "actionPermissionAlias": "SET_ALWAYS_FINISH", "id": 55, "sensitive": false}, {"actionId": 22002, "actionName": "获取运行中的进程", "actionPermission": "android.permission.GET_TASKS", "actionPermissionAlias": "GET_TASKS", "id": 56, "sensitive": true}, {"actionId": 23001, "actionName": "获取设备帐户数据", "actionPermission": "android.permission.GET_ACCOUNTS", "actionPermissionAlias": "GET_ACCOUNTS", "id": 57, "sensitive": true}, {"actionId": 23002, "actionName": "管理设备账户数据", "actionPermission": "android.permission.ACCOUNT_MANAGER", "actionPermissionAlias": "ACCOUNT_MANAGER", "id": 58, "sensitive": false}, {"actionId": 24001, "actionName": "获取网络状态", "actionPermission": "android.permission.ACCESS_NETWORK_STATE", "actionPermissionAlias": "ACCESS_NETWORK_STATE", "id": 59, "sensitive": false}, {"actionId": 24002, "actionName": "改变网络状态", "actionPermission": "android.permission.CHANGE_NETWORK_STATE", "actionPermissionAlias": "CHANGE_NETWORK_STATE", "id": 60, "sensitive": false}, {"actionId": 24003, "actionName": "获取WIFI状态", "actionPermission": "android.permission.ACCESS_WIFI_STATE", "actionPermissionAlias": "ACCESS_WIFI_STATE", "id": 61, "sensitive": false}, {"actionId": 24004, "actionName": "打开WIFI", "actionPermission": "android.permission.CHANGE_WIFI_STATE", "actionPermissionAlias": "CHANGE_WIFI_STATE", "id": 62, "sensitive": false}, {"actionId": 24005, "actionName": "关闭WIFI", "actionPermission": "android.permission.CHANGE_WIFI_STATE", "actionPermissionAlias": "CHANGE_WIFI_STATE", "id": 63, "sensitive": false}, {"actionId": 24006, "actionName": "扫描附近的WIFI热点", "id": 64, "sensitive": false}, {"actionId": 24007, "actionName": "获取WIFI扫描结果", "id": 65, "sensitive": false}, {"actionId": 25001, "actionName": "获取蓝牙适配器", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 66, "sensitive": false}, {"actionId": 25002, "actionName": "获取蓝牙是否可用", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 67, "sensitive": false}, {"actionId": 25003, "actionName": "获取蓝牙状态信息", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 68, "sensitive": false}, {"actionId": 25004, "actionName": "设置蓝牙可用", "actionPermission": "android.permission.BLUETOOTH_ADMIN", "actionPermissionAlias": "BLUETOOTH_ADMIN", "id": 69, "sensitive": false}, {"actionId": 25005, "actionName": "设置蓝牙不可用", "actionPermission": "android.permission.BLUETOOTH_ADMIN", "actionPermissionAlias": "BLUETOOTH_ADMIN", "id": 70, "sensitive": false}, {"actionId": 25006, "actionName": "获取蓝牙设备地址", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 71, "sensitive": false}, {"actionId": 25007, "actionName": "获取蓝牙名称", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 72, "sensitive": false}, {"actionId": 25008, "actionName": "设置蓝牙名称", "actionPermission": "android.permission.BLUETOOTH_ADMIN", "actionPermissionAlias": "BLUETOOTH_ADMIN", "id": 73, "sensitive": false}, {"actionId": 25009, "actionName": "获取蓝牙扫描模式", "actionPermission": "android.permission.BLUETOOTH", "actionPermissionAlias": "BLUETOOTH", "id": 74, "sensitive": false}, {"actionId": 25010, "actionName": "设置蓝牙扫描模式", "actionPermission": "android.permission.BLUETOOTH_ADMIN", "actionPermissionAlias": "BLUETOOTH_ADMIN", "id": 75, "sensitive": false}, {"actionId": 26001, "actionName": "写入系统设置", "actionPermission": "android.permission.WRITE_SETTINGS", "actionPermissionAlias": "WRITE_SETTINGS", "id": 76, "sensitive": true}, {"actionId": 26002, "actionName": "读取系统设置", "actionPermission": "android.permission.WRITE_SETTINGS", "actionPermissionAlias": "WRITE_SETTINGS", "id": 77, "sensitive": true}, {"actionId": 27001, "actionName": "获取NFC适配器", "actionPermission": "android.permission.NFC", "actionPermissionAlias": "NFC", "id": 78, "sensitive": false}, {"actionId": 28001, "actionName": "权限", "id": 79, "sensitive": false}, {"actionId": 28002, "actionName": "卸载应用", "actionPermission": "android.permission.DELETE_PACKAGES", "actionPermissionAlias": "DELETE_PACKAGES", "id": 80, "sensitive": false}, {"actionId": 28003, "actionName": "DUMP系统服务信息", "actionPermission": "android.permission.DUMP", "actionPermissionAlias": "DUMP", "id": 81, "sensitive": false}, {"actionId": 28004, "actionName": "安装应用程序", "actionPermission": "android.permission.INSTALL_PACKAGES", "actionPermissionAlias": "INSTALL_PACKAGES", "id": 82, "sensitive": true}, {"actionId": 28005, "actionName": "获取已安装的应用", "id": 83, "sensitive": true}, {"actionId": 29001, "actionName": "设置状态栏", "actionPermission": "android.permission.STATUS_BAR", "actionPermissionAlias": "STATUS_BAR", "id": 84, "sensitive": false}, {"actionId": 30001, "actionName": "禁用键盘锁", "actionPermission": "android.permission.DISABLE_KEYGUARD", "actionPermissionAlias": "DISABLE_KEYGUARD", "id": 85, "sensitive": false}, {"actionId": 10009, "actionName": "获取SIM卡服务商信息", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 86, "sensitive": true}, {"actionId": 10010, "actionName": "获取SIM卡信息", "actionPermission": "android.permission.READ_PHONE_STATE", "actionPermissionAlias": "READ_PHONE_STATE", "id": 87, "sensitive": true}, {"actionId": 11002, "actionName": "停止录音", "actionPermission": "android.permission.RECORD_AUDIO", "actionPermissionAlias": "RECORD_AUDIO", "id": 88, "sensitive": true}, {"actionId": 21003, "actionName": "调用系统相册", "id": 89, "sensitive": true}, {"actionId": 24008, "actionName": "获取IP", "id": 90, "sensitive": true}, {"actionId": 24009, "actionName": "获取MAC地址", "id": 91, "sensitive": true}, {"actionId": 31001, "actionName": "获取设备序列号", "id": 166, "sensitive": true}, {"actionId": 32001, "actionName": "应用自启动", "id": 167, "sensitive": true}, {"actionId": 33001, "actionName": "获取剪切板内容", "id": 169, "sensitive": false}, {"actionId": 21004, "actionName": "启动Activity", "id": 170, "sensitive": false}, {"actionId": 21005, "actionName": "启动Service", "id": 171, "sensitive": false}]