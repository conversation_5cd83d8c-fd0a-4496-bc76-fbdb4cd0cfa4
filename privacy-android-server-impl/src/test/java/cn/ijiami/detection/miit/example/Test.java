package cn.ijiami.detection.miit.example;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Test {

    public static void main2(String[] args) {
        String text = "三十天内回复，30天内回复，15个工作日回复你，十五个工作日回复你。";
//        text = "15个工作日电话回复";
        // 匹配中文数字或阿拉伯数字的正则表达式
//        Pattern pattern = Pattern.compile("(?:[一二三四五六七八九十]+|\\d+)(?:天内|个工作日|天内)(\\s?:回复|答复|处理)");
//        Pattern pattern = Pattern.compile("(?:[一二三四五六七八九十]+|\\d+)(?:天内|个工作日|天内)");
        Pattern pattern = Pattern.compile("(?:[一二三四五六七八九十百千万亿零两]+|\\d+)(?:天|天内|个工作日|工作日)?(回复|答复|处理)");
        Matcher matcher = pattern.matcher(text);
        Map<String, Integer> chineseToArabicMap = createChineseToArabicMap();
        System.out.println("匹配的数字:");
        while (matcher.find()) {
            String matchedText = matcher.group();
            System.out.println(matchedText);
            // 提取数字部分（去除"天内回复"或"个工作日回复"）
            String numberPart = matchedText.replaceAll("[^一二三四五六七八九十百千万亿零两\\d]+", "");
            // 将中文数字转换为阿拉伯数字
            if(isNumeric(numberPart)) {
            	System.out.println(matchedText + " 对应的阿拉伯数字是：" + numberPart);
            } else {
            	int arabicNumber = convertChineseToArabic(numberPart, chineseToArabicMap);
                System.out.println(matchedText + " 对应的阿拉伯数字是：" + arabicNumber);
            }
        }
    }
    
    public static void main(String[] args) throws IOException {
        PostMethod postMethod = new PostMethod("https://ijmdev.zywa.com.cn/privacy-dev/detection/oauth/token");
        postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        NameValuePair[] data = {
                new NameValuePair("grant_type", "authorization_code"),
                new NameValuePair("code", "ogFylo1"),
                new NameValuePair("scope", "read write"),
                new NameValuePair("client_id", "u2kbnfzsr9"),
                new NameValuePair("client_secret", "xd2arke8qg"),
                new NameValuePair("redirect_uri", "https://ijmdev.zywa.com.cn/privacy-dev/detection/manager/authentication/user/receiveCode")
        };
        postMethod.setRequestBody(data);
        org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
        // 鎵цPOST鏂规硶
        int response = httpClient.executeMethod(postMethod);
        System.out.println(response);
	}
    
    public static boolean isNumeric(String str) {
        // 匹配纯数字的正则表达式
        Pattern pattern = Pattern.compile("^\\d+$");
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

        // 创建中文数字与阿拉伯数字的映射表
        public static Map<String, Integer> createChineseToArabicMap() {
            Map<String, Integer> chineseToArabicMap = new HashMap<>();
            chineseToArabicMap.put("一", 1);
            chineseToArabicMap.put("二", 2);
            chineseToArabicMap.put("三", 3);
            chineseToArabicMap.put("四", 4);
            chineseToArabicMap.put("五", 5);
            chineseToArabicMap.put("六", 6);
            chineseToArabicMap.put("七", 7);
            chineseToArabicMap.put("八", 8);
            chineseToArabicMap.put("九", 9);
            chineseToArabicMap.put("十", 10);
            chineseToArabicMap.put("十一", 11);
            chineseToArabicMap.put("十二", 12);
            chineseToArabicMap.put("十三", 13);
            chineseToArabicMap.put("十四", 14);
            chineseToArabicMap.put("十五", 15);
            chineseToArabicMap.put("十六", 16);
            chineseToArabicMap.put("十七", 17);
            chineseToArabicMap.put("十八", 18);
            chineseToArabicMap.put("十九", 19);
            chineseToArabicMap.put("二十", 20);
            chineseToArabicMap.put("二十一", 21);
            chineseToArabicMap.put("二十二", 22);
            chineseToArabicMap.put("二十三", 23);
            chineseToArabicMap.put("二十四", 24);
            chineseToArabicMap.put("二十五", 25);
            chineseToArabicMap.put("二十六", 26);
            chineseToArabicMap.put("二十七", 27);
            chineseToArabicMap.put("二十八", 28);
            chineseToArabicMap.put("二十九", 29);
            chineseToArabicMap.put("三十", 30);
            return chineseToArabicMap;
        }

        // 将中文数字转换为阿拉伯数字的方法
        public static int convertChineseToArabic(String chineseNumber, Map<String, Integer> map) {
            int result = 0;
            int temp = 0;
            int num;
            for (int i = 0; i < chineseNumber.length(); i++) {
                num = map.get(String.valueOf(chineseNumber.charAt(i)));
                if (num < 10) {
                    temp = num;
                } else {
                    if (temp == 0) {
                        temp = 1;
                    }
                    result += temp * num;
                    temp = 0;
                }
            }
            result += temp;
            return result;
        }



}
