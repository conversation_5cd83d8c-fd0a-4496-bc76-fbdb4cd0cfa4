package cn.ijiami.detection.miit.example;

import java.util.Arrays;
import java.util.HashSet;

import org.junit.jupiter.api.Test;

import cn.ijiami.detection.miit.domain.CustomDetectInfo;
//import org.junit.Test;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10201;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10202;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10203;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10204;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10205;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10206;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10207;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10208;

/**
 * <AUTHOR>
 * @date 2020-12-23 15:43
 */
public class DetectExample1020 extends DetectExample {

    @Test
    public void detectPoint10201() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10201");
        customDetectInfo.setDecideRuleActionIds(
                new HashSet<>(Arrays.asList(10002L, 24009L, 28005L, 14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L)));
        // 执行检测
        detectPoint(new DetectPoint10201(), customDetectInfo, true);
    }

    @Test
    public void detectPoint10202() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10202");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(12001L, 12002L, 12003L, 12004L, 12005L, 12006L)));
        // 执行检测
        detectPoint(new DetectPoint10202(), customDetectInfo, false);
    }

    @Test
    public void detectPoint10203() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10203");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(24009L, 28005L)));
        // 执行检测
        detectPoint(new DetectPoint10203(), customDetectInfo, false);
    }

    @Test
    public void detectPoint10204() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10204");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(12001L, 12002L, 12003L, 12004L, 12005L, 12006L)));
        // 执行检测
        detectPoint(new DetectPoint10204(), customDetectInfo, true);
    }

    @Test
    public void detectPoint10205() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10205");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(
                Arrays.asList(13001L, 13002L, 13003L, 13004L, 13005L, 14001L, 14002L, 14003L, 14004L, 14005L, 14006L, 14007L, 14008L, 14009L, 14010L, 14011L,
                        14012L, 21002L)));
        // 执行检测
        detectPoint(new DetectPoint10205(), customDetectInfo, false);
    }

    @Test
    public void detectPoint10206() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10206");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(
                Arrays.asList(10002L, 12001L, 12002L, 12003L, 12004L, 12005L, 12006L, 14005L, 14006L, 14007L, 14008L, 14009L, 14010L, 14011L, 14012L, 13001L,
                        13002L, 13003L, 13004L, 13005L, 14001L, 14002L, 14003L, 14004L, 21003L, 16004L)));
        // 执行检测
        detectPoint(new DetectPoint10206(), customDetectInfo, true);
    }

    @Test
    public void detectPoint10207() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10207");
        // jz.do 缺少行为判断依据
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList()));
        // 执行检测
        detectPoint(new DetectPoint10207(), customDetectInfo, true);
    }

    @Test
    public void detectPoint10208() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10208");
        // jz.do 缺少行为判断依据
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList()));
        // 执行检测
        detectPoint(new DetectPoint10208(), customDetectInfo, true);
    }
}
