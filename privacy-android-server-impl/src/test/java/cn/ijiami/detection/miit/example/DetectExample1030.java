package cn.ijiami.detection.miit.example;

import java.util.Arrays;
import java.util.HashSet;

import org.junit.jupiter.api.Test;

import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10104;
//import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-12-23 15:43
 */
public class DetectExample1030 extends DetectExample {

    @Test
    public void detectPoint10301() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10104");
        // jz.do 缺少行为判断依据
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList()));
        // 执行检测
        detectPoint(new DetectPoint10104(), customDetectInfo, false);
    }

    @Test
    public void detectPoint10302() {

    }
}
