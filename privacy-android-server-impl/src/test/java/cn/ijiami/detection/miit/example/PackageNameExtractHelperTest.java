package cn.ijiami.detection.miit.example;

import java.util.List;

import org.junit.jupiter.api.Test;

import cn.ijiami.detection.helper.PackageNameExtractHelper;
//import org.junit.Test;

public class PackageNameExtractHelperTest {

    @Test
    public void testExtractFullName() {
        String stackInfo = "java.io.FileInputStream.<init>(FileInputStream.java:142)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.a(Unknown Source:42)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.b(Unknown Source:38)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.readFromSPUnified(Unknown Source:28)";
        List<String> names = PackageNameExtractHelper.extractFullName(stackInfo);
        assert names.stream().anyMatch(s -> s.equals("java.io.FileInputStream"));
        assert names.stream().anyMatch(s -> s.equals("com.taobao.wireless.security.adapter.common.SPUtility2.a"));
        assert names.stream().anyMatch(s -> s.equals("com.taobao.wireless.security.adapter.common.SPUtility2.b"));
        assert names.stream().anyMatch(s -> s.equals("com.taobao.wireless.security.adapter.common.SPUtility2.readFromSPUnified"));
        assert names.size() == 4;
    }

    @Test
    public void testExtractSampleName() {
        String stackInfo = "java.io.FileInputStream.<init>(FileInputStream.java:142)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.a(Unknown Source:42)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.b(Unknown Source:38)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.readFromSPUnified(Unknown Source:28)";
        List<String> names = PackageNameExtractHelper.extractSampleName(stackInfo);
        assert names.stream().anyMatch(s -> s.equals("java.io.FileInputStream"));
        assert names.stream().anyMatch(s -> s.equals("com.taobao.wireless"));
        assert names.size() == 2;
    }


    @Test
    public void testShrinkingPackageName() {
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.abc");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.abc.start");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.c.abc.start");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.c.b.start");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.c.b.start.a");
        assert PackageNameExtractHelper.isShrinkingPackageName("c.a.b.c.b.start.abc");
        assert !PackageNameExtractHelper.isShrinkingPackageName("c.a.start");
        assert !PackageNameExtractHelper.isShrinkingPackageName("c.a.start.running");
        assert !PackageNameExtractHelper.isShrinkingPackageName("c.abb.start.running");
        assert !PackageNameExtractHelper.isShrinkingPackageName("cc.abb.st");
    }


    @Test
    public void testExtractMergePackageName() {
        assert PackageNameExtractHelper.extractMergePackageName("com.qq.a").equals("com.qq");
        assert PackageNameExtractHelper.extractMergePackageName("com.qq.local.b").equals("com.qq.local");
        assert PackageNameExtractHelper.extractMergePackageName("com.qq.local.store.c").equals("com.qq.local.store");
        assert PackageNameExtractHelper.extractMergePackageName("com.qq.local").isEmpty();
        assert PackageNameExtractHelper.extractMergePackageName("com.qq").isEmpty();
        assert PackageNameExtractHelper.extractMergePackageName("com").isEmpty();
    }
}
