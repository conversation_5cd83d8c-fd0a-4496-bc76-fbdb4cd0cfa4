package cn.ijiami.detection.miit.example;

import java.util.Arrays;
import java.util.HashSet;

import org.junit.jupiter.api.Test;

import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20201;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20202;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20203;
//import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020-12-23 15:43
 */
public class DetectExample2020 extends DetectExample {

    @Test
    public void detectPoint20201() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20201");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(21004L, 21005L)));
        // 执行检测
        detectPoint(new DetectPoint20201(), customDetectInfo, false);
    }

    @Test
    public void detectPoint20202() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20202");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(21004L, 21005L)));
        // 执行检测
        detectPoint(new DetectPoint20202(), customDetectInfo, true);
    }

    @Test
    public void detectPoint20203() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20203");
        customDetectInfo.setDecideRuleActionIds(new HashSet<>(Arrays.asList(21004L, 21005L)));
        // 执行检测
        detectPoint(new DetectPoint20203(), customDetectInfo, true);
    }
}
