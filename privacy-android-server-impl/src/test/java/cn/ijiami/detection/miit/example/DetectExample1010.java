package cn.ijiami.detection.miit.example;

import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10101;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10104;
//import org.junit.Test;

import java.util.Arrays;
import java.util.HashSet;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2020-12-23 15:43
 */
public class DetectExample1010 extends DetectExample {

    @Test
    public void detectPoint010101() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10101");
        customDetectInfo.setDecideRuleActionIds(
                new HashSet<>(Arrays.asList(10002L, 24009L, 28005L, 14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L, 21004L, 21005L)));
        // 执行检测
        detectPoint(new DetectPoint10101(), customDetectInfo, false);
    }

    @Test
    public void detectPoint010104() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("10104");
        customDetectInfo.setDecideRuleActionIds(
                new HashSet<>(Arrays.asList(10002L, 24009L, 28005L, 14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L, 21004L, 21005L)));
        // 执行检测
        detectPoint(new DetectPoint10104(), customDetectInfo, false);
    }

}
