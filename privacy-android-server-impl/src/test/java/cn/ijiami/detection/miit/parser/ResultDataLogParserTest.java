package cn.ijiami.detection.miit.parser;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Test;

import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.kit.MiitLogKit;
//import org.junit.Test;

public class ResultDataLogParserTest {

    private static final String PATH =
            "E:\\E0-workspace\\E0-1-privacy-detection\\E0-1-08 privacy-detection-web-2.3.2\\pinfo-detection\\detection-service-impl\\src\\test\\resources\\miit\\";

    @Test
    public void method() {
        IDetectDataParser resultDataLogParser = new AndroidResultDataLogParser();
        List<ResultDataLogBO> parser = resultDataLogParser.parser(PATH + "resultDataLog", null);
        for (ResultDataLogBO resultDataLogBO : parser) {
            System.err.println(resultDataLogBO);
        }
    }

    @Test
    public void log() {
        List<ResultDataLogBO> resultDataLogs = MiitLogKit.readLogToBeanByAbsolutePath(PATH + "resultDataLog");
        for (ResultDataLogBO resultDataLog : resultDataLogs) {
            System.err.println(resultDataLog.getTime() + ":" + resultDataLog.getImgPath());
        }

        List<String> collect =
                resultDataLogs.stream().sorted(Comparator.comparing(ResultDataLogBO::getTime)).map(ResultDataLogBO::getImgPath).collect(Collectors.toList());

        for (String str : collect) {
            System.err.println(str);
        }
    }

    @Test
    public void harmonyTest() {
        HarmonyResultDataLogParser resultDataLogParser = new HarmonyResultDataLogParser();
        List<ResultDataLogBO> parser = resultDataLogParser.parser("D:\\harmony\\data\\resultFiles", null);
        assert !parser.isEmpty();
    }
}