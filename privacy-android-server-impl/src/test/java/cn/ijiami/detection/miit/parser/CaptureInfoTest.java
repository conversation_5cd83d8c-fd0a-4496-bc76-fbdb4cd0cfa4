package cn.ijiami.detection.miit.parser;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.parser.CaptureInfoParser;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.URLTokenizer;

public class CaptureInfoTest {
	
	
	public static void main(String[] args) {
		String filePath = "C:/Users/<USER>/Desktop/华住会_20220601175355/431546363586dfde4a63fc9f4ae7ea5b_74051_MANUAL/capture_info/";
		List<CaptureInfoBO> captureInfos = new ArrayList<>();
		IDetectDataParser<CaptureInfoBO> parser = new CaptureInfoParser();
        if(new File(filePath).isDirectory()) {
        	List<String> filePaths = CommonUtil.listAllFilesInDir(filePath + File.separator, new String[]{".txt", ".TXT"}, true);
        	for (int i = 0; i < filePaths.size(); i++) {
        		captureInfos.addAll(parser.parser(filePaths.get(i), null));
			}
        }
        System.out.println(captureInfos.size());
        
        for (CaptureInfoBO captureInfo1 : captureInfos) {
        	String original = captureInfo1.getOriginal();
            // 不符合要求的数据，不包含host视作无效数据
//            if (StringUtils.isBlank(original) || !original.contains("Host: ")) {
//                continue;
//            }
//            if(captureInfo.getPath().contains("queryChannelDetailProduct") ){
//            	System.out.println(captureInfo.getUrl());
        	String staic = "android.net.Uri$StringUri.<init>(Uri.java:490)<---android.net.Uri$StringUri.<init>(Unknown Source:0)<---android.net.Uri.parse(Uri.java:443)<---com.facebook.react.modules.network.NetworkingModule.sendRequestInternal(NetworkingModule.java:2)<---com.facebook.react.modules.network.NetworkingModule.sendRequest(NetworkingModule.java:1)<---java.lang.reflect.Method.invoke(Native Method)<---com.facebook.react.bridge.JavaMethodWrapper.invoke(JavaMethodWrapper.java:18)<---com.facebook.react.bridge.JavaModuleWrapper.invoke(JavaModuleWrapper.java:2)<---com.facebook.react.bridge.queue.NativeRunnable.run(Native Method)<---android.os.Handler.handleCallback(Handler.java:790)<---android.os.Handler.dispatchMessage(Handler.java:99)<---com.facebook.react.bridge.queue.MessageQueueThreadHandler.dispatchMessage(MessageQueueThreadHandler.java:1)<---android.os.Looper.loop(Looper.java:164)<---com.facebook.react.bridge.queue.MessageQueueThreadImpl$4.run(MessageQueueThreadImpl.java:8)<---java.lang.Thread.run(Thread.java:764)";
//        	if(staic.contains(captureInfo.getStackInfo())){
//        		System.out.println(staic);
//        	}
//        	if(captureInfo.getParams().contains("上海")) {
//        		System.out.println(captureInfo.getParams());
        	
//        	}
//            }
//            System.out.println(captureInfo.getUrl());
//            System.out.println(CommonUtil.filterPrintString(captureInfo.getDetailsData()));
//			System.out.println(captureInfo.getHost());
        	
        	 
            
		}
        
        String detailsData = "https://hweb-channel.huazhu.com/channel/detail/queryChannelDetailProduct?articleId=8742&cityName=上海&latitude=0.0&longitude=0.0&newCurrentCityName=";
        List<String> list = URLTokenizer.segmentUrl(detailsData);
        CaptureInfoBO captureInfo = new CaptureInfoBO();
        TPrivacyActionNougat stackDetail = new TPrivacyActionNougat();
		list.forEach(str->{
			System.out.println(str);
			try {
				URL u = new URL(detailsData);
				System.out.println(u.getProtocol());
				System.out.println(u.getPort());
				System.out.println(u.getHost());
				System.out.println(u.getAuthority());
				
				String port = u.getPort()==-1 ? "80" : String.valueOf(u.getPort());
				if(u.getPort()==-1 && str.startsWith("https")){
					port = "443";
				}
				
				captureInfo.setDetailsData(detailsData);
				captureInfo.setDataType("3"); //HTTP 
				captureInfo.setExecutor(stackDetail.getExecutor()); //主体
				captureInfo.setExecutorType(stackDetail.getExecutorType());
				captureInfo.setHost(u.getHost());
				captureInfo.setIp("");//TODO
				captureInfo.setMethod("GET/POST");
				captureInfo.setOriginal(detailsData);
				captureInfo.setPackageName(stackDetail.getPackageName());
				captureInfo.setParams(u.getQuery());
				captureInfo.setPath(u.getPath());
				captureInfo.setPort(port);
				captureInfo.setProtocol(u.getProtocol());
				captureInfo.setResponse("");
				captureInfo.setSdkIds(stackDetail.getSdkIds());
				captureInfo.setStackInfo(stackDetail.getStackInfo());
				captureInfo.setTimeStamp(stackDetail.getActionTimeStamp()==null ? null : String.valueOf(stackDetail.getActionTimeStamp()));
				captureInfo.setType(str.startsWith("https") ? "2" :"1");
				captureInfo.setUrl(detailsData);
			} catch (Exception e) {
				
				e.getMessage();
			}
		});
	}
	
	
}
