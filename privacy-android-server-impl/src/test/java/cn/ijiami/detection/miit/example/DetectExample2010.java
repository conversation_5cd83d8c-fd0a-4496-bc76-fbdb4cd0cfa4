package cn.ijiami.detection.miit.example;

import org.junit.jupiter.api.Test;
//import org.junit.Test;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20101;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20102;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20103;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20104;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20105;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20106;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20107;

/**
 * <AUTHOR>
 * @date 2020-12-23 15:43
 */
public class DetectExample2010 extends DetectExample {

    @Test
    public void detectPoint20101() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20101");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20101(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20102() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20102");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20102(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20103() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20103");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20103(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20104() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20104");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20104(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20105() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20105");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20105(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20106() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20106");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20106(), customDetectInfo, false));
    }

    @Test
    public void detectPoint20107() {
        // 自定义参数
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo("20107");
        // 执行检测
        checkResult(detectPoint(new DetectPoint20107(), customDetectInfo, false));
    }

    private void checkResult(DetectResult result) {
        if (result.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            assert result.getScreenshots().size() > 0;
            assert result.getRequestPermissionNames().size() > 0;
        } else {
            assert CollectionUtils.isEmpty(result.getRequestPermissionNames());
        }
    }

}
