package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.AssetsServiceApi;
import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.param.*;
import cn.ijiami.detection.feign.converter.AssetsConverter;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsController.java
 * @Description 资产相关接口
 * @createTime 2025年06月03日 18:39:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "资产相关接口")
@RestController
public class AssetsController implements AssetsServiceApi {

    @Autowired
    private IAssetsService assetsService;

    @Override
    public Integer insert(AssetsDTO assets) {
        try {
            return assetsService.insert(AssetsConverter.convertToTAssets(assets));
        } catch (Exception e) {
            log.error("新增资产失败", e);
            throw new RuntimeException("新增资产失败", e);
        }
    }

    @Override
    public AssetsListPageDTO findAssetsListPage(AssetsDTO assets) {
        try {
            return AssetsConverter.convertToAssetsListPageDTO(assetsService.findAssetsListPage(AssetsConverter.convertToTAssets(assets)));
        } catch (Exception e) {
            log.error("查询资产列表失败", e);
            throw new RuntimeException("查询资产列表失败", e);
        }
    }

    @Override
    public AssetsDTO analysisApk(AnalysisApkParam param) {
        try {
            return AssetsConverter.convertToAssetsDTO(assetsService.analysisApk(
                    AssetsConverter.convertToFileVO(param.getFileVO()),
                    param.getSaveFlag(),
                    param.getUserId(),
                    param.getIsAbsolutePath(),
                    param.getShellPath()
            ));
        } catch (IjiamiApplicationException e) {
            log.error("解析APK失败", e);
            throw new RuntimeException("解析APK失败", e);
        }
    }

    @Override
    public BaseResponseDTO<AssetsDTO> analysisApkWithResponse(AnalysisApkParam param) {
        try {
            return AssetsConverter.convertToBaseResponseDTO(assetsService.analysisApk(
                    param.getUserId(),
                    AssetsConverter.convertToFileVO(param.getFileVO()),
                    param.getIsAbsolutePath()
            ));
        } catch (IjiamiApplicationException e) {
            log.error("解析APK失败", e);
            throw new RuntimeException("解析APK失败", e);
        }
    }

    @Override
    public AssetsDTO findTassets(AssetsDTO assets) {
        try {
            return AssetsConverter.convertToAssetsDTO(assetsService.findTassets(AssetsConverter.convertToTAssets(assets)));
        } catch (Exception e) {
            log.error("查询资产失败", e);
            throw new RuntimeException("查询资产失败", e);
        }
    }

    @Override
    public AssetsDTO getAssetsById(Long assetsId) {
        try {
            return AssetsConverter.convertToAssetsDTO(assetsService.getAssetsById(assetsId));
        } catch (Exception e) {
            log.error("根据ID获取资产失败", e);
            throw new RuntimeException("根据ID获取资产失败", e);
        }
    }

    @Override
    public AssetsDTO saveOrUpdate(AssetsDTO assets) {
        try {
            return AssetsConverter.convertToAssetsDTO(assetsService.saveOrUpdate(AssetsConverter.convertToTAssets(assets)));
        } catch (IjiamiApplicationException | IjiamiCommandException e) {
            log.error("保存或更新资产失败", e);
            throw new RuntimeException("保存或更新资产失败", e);
        }
    }

    @Override
    public void saveOrUpdateList(SaveOrUpdateListParam param) {
        try {
            assetsService.saveOrUpdateList(
                    AssetsConverter.convertToTAssetsList(param.getAssetsList()),
                    AssetsConverter.convertToTerminalTypeEnum(param.getTerminalType()),
                    param.getUserId()
            );
        } catch (IjiamiApplicationException | IjiamiCommandException e) {
            log.error("批量保存或更新资产失败", e);
            throw new RuntimeException("批量保存或更新资产失败", e);
        }
    }

    @Override
    public void saveOrUpdateListByFileVO(SaveOrUpdateListByFileParam param) {
        try {
            assetsService.saveOrUpdateListByFileVO(
                    AssetsConverter.convertToFileVOList(param.getFileVOList()),
                    param.getUserId()
            );
        } catch (IjiamiApplicationException | IjiamiCommandException e) {
            log.error("通过文件批量保存或更新资产失败", e);
            throw new RuntimeException("通过文件批量保存或更新资产失败", e);
        }
    }

    @Override
    public CheckChunkFileDTO checkChunkFile(CheckChunkFileParam param) {
        try {
            return AssetsConverter.convertToCheckChunkFileDTO(assetsService.checkChunkFile(
                    param.getFileMd5(),
                    param.getSourceFileName(),
                    param.getChunkTotal(),
                    param.getUserId()
            ));
        } catch (Exception e) {
            log.error("检查分块文件失败", e);
            throw new RuntimeException("检查分块文件失败", e);
        }
    }

    @Override
    public void stopUrlUpload(String url, Long userId) {
        try {
            assetsService.stopUrlUpload(url, userId);
        } catch (Exception e) {
            log.error("停止URL上传失败", e);
            throw new RuntimeException("停止URL上传失败", e);
        }
    }

    @Override
    public void stopChunkUpload(String md5, Long userId) {
        try {
            assetsService.stopChunkUpload(md5, userId);
        } catch (Exception e) {
            log.error("停止分块上传失败", e);
            throw new RuntimeException("停止分块上传失败", e);
        }
    }

    @Override
    public void delete(Long id, Long userId) {
        try {
            assetsService.delete(id, userId);
        } catch (IjiamiApplicationException e) {
            log.error("删除资产失败", e);
            throw new RuntimeException("删除资产失败", e);
        }
    }

    @Override
    public void deleteApk(String startDate, String endDate) {
        try {
            assetsService.deleteApk(startDate, endDate);
        } catch (Exception e) {
            log.error("删除APK失败", e);
            throw new RuntimeException("删除APK失败", e);
        }
    }

    @Override
    public Map<String, String> getFileByAssetsId(Long assetsId) {
        try {
            return assetsService.getFileByAssetsId(assetsId);
        } catch (IjiamiApplicationException e) {
            log.error("获取资产文件包失败", e);
            throw new RuntimeException("获取资产文件包失败", e);
        }
    }

    @Override
    public String getDumpByAssetsId(Long assetsId) {
        try {
            return assetsService.getDumpByAssetsId(assetsId);
        } catch (IjiamiApplicationException e) {
            log.error("下载脱壳包失败", e);
            throw new RuntimeException("下载脱壳包失败", e);
        }
    }

    @Override
    public void chunkClean() {
        try {
            assetsService.chunkClean();
        } catch (Exception e) {
            log.error("清理分块文件失败", e);
            throw new RuntimeException("清理分块文件失败", e);
        }
    }

    @Override
    public String extractAppSignature(String appFilePath) {
        try {
            return assetsService.extractAppSignature(appFilePath);
        } catch (Exception e) {
            log.error("获取应用签名信息失败", e);
            throw new RuntimeException("获取应用签名信息失败", e);
        }
    }

    @Override
    public List<ChunkUploadFileDTO> queryAnalysisQueue(BaseQueryParam baseQuery) {
        try {
            return AssetsConverter.convertToChunkUploadFileDTOList(assetsService.queryAnalysisQueue(AssetsConverter.convertToBaseQuery(baseQuery)));
        } catch (Exception e) {
            log.error("查询分析队列失败", e);
            throw new RuntimeException("查询分析队列失败", e);
        }
    }

    @Override
    public void saveObb(Long assetsId, String obbPath, String fileId, Long userId) {
        try {
            assetsService.saveObb(assetsId, obbPath, fileId, userId);
        } catch (Exception e) {
            log.error("保存OBB数据失败", e);
            throw new RuntimeException("保存OBB数据失败", e);
        }
    }

    @Override
    public void batchAddApp(BatchAddAppParam param) {
        try {
            assetsService.batchAddApp(AssetsConverter.convertToBatchAddApp(param), param.getUserId());
        } catch (IjiamiApplicationException | IjiamiCommandException e) {
            log.error("批量添加应用失败", e);
            throw new RuntimeException("批量添加应用失败", e);
        }
    }

    @Override
    public void setAssetsFile(SetAssetsFileParam param) {
        try {
            assetsService.setAssetsFile(
                    AssetsConverter.convertToTAssets(param.getAssets()),
                    param.getAppPrivacyPolicyFileId(),
                    param.getThirdPartySharedListFileId(),
                    param.getQrcodeFileId()
            );
        } catch (Exception e) {
            log.error("设置资产文件失败", e);
            throw new RuntimeException("设置资产文件失败", e);
        }
    }


}
