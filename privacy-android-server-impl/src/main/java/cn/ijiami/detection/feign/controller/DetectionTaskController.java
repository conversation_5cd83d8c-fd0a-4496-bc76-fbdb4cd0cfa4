package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.ChongqinVO;
import cn.ijiami.detection.VO.ClientUpdateProgressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.android.client.api.DetectionTaskServiceApi;
import cn.ijiami.detection.android.client.dto.BaseMessageDTO;
import cn.ijiami.detection.android.client.dto.BigDataDTO;
import cn.ijiami.detection.android.client.dto.ChongqinDTO;
import cn.ijiami.detection.android.client.dto.ClientUpdateProgressDTO;
import cn.ijiami.detection.android.client.dto.PrivacyActionNougatDTO;
import cn.ijiami.detection.android.client.dto.SdkDTO;
import cn.ijiami.detection.android.client.dto.SdkResponseDTO;
import cn.ijiami.detection.android.client.dto.TaskPageDTO;
import cn.ijiami.detection.android.client.dto.TaskDetailDTO;
import cn.ijiami.detection.android.client.dto.TaskDTO;
import cn.ijiami.detection.android.client.param.StartTaskParam;
import cn.ijiami.detection.android.client.param.TaskProgressParam;
import cn.ijiami.detection.android.client.param.TaskQueryParam;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.feign.converter.TaskDTOConverter;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionTaskController.java
 * @Description 检测任务相关接口控制器
 * @createTime 2025年01月15日 10:00:00
 */
@Slf4j
@RequestMapping("/api/detection/task")
@Api(value = "/api/detection/task", tags = "检测任务相关接口")
@RestController
public class DetectionTaskController implements DetectionTaskServiceApi {

    @Autowired
    private ITaskService taskService;

    @Override
    @ApiOperation("启动任务接口")
    @PostMapping(
            value = {"/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public Long startTask(@Valid @RequestBody StartTaskParam param) {
        try {
            return taskService.startTask(param);
        } catch (Exception e) {
            log.error("启动任务失败", e);
            throw new RuntimeException("启动任务失败", e);
        }
    }

    @Override
    @ApiOperation("停止任务")
    @PostMapping(
            value = {"/stopTask"},
            produces = {"application/json"}
    )
    public Boolean stopTask(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.stopTask(taskId);
        } catch (IjiamiApplicationException e) {
            log.error("停止任务失败", e);
            return false;
        }
    }

    @Override
    @ApiOperation("强制停止任务")
    @PostMapping(
            value = {"/stopTaskForce"},
            produces = {"application/json"}
    )
    public Boolean stopTask(@RequestParam("taskId") Long taskId, @RequestParam("forceStop") boolean forceStop) {
        try {
            return taskService.stopTask(taskId, forceStop);
        } catch (IjiamiApplicationException e) {
            log.error("强制停止任务失败", e);
            return false;
        }
    }

    @Override
    @ApiOperation("重启静态检测")
    @PostMapping(
            value = {"/restartTask"},
            produces = {"application/json"}
    )
    public Long restartTask(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.restartTask(taskId);
        } catch (IjiamiCommandException e) {
            log.error("重启静态检测失败", e);
            throw new RuntimeException("重启静态检测失败", e);
        }
    }

    @Override
    @ApiOperation("重启动态检测")
    @PostMapping(
            value = {"/restartDynamicTask"},
            produces = {"application/json"}
    )
    public Long restartDynamicTask(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.restartDynamicTask(taskId);
        } catch (IjiamiCommandException e) {
            log.error("重启动态检测失败", e);
            throw new RuntimeException("重启动态检测失败", e);
        }
    }

    @Override
    @ApiOperation("重启动态检测阶段")
    @PostMapping(
            value = {"/restartDynamicTaskStage"},
            produces = {"application/json"}
    )
    public Integer restartDynamicTaskStage(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.restartDynamicTaskStage(taskId);
        } catch (Exception e) {
            log.error("重启动态检测阶段失败", e);
            throw new RuntimeException("重启动态检测阶段失败", e);
        }
    }

    @Override
    @ApiOperation("启动动态检测")
    @PostMapping(
            value = {"/startDynamicTask"},
            produces = {"application/json"}
    )
    public Long startDynamicTask(@RequestParam("taskId") Long taskId, @RequestParam("type") Integer type) {
        try {
            return taskService.startDynamicTask(taskId, type);
        } catch (Exception e) {
            log.error("启动动态检测失败", e);
            throw new RuntimeException("启动动态检测失败", e);
        }
    }

    @Override
    @ApiOperation("启动动态检测（带设备类型）")
    @PostMapping(
            value = {"/startDynamicTaskWithDevice"},
            produces = {"application/json"}
    )
    public Long startDynamicTask(@RequestParam("taskId") Long taskId, @RequestParam("type") Integer type, @RequestParam("deviceType") int deviceType) {
        try {
            return taskService.startDynamicTask(taskId, type, deviceType);
        } catch (Exception e) {
            log.error("启动动态检测（带设备类型）失败", e);
            throw new RuntimeException("启动动态检测（带设备类型）失败", e);
        }
    }

    @Override
    @ApiOperation("开始AI智能检测")
    @PostMapping(
            value = {"/startAiDetection"},
            produces = {"application/json"}
    )
    public Long startAiDetection(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.startAiDetection(taskId);
        } catch (IjiamiCommandException e) {
            log.error("开始AI智能检测失败", e);
            throw new RuntimeException("开始AI智能检测失败", e);
        }
    }

    @Override
    @ApiOperation("任务抢占")
    @PostMapping(
            value = {"/taskPreempted"},
            produces = {"application/json"}
    )
    public Long taskPreempted(@RequestParam("taskId") Long taskId, @RequestParam("deviceType") int deviceType) {
        try {
            return taskService.taskPreempted(taskId, deviceType);
        } catch (Exception e) {
            log.error("任务抢占失败", e);
            throw new RuntimeException("任务抢占失败", e);
        }
    }

    @Override
    @ApiOperation("分页查询任务")
    @PostMapping(
            value = {"/findTaskByPage"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public TaskPageDTO findTaskByPage(@Valid @RequestBody TaskQueryParam taskQueryParam) {
        try {
            TaskQuery taskQuery = TaskDTOConverter.toOriginal(taskQueryParam);
            TaskVO taskVO = taskService.findTaskByPage(taskQuery);
            return TaskDTOConverter.toClient(taskVO);
        } catch (Exception e) {
            log.error("分页查询任务失败", e);
            throw new RuntimeException("分页查询任务失败", e);
        }
    }

    @Override
    @ApiOperation("查找已检测任务")
    @GetMapping(
            value = {"/findDetectedTasks"},
            produces = {"application/json"}
    )
    public List<TaskDTO> findDetectedTasks(@RequestParam("userId") Long userId) {
        try {
            List<TTask> taskList = taskService.findDetectedTasks(userId);
            return TaskDTOConverter.toClientTaskList(taskList);
        } catch (Exception e) {
            log.error("查找已检测任务失败", e);
            throw new RuntimeException("查找已检测任务失败", e);
        }
    }

    @Override
    @ApiOperation("根据文档ID查找任务")
    @GetMapping(
            value = {"/findByDocumentId"},
            produces = {"application/json"}
    )
    public TaskDTO findByDocumentId(@RequestParam("documentId") String documentId) {
        try {
            TTask task = taskService.findByDocumentId(documentId);
            return TaskDTOConverter.toClient(task);
        } catch (Exception e) {
            log.error("根据文档ID查找任务失败", e);
            throw new RuntimeException("根据文档ID查找任务失败", e);
        }
    }

    @Override
    @ApiOperation("根据ID查找任务")
    @GetMapping(
            value = {"/findById"},
            produces = {"application/json"}
    )
    public TaskDTO findById(@RequestParam("taskId") Long taskId) {
        try {
            TTask task = taskService.findById(taskId);
            return TaskDTOConverter.toClient(task);
        } catch (Exception e) {
            log.error("根据ID查找任务失败", e);
            throw new RuntimeException("根据ID查找任务失败", e);
        }
    }

    @Override
    @ApiOperation("获取任务详情")
    @GetMapping(
            value = {"/getTaskDetail"},
            produces = {"application/json"}
    )
    public TaskDetailDTO getTaskDetail(@RequestParam("id") String id) {
        try {
            TaskDetailVO taskDetailVO = taskService.getTaskDetail(id);
            return TaskDTOConverter.toClient(taskDetailVO);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            throw new RuntimeException("获取任务详情失败", e);
        }
    }

    @Override
    @ApiOperation("根据任务ID获取详情")
    @GetMapping(
            value = {"/getTaskDetailByTaskId"},
            produces = {"application/json"}
    )
    public TaskDetailDTO getTaskDetailByTaskId(@RequestParam("taskId") Long taskId) {
        try {
            TaskDetailVO taskDetailVO = taskService.getTaskDetailByTaskId(taskId);
            return TaskDTOConverter.toClient(taskDetailVO);
        } catch (Exception e) {
            log.error("根据任务ID获取详情失败", e);
            throw new RuntimeException("根据任务ID获取详情失败", e);
        }
    }

    @Override
    @ApiOperation("获取基础信息")
    @GetMapping(
            value = {"/getBaseMessage"},
            produces = {"application/json"}
    )
    public BaseMessageDTO getBaseMessage(@RequestParam("documentId") String documentId) {
        try {
            BaseMessageVO baseMessageVO = taskService.getBaseMessage(documentId);
            return TaskDTOConverter.toClient(baseMessageVO);
        } catch (Exception e) {
            log.error("获取基础信息失败", e);
            throw new RuntimeException("获取基础信息失败", e);
        }
    }

    @Override
    @ApiOperation("获取SDK列表")
    @GetMapping(
            value = {"/getSdkList"},
            produces = {"application/json"}
    )
    public List<SdkDTO> getSdkList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId) {
        try {
            List<SdkVO> sdkList = taskService.getSdkList(documentId, taskId);
            return TaskDTOConverter.toClientList(sdkList);
        } catch (IjiamiApplicationException e) {
            log.error("获取SDK列表失败", e);
            throw new RuntimeException("获取SDK列表失败", e);
        }
    }

    @Override
    @ApiOperation("获取iOS SDK列表")
    @GetMapping(
            value = {"/getIosSDKList"},
            produces = {"application/json"}
    )
    public SdkResponseDTO getIosSDKList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId) {
        try {
            SdkResponseVO sdkResponseVO = taskService.getIosSDKList(documentId, taskId);
            return TaskDTOConverter.toClient(sdkResponseVO);
        } catch (Exception e) {
            log.error("获取iOS SDK列表失败", e);
            throw new RuntimeException("获取iOS SDK列表失败", e);
        }
    }

    @Override
    @ApiOperation("获取API SDK列表")
    @GetMapping(
            value = {"/getAPISdkList"},
            produces = {"application/json"}
    )
    public List<SdkDTO> getAPISdkList(@RequestParam("documentId") String documentId, @RequestParam("taskId") Long taskId) {
        try {
            List<SdkVO> sdkList = taskService.getAPISdkList(documentId, taskId);
            return TaskDTOConverter.toClientList(sdkList);
        } catch (IjiamiApplicationException e) {
            log.error("获取API SDK列表失败", e);
            throw new RuntimeException("获取API SDK列表失败", e);
        }
    }

    @Override
    @ApiOperation("获取可疑SDK列表")
    @GetMapping(
            value = {"/getSuspiciousSdkList"},
            produces = {"application/json"}
    )
    public List<SdkDTO> getSuspiciousSdkList(@RequestParam("taskId") Long taskId) {
        try {
            List<SdkVO> sdkList = taskService.getSuspiciousSdkList(taskId);
            return TaskDTOConverter.toClientList(sdkList);
        } catch (Exception e) {
            log.error("获取可疑SDK列表失败", e);
            throw new RuntimeException("获取可疑SDK列表失败", e);
        }
    }

    @Override
    @ApiOperation("获取热更新SDK")
    @GetMapping(
            value = {"/getHotfixSdk"},
            produces = {"application/json"}
    )
    public List<String> getHotfixSdk(@RequestParam("taskId") Long taskId) {
        try {
            return taskService.getHotfixSdk(taskId);
        } catch (Exception e) {
            log.error("获取热更新SDK失败", e);
            throw new RuntimeException("获取热更新SDK失败", e);
        }
    }

    @Override
    @ApiOperation("删除任务")
    @PostMapping(
            value = {"/deleteByTaskId"},
            produces = {"application/json"}
    )
    public void deleteByTaskId(@RequestParam("taskId") Long taskId, @RequestParam("userId") Long userId, @RequestParam("isAdmin") boolean isAdmin) {
        try {
            taskService.deleteByTaskId(taskId, userId, isAdmin);
        } catch (IjiamiApplicationException e) {
            log.error("删除任务失败", e);
            throw new RuntimeException("删除任务失败", e);
        }
    }

    @Override
    @ApiOperation("批量删除任务")
    @PostMapping(
            value = {"/deleteInTaskId"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void deleteInTaskId(@RequestBody List<Long> taskId, @RequestParam("userId") Long userId, @RequestParam("isAdmin") boolean isAdmin) {
        try {
            taskService.deleteInTaskId(taskId, userId, isAdmin);
        } catch (IjiamiApplicationException e) {
            log.error("批量删除任务失败", e);
            throw new RuntimeException("批量删除任务失败", e);
        }
    }

    @Override
    @ApiOperation("修改任务状态")
    @PostMapping(
            value = {"/changeTaskStatus"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void changeTaskStatus(@Valid @RequestBody cn.ijiami.detection.android.client.dto.TaskChangeStatusDTO taskChangeStatusDTO) {
        try {
            TaskChangeStatusDTO originalDto = TaskDTOConverter.toOriginal(taskChangeStatusDTO);
            taskService.changeTaskStatus(originalDto);
        } catch (Exception e) {
            log.error("修改任务状态失败", e);
            throw new RuntimeException("修改任务状态失败", e);
        }
    }

    @Override
    @ApiOperation("更新进度")
    @PostMapping(
            value = {"/updateProgress"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void updateProgress(@Valid @RequestBody ClientUpdateProgressDTO clientUpdateProgressDTO) {
        try {
            ClientUpdateProgressVO originalVo = TaskDTOConverter.toOriginal(clientUpdateProgressDTO);
            taskService.updateProgress(originalVo);
        } catch (Exception e) {
            log.error("更新进度失败", e);
            throw new RuntimeException("更新进度失败", e);
        }
    }

    @Override
    @ApiOperation("刷新任务排序")
    @PostMapping(
            value = {"/refreshTaskSort"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void refreshTaskSort(@Valid @RequestBody TaskDTO taskParam) {
        try {
            TTask task = TaskDTOConverter.toOriginal(taskParam);
            taskService.refreshTaskSort(task);
        } catch (Exception e) {
            log.error("刷新任务排序失败", e);
            throw new RuntimeException("刷新任务排序失败", e);
        }
    }

    @Override
    @ApiOperation("根据时间查询任务")
    @GetMapping(
            value = {"/findDetectedTasksByTime"},
            produces = {"application/json"}
    )
    public List<String> findDetectedTasksByTime(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime) {
        try {
            return taskService.findDetectedTasksByTime(beginTime, endTime);
        } catch (Exception e) {
            log.error("根据时间查询任务失败", e);
            throw new RuntimeException("根据时间查询任务失败", e);
        }
    }

    @Override
    @ApiOperation("启动iOS动态检测")
    @PostMapping(
            value = {"/startIosDynamicDetect"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void startIosDynamicDetect(@Valid @RequestBody TaskProgressParam taskProgressParam) {
        try {
            TaskProgressQuery taskProgressQuery = TaskDTOConverter.toOriginal(taskProgressParam);
            taskService.startIosDynamicDetect(taskProgressQuery);
        } catch (IjiamiCommandException e) {
            log.error("启动iOS动态检测失败", e);
            throw new RuntimeException("启动iOS动态检测失败", e);
        }
    }

    @Override
    @ApiOperation("停止iOS动态检测")
    @PostMapping(
            value = {"/stopIosDynamicDetect"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public Boolean stopIosDynamicDetect(@Valid @RequestBody TaskProgressParam taskProgressParam) {
        try {
            TaskProgressQuery taskProgressQuery = TaskDTOConverter.toOriginal(taskProgressParam);
            return taskService.stopIosDynamicDetect(taskProgressQuery);
        } catch (IjiamiCommandException e) {
            log.error("停止iOS动态检测失败", e);
            return false;
        }
    }

    @Override
    @ApiOperation("查找MD5")
    @GetMapping(
            value = {"/findMd5"},
            produces = {"application/json"}
    )
    public List<BigDataDTO> findMd5() {
        try {
            List<BigDataVO> bigDataList = taskService.findMd5();
            return TaskDTOConverter.toClientBigDataList(bigDataList);
        } catch (Exception e) {
            log.error("查找MD5失败", e);
            throw new RuntimeException("查找MD5失败", e);
        }
    }

    @Override
    @ApiOperation("根据MD5查找文档ID")
    @GetMapping(
            value = {"/findDocumentId"},
            produces = {"application/json"}
    )
    public TaskDTO findDocumentId(@RequestParam("md5") String md5) {
        try {
            TTask task = taskService.findDocumentId(md5);
            return TaskDTOConverter.toClient(task);
        } catch (Exception e) {
            log.error("根据MD5查找文档ID失败", e);
            throw new RuntimeException("根据MD5查找文档ID失败", e);
        }
    }

    @Override
    @ApiOperation("大数据回调")
    @PostMapping(
            value = {"/bigDataCallback"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void bigDataCallback(@Valid @RequestBody BigDataDTO bigDataDTO) {
        try {
            BigDataVO bigDataVO = TaskDTOConverter.toOriginal(bigDataDTO);
            taskService.bigDataCallback(bigDataVO);
        } catch (Exception e) {
            log.error("大数据回调失败", e);
            throw new RuntimeException("大数据回调失败", e);
        }
    }

    @Override
    @ApiOperation("重庆回调")
    @PostMapping(
            value = {"/chongqinCallback"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void chongqinCallback(@Valid @RequestBody ChongqinDTO chongqinDTO) {
        try {
            ChongqinVO chongqinVO = TaskDTOConverter.toOriginal(chongqinDTO);
            taskService.chongqinCallback(chongqinVO);
        } catch (Exception e) {
            log.error("重庆回调失败", e);
            throw new RuntimeException("重庆回调失败", e);
        }
    }

    @Override
    @ApiOperation("删除个人词汇")
    @PostMapping(
            value = {"/delPersonalWord"},
            produces = {"application/json"}
    )
    public void delPersonalWord(@RequestParam("type") Integer type, @RequestParam("id") Long id) {
        try {
            taskService.delPersonalWord(type, id);
        } catch (IjiamiCommandException e) {
            log.error("删除个人词汇失败", e);
            throw new RuntimeException("删除个人词汇失败", e);
        }
    }

    @Override
    @ApiOperation("IDB回调停止动态检测")
    @PostMapping(
            value = {"/stopDynamicByIdbCallback"},
            produces = {"application/json"}
    )
    public void stopDynamicByIdbCallback(@RequestParam("taskId") Long taskId, @RequestParam("deviceId") String deviceId,
                                         @RequestParam("reason") String reason, @RequestParam("frpcPort") String frpcPort) {
        try {
            taskService.stopDynamicByIdbCallback(taskId, deviceId, reason, frpcPort);
        } catch (Exception e) {
            log.error("IDB回调停止动态检测失败", e);
            throw new RuntimeException("IDB回调停止动态检测失败", e);
        }
    }

    @Override
    @ApiOperation("根据MD5查找完成的检测")
    @GetMapping(
            value = {"/findDetectionCompleteByMd5"},
            produces = {"application/json"}
    )
    public TaskDTO findDetectionCompleteByMd5(@RequestParam("md5") String md5) {
        try {
            TTask task = taskService.findDetectionCompleteByMd5(md5);
            return TaskDTOConverter.toClient(task);
        } catch (Exception e) {
            log.error("根据MD5查找完成的检测失败", e);
            throw new RuntimeException("根据MD5查找完成的检测失败", e);
        }
    }

    @Override
    @ApiOperation("更新SDK权限")
    @PostMapping(
            value = {"/updateSdkPermission"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    public void updateSdkPermission(@Valid @RequestBody List<PrivacyActionNougatDTO> nougatList) {
        try {
            List<TPrivacyActionNougat> originalList = TaskDTOConverter.toOriginalActionList(nougatList);
            taskService.updateSdkPermission(originalList);
        } catch (Exception e) {
            log.error("更新SDK权限失败", e);
            throw new RuntimeException("更新SDK权限失败", e);
        }
    }

    @Override
    @ApiOperation("查找小程序动态检测数量")
    @GetMapping(
            value = {"/findAppletDynamicDetectionCount"},
            produces = {"application/json"}
    )
    public Integer findAppletDynamicDetectionCount(@RequestParam("userId") Long userId, @RequestParam("terminalType") Integer terminalType) {
        try {
            return taskService.findAppletDynamicDetectionCount(userId, terminalType);
        } catch (Exception e) {
            log.error("查找小程序动态检测数量失败", e);
            throw new RuntimeException("查找小程序动态检测数量失败", e);
        }
    }

}
