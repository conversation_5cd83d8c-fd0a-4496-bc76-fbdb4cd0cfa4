package cn.ijiami.detection.feign.controller;

import cn.ijiami.detection.android.client.api.StatisticsServiceApi;
import cn.ijiami.detection.android.client.dto.*;
import cn.ijiami.detection.android.client.param.*;
import cn.ijiami.detection.service.api.StatisticsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionTaskController.java
 * @Description 任务相关接口
 * @createTime 2025年05月12日 18:15:00
 */
@Slf4j
@RequestMapping("/api/detection/")
@Api(value = "/api/detection/", tags = "任务相关接口")
@RestController
public class StatisticsController implements StatisticsServiceApi {

    @Autowired
    private StatisticsService statisticsService;


    @Override
    public AssetsStatisticalSummaryDTO assetsStatisticalSummary(AssetsStatisticsParam param) {
        return statisticsService.assetsStatisticalSummary(param);
    }

    @Override
    public PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(AssetsDetailsParam param) {
        return statisticsService.assetsDetailsByPage(param);
    }
}
