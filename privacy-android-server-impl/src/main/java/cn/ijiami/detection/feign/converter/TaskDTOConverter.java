package cn.ijiami.detection.feign.converter;

import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.ChongqinVO;
import cn.ijiami.detection.VO.ClientUpdateProgressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.android.client.dto.BaseMessageDTO;
import cn.ijiami.detection.android.client.dto.BigDataDTO;
import cn.ijiami.detection.android.client.dto.ChongqinDTO;
import cn.ijiami.detection.android.client.dto.ClientUpdateProgressDTO;
import cn.ijiami.detection.android.client.dto.PrivacyActionNougatDTO;
import cn.ijiami.detection.android.client.dto.SdkDTO;
import cn.ijiami.detection.android.client.dto.SdkResponseDTO;
import cn.ijiami.detection.android.client.dto.TaskDTO;
import cn.ijiami.detection.android.client.dto.TaskDetailDTO;
import cn.ijiami.detection.android.client.dto.TaskParam;
import cn.ijiami.detection.android.client.param.TaskProgressParam;
import cn.ijiami.detection.android.client.param.TaskQueryParam;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDTOConverter.java
 * @Description 任务DTO转换工具类
 * @createTime 2025年06月03日 11:00:00
 */
public class TaskDTOConverter {

    /**
     * 将client版本的TaskChangeStatusDTO转换为原始版本
     * @param clientDto client版本DTO
     * @return 原始版本DTO
     */
    public static TaskChangeStatusDTO toOriginal(cn.ijiami.detection.android.client.dto.TaskChangeStatusDTO clientDto) {
        if (clientDto == null) {
            return null;
        }
        TaskChangeStatusDTO original = new TaskChangeStatusDTO();
        BeanUtils.copyProperties(clientDto, original);
        return original;
    }

    /**
     * 将client版本的BigDataDTO转换为原始版本
     * @param clientDto client版本DTO
     * @return 原始版本VO
     */
    public static BigDataVO toOriginal(BigDataDTO clientDto) {
        if (clientDto == null) {
            return null;
        }
        BigDataVO original = new BigDataVO();
        BeanUtils.copyProperties(clientDto, original);
        return original;
    }

    /**
     * 将原始版本的BigDataVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static BigDataDTO toClient(BigDataVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        BigDataDTO clientDto = new BigDataDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将client版本的ChongqinDTO转换为原始版本
     * @param clientDto client版本DTO
     * @return 原始版本VO
     */
    public static ChongqinVO toOriginal(ChongqinDTO clientDto) {
        if (clientDto == null) {
            return null;
        }
        ChongqinVO original = new ChongqinVO();
        BeanUtils.copyProperties(clientDto, original);
        return original;
    }

    /**
     * 将client版本的ClientUpdateProgressDTO转换为原始版本
     * @param clientDto client版本DTO
     * @return 原始版本VO
     */
    public static ClientUpdateProgressVO toOriginal(ClientUpdateProgressDTO clientDto) {
        if (clientDto == null) {
            return null;
        }
        ClientUpdateProgressVO original = new ClientUpdateProgressVO();
        BeanUtils.copyProperties(clientDto, original);
        return original;
    }

    /**
     * 将原始版本的TaskDetailVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static TaskDetailDTO toClient(TaskDetailVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        TaskDetailDTO clientDto = new TaskDetailDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的TaskVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static TaskDTO toClient(TaskVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        TaskDTO clientDto = new TaskDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的BaseMessageVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static BaseMessageDTO toClient(BaseMessageVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        BaseMessageDTO clientDto = new BaseMessageDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的SdkVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static SdkDTO toClient(SdkVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        SdkDTO clientDto = new SdkDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的SdkVO列表转换为client版本
     * @param originalList 原始版本VO列表
     * @return client版本DTO列表
     */
    public static List<SdkDTO> toClientList(List<SdkVO> originalList) {
        if (originalList == null) {
            return new ArrayList<>();
        }
        return originalList.stream()
                .map(TaskDTOConverter::toClient)
                .collect(Collectors.toList());
    }

    /**
     * 将原始版本的SdkResponseVO转换为client版本
     * @param originalVo 原始版本VO
     * @return client版本DTO
     */
    public static SdkResponseDTO toClient(SdkResponseVO originalVo) {
        if (originalVo == null) {
            return null;
        }
        SdkResponseDTO clientDto = new SdkResponseDTO();
        BeanUtils.copyProperties(originalVo, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的TTask转换为client版本
     * @param originalEntity 原始版本实体
     * @return client版本DTO
     */
    public static TaskParam toClient(TTask originalEntity) {
        if (originalEntity == null) {
            return null;
        }
        TaskParam clientDto = new TaskParam();
        BeanUtils.copyProperties(originalEntity, clientDto);
        return clientDto;
    }

    /**
     * 将原始版本的TTask列表转换为client版本
     * @param originalList 原始版本实体列表
     * @return client版本DTO列表
     */
    public static List<TaskParam> toClientTaskList(List<TTask> originalList) {
        if (originalList == null) {
            return new ArrayList<>();
        }
        return originalList.stream()
                .map(TaskDTOConverter::toClient)
                .collect(Collectors.toList());
    }

    /**
     * 将client版本的TaskParam转换为原始版本
     * @param clientDto client版本DTO
     * @return 原始版本实体
     */
    public static TTask toOriginal(TaskParam clientDto) {
        if (clientDto == null) {
            return null;
        }
        TTask original = new TTask();
        BeanUtils.copyProperties(clientDto, original);
        return original;
    }

    /**
     * 将client版本的TaskProgressParam转换为原始版本
     * @param clientParam client版本参数
     * @return 原始版本查询对象
     */
    public static TaskProgressQuery toOriginal(TaskProgressParam clientParam) {
        if (clientParam == null) {
            return null;
        }
        TaskProgressQuery original = new TaskProgressQuery();
        BeanUtils.copyProperties(clientParam, original);
        return original;
    }

    /**
     * 将client版本的TaskQueryParam转换为原始版本
     * @param clientParam client版本参数
     * @return 原始版本查询对象
     */
    public static TaskQuery toOriginal(TaskQueryParam clientParam) {
        if (clientParam == null) {
            return null;
        }
        TaskQuery original = new TaskQuery();
        BeanUtils.copyProperties(clientParam, original);
        return original;
    }

    /**
     * 将原始版本的TPrivacyActionNougat列表转换为client版本
     * @param originalList 原始版本实体列表
     * @return client版本DTO列表
     */
    public static List<PrivacyActionNougatDTO> toClientActionList(List<TPrivacyActionNougat> originalList) {
        if (originalList == null) {
            return new ArrayList<>();
        }
        return originalList.stream()
                .map(original -> {
                    PrivacyActionNougatDTO clientDto = new PrivacyActionNougatDTO();
                    BeanUtils.copyProperties(original, clientDto);
                    return clientDto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将client版本的PrivacyActionNougatDTO列表转换为原始版本
     * @param clientList client版本DTO列表
     * @return 原始版本实体列表
     */
    public static List<TPrivacyActionNougat> toOriginalActionList(List<PrivacyActionNougatDTO> clientList) {
        if (clientList == null) {
            return new ArrayList<>();
        }
        return clientList.stream()
                .map(clientDto -> {
                    TPrivacyActionNougat original = new TPrivacyActionNougat();
                    BeanUtils.copyProperties(clientDto, original);
                    return original;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将原始版本的BigDataVO列表转换为client版本
     * @param originalList 原始版本VO列表
     * @return client版本DTO列表
     */
    public static List<BigDataDTO> toClientBigDataList(List<BigDataVO> originalList) {
        if (originalList == null) {
            return new ArrayList<>();
        }
        return originalList.stream()
                .map(TaskDTOConverter::toClient)
                .collect(Collectors.toList());
    }

    /**
     * 将原始版本的TTask转换为client版本的TaskDetailDTO
     * @param originalEntity 原始版本实体
     * @return client版本DTO
     */
    public static TaskDetailDTO toClientTaskDetail(TTask originalEntity) {
        if (originalEntity == null) {
            return null;
        }
        TaskDetailDTO clientDto = new TaskDetailDTO();
        BeanUtils.copyProperties(originalEntity, clientDto);
        return clientDto;
    }
}
