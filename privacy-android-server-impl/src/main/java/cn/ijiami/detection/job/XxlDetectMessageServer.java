package cn.ijiami.detection.job;

import static cn.ijiami.detection.constant.PinfoConstant.STATIC_FUNCTION_ANALYSIS_MAX_PROCESS;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.*;
import static cn.ijiami.detection.utils.CommonUtil.analysisErrorMsg;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.ijiami.detection.VO.detection.DetectionResultVO;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.service.impl.BaseDetectionMongoDBDAOImpl;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.UriUtils;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.message.MessageSendKit;
import net.sf.json.JSONObject;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

/**
 * xxl 推送消息处理助手
 *
 * <AUTHOR>
 * @date 2020-08-17 17:57
 */
@Component
public class XxlDetectMessageServer extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> {

    private static final Logger                        LOG = LoggerFactory.getLogger(XxlDetectMessageServer.class);
    private final        TTaskMapper                   taskMapper;
    private final        XxlDetectWebServer            xxlDetectWebServer;
    private final        ISendMessageService           iSendMessageService;
    private final IDynamicAndroidDetectionService dynamicAndroidDetectionService;
    private final IDynamicIOSActionDataService dynamicIosActionDataService;
    private final IDynamicWechatAppletDetectionService dynamicWechatAppletDetectionService;
    private final IDynamicAlipayAppletDetectionService dynamicAlipayAppletDetectionService;

    private final IDynamicHarmonyDetectionService dynamicHarmonyDetectionService;
    private final TaskSortThread taskSortThread;
    private final        IPrivacyOutsideAddressService iPrivacyOutsideAddressService;
    private final        MessageSendKit                messageSendKit;
    private final        ApiPushProgressServer         apiPushProgressServer;
    private final        StaticFunctionAnalysisService staticFunctionAnalysisService;
    private final        TAssetsMapper                 assetsMapper;
    private final        CacheService                  cacheService;
    private final        TaskDAO                       taskDAO;
    private final        DataSourceTransactionManager  dataSourceTransactionManager;
    private final        TransactionDefinition         transactionDefinition;
    private final        ITaskExtendService            taskExtendService;
    private final        ITaskService                  taskService;
    private final        DeviceManagerService          deviceManagerService;
    private final        IPrivacyDetectionService      privacyDetectionService;

    public XxlDetectMessageServer(TTaskMapper taskMapper, XxlDetectWebServer xxlDetectWebServer, ISendMessageService iSendMessageService,
                                  IDynamicAndroidDetectionService dynamicDetectionService, TaskSortThread taskSortThread,
                                  IPrivacyOutsideAddressService iPrivacyOutsideAddressService, MessageSendKit messageSendKit,
                                  ApiPushProgressServer apiPushProgressServer, StaticFunctionAnalysisService staticFunctionAnalysisService, TAssetsMapper assetsMapper,
                                  CacheService cacheService, TaskDAO taskDAO, IDynamicIOSActionDataService dynamicIosActionDataService,
                                  DataSourceTransactionManager transactionManager, TransactionDefinition transactionDefinition,
                                  ITaskExtendService taskExtendService, ITaskService taskService, IDynamicWechatAppletDetectionService dynamicAppletDataService,
                                  DeviceManagerService deviceManagerService, IDynamicAlipayAppletDetectionService dynamicAlipayAppletDetectionService,
                                  IDynamicHarmonyDetectionService dynamicHarmonyDetectionService, IPrivacyDetectionService privacyDetectionService) {
        this.taskMapper = taskMapper;
        this.xxlDetectWebServer = xxlDetectWebServer;
        this.iSendMessageService = iSendMessageService;
        this.dynamicAndroidDetectionService = dynamicDetectionService;
        this.dynamicIosActionDataService = dynamicIosActionDataService;
        this.taskSortThread = taskSortThread;
        this.iPrivacyOutsideAddressService = iPrivacyOutsideAddressService;
        this.messageSendKit = messageSendKit;
        this.apiPushProgressServer = apiPushProgressServer;
        this.staticFunctionAnalysisService = staticFunctionAnalysisService;
        this.assetsMapper = assetsMapper;
        this.cacheService = cacheService;
        this.taskDAO = taskDAO;
        this.dataSourceTransactionManager = transactionManager;
        this.transactionDefinition = transactionDefinition;
        this.taskExtendService = taskExtendService;
        this.taskService = taskService;
        this.dynamicWechatAppletDetectionService = dynamicAppletDataService;
        this.deviceManagerService = deviceManagerService;
        this.dynamicAlipayAppletDetectionService = dynamicAlipayAppletDetectionService;
        this.dynamicHarmonyDetectionService = dynamicHarmonyDetectionService;
        this.privacyDetectionService = privacyDetectionService;
    }

    /**
     * 分析静态检测获取到得消息
     *
     * @param message 获取到的kafka中的消息数据
     * @return
     */
    public boolean analysisStaticDetectionMessage(String message) {
        if (StringUtils.isBlank(message)) {
            return false;
        }
        JSONObject msgJsonObj = JSONObject.fromObject(message);
        // 此字段个人信息定制，与apkId、businessId一致
        Long taskId = msgJsonObj.getLong("privacyDetection");
        LOG.debug("xxl-检测引擎消息捕获，任务ID：{},完整记录：{}", taskId, message);
        TTask task = verifyTaskExistAndRun(taskId);
        if (Objects.isNull(task)) {
            LOG.debug("当前消息无法消费，任务不存在或已中断，任务ID：{},完整记录：{}", taskId, message);
            return false;
        }

        // 1.查询mongodb文档
        TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            LOG.debug("当前消息无法消费，检测详情不存在，任务ID：{},完整记录：{}", taskId, message);
            return false;
        }

        String statusStr = msgJsonObj.getJSONArray("status").get(0).toString();
        if (StringUtils.isBlank(statusStr)) {
            LOG.debug("当前消息无法消费，无效消息数据，任务ID：{},完整记录：{}", taskId, message);
            return false;
        }
        Integer status = Integer.parseInt(statusStr);
        if (XXLMessageStatus.ERROR.value.equals(status)) {
            // 异常中断处理
            handlerExceptionMessage(task, taskDetailVO, msgJsonObj);
        } else if (XXLMessageStatus.FINISH.value.equals(status)) {
            // 正常结束处理
            handleFinishMessage(task, taskDetailVO, msgJsonObj);
        } else if (XXLMessageStatus.PROGRESS.value.equals(status)) {
            // 运行中处理
            handleProgressMessage(task, taskDetailVO, msgJsonObj);
        } else {
            LOG.debug("xxl-无法处理该调消息，任务ID：{},完整记录：{}", taskId, message);
        }
        return true;
    }

    /**
     * 发送任务状态广播
     * @param typeEnum
     * @param describe
     * @param task
     * @param msgJsonObj
     */
    private void sendStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task, JSONObject msgJsonObj) {
        // 广播给前端
        iSendMessageService.sendTaskStatusBroadcast(typeEnum, describe, task);
        // 推送进度给第三方
        apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.STATIC);
    }

    /**
     * 发送任务状态消息
     * @param typeEnum
     * @param describe
     * @param task
     * @param msgJsonObj
     */
    public void sendStatusMessage(BroadcastMessageTypeEnum typeEnum, String describe, TTask task, JSONObject msgJsonObj) {
        // 推送给前端
        iSendMessageService.sendTaskStatusMessage(typeEnum, describe, task);
        // 推送进度给第三方
        apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.STATIC);
    }

    /**
     * 发送任务进度消息
     * @param progress
     * @param task
     * @param msgJsonObj
     */
    private void sendProgressMessage(int progress, TTask task, JSONObject msgJsonObj) {
        // 推送给前端
        iSendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_RUNNING, progress, task);
        // 推送静态检测进度给第三方
        apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.STATIC);
    }


    /**
     * 处理程序异常消息
     *
     * @param task         任务信息
     * @param taskDetailVO 任务详情
     * @param msgJsonObj   接收到的消息
     * @return
     */
    private void handlerExceptionMessage(TTask task, TaskDetailVO taskDetailVO, JSONObject msgJsonObj) {
        // 错误描述
        String errorInfo = (String) msgJsonObj.getJSONArray("error").get(0);
        if (task.getTerminalType().isApplet()) {
            messageSendKit.sendReleaseDeviceMsgToXxlDetection(task);
            if (isConnectDeviceError(errorInfo)) {
                try {
                    String key = PinfoConstant.CACHE_STATIC_DETECTION_RETRY_COUNT_PREFIX + task.getTaskId();
                    String retryCountString = cacheService.get(key);
                    int retryCount = Objects.isNull(retryCountString) ? 0 : Integer.parseInt(retryCountString);
                    if (retryCount < PinfoConstant.TASK_DEVICE_TIMEOUT_RETRY_COUNT) {
                        // 等待一段时间再重新进入等待检测中
                        Thread.sleep(3000L + retryCount * 1000L);
                        LOG.info("TaskId:{}  手机设备获取异常 重新进入任务等待队列", task.getTaskId());
                        updateStaticTaskWaiting(task);
                        cacheService.set(key, String.valueOf(retryCount + 1), 60L, TimeUnit.MINUTES);
                    } else {
                        LOG.info("TaskId:{}  手机设备获取异常 重试次数太多", task.getTaskId());
                        updateStaticTaskFailure(task, errorInfo + " 已重试超过" + retryCount + "次", msgJsonObj);
                        cacheService.delete(key);
                    }
                } catch (Exception e) {
                    LOG.error("TaskId:{}  任务重试处理失败", task.getTaskId(), e);
                    updateStaticTaskFailure(task, errorInfo, msgJsonObj);
                }
            } else {
                updateStaticTaskFailure(task, errorInfo, msgJsonObj);
            }
        } else {
            updateStaticTaskFailure(task, errorInfo, msgJsonObj);
        }
    }

    private void updateStaticTaskFailure(TTask task, String errorInfo, JSONObject msgJsonObj) {
        taskDAO.updateStaticFailure(task, errorInfo);
        taskSortThread.checkTaskSortListAsSoonASPossible();
        // 发送广播消息
        sendStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, errorInfo, task, msgJsonObj);
    }

    private void updateStaticTaskWaiting(TTask task) {
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_OVER
                || task.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP) {
            LOG.info("TaskId:{} 不会重新回到等待队列 任务状态={}", task.getTaskId(), task.getTaskTatus());
            return;
        }
        taskDAO.updateStaticStatusGoBackWaiting(task);
    }

    private static boolean isConnectDeviceError(String error) {
        return error.contains("连接不到手机") || error.contains("获取不到手机设备");
    }

    /**
     * 处理完成消息
     *
     * @param task         任务信息
     * @param taskDetailVO 任务详情
     * @param msgJsonObj   接收到的消息
     * @return
     */
    private void handleFinishMessage(TTask task, TaskDetailVO taskDetailVO, JSONObject msgJsonObj) {
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_OVER) {
            LOG.info("handleFinishMessage 检测任务已经完成，不用再进行处理");
            return;
        } else if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP) {
            LOG.info("handleFinishMessage 检测任务已经失败，不用再进行处理");
            return;
        }
        // 静态检测完成只是85进度，后面还有静态函数分析
        TerminalTypeEnum terminal = task.getTerminalType();
        // 组装返回结果改掉进度，Android的静态检测完成并不是静态检测的100进度，后面还有静态函数分析
        Integer progress = getProgress(msgJsonObj, task.getTerminalType());
        LOG.info("handleFinishMessage 进度={}", progress);
        // 更新任务详情
        DetectionResultVO detectionResult = null;
        if (terminal == TerminalTypeEnum.ANDROID) {
            detectionResult = xxlDetectWebServer.getAndroidDetectionResult(taskDetailVO.getMd5());
        }
        if (terminal == TerminalTypeEnum.IOS) {
            detectionResult = xxlDetectWebServer.getIosDetectionResult(taskDetailVO.getMd5());
        }
        if (terminal.isApplet()) {
            detectionResult = xxlDetectWebServer.getAppletDetectionResult(terminal, taskDetailVO.getMd5());
        }
        if (terminal == TerminalTypeEnum.HARMONY) {
            detectionResult = xxlDetectWebServer.getHarmonyDetectionResult(taskDetailVO.getMd5());
        }
        if (detectionResult == null) {
            taskDAO.updateStaticFailure(task, "获取分析结果失败");
            return;
        }
        if (StringUtils.isNotBlank(detectionResult.getDumpZipUrl())) {
            assetsMapper.updateDumpZipUrlById(
                    UriUtils.getHttpPath(detectionResult.getDumpZipUrl()),
                    detectionResult.getEncrypt() ? PackerStatusEnum.SHELLING.getValue() : PackerStatusEnum.SHELL_LESS.getValue(),
                    task.getAssetsId());
        }
        // 只有Android才是在静态检测完成后进行静态函数分析
        if (terminal == TerminalTypeEnum.ANDROID) {
            taskDAO.updateAndroidStaticFunctionAnalysis(task, XxlDetectAnalysisHelper.analysis(terminal, detectionResult.getApkDetectionEndTime(), detectionResult.getResult()));
            TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
            if (assets != null) {
                staticFunctionAnalysisService.startAnalysis(task, assets);
            } else {
                LOG.warn("找不到资产 无法进行静态函数分析 {} {}", task.getTaskId(), task.getAssetsId());
            }

            JSONArray msgJsonStatus = new JSONArray();
            msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_RUNNING.getValue());
            msgJsonObj.put("progress", progress);
            msgJsonObj.put("status", msgJsonStatus);
            msgJsonObj.put("desc", "静态函数分析中");

            sendProgressMessage(progress, task, msgJsonObj);
        } else {
            if (terminal.isApplet()) {
                messageSendKit.sendReleaseDeviceMsgToXxlDetection(task);
                if (terminal == TerminalTypeEnum.WECHAT_APPLET) {
                    dynamicWechatAppletDetectionService.analysisStatic(task, detectionResult.getAppletResult());
                } else if (terminal == TerminalTypeEnum.ALIPAY_APPLET) {
                    dynamicAlipayAppletDetectionService.analysisStatic(task, detectionResult.getAppletResult());
                }
            } else {
                taskDAO.updateStaticSuccess(task.getTaskId(),
                        XxlDetectAnalysisHelper.analysis(terminal, detectionResult.getApkDetectionEndTime(), detectionResult.getResult()));
            }
            JSONArray msgJsonStatus = new JSONArray();
            msgJsonStatus.add(ThirdPartyMessageTypeEnum.DETECTION_FINISH.getValue());
            msgJsonObj.put("progress", progress);
            msgJsonObj.put("status", msgJsonStatus);
            msgJsonObj.put("desc", "静态检测完成");
            sendStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_FINISH, StringUtils.EMPTY, task, msgJsonObj);
            // 有可能动态检测已经完成，调用一次生成默认报告
            privacyDetectionService.buildAndUploadDefaultReport(task.getTaskId());
        }
        // 如果动态检测已经完成，说明统计数据已经录入，需要重新录入静态检测的数据，以免错漏
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED && terminal == TerminalTypeEnum.IOS) {
        	dynamicIosActionDataService.restoreSdkDectDetail(task);
        }
        // 静态检测完成，更新t_privacy_outside_address表数据信息
        iPrivacyOutsideAddressService.updateOutSideWhenStaticComplete(task.getTaskId(), taskDetailVO.getId());
        taskSortThread.checkTaskSortListAsSoonASPossible();
    }

    /**
     * 处理进度消息
     *
     * @param task         任务信息
     * @param taskDetailVO 任务详情
     * @param msgJsonObj   接收到的消息
     * @return
     */
    private void handleProgressMessage(TTask task, TaskDetailVO taskDetailVO, JSONObject msgJsonObj) {
        // 进度数据
        Integer newProgress = getProgress(msgJsonObj, task.getTerminalType());
        LOG.info("handleProgressMessage 进度={}", newProgress);
        // 更新任务信息
        String msg = msgJsonObj.optString("desc");
        if (task.getTerminalType().isApplet()) {
            if (CommonUtil.isAppletLoginApp(msg)) {
                sendStatusMessage(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_NOT_LOGGED_IN, msg, task, msgJsonObj);
            } else if (CommonUtil.isAppletLogoutApp(msg)) {
                sendStatusMessage(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_LOGOUT, msg, task, msgJsonObj);
            }
            if (newProgress == 0) {
                // 关闭前端等待设备窗口
                iSendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING, "", task, 6000);
            }
            taskDAO.updateStaticProgress(task, msgJsonObj.optString("serial"), msgJsonObj.optString("token"), newProgress);
        } else {
            taskDAO.updateStaticProgress(task, StringUtils.EMPTY, StringUtils.EMPTY, newProgress);
        }
        // 发送广播消息
        sendProgressMessage(newProgress, task, msgJsonObj);
    }

    private Integer getProgress(JSONObject msgJsonObj, TerminalTypeEnum terminalTypeEnum) {
        Integer xxlProgress = (Integer) msgJsonObj.getJSONArray("progress").get(0);
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            Double xxlProgressPercentage = (100 - STATIC_FUNCTION_ANALYSIS_MAX_PROCESS) / 100.0;
            return Math.max(1, (int) (xxlProgress * xxlProgressPercentage));
        } else {
            return xxlProgress;
        }
    }

    /**
     * 验证任务是否存在并运行
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    private TTask verifyTaskExistAndRun(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        // 处理消息与系统数据延迟的问题
        if (Objects.isNull(task) || task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException e) {
                e.getMessage();
            }
            task = taskMapper.selectByPrimaryKey(taskId);
        }
        // 任务已经中断，不再更新数据
        if (Objects.isNull(task)
                || task.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP
                || task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART) {
            return null;
        }
        return task;
    }

    /**
     * 分析动态检测获取到得消息
     *
     * @param message
     * @return
     */
    public boolean analysisDynamicDetectionMessage(String message,Acknowledgment acknowledgment) {
        try {
            if (StringUtils.isBlank(message)) {
                return false;
            }
            JSONObject msgJsonObj = JSONObject.fromObject(message);
            Long taskId = msgJsonObj.getLong("privacyDetection");
            int status = msgJsonObj.getInt("status");
            LOG.info("TaskId:{} dynamic cloud msg:{}", taskId, message);
            if (XXLMessageStatus.ERROR.value.equals(status)) {
                handleDynamicExceptionMessage(taskId, msgJsonObj);
            } else if (XXLMessageStatus.FINISH.value.equals(status)) {
                handleDynamicFinishMessage(taskId, msgJsonObj);
            } else if (XXLMessageStatus.PROGRESS.value.equals(status)) {
                handleDynamicProgressMessage(taskId, msgJsonObj);
            } else if (XXLMessageStatus.STAGED_DATA.value.equals(status)) {
                handleDynamicStagedDataMessage(taskId, msgJsonObj);
            } else {
                LOG.info("TaskId:{} 无法处理该调消息", taskId);
            }
            if (acknowledgment != null){
                acknowledgment.acknowledge();
            }
        } catch (Exception e) {
            LOG.error("analysisDynamicDetectionMessage error", e);
        }
        return true;
    }

    private void handleDynamicExceptionMessage(Long taskId, JSONObject msgJsonObj) {
        TransactionStatus transactionStatus = null;
        try {
            transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            // 开启悲观锁锁住任务记录，避免多条失败的消息重复处理
            TTask task = taskMapper.findByTaskIdForUpdate(taskId);
            if (task == null) {
                LOG.error("TaskId:{} 任务不存在", taskId);
                dataSourceTransactionManager.commit(transactionStatus);
                return;
            }
            // 停止xxl检测
            messageSendKit.stopXxlDynamicDetectionTask(task);
            if (task.getDynamicStatus() == DETECTION_AUTO_FAILED) {
                LOG.info("TaskId:{} 动态检测任务已经失败", taskId);
                dataSourceTransactionManager.commit(transactionStatus);
                return;
            }
            String errorMsg;
            if (msgJsonObj.containsKey("desc")) {
                errorMsg = msgJsonObj.getString("desc");
            } else {
                errorMsg = msgJsonObj.optString("error", StringUtils.EMPTY);
            }
            if (isConnectDeviceError(errorMsg) || errorMsg.contains("检测异常") || errorMsg.contains("检测失败") || errorMsg.contains("用户自己中断") ||
            		errorMsg.contains("其他异常") || errorMsg.contains("文件下载异常") || errorMsg.contains("数据包保存失败") || errorMsg.contains("检测执行失败") 
            		|| errorMsg.contains("连接不到") || errorMsg.contains("安装失败") || errorMsg.contains("获取不到应用") ) {
                String key = PinfoConstant.CACHE_DYNAMIC_DETECTION_RETRY_COUNT_PREFIX + task.getTaskId();
                String retryCountString = cacheService.get(key);
                int retryCount = Objects.isNull(retryCountString) ? 0 : Integer.parseInt(retryCountString);
                if (retryCount < PinfoConstant.TASK_DEVICE_TIMEOUT_RETRY_COUNT) {
                    // 等待一段时间再重新进入等待检测中
                    Thread.sleep(3000L + retryCount * 1000L);
                    LOG.info("TaskId:{}  手机设备获取异常 重新进入任务等待队列", task.getTaskId());
                    updateDynamicTaskWaiting(task);
                    cacheService.set(key, String.valueOf(retryCount + 1), 60L, TimeUnit.MINUTES);
                    dataSourceTransactionManager.commit(transactionStatus);
                    return;
                } else {
                    LOG.info("TaskId:{}  手机设备获取异常 重试次数太多", task.getTaskId());
                    updateDynamicTaskFailure(task, errorMsg + " 已重试超过" + retryCount + "次");
                    messageSendKit.sendReleaseDeviceMsgToXxlDetection(task);
                    cacheService.delete(key);
                }
            } else {
                updateDynamicTaskFailure(task, errorMsg);
                messageSendKit.sendReleaseDeviceMsgToXxlDetection(task);
            }
            taskSortThread.checkTaskSortListAsSoonASPossible();
            // 推送动态检测进度给第三方
            apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.DYNAMIC);
            dataSourceTransactionManager.commit(transactionStatus);
        } catch (Throwable t) {
            LOG.error("TaskId:{} 处理动态检测状态失败", taskId, t);
            if (Objects.nonNull(transactionStatus) && !transactionStatus.isCompleted()) {
                dataSourceTransactionManager.rollback(transactionStatus);
            }
        }
    }

    private void updateDynamicTaskWaiting(TTask task) {
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
            LOG.info("TaskId:{} 不会重新回到等待队列 任务状态={}", task.getTaskId(), task.getDynamicStatus());
            return;
        }
        taskDAO.updateDynamicAutoStatusGoBackWaiting(task);
        JSONObject msg = new JSONObject();
        msg.put("describe", "获取不到手机设备，重新进入任务等待队列");
        msg.put("messageType", BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING.getValue());
        msg.put("taskId", task.getTaskId());
        iSendMessageService.sendBroadcast(msg.toString(), task);
    }

    private void updateDynamicTaskFailure(TTask task, String errorMsg) {
        taskDAO.updateDynamicFailure(task, errorMsg);

        JSONObject msg = new JSONObject();
        msg.put("messageType", BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR.getValue());
        msg.put("taskId", task.getTaskId());
        msg.put("describe", errorMsg);
        iSendMessageService.sendBroadcast(msg.toString(), task);
    }

    private void handleDynamicProgressMessage(Long taskId, JSONObject msgJsonObj) {
        TransactionStatus transactionStatus = null;
        try {
            transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            TTask task = taskMapper.findByTaskIdForUpdate(taskId);
            if (task == null) {
                LOG.error("TaskId:{} 任务不存在", taskId);
                dataSourceTransactionManager.commit(transactionStatus);
                return;
            }
            if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED) {
                LOG.info("TaskId:{} 动态检测任务已经完成", taskId);
                dataSourceTransactionManager.commit(transactionStatus);
                return;
            }
            if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
                LOG.debug("TaskId:{} status = DETECTION_AUTO_IN ignore message:{}", task.getTaskId(), msgJsonObj.toString());
                dataSourceTransactionManager.commit(transactionStatus);
                return;
            }
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
            int oldProgress = Objects.isNull(taskDetailVO.getDynamicProgress()) ? 0 : taskDetailVO.getDynamicProgress().intValue();
            // 推送过来的进度消息如果没有进度值则取任务的旧进度值
            int progress = msgJsonObj.optInt("progress", oldProgress);
            // 限制进度的最高数字不能超过99
            if (progress >= 100) {
                progress = 99;
                msgJsonObj.put("progress", progress);
            }

            String msg = null;
            if (msgJsonObj.containsKey("desc")) {
                msg = msgJsonObj.getString("desc");
            }

            BroadcastMessageTypeEnum messageTypeEnum = BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING;
            if (task.getTerminalType().isApplet()) {
                if (CommonUtil.isAppletLoginApp(msg)) {
                    sendStatusMessage(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_NOT_LOGGED_IN, msg, task, msgJsonObj);
                } else if (CommonUtil.isAppletLoginApp(msg)) {
                    sendStatusMessage(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_LOGOUT, msg, task, msgJsonObj);
                }
            } else if (CommonUtil.isAIDetectLoginApp(msg)) {
                sendStatusMessage(BroadcastMessageTypeEnum.DETECTION_AI_DETECT_NOT_LOGGED_IN, msg, task, msgJsonObj);
            }

            taskDAO.updateDynamicAutoProgress(task,
                    msgJsonObj.optString("serial"),
                    msgJsonObj.optString("token"),
                    progress,
                    taskDetailVO.getDynamicProgress().intValue(), msgJsonObj);
            dataSourceTransactionManager.commit(transactionStatus);
            iSendMessageService.sendTaskProgressBroadcast(messageTypeEnum, progress, task);
            // 推送动态检测进度给第三方
            apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.DYNAMIC);
        } catch (Throwable t) {
            LOG.error(String.format("TaskId:%s 处理动态检测状态失败", taskId), t);
            if (Objects.nonNull(transactionStatus) && !transactionStatus.isCompleted()) {
                dataSourceTransactionManager.rollback(transactionStatus);
            }
        }
    }

    private void handleDynamicStagedDataMessage(Long taskId, JSONObject msgJsonObj) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            LOG.error("TaskId:{} 任务不存在", taskId);
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED) {
            LOG.info("TaskId:{} 动态检测任务阶段数据完成", taskId);
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_FAILED) {
            LOG.info("TaskId:{} 动态检测已经中断", taskId);
            return;
        }
        String manualResultPath = msgJsonObj.getString("manual_result_path");
        String stage = msgJsonObj.getString("stage");
        try {
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                dynamicAndroidDetectionService.analysisAutoFromXXLData(task.getTaskId(),
                        IdbStagedDataEnum.getItem(Integer.parseInt(stage)), UriUtils.getHttpPath(manualResultPath));
            } else if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) {
                dynamicWechatAppletDetectionService.analysisAutoFromXXLData(task.getTaskId(),
                        IdbStagedDataEnum.getItem(Integer.parseInt(stage)), UriUtils.getHttpPath(manualResultPath));
            } else if (task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
                dynamicAlipayAppletDetectionService.analysisAutoFromXXLData(task.getTaskId(),
                        IdbStagedDataEnum.getItem(Integer.parseInt(stage)), UriUtils.getHttpPath(manualResultPath));
            } else if (task.getTerminalType() == TerminalTypeEnum.HARMONY) {
                dynamicHarmonyDetectionService.analysisAutoFromXXLData(task.getTaskId(),
                        IdbStagedDataEnum.getItem(Integer.parseInt(stage)), UriUtils.getHttpPath(manualResultPath));
            }
        } catch (Exception e) {
            LOG.error("TaskId:{} handleDynamicStagedDataMessage error", task.getTaskId(), e);
        }
    }

    private void handleDynamicFinishMessage(Long taskId, JSONObject msgJsonObj) {
        // 解析数据的函数中会使用分布式锁去避免重复解析，这里就不用加悲观锁了
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            LOG.error("TaskId:{} 任务不存在", taskId);
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED) {
            LOG.info("TaskId:{} 动态检测任务已经完成", taskId);
            return;
        }
        LOG.info("TaskId:{} 动态检测完成", task.getTaskId());
        try {
            // 发送释放设备的消息到xxl-detection，避免任务检测完后设备还未释放
            messageSendKit.sendReleaseDeviceMsgToXxlDetection(task);
            taskExtendService.updatePushStatus(task.getTaskId(), PushStatusEnum.PUSH_NOSTART);
            // 推送动态检测进度给第三方
            apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.DYNAMIC);
        } catch (Throwable e) {
            LOG.error("TaskId:{} handleDynamicFinishMessage error", task.getTaskId(), e);
            updateDynamicTaskFailure(task, analysisErrorMsg(e));
            msgJsonObj.put("status", ThirdPartyMessageTypeEnum.DETECTION_ERROR.getValue());
            // 推送动态检测进度给第三方
            apiPushProgressServer.pushProgress(task, msgJsonObj, DetectionTypeEnum.DYNAMIC);
        }
    }

}
