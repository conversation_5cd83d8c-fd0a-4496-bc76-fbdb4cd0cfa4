package cn.ijiami.detection.bigdataLoginTools.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

public class SignUtil {
    private static final Logger log = LoggerFactory.getLogger(SignUtil.class);
    private static final String UTC_PATTERN = "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z";
    public static final String CLIENT_ID = "client_1";

    public SignUtil() {
    }

    public static boolean verifySign(SortedMap<String, Object> params, Map<String, String> headerParams, StringBuffer requestUrl) {
        String urlSign = (String)headerParams.get("sign");
        if (StringUtils.isEmpty(urlSign)) {
            return false;
        } else {
            String paramsSign = reParamsSign(params, headerParams, requestUrl);
            return !StringUtils.isEmpty(paramsSign) && urlSign.equals(paramsSign);
        }
    }

    private static String reParamsSign(SortedMap<String, Object> params, Map<String, String> headerParams, StringBuffer requestUrl) {
        String clientId = (String)headerParams.get("clientid");
        String timestamp = (String)headerParams.get("timestamp");
        StringBuilder sb = new StringBuilder();
        String key;
        if (!CollectionUtils.isEmpty(params)) {
            Iterator var6 = params.keySet().iterator();

            while(var6.hasNext()) {
                key = (String)var6.next();
                Object value = params.get(key);
                if (Objects.nonNull(value)) {
                    String signValue = convertValue(value);
                    sb.append(key).append(signValue);
                }
            }
        } else {
            int beginIndex = requestUrl.indexOf("/", 2);
            key = requestUrl.substring(beginIndex);
            sb.append(key);
        }

        String signStr = clientId + sb.toString() + timestamp + clientId;
        return DigestUtils.md5DigestAsHex(signStr.getBytes()).toUpperCase();
    }

    public static void main(String[] args) {
        String str = "/armp-auth/b/e/d/f";
        int beginIndex = str.indexOf("/", 2);
        String substring = str.substring(beginIndex);
        System.out.println(substring);
    }

    private static String reParamsSign2(SortedMap<String, Object> params, Map<String, String> headerParams, StringBuffer requestUrl) {
        String clientId = (String)headerParams.get("clientid");
        String timestamp = (String)headerParams.get("timestamp");
        StringBuilder sb = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            Iterator var6 = params.keySet().iterator();

            while(var6.hasNext()) {
                String key = (String)var6.next();
                Object value = params.get(key);
                if (Objects.nonNull(value)) {
                    String signValue = convertValue(value);
                    sb.append(key).append(signValue);
                }
            }
        } else {
            sb.append(requestUrl);
        }

        String signStr = clientId + sb.toString() + timestamp + clientId;
        return DigestUtils.md5DigestAsHex(signStr.getBytes()).toUpperCase();
    }

    public static String convertValue(Object value) {
        String valueStr = String.valueOf(value);
        if (isJsonString(valueStr)) {
            Object object = JSON.parse(valueStr);
            valueStr = JSON.toJSONString(object, new SerializerFeature[]{SerializerFeature.MapSortField});
        } else if (Pattern.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z", valueStr)) {
            valueStr = convertUtcTime(valueStr);
        }

        return valueStr;
    }

    public static boolean isJsonString(String json) {
        try {
            JSON.parse(json);
            return true;
        } catch (Exception var2) {
            return false;
        }
    }

    public static String convertUtcTime(String value) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            LocalDateTime localDateTime = LocalDateTime.parse(value, dateTimeFormatter);
            value = localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli() + "";
        } catch (Exception var3) {
            log.error("签名验证日期格式转换异常", var3);
        }

        return value;
    }

    public static String generalUrlSign(String urlPath, String timestamp, JSONObject paramObj) {
        try {
            SortedMap<String, Object> params = (SortedMap)JSON.parseObject(paramObj.toJSONString(), TreeMap.class);
            Map<String, String> headerParams = new HashMap(2);
            headerParams.put("clientid", "admin");
            headerParams.put("timestamp", timestamp);
            return reParamsSign(params, headerParams, (new StringBuffer()).append(urlPath));
        } catch (Exception var5) {
            log.error("SignUtil.generalUrlSign++++发生错误++++++++++", var5);
            return "";
        }
    }

    public static String generalUrlSign2(String urlPath, String timestamp, JSONObject paramObj) {
        try {
            SortedMap<String, Object> params = (SortedMap)JSON.parseObject(paramObj.toJSONString(), TreeMap.class);
            Map<String, String> headerParams = new HashMap(2);
            headerParams.put("clientid", "client_1");
            headerParams.put("timestamp", timestamp);
            return reParamsSign(params, headerParams, (new StringBuffer()).append(urlPath));
        } catch (Exception var5) {
            log.error("SignUtil.generalUrlSign++++发生错误++++++++++", var5);
            return "";
        }
    }

    public static String generalReportDownSign(String reportDownUrl) {
        try {
            String url = reportDownUrl.concat("=key");
            String md5DigestAsHex = DigestUtils.md5DigestAsHex(url.getBytes());
            reportDownUrl = URLEncoder.encode(reportDownUrl, "UTF-8");
            String result = URLEncoder.encode(reportDownUrl.concat("=").concat(md5DigestAsHex), "UTF-8");
            log.info("下载报告url签名: " + result);
            return result;
        } catch (Exception var4) {
            log.error("AppDetectionServiceImpl.generalDownSign++++发生错误++++++++++", var4);
            return "";
        }
    }
}