package cn.ijiami.detection.bigdataLoginTools.service;

import cn.ijiami.detection.bigdataLoginTools.exception.CreateSingletonException;
import cn.ijiami.detection.bigdataLoginTools.exception.LoginException;
import cn.ijiami.detection.bigdataLoginTools.util.RsaEncrypt;
import cn.ijiami.detection.bigdataLoginTools.util.SignUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Collections;
import java.util.Iterator;
import java.util.Objects;

/**
 * 大数据系统获取tonken服务
 */
public class HttpAuthenticateServiceImpl {
    private static final Logger log = LoggerFactory.getLogger(HttpAuthenticateServiceImpl.class);
    RestTemplate restTemplate = new RestTemplate();
    private JSONObject tokenResult = null;
    private String username;
    private String password;
    private String url;
    private static HttpAuthenticateServiceImpl instance;
    private int timeoutMin = 90;

    private HttpAuthenticateServiceImpl() {
        if (instance != null) {
            throw new CreateSingletonException("Use getInstance() method to get the single instance of HttpAuthenticateServiceImpl class.");
        }
    }

    public static synchronized HttpAuthenticateServiceImpl getInstance(String username, String password,String url) {
        if (instance == null) {
            instance = new HttpAuthenticateServiceImpl();
        }

        instance.username = username;
        instance.password = password;
        instance.url = url;
        return instance;
    }

    private HttpHeaders putAuthenticate() throws Exception {
        log.info("tokenResult={}",this.tokenResult);
        if (null != this.tokenResult) {
            Boolean success = this.tokenResult.getBoolean("success");
            if(success){
                Long current = System.currentTimeMillis();
                Long getTokenDataStr = (Long)this.tokenResult.getJSONObject("data").get("getTokenDataStr");
                long minut = (current - getTokenDataStr) / 1000L / 60L;
                if (minut > (long)this.timeoutMin) {
                    String refreshToken = (String)this.tokenResult.getJSONObject("data").get("refresh_token");
                    this.tokenResult = this.refresh(refreshToken);
                }
            }else {
                this.tokenResult = this.login();
            }
        } else {
            this.tokenResult = this.login();
        }

        JSONObject oAuth2AccessToken = this.tokenResult.getJSONObject("data");
        log.info("获取token----------------" + this.tokenResult.toString());
        if (Objects.isNull(oAuth2AccessToken)) {
            throw new IjiamiRuntimeException("oAuth2AccessToken is null");
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + oAuth2AccessToken.get("access_token"));
        return httpHeaders;
    }

    public HttpHeaders certificate(String url, JSONObject requestParam) throws Exception {
        requestParam = this.processEmptyValue(requestParam);
        HttpHeaders httpHeaders = this.putAuthenticate();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = SignUtil.generalUrlSign2(url, timestamp, requestParam);
        httpHeaders.add("timestamp", timestamp);
        httpHeaders.add("sign", sign);
        httpHeaders.add("clientid", "client_1");
        return httpHeaders;
    }

    public JSONObject processEmptyValue(JSONObject requestParam) {
        Iterator var2 = requestParam.keySet().iterator();

        while(var2.hasNext()) {
            String key = (String)var2.next();
            Object o = requestParam.get(key);
            if (o instanceof String && StringUtils.isEmpty((String)o)) {
                requestParam.put(key, (Object)null);
            }
        }

        return requestParam;
    }

    private JSONObject login() throws Exception {
        String clientAndSecret = "client_1:123";
        clientAndSecret = "Basic " + Base64.getEncoder().encodeToString(clientAndSecret.getBytes());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", clientAndSecret);
        MultiValueMap<String, String> map = new LinkedMultiValueMap();
        map.put("username", Collections.singletonList(this.username));
        byte[] encrypt = RsaEncrypt.encrypt(RsaEncrypt.loadPublicKeyByStr("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwobv/Z+HSymbQ4G30wQYqP7YqvfoWuwqOqYUTIpLL+HAAinDDnpqltsma82O8m67kDzSI/TxezZvGhwTqQS5q/uOpIsRWz+0UZWiFSvd2aA4nnoIaMqdqVktK4VsPxumRoKP4q7BJQM9/zbkIzifKjwuDQQyp+S9BdVdhzpGefwIDAQAB"), this.password.getBytes());
        map.put("password", Collections.singletonList(Base64.getEncoder().encodeToString(encrypt)));
        map.put("grant_type", Collections.singletonList("password"));
        map.put("scope", Collections.singletonList("read"));
        HttpEntity<MultiValueMap<String, String>> authHttpEntity = new HttpEntity(map, httpHeaders);

        try {
            log.info("this.url={},authHttpEntity={}",this.url,authHttpEntity.toString());
            ResponseEntity<JSONObject> exchange = this.restTemplate.exchange(this.url, HttpMethod.POST, authHttpEntity, JSONObject.class, new Object[0]);
            log.info("exchange={}",exchange.getBody().toJSONString());
            return (JSONObject)exchange.getBody();
        } catch (Exception var7) {
            var7.getMessage();
            log.error("-----------------首次登录认证失败");
            throw new LoginException("首次登录认证失败");
        }
    }

    JSONObject refresh(String refreshToken) throws LoginException {
        MultiValueMap<String, String> map = new LinkedMultiValueMap();
        map.put("client_id", Collections.singletonList("client_1"));
        map.put("client_secret", Collections.singletonList("123"));
        map.put("grant_type", Collections.singletonList("refresh_token"));
        map.put("refresh_token", Collections.singletonList(refreshToken));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> authHttpEntity = new HttpEntity(map, headers);

        try {
            ResponseEntity<JSONObject> exchange = this.restTemplate.exchange(this.url, HttpMethod.POST, authHttpEntity, JSONObject.class, new Object[0]);
            return (JSONObject)exchange.getBody();
        } catch (Exception var8) {
            log.error("----------------------刷新认证失败:" + var8.getMessage());

            try {
                return this.login();
            } catch (Exception var7) {
                var7.getMessage();
                throw new LoginException("-----------------刷新失败后，重新认证依然失败");
            }
        }
    }
}