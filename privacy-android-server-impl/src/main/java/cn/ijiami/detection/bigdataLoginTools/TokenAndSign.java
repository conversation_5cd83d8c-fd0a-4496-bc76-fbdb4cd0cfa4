package cn.ijiami.detection.bigdataLoginTools;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import cn.ijiami.detection.bigdataLoginTools.service.HttpAuthenticateServiceImpl;
import cn.ijiami.detection.utils.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.framework.base.utils.PasswordUtil;

/**
 * <AUTHOR>
 * @description
 * @date 19.8.13
 **/
@Component
public class TokenAndSign {

    private static final Logger LOG = LoggerFactory.getLogger(TokenAndSign.class);

    private static String TOKEN_NAME;
    private static String USERNAME;
    private static String PASSWORD;
    private static String API_URL;

    public TokenAndSign() {

    }

    public TokenAndSign(IjiamiCommonProperties properties){
    	if(properties != null) {
    		API_URL = StringUtils.isEmpty(properties.getProperty("ijiami.bigdata.api_url"))?null:properties.getProperty("ijiami.bigdata.api_url");
        	USERNAME =  StringUtils.isEmpty(properties.getProperty("ijiami.bigdata.username"))?null:properties.getProperty("ijiami.bigdata.username");
        	PASSWORD = StringUtils.isEmpty(properties.getProperty("ijiami.bigdata.password"))?null:properties.getProperty("ijiami.bigdata.password");
            TOKEN_NAME = StringUtils.isEmpty(properties.getProperty("ijiami.bigdata.token_name"))?null:properties.getProperty("ijiami.bigdata.token_name");
    	}
    }


    public String getBigdataToken(){
        String status = "200";
        try{
            String url = API_URL + "/armp-auth/oauth/token";
            HttpAuthenticateServiceImpl authenticateService = HttpAuthenticateServiceImpl.getInstance(USERNAME,PasswordUtil.encode(PASSWORD),url);
            HttpHeaders httpHeaders = authenticateService.certificate(url,new JSONObject());
            if(httpHeaders.isEmpty()){
                status = "500";
            }
            DataContainer.tokenMap.put(TOKEN_NAME,httpHeaders.get("Authorization").get(0));
        }catch (Exception e){
            LOG.error("getBigdataToken error", e);
            status = "500";
        }
        return status;
    }

    /**
     * 调用大数据系统通用接口
     * @param jsonObject 参数
     * @param apiPath 接口地址
     * @return
     */
    public JSONObject appInfo(net.sf.json.JSONObject jsonObject,String apiPath) {
        try{
            //客户端本地部署可能存在无法访问外网情况
            if(StringUtils.isEmpty(API_URL)){
                return null;
            }
            URL apiUrl = new URL(API_URL);
            HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(2000);
            int responseCode = connection.getResponseCode();
            if(responseCode != 200){
                return null;
            }
            if(!getBigdataToken().equals("200")){
                return null;
            }
            String url = API_URL + apiPath;
            LOG.info("appInfo.URL={}",url);
            Map<String,String> heardMap = buildHeaderMap();
            String response = HttpUtils.post(jsonObject,url,heardMap);
            if(response == null){
                return null;
            }
            if(!JSON.isValid(response)){
                return null;
            }
            //LOG.info(response);
            JSONObject json = JSON.parseObject(response);
            return json;
        } catch (IOException e) {
            e.getMessage();
        }
        return null;
    }

    /**
     * 构建请求头
     * @return
     */
    private static Map<String,String> buildHeaderMap(){
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json;charset=utf-8");
        headerMap.put("Authorization",DataContainer.tokenMap.get(TOKEN_NAME));
        headerMap.put("clientid","client_1");
        return headerMap;
    }

    public static void main(String[] args) {
        TokenAndSign invoker = new TokenAndSign(null);


        net.sf.json.JSONObject jsonObject = new net.sf.json.JSONObject();

        jsonObject.put("packageName","com.tencent.mobileqq");
        jsonObject.put("page",1);
        jsonObject.put("rows",10);
        jsonObject.put("appType","1");
        jsonObject.put("listType","32");
        jsonObject.put("packageMd5","554af470ff669454c9dbcae8a5e9a8bf");

        String apiPath = "/armp/es/appinfo/v1/list";
        invoker.appInfo(jsonObject,apiPath);

    }

}
