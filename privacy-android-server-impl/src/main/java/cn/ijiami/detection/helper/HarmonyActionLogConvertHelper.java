package cn.ijiami.detection.helper;

import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.CaptureHostIpAction;
import cn.ijiami.detection.analyzer.parser.HarmonyBehaviorInfoAction;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.ijiami.detection.analyzer.parser.HarmonyBehaviorInfoAction.buildDetailsData;
import static cn.ijiami.detection.analyzer.parser.HarmonyCaptureHostIpAction.*;
import static cn.ijiami.detection.constant.IdbMsgFieldName.*;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonyActionLogConvertHelper.java
 * @Description 日志和行为转换器
 * @createTime 2025年02月18日 09:44:00
 */
@Slf4j
@Component
public class HarmonyActionLogConvertHelper {

    @Autowired
    private IpUtil ipUtil;

    public TPrivacyOutsideAddress buildPrivacyOutsideAddress(JSONObject msgJson, DynamicTaskContext taskContext) {
        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        //IP地址
        String url = msgJson.optString(PARAM_URL, PinfoConstant.DETAILS_EMPTY);
        String content = msgJson.optString(PARAM_CONTENTS, PinfoConstant.DETAILS_EMPTY);
        String port;
        String protocol;
        String ip;
        try {
            URI uri = new URI(url);
            port = uri.getPort() < 0 ? PinfoConstant.DETAILS_EMPTY : String.valueOf(uri.getPort());
            protocol = StringUtils.isEmpty(uri.getScheme()) ? PinfoConstant.DETAILS_EMPTY : uri.getScheme();
            try {
                ip = InetAddress.getByName(uri.getHost()).getHostAddress();
            } catch (UnknownHostException e) {
                ip = PinfoConstant.DETAILS_EMPTY;
            }
        } catch (URISyntaxException e) {
            port = PinfoConstant.DETAILS_EMPTY;
            protocol = PinfoConstant.DETAILS_EMPTY;
            ip = PinfoConstant.DETAILS_EMPTY;
        }
        JSONObject headers = msgJson.getJSONObject(PARAM_HEADERS);
        //域名
        String domain = msgJson.optString(PARAM_HOST);
        String cookie = Objects.isNull(headers) ? PinfoConstant.DETAILS_EMPTY : headers.optString("Cookie");
        //行为发生时间
        long actionTime = Long.parseLong(msgJson.getString(PARAM_QUEST_TIME));
        String city = ipUtil.getAddress(ip);
        outsideAddress.setTaskId(taskContext.getTaskId());
        outsideAddress.setIp(optDetails(ip));
        outsideAddress.setHost(optDetails(domain));
        outsideAddress.setCookie(optDetails(cookie));
        //新增cookie标识，以便前台展示
        outsideAddress.setCookieMark(StringUtils.isNotBlank(cookie) ? BooleanEnum.TRUE.value : BooleanEnum.FALSE.value);
        //协议类型
        outsideAddress.setProtocol(protocol);
        //url地址
        outsideAddress.setUrl(url);
        outsideAddress.setPort(port);
        // 境内外判断
        outsideAddress.setOutside(IpUtil.isOutSide(city));
        // 网络访问数据类型
        outsideAddress.setRequestMethod(PinfoConstant.DETAILS_EMPTY);
        // 请求内容
        outsideAddress.setDetailsData(content);
        outsideAddress.setStackInfo(PinfoConstant.DETAILS_EMPTY);
        // 响应内容
        outsideAddress.setResponseData(PinfoConstant.DETAILS_EMPTY);
        outsideAddress.setActionTime(new Date(actionTime));
        BehaviorStageEnum behaviorStage = BehaviorStageEnum.getItem(msgJson.optInt(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue()));
        outsideAddress.setBehaviorStage(behaviorStage);
        outsideAddress.setStrTime(new Date(actionTime));
        String address = "";
        if (StringUtils.isNotBlank(ip)) {
            if (StringUtils.contains(ip, ":")) {
                // 避免端口获取不到具体地址信息
                address = ipUtil.getAddress(ip.split(":")[0]);
            } else {
                address = ipUtil.getAddress(ip);
            }
        }
        outsideAddress.setAddress(optDetails(address));
        outsideAddress.setCounter(1);
        CaptureHostIpAction.setExecutorInfo(outsideAddress, outsideAddress.getStackInfo(), taskContext.getSdkLibraries(), taskContext.getPackageName(), taskContext.getAppName());
        return outsideAddress;
    }

    public TPrivacyActionNougat buildPrivacyActionNougat(Long taskId, JSONObject msgJson, DynamicTaskContext taskData, PrivacyStatusEnum privacyStatus) {
        TPrivacyActionNougat nougat = new TPrivacyActionNougat();
        long actionTime = HarmonyBehaviorInfoAction.parseTimestamp(msgJson.getString(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME));
        BehaviorStageEnum behaviorStage = BehaviorStageEnum.getItem(
                msgJson.optInt(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue()));
        nougat.setTaskId(taskId);
        // 组装堆栈数据
        nougat.setActionId(msgJson.getLong(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID));
        nougat.setStackInfo(PinfoConstant.DETAILS_EMPTY);
        nougat.setJniStackInfo(PinfoConstant.DETAILS_EMPTY);
        nougat.setDetailsData(buildDetailsData(msgJson.getString(CMD_DATA_LOG_MARK)));
        nougat.setBehaviorStage(behaviorStage);
        nougat.setActionTime(new Date(actionTime));
        nougat.setActionTimeStamp(actionTime);
        // 是否涉及个人隐私
        nougat.setIsPersonal(String.valueOf(privacyStatus.getValue()));
        // 是否是使用APP前触发
        nougat.setType(behaviorStage == BehaviorStageEnum.BEHAVIOR_GRANT);
        BehaviorInfoAction.setExecutorInfo(nougat, nougat.getDetailsData(), nougat.getStackInfo(), nougat.getActionId(),
                taskData.getSdkLibraries(), taskData.getPackageName(), taskData.getAppName());
        return nougat;
    }

}