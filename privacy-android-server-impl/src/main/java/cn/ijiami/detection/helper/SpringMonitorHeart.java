package cn.ijiami.detection.helper;

import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.impl.HitShellServiceImpl;
import cn.ijiami.detection.system.DetectionSecurityManager;
import com.ijiami.ios.config.InitConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.helper.thread.IpaShellMonitorHelper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TIpaShellRecordMapper;
import cn.ijiami.detection.service.api.IShellSendMessage;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;

/**
 * 应用助手监控中心
 *
 * <AUTHOR>
 * @date 2020/6/30 11:26
 **/
public class SpringMonitorHeart implements ApplicationContextAware {

    private static final Logger logger = LoggerFactory.getLogger(SpringMonitorHeart.class);

    public static IjiamiCommonProperties commonProperties;
    public static TIpaShellRecordMapper  ipaShellRecordMapper;
    public static IShellSendMessage      shellSendMessage;
    public static TAssetsMapper          assetsMapper;
    public static ExecutorServiceHelper  executorServiceHelper;
    public static TTaskMapper taskMapper;
    public static HitShellServiceImpl iHitShellService;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringMonitorHeart.ipaShellRecordMapper = applicationContext.getBean(TIpaShellRecordMapper.class);
        SpringMonitorHeart.commonProperties = applicationContext.getBean(IjiamiCommonProperties.class);
        SpringMonitorHeart.shellSendMessage = applicationContext.getBean(IShellSendMessage.class);
        SpringMonitorHeart.assetsMapper = applicationContext.getBean(TAssetsMapper.class);
        SpringMonitorHeart.executorServiceHelper = applicationContext.getBean(ExecutorServiceHelper.class);
        SpringMonitorHeart.taskMapper = applicationContext.getBean(TTaskMapper.class);
        SpringMonitorHeart.iHitShellService = applicationContext.getBean(HitShellServiceImpl.class);
    }

    private void init() {
        // ios解析工具路径初始化
        String rootPath = commonProperties.getProperty("ijiami.framework.rootPath");
        if (StringUtils.isNotBlank(rootPath)) {
            InitConfig.configRootPath(rootPath);
        }
        System.setSecurityManager(new DetectionSecurityManager());
        IpaShellMonitorHelper.getInstance().start();
        logger.info("IpaShellMonitorHelper - 应用工具监测中心初始化完成...");
        CustomDetectHelper.getInstance().init();
        logger.info("CustomDetectHelper - 云手机切换助手初始化完成...");
    }

    private void destroy() {
        IpaShellMonitorHelper.getInstance().toStop();
        logger.info("IpaShellMonitorHelper - 应用工具监测中心销毁完成...");
        CustomDetectHelper.getInstance().destroy();
        logger.info("CustomDetectHelper - 云手机切换助手销毁完成...");
    }

}
