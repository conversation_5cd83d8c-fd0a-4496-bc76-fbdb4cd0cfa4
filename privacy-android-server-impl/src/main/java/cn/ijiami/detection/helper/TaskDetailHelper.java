package cn.ijiami.detection.helper;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.entity.TIosDeviceConnection;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.IdbTypeEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDetailHelper.java
 * @Description 任务详情工具类
 * @createTime 2023年05月15日 18:08:00
 */
public class TaskDetailHelper {

    public static void setTaskDetailVoManualTaskExt(TaskDetailVO taskDetailVO, TTask task, Optional<TIosDeviceConnection> connection) {
        if (task.getTerminalType() == TerminalTypeEnum.IOS
                && task.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()
                && task.isDynamicTask()
                && IdbTypeEnum.WIFI.getValue().toString().equals(taskDetailVO.getIdb_type())) {
            if (connection.isPresent()) {
                taskDetailVO.setIdb_address(connection.get().getAddress());
                taskDetailVO.setIdb_device_id(connection.get().getDeviceId());
            }
        }
        // 小程序任务去掉md5和包名
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.WECHAT_APPLET.getValue()
                || taskDetailVO.getTerminal_type() == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
            taskDetailVO.setMd5(StringUtils.EMPTY);
            taskDetailVO.setApk_package(StringUtils.EMPTY);
        }
    }

}
