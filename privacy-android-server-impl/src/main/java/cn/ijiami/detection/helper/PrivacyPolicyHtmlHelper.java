package cn.ijiami.detection.helper;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.helper.bean.PrivacyPolicyHtmlInfo;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.utils.HtmlDomUtils;
import cn.ijiami.detection.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyPolicyHtmlHelper.java
 * @Description
 * @createTime 2021年12月01日 17:09:00
 */
@Slf4j
public class PrivacyPolicyHtmlHelper {

    /**
     *
     * @param dirPath
     * @return
     */
    public static List<PrivacyPolicyTextInfo> getSubPagePrivacyDetailByHtmlDir(String dirPath,
                                                                               String privacyHtmlRegex,
                                                                               String thirdPartySdkTableSdkNameRegex,
                                                                               String thirdPartySdkTablePermissionRegex) {
        File privacyHtmlDir = new File(dirPath);
        return getSubPagePrivacyDetailHtml(privacyHtmlDir)
                .stream()
                .map(info -> buildTextInfo(info, privacyHtmlRegex, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex))
                .filter(info -> !info.content.isEmpty())
                .filter(StreamUtils.distinctByKey((Function<PrivacyPolicyTextInfo, String>) privacyPolicyHtmlInfo -> privacyPolicyHtmlInfo.content))
                .collect(Collectors.toList());
    }

    private static PrivacyPolicyTextInfo buildTextInfo(PrivacyPolicyHtmlInfo info,
                                                       String privacyHtmlRegex,
                                                       String thirdPartySdkTableSdkNameRegex,
                                                       String thirdPartySdkTablePermissionRegex) {
        org.jsoup.nodes.Document doc = Jsoup.parse(info.content);
        String text = getPrivacyDetailByHtml(doc.body(), privacyHtmlRegex);
        Elements tables = doc.select("table");
        return new PrivacyPolicyTextInfo(info.fileName, text,
                filterThirdPartySdkList(tables, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex));
    }

    public static List<Element> filterThirdPartySdkList(Elements tables, String thirdPartySdkTableSdkNameRegex, String thirdPartySdkTablePermissionRegex) {
        if (tables == null) {
            return null;
        }
        return tables.stream().filter(table -> {
            Elements ths = tables.select("th");
            if (ths != null) {
                return ths.stream().anyMatch(td -> Pattern.compile(thirdPartySdkTableSdkNameRegex).matcher(td.text()).find())
                        && ths.stream().anyMatch(td -> Pattern.compile(thirdPartySdkTablePermissionRegex).matcher( td.text()).find());
            }
            Elements tds = tables.select("td");
            if (tds == null) {
                return false;
            }
            return tds.stream()
                    .filter(td -> Pattern.compile(thirdPartySdkTableSdkNameRegex).matcher(td.text()).find())
                    .anyMatch(td -> Pattern.compile(thirdPartySdkTablePermissionRegex).matcher( td.text()).find());
        }).collect(Collectors.toList());
    }

    public static List<PrivacyPolicyHtmlInfo> getSubPagePrivacyDetailHtml(File privacyHtmlDir) {
        if (!privacyHtmlDir.exists() || !privacyHtmlDir.isDirectory()) {
            return Collections.emptyList();
        }
        File[] privacyHtmlFiles = privacyHtmlDir.listFiles();
        if (Objects.isNull(privacyHtmlFiles)) {
            return Collections.emptyList();
        }
        List<PrivacyPolicyHtmlInfo> privacyHtmlTextList = new ArrayList<>();
        for (File privacyHtmlFile:privacyHtmlFiles) {
            if (privacyHtmlFile.isDirectory()) {
                privacyHtmlTextList.addAll(getSubPagePrivacyDetailHtml(privacyHtmlFile));
            } else if (privacyHtmlFile.getName().contains(".html")){
                loadHtmlText(privacyHtmlTextList, privacyHtmlFile);
            }
        }
        return privacyHtmlTextList;
    }

    public static Set<String> getSubPagePrivacyDetailUrl(File privacyUrlDir, String privacyUrlRegex) {
        Set<String> privacyUrlSet = new HashSet<>();
        if (!privacyUrlDir.exists() || !privacyUrlDir.isDirectory()) {
            return Collections.emptySet();
        }
        File[] privacyUrlFiles = privacyUrlDir.listFiles();
        if (Objects.isNull(privacyUrlFiles)) {
            return Collections.emptySet();
        }
        for (File privacyUrlFile:privacyUrlFiles) {
            if (privacyUrlFile.isDirectory()) {
                privacyUrlSet.addAll(getSubPagePrivacyDetailUrl(privacyUrlFile, privacyUrlRegex));
            } else if (privacyUrlFile.getName().contains(".txt")){
                privacyUrlSet.addAll(getPrivacyUrlList(privacyUrlFile, privacyUrlRegex));
            }
        }
        return privacyUrlSet;
    }

    public static void loadHtmlText(List<PrivacyPolicyHtmlInfo> privacyHtmlTextList, File privacyHtmlFile) {
        try {
            privacyHtmlTextList.add(new PrivacyPolicyHtmlInfo(privacyHtmlFile.getName(), FileUtils.readFileToString(privacyHtmlFile, "UTF-8")));
        } catch (IOException e) {
            log.info("读取html文件失败 message={}", e.getMessage());
        }
    }

    public static List<String> getPrivacyUrlList(File privacyUrlFile, String privacyUrlRegex) {
        try {
            Pattern pattern = Pattern.compile(PinfoConstant.URL_REGEX);
            return FileUtils.readLines(privacyUrlFile, "UTF-8")
                    .stream()
                    .filter(url -> pattern.matcher(url).find())
                    .filter(url -> isPrivacyUrl(url, privacyUrlRegex))
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.info("读取隐私url文件失败 {}", privacyUrlFile.getAbsolutePath());
            return Collections.emptyList();
        }
    }

    public static boolean isPrivacyUrl(String url, String privacyUrlRegex) {
        return Pattern.compile(privacyUrlRegex).matcher(url.toLowerCase()).find();
    }

    public static String getPrivacyDetailByHtml(Element body, String privacyHtmlRegex) {
        String privacyDetail = HtmlDomUtils.extractMainContentHtmlText(body);
        return isPrivacyDetail(privacyDetail, privacyHtmlRegex) ? privacyDetail : StringUtils.EMPTY;
    }

    public static boolean isPrivacyDetail(String privacyDetail, String privacyHtmlRegex) {
        return (privacyDetail.length() > 400 || StringUtils.containsIgnoreCase(privacyDetail, "SDK"))
                && !privacyDetail.contains("security by Cloudflare")
                && isPrivacyHtml(privacyDetail, privacyHtmlRegex);
    }

    private static boolean isPrivacyHtml(String privacyDetail, String privacyHtmlRegex) {
        if (StringUtils.isBlank(privacyHtmlRegex)) {
            return true;
        }
        return Pattern.compile(privacyHtmlRegex, Pattern.CASE_INSENSITIVE)
                .matcher(privacyDetail).find();
    }
}
