package cn.ijiami.detection.helper;

import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.CaptureHostIpAction;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.HttpPacketFormatter;
import cn.ijiami.detection.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AndroidActionLogConvertHelper.java
 * @Description 日志和行为转换器
 * @createTime 2024年08月09日 09:44:00
 */
@Slf4j
@Component
public class AndroidActionLogConvertHelper {

    @Autowired
    private IpUtil ipUtil;

    public TPrivacyOutsideAddress buildPrivacyOutsideAddress(JSONObject msgJson, DynamicTaskContext taskContext) {
        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        String responseContent = msgJson.optString("responseContent");
        //IP地址
        String ip = msgJson.optString("ip");
        //域名
        String domain = msgJson.optString("hostName");
        String cookie = msgJson.optString("cookie");
        //行为发生时间
        long actionTime = msgJson.getLong("activeTime");
        String city = ipUtil.getAddress(ip);
        outsideAddress.setTaskId(taskContext.getTaskId());
        outsideAddress.setIp(optDetails(ip));
        outsideAddress.setHost(optDetails(domain));
        outsideAddress.setCookie(optDetails(cookie));
        //新增cookie标识，以便前台展示
        outsideAddress.setCookieMark(StringUtils.isNotBlank(cookie) ? BooleanEnum.TRUE.value : BooleanEnum.FALSE.value);
        //协议类型
        outsideAddress.setProtocol(optDetails(msgJson.optString("protocol")));
        //url地址
        outsideAddress.setUrl(optDetails(msgJson.optString("url")));
        outsideAddress.setPort(optDetails(msgJson.optString("port")));
        // 境内外判断
        outsideAddress.setOutside(IpUtil.isOutSide(city));
        // 网络访问数据类型
        outsideAddress.setRequestMethod(optDetails(msgJson.optString("methodName")));
        // 请求内容
        outsideAddress.setDetailsData(optDetails(msgJson.optString("captureContent")));
        outsideAddress.setStackInfo(optDetails(CommonUtil.filterPrintString(msgJson.optString("reqStackInfo"))));
        // 响应内容
        outsideAddress.setResponseData(optDetails(CommonUtil.filterPrintString(responseContent)));
        outsideAddress.setActionTime(new Date(actionTime));
        BehaviorStageEnum behaviorStage = BehaviorStageEnum.getItem(msgJson.optInt(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue()));
        outsideAddress.setBehaviorStage(behaviorStage);
        outsideAddress.setStrTime(new Date(actionTime));
        outsideAddress.setAddress(optDetails(ipUtil.getAddressFromIP(ip)));
        outsideAddress.setCounter(1);
        CaptureHostIpAction.setExecutorInfo(outsideAddress, outsideAddress.getStackInfo(), taskContext.getSdkLibraries(), taskContext.getPackageName(), taskContext.getAppName());
        return outsideAddress;
    }

    public TPrivacyActionNougat buildPrivacyActionNougat(Long taskId, JSONObject msgJson, DynamicTaskContext taskData, PrivacyStatusEnum privacyStatus) {
        TPrivacyActionNougat nougat = new TPrivacyActionNougat();
        long actionTime = msgJson.getLong(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME);
        BehaviorStageEnum behaviorStage = BehaviorStageEnum.getItem(
                msgJson.optInt(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue()));
        nougat.setTaskId(taskId);
        // 组装堆栈数据
        nougat.setActionId(msgJson.getLong(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID));
        nougat.setStackInfo(msgJson.optString(CMD_DATA_ANDROID_JAVA_STACK));
        nougat.setJniStackInfo(msgJson.optString(CMD_DATA_ANDROID_JNI_STACK));
        nougat.setDetailsData(msgJson.optString(CMD_DATA_ANDROID_LOG_DETAILS_DATA));
        nougat.setBehaviorStage(behaviorStage);
        nougat.setActionTime(new Date(actionTime));
        nougat.setActionTimeStamp(actionTime);
        // 是否涉及个人隐私
        nougat.setIsPersonal(String.valueOf(privacyStatus.getValue()));
        // 是否是使用APP前触发
        nougat.setType(behaviorStage == BehaviorStageEnum.BEHAVIOR_GRANT);
        BehaviorInfoAction.setExecutorInfo(nougat, nougat.getDetailsData(), nougat.getStackInfo(), nougat.getActionId(),
                taskData.getSdkLibraries(), taskData.getPackageName(), taskData.getAppName());
        return nougat;
    }
}