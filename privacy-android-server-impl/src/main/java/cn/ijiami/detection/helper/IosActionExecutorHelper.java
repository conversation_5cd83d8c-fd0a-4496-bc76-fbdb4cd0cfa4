package cn.ijiami.detection.helper;

import cn.ijiami.detection.DTO.IosSdkDTO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.analyzer.helper.SpecialActionTypeConvertHelper;
import cn.ijiami.detection.bean.IosExecutable;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TIosFrameworkLibrary;
import cn.ijiami.detection.entity.TIosPrivacyActionSdk;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryClass;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.mapper.TSdkLibraryClassMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.service.api.IosSdkLibraryService;
import cn.ijiami.detection.utils.StreamUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ocpframework.sdk.detection.vo.DiscernVO;
import com.ocpframework.sdk.detection.vo.SdkApiVO;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO.buildSuspiciousSdkBehavior;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosActionExecutorHelper.java
 * @Description ios主体信息分析帮助类
 * @createTime 2023年08月09日 18:12:00
 */
@Slf4j
@Component
public class IosActionExecutorHelper {

    @Autowired
    private IosSdkLibraryService iosSdkLibraryService;

    @Autowired
    private TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    private TSdkLibraryClassMapper sdkLibraryClassMapper;

  //主体过滤-动态库
    public static final String executorFillerReg = "libobjc.A.dylib|sandbao|CoreFoundation|libdispatch.dylib|libsystem_pthread.dylib";

    private final Cache<String, Long> sdkEmptyCache = CacheBuilder.newBuilder().recordStats().expireAfterAccess(5, TimeUnit.MINUTES).build();

    public void setNougatExecutor(ActionExecutor actionExecutor, String stackInfo, String executableName,
                                  String appName, String packageName, Map<String, TIosPrivacyActionSdk> sdkApiVOMap,
                                  Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                  List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList) {
        IosExecutable iosExecutable = findExecutorInfo(actionExecutor.getTaskId(), stackInfo, suspiciousSdkMap, libraryList, classList, sdkApiVOMap, executableName);
        if (iosExecutable.isApp()) {
            actionExecutor.setExecutorType(ExecutorTypeEnum.APP.getValue());
            actionExecutor.setExecutor(appName);
            actionExecutor.setPackageName(packageName);
        } else if (iosExecutable.isSdk()) {
            actionExecutor.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            actionExecutor.setExecutor(iosExecutable.getName());
            actionExecutor.setPackageName(iosExecutable.getPackageName());
        }
    }

    private static String executorDef(String stackInfo){
    	if (StringUtils.isBlank(stackInfo) || DETAILS_EMPTY.equals(stackInfo)) {
            return "";
        }
        try {
            JSONArray jsonArray = JSONArray.fromObject(stackInfo);
            if (jsonArray.size() > 0) {
            	for (int i = 0; i < jsonArray.size(); i++) {
            		String item = jsonArray.getString(i);
                    String[] array = item.split("###");
                    if (array.length > 1) {
                         String executor = array[1];
                         if(StringUtils.isNotBlank(executor) && SpecialActionTypeConvertHelper.isMatcherContent(executor, executorFillerReg)){
                         	continue;
                         }
                         if (executor.endsWith(".dylib")) {
                             continue;
                         }
                         return executor;
                    }
				}

            }
        } catch (Exception e) {
            log.error("stackInfo error={}", e.getMessage(), e);
            return "";
        }
        return "";
    }

    public List<SdkApiVO> findExecutorSdk(String executableName, String stackInfo) {
        List<SdkApiVO> executorSdk = new ArrayList<>();
        // 无法判断的内容才去查询第三方sdk信息
        try {
            Long count = sdkEmptyCache.get(executableName, () -> 0L);
            // 超过50次没查出sdk信息，后续就不再查询，节省时间
            if (count < 50) {
                DiscernVO discernVO = iosSdkLibraryService.findSdkLibraryByStackInfo(stackInfo);
                if (CollectionUtils.isEmpty(discernVO.getDumpApis())) {
                    sdkEmptyCache.put(executableName, count + 1);
                } else {
                    // sdk的调用要比app的调用在前才确定调用主体是SDK
                    executorSdk.addAll(discernVO.getDumpApis().stream()
                            .filter(StreamUtils.distinctByKey(SdkApiVO::getSdkName))
                            .collect(Collectors.toList()));
                    // 清空次数
                    if (count > 0) {
                        sdkEmptyCache.put(executableName, 0L);
                    }
                }
            }
        } catch (ExecutionException e) {
            log.error("ios sdk error", e);
        }
        return executorSdk;
    }

    /**
     * 判断是否系统库调用
     *
     * @param libraryList
     * @param stackInfo
     * @param executableName
     * @return
     */
    public static boolean isSystemLibrary(List<TIosFrameworkLibrary> libraryList, JSONArray stackInfo, String executableName) {
        if (Objects.isNull(stackInfo)) {
            return false;
        }
        for (int i = 0; i < stackInfo.size(); i++) {
            String item = stackInfo.getString(i);
            String[] array = item.split("###");
            if (array.length <= 1) {
                // 数据缺失
                return false;
            }
            String name = array[1];
            if (name.endsWith(".dylib")) {
                continue;
            }
            if(StringUtils.isNotBlank(name) && SpecialActionTypeConvertHelper.isMatcherContent(name, executorFillerReg)){
            	continue;
            }
            String executor = array[1] + ".framework";
            // 一旦遇到有非系统库的名字，可以直接判断为非系统库调用
            if (!matchSystemLibraryName(libraryList, executor)) {
                return false;
            }
            // 如果是系统库的名字，往后面判断是否有动态库调用
            // 如果是系统库的名字，判断下一个调用是APP调用
            // 以上有任意一个符合都算系统库调用
            if (afterHaveDyLib(i + 1, stackInfo) || nextIsExecutableName(i + 1, stackInfo, executableName)) {
                return true;
            }
        }
        return true;
    }

    public static boolean matchSystemLibraryName(List<TIosFrameworkLibrary> libraryList, String executor) {
        return libraryList.stream().anyMatch(lib -> lib.getName().equalsIgnoreCase(executor));
    }

    public static boolean afterHaveDyLib(int start, JSONArray jsonArray) {
        for (int i = start; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            String[] array = item.split("###");
            if (array.length <= 1) {
                continue;
            }
            if (array[1].endsWith(".dylib") || SpecialActionTypeConvertHelper.isMatcherContent(array[1], executorFillerReg)) {
                return true;
            }
        }
        return false;
    }

    public static boolean nextIsExecutableName(int nextIndex, JSONArray jsonArray, String executableName) {
        if (nextIndex < jsonArray.size()) {
            String item = jsonArray.getString(nextIndex);
            String[] array = item.split("###");
            if (array.length > 1) {
                return array[1].equalsIgnoreCase(executableName);
            }
        }
        return false;
    }

    protected IosExecutable findExecutorInfo(Long taskId, String stackInfo, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                             List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList,
                                             Map<String, TIosPrivacyActionSdk> sdkMap, String executableName) {
        if (StringUtils.isBlank(stackInfo) || DETAILS_EMPTY.equals(stackInfo)) {
            return IosExecutable.buildApp(executableName);
        }
        try {
            JSONArray jsonArray = JSONArray.fromObject(stackInfo);
            if (!jsonArray.isEmpty()) {
                List<IosSdkDTO> sdkList = parseSdkList(jsonArray, stackInfo, libraryList, classList, executableName);
                if (!sdkList.isEmpty()) {
                    // 保存匹配到的第三方SDK
                    saveSdkMap(taskId, sdkList, suspiciousSdkMap, sdkMap);
                    String sdkExecutor = sdkList.stream().map(IosSdkDTO::getSdkName).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    String sdkPackageName = sdkList.stream().map(IosSdkDTO::getSdkPackageName).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    return IosExecutable.buildSdk(sdkExecutor, StringUtils.isBlank(sdkPackageName) ? DETAILS_EMPTY : sdkPackageName);
                } else {
                    return IosExecutable.buildApp(executableName);
                }
            }
        } catch (Exception e) {
            log.error("stackInfo error={}", e.getMessage(), e);
            return IosExecutable.buildApp(executableName);
        }
        return IosExecutable.buildApp(executableName);
    }

    private void saveSdkMap(Long taskId, List<IosSdkDTO> sdkList, Map<String, SuspiciousSdkBO> suspiciousSdkMap, Map<String, TIosPrivacyActionSdk> sdkMap) {
        sdkList.forEach(sdk -> {
            if (sdk.getSuspicious()) {
                if (Objects.nonNull(suspiciousSdkMap) && !suspiciousSdkMap.containsKey(sdk.getSdkName())) {
                    suspiciousSdkMap.put(sdk.getSdkName(),
                            new SuspiciousSdkBO(buildSuspiciousSdkBehavior(sdk.getSdkName(), ExecutorTypeEnum.SDK.getValue(), sdk.getSdkName(), taskId)));
                }
            } else {
                if (Objects.nonNull(sdkMap)) {
                    sdkMap.put(sdk.getSdkName(), makeTIosPrivacyActionSdk(taskId, sdk));
                }
            }
        });
    }

    private List<IosSdkDTO> parseSdkList(JSONArray jsonArray, String stackInfo, List<TIosFrameworkLibrary> libraryList,
                                         List<TSdkLibraryClass> classList, String executableName) {
        List<IosSdkDTO> sdkList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            String[] array = item.split("###");
            if (array.length > 1) {
                String executor = array[1];
                if (StringUtils.isNotBlank(executor) && SpecialActionTypeConvertHelper.isMatcherContent(executor, executorFillerReg)){
                    continue;
                }
                // 匹配类名
                String[] classMethod = findClassMethod(array[2]);
                // 是否匹配到自定义sdk
                Optional<TSdkLibraryClass> classOpt = classList.stream()
                        .filter(c -> StringUtils.equalsIgnoreCase(c.getClazz(), classMethod[0]) && StringUtils.equalsIgnoreCase(c.getFunction(), classMethod[1]))
                        .findAny();
                if (classOpt.isPresent()) {
                    TSdkLibrary tSdkLibrary = sdkLibraryMapper.selectByPrimaryKey(classOpt.get().getSdkId());
                    // 非自研SDK，自研sdk算APP类型
                    if (tSdkLibrary != null && !tSdkLibrary.getFirstPartyLibrary()) {
                        addSdk(sdkList, makeIosSdk(tSdkLibrary));
                    }
                } else if (!executor.endsWith(".dylib")) {
                    // 第一个调用名与APP执行名一致或者本次为系统库调用，都归属于APP。其它属于疑似SDK
                    if (StringUtils.isNotBlank(executor) &&
                            !executableName.equals(executor) &&
                            !isBaseFramework(libraryList, executor)) {
                        addSdk(sdkList, makeIosSuspiciousSdk(executor));
                    }
                }
            }
        }
        // 查询SDK库信息
        List<SdkApiVO> executorSdk = findExecutorSdk(executableName, stackInfo);
        for (SdkApiVO sdkApiVO:executorSdk) {
            addSdk(sdkList, makeIosSdk(sdkApiVO));
        }
        return sdkList;
    }

    /**
     * 判断一个框架名称是否是 iOS 的基础框架
     *
     * @param name 框架名称
     * @return 如果是基础框架，返回 true；否则返回 false
     */
    public static boolean isBaseFramework(List<TIosFrameworkLibrary> libraryList, String name) {
        if (name == null || name.isEmpty()) {
            return false;
        }
        String frameworkName;
        if (!name.contains(".framework")) {
            frameworkName = name + ".framework";
        } else {
            frameworkName = name;
        }
        return libraryList.stream().anyMatch(lib -> lib.getName().equals(frameworkName));
    }

    private void addSdk(List<IosSdkDTO> sdkList, IosSdkDTO dto) {
        if (sdkList.stream().noneMatch(sdk -> sdk.getSdkName().equals(dto.getSdkName()))) {
            sdkList.add(dto);
        }
    }

    private static String[] findClassMethod(String str) {
        int classStart = str.indexOf("[");
        if (classStart >= 0) {
            int classEnd = str.indexOf(" ", classStart);
            if (classEnd > classStart + 1) {
                String className = str.substring(classStart + 1, classEnd);
                return new String[]{className, findMethodName(classEnd, str)};
            }
        }
        return new String[]{"", ""};
    }

    private static String findMethodName(int classEnd, String str) {
        int methodStart = classEnd + 1;
        if (methodStart < str.length()) {
            int methodEnd = str.indexOf(":", methodStart);
            if (methodEnd == -1) {
                methodEnd = str.indexOf("]", methodStart);
            }
            if (methodEnd > methodStart) {
                return str.substring(methodStart, methodEnd);
            }
        }
        return "";
    }

    private static IosSdkDTO makeIosSdk(SdkApiVO sdkApiVO) {
        IosSdkDTO sdk = new IosSdkDTO();
        sdk.setSdkName(sdkApiVO.getSdkName());
        sdk.setSdkPackageName(replaceSdkEmptyData(sdkApiVO.getPackageName()));
        sdk.setTypeName(replaceSdkEmptyData(sdkApiVO.getFuncClassify()));
        sdk.setManufacturer(replaceSdkEmptyData(sdkApiVO.getCompanyName()));
        sdk.setSdkDescribe(replaceSdkEmptyData(sdkApiVO.getDescription()));
        return sdk;
    }

    private static IosSdkDTO makeIosSdk(TSdkLibrary sdkLibrary) {
        IosSdkDTO sdk = new IosSdkDTO();
        sdk.setSdkName(sdkLibrary.getName());
        sdk.setSdkPackageName(replaceSdkEmptyData(sdkLibrary.getPackageName()));
        sdk.setTypeName(replaceSdkEmptyData(sdkLibrary.getTypeName()));
        sdk.setManufacturer(replaceSdkEmptyData(sdkLibrary.getManufacturer()));
        sdk.setSdkDescribe(replaceSdkEmptyData(sdkLibrary.getDescribe()));
        sdk.setFirstParty(sdkLibrary.getFirstPartyLibrary());
        return sdk;
    }

    private static IosSdkDTO makeIosSuspiciousSdk(String name) {
        IosSdkDTO sdk = new IosSdkDTO();
        sdk.setSdkName(name);
        sdk.setSdkPackageName(name);
        sdk.setTypeName("");
        sdk.setManufacturer("");
        sdk.setSdkDescribe("");
        sdk.setSuspicious(true);
        return sdk;
    }

    private static TIosPrivacyActionSdk makeTIosPrivacyActionSdk(Long taskId, IosSdkDTO iosSdkDTO) {
        TIosPrivacyActionSdk sdk = new TIosPrivacyActionSdk();
        sdk.setTaskId(taskId);
        sdk.setSdkName(iosSdkDTO.getSdkName());
        sdk.setSdkPackageName(iosSdkDTO.getSdkPackageName());
        sdk.setTypeName(iosSdkDTO.getTypeName());
        sdk.setManufacturer(iosSdkDTO.getManufacturer());
        sdk.setSdkDescribe(iosSdkDTO.getSdkDescribe());
        return sdk;
    }

    private static String replaceSdkEmptyData(String data) {
        if (StringUtils.isBlank(data)) {
            return PinfoConstant.DETAILS_EMPTY;
        } else {
            return data;
        }
    }

}
