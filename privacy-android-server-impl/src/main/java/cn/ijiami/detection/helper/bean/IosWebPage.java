package cn.ijiami.detection.helper.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosWebPagePrivacyText.java
 * @Description ios的web界面信息
 * @createTime 2025年01月22日 16:25:00
 */
@Data
public class IosWebPage {

    /**
     * 界面的文本信息
     */
    private String content;
    /**
     * 本页面解析出的隐私政策或第三方sdk声明页面，一个界面可能会解析出多个
     */
    private List<PrivacyPolicyTextInfo> policyTextInfoList;

    public IosWebPage(String content, List<PrivacyPolicyTextInfo> policyTextInfoList) {
        this.content = content;
        this.policyTextInfoList = policyTextInfoList;
    }

    public IosWebPage() {
    }
}
