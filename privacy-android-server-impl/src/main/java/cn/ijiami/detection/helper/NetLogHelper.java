package cn.ijiami.detection.helper;

import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NetLogHelper.java
 * @Description 深度检测网络日志工具
 * @createTime 2025年03月05日 10:38:00
 */
public class NetLogHelper {


    private static final Pattern STATUS_CODE_PATTERN = Pattern.compile("^HTTP/[0-9.]{1,3}\\s(\\d{3})\\s|:status:(\\d{3})");

    public static RealTimeNetLog buildNetLog(TPrivacyOutsideAddress outsideAddress) {
        RealTimeNetLog log = new RealTimeNetLog();
        //IP地址
        log.setId(outsideAddress.getId().toString());
        log.setIp(outsideAddress.getIp());
        log.setHost(outsideAddress.getHost());
        log.setCookie(outsideAddress.getCookie());
        //协议类型
        log.setProtocol(outsideAddress.getProtocol());
        //url地址
        log.setUrl(outsideAddress.getUrl());
        log.setPort(outsideAddress.getPort());
        // 境内外判断
        log.setOutside(outsideAddress.getOutside());
        // 网络访问数据类型
        log.setRequestMethod(outsideAddress.getRequestMethod());
        log.setAddress(outsideAddress.getAddress());
        // 解析状态码
        Matcher matcher = STATUS_CODE_PATTERN.matcher(outsideAddress.getResponseData());
        if (matcher.find()) {
            String code = matcher.group(1);
            if (StringUtils.isNotBlank(code)) {
                log.setStatusCode(code);
            } else {
                log.setStatusCode(matcher.group(2));
            }
        } else {
            log.setStatusCode(StringUtils.EMPTY);
        }
        // 请求内容
        log.setRequestData(outsideAddress.getDetailsData());
        // 响应内容
        log.setResponseData(outsideAddress.getResponseData());
        log.setActionTime(outsideAddress.getActionTime().getTime());
        log.setBehaviorStage(outsideAddress.getBehaviorStage().getValue());
        return log;
    }

}
