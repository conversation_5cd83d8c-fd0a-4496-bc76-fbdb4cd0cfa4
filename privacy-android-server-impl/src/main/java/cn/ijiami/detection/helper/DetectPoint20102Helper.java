package cn.ijiami.detection.helper;

import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static cn.ijiami.detection.helper.DetectPointCommonHelper.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint20102Helper.java
 * @Description 检测点公共函数类
 * @createTime 2024年07月29日 16:36:00
 */
public class DetectPoint20102Helper {

    public static DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(未测试)
        List<ResultDataLogBO> root = commonDetectInfo.getResultDataLogs();
        List<ResultDataLogBO> resultDataLogs = MiitLogKit.filterByTagAndRunStatus(root, MiitDataTypeEnum.UI_AUTHOR_DENIED, MiitRunStatusEnum.Death);
        if (CollectionUtils.isEmpty(resultDataLogs)) {
            return buildNonInvolved(commonDetectInfo, customDetectInfo);
        }
        // 根据时间获取对应tag的图片
        String directory = commonDetectInfo.getFilePath();
        Set<String> images = new HashSet<>();
        Set<String> names = new HashSet<>();
        for (ResultDataLogBO log : resultDataLogs) {
            String absolutePath = directory + File.separator + log.getImgPath();
            if (MiitLogKit.isFileExist(absolutePath)) {
                images.add(absolutePath);
            }
            if (log.getUiDumpResult() != null) {
                // 获取拒绝权限名
                names.add(findApplyPermissionRegex(commonDetectInfo, log.getUiDumpResult()).getApplyName());
            }
        }
        // 无有效的图片
        if (CollectionUtils.isEmpty(images)) {
            return buildNonInvolved(commonDetectInfo, customDetectInfo);
        }
        String privacyPolicyText = getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        DetectResult detectResult = buildNonCompliance(commonDetectInfo, customDetectInfo);
        detectResult.setPrivacyPolicyFragment(privacyPolicyText);
        detectResult.setScreenshots(images);
        // 有涉及到的字样，则算不涉及
        if (StringUtils.isNotBlank(privacyPolicyText)) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            // 不合规 设置拒绝权限名
            detectResult.setRequestPermissionNames(names);
        }

        // 不涉及 添加图片
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            savePermissionImage(commonDetectInfo, detectResult);
        }

        //判断是否只包含存储权限，如果是只是存在存储就不违规
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE && detectResult.getRequestPermissionNames()!= null &&
                detectResult.getRequestPermissionNames().size()==1 && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }

        if(detectResult.getRequestPermissionNames()!=null && !detectResult.getRequestPermissionNames().isEmpty() && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
            Set<String> set = detectResult.getRequestPermissionNames();
            set.remove(PermissionNameHelper.FILTERWORD);
            detectResult.setRequestPermissionNames(set);
        }

        return detectResult;
    }

}
