package cn.ijiami.detection.helper;

import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.service.impl.IPrivacyLogCrtlServiceImpl;
import cn.ijiami.detection.utils.ActionNougatUtils;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.DateUtils;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.kit.utils.FileUtil;
import com.ijm.ios.RuntimeDetection.data.InfoAct;
import com.ijm.ios.RuntimeDetection.data.InfoNet;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.ijiami.detection.utils.CommonUtil.optDetails;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosActionLogConvertHelper.java
 * @Description 日志和行为转换器
 * @createTime 2024年08月08日 16:57:00
 */
@Slf4j
@Component
public class IosActionLogConvertHelper {

    private static final Pattern STATUS_CODE_PATTERN = Pattern.compile("^(\\d{3})\\s");

    public static final String INFO_NET_RESPONSE_FLAG = "response";
    // 网络请求状态码
    public static final int INFO_NET_NUM_CODE = 0;

    private static final int MYSQL_TEXT_INDEX_LENGTH_MAX = 65535;

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private IosActionExecutorHelper iosActionExecutorHelper;

    public RealTimeNetLog buildNetLog(TPrivacyOutsideAddress address) {
        RealTimeNetLog log = new RealTimeNetLog();
        //IP地址
        String city = ipUtil.getAddress(address.getIp());
        log.setId(address.getId().toString());
        log.setIp(address.getIp());
        log.setHost(address.getHost());
        //协议类型
        log.setProtocol(address.getProtocol());
        //url地址
        log.setUrl(address.getUrl());
        //ios目前没有返回端口号
        log.setPort(address.getPort());
        // 境内外判断
        log.setOutside(address.getOutside());
        //网络访问数据类型
        log.setRequestMethod(address.getRequestMethod());
        log.setAddress(city);
        Matcher matcher = STATUS_CODE_PATTERN.matcher(address.getResponseData());
        if (matcher.find()) {
            log.setStatusCode(matcher.group(1));
        } else {
            log.setStatusCode(StringUtils.EMPTY);
        }
        log.setActionTime(address.getActionTime().getTime());
        log.setCookie(address.getCookie());
        log.setRequestData(address.getDetailsData());
        log.setResponseData(address.getResponseData());
        log.setBehaviorStage(address.getBehaviorStage().getValue());
        return log;
    }

    public TPrivacyOutsideAddress buildPrivacyOutsideAddress(InfoAct infoAct, DynamicTaskContext taskData) {
        JSONArray jAryNetInfo = JSONArray.fromObject(infoAct.getStrInfo());

        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        //行为发生时间
        Long actionTime = ActionNougatUtils.parseLogTime(infoAct);
        //IP地址
        String ip = jAryNetInfo.getString(InfoNet.NUM_HOST);
        String host = jAryNetInfo.getString(InfoNet.NUM_DOMAIN);
        String city = ipUtil.getAddress(ip);
        outsideAddress.setTaskId(taskData.getTaskId());
        outsideAddress.setIp(StringUtils.isBlank(ip) ? "内部请求" : ip);
        outsideAddress.setHost(StringUtils.isBlank(host) ? "内部请求" : host);
        outsideAddress.setCounter(1);
        //协议类型
        outsideAddress.setProtocol(optDetails(jAryNetInfo.getString(InfoNet.NUM_PROTOCOL)));
        //url地址
        outsideAddress.setUrl(optDetails(CommonUtil.checkBase64(jAryNetInfo.getString(InfoNet.NUM_ADDR))));
        //ios目前没有返回端口号
        outsideAddress.setPort(PinfoConstant.DETAILS_EMPTY);
        // 境内外判断
        outsideAddress.setOutside(IpUtil.isOutSide(city));
        //网络访问数据类型
        outsideAddress.setRequestMethod(optDetails(jAryNetInfo.getString(InfoNet.NUM_TRANSTYPE)));
        outsideAddress.setAddress(optDetails(city));
        String statusCode = jAryNetInfo.getString(INFO_NET_NUM_CODE);
        outsideAddress.setActionTime(new Date(actionTime));
        //详细数据
        String bodyData = CommonUtil.checkBase64(jAryNetInfo.getString(InfoNet.NUM_CONTENT));
        String headerData = CommonUtil.checkBase64(jAryNetInfo.getString(InfoNet.NUM_COOKIE));
        StringBuilder dataBuilder = new StringBuilder();
        // 有时候数据里面会返回状态码为0，不保存这个非法状态码
        if (!StringUtils.equals("0", statusCode)) {
            dataBuilder.append(statusCode).append(" ");
        }
        if (!INFO_NET_RESPONSE_FLAG.equals(outsideAddress.getRequestMethod())) {
            dataBuilder.append(outsideAddress.getRequestMethod())
                    .append(" ");
        }
        if (StringUtils.isNotBlank(outsideAddress.getUrl()) || StringUtils.isNotBlank(outsideAddress.getProtocol())) {
            dataBuilder.append(outsideAddress.getUrl())
                    .append(" ")
                    .append(outsideAddress.getProtocol())
                    .append('\n');
        }
        String cookie = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(headerData)) {
            JSONObject headers = JSONObject.fromObject(headerData);
            for (Object key:headers.keySet()) {
                String keyStr = key.toString();
                dataBuilder.append(keyStr).append(":").append(headers.optString(keyStr)).append('\n');
                if (StringUtils.equalsIgnoreCase("Cookie", keyStr)) {
                    cookie = headers.optString(keyStr, StringUtils.EMPTY);
                }
            }
        }
        if (StringUtils.isNotBlank(bodyData)) {
            dataBuilder.append(bodyData).append('\n');
        }
        outsideAddress.setCookie(optDetails(cookie));
        //新增cookie标识，以便前台展示
        outsideAddress.setCookieMark(StringUtils.isNotBlank(cookie) ? BooleanEnum.TRUE.value : BooleanEnum.FALSE.value);
        if (INFO_NET_RESPONSE_FLAG.equals(outsideAddress.getRequestMethod())) {
            outsideAddress.setDetailsData(PinfoConstant.DETAILS_EMPTY);
            outsideAddress.setResponseData(dataBuilder.toString());
        } else {
            outsideAddress.setDetailsData(dataBuilder.toString());
            outsideAddress.setResponseData(PinfoConstant.DETAILS_EMPTY);
        }
        outsideAddress.setBehaviorStage(IPrivacyLogCrtlServiceImpl.changeIosBehaviorStage(infoAct.getStage()));
        outsideAddress.setStackInfo(FileUtil.decodeData(infoAct.getStrTrigger()));
        iosActionExecutorHelper.setNougatExecutor(outsideAddress, outsideAddress.getStackInfo(), taskData.getIosExecutableName(),
                taskData.getAppName(), taskData.getPackageName(),
                taskData.getIosSdkApiMap(), taskData.getSuspiciousSdkMap(), taskData.getIosLibraryList(), taskData.getSdkLibraryClassList());
        return outsideAddress;
    }


    public TPrivacyActionNougat buildPrivacyActionNougat(InfoAct infoAct, TActionNougat nougat, DynamicTaskContext taskData) {
        TPrivacyActionNougat actionNougat = new TPrivacyActionNougat();
        actionNougat.setActionId(nougat.getActionId());
        actionNougat.setActionName(nougat.getActionName());
        Date date = DateUtils.formatDate(infoAct.getStrTime(), "yyyy-MM-dd HH:mm:ss");
        actionNougat.setActionTime(date);
        actionNougat.setActionTimeStamp(date.getTime());
        //获取详细数据，判断详细数据长度大于数据库存储长度则截取第5位content的内容
        String[] info = infoAct.getStrInfo().replace("[","").replace("]","").split(",");
        List<String> detailData = Arrays.asList(info);
        if (detailData.toString().length()>=MYSQL_TEXT_INDEX_LENGTH_MAX && detailData.size()>5 &&StringUtils.isNotBlank(detailData.get(5))){
            int length = detailData.get(5).length() - 1 - (detailData.toString().length() - MYSQL_TEXT_INDEX_LENGTH_MAX);
            length = length < 0 ? detailData.get(5).length() : length;
            detailData.set(5, detailData.get(5).substring(0, length));
            actionNougat.setDetailsData(StringUtils.isEmpty(detailData.toString()) ? PinfoConstant.DETAILS_EMPTY : detailData.toString());
        } else if (detailData.toString().length() >= MYSQL_TEXT_INDEX_LENGTH_MAX) {
            actionNougat.setDetailsData(StringUtils.isEmpty(detailData.toString()) ? PinfoConstant.DETAILS_EMPTY : detailData.toString().substring(0, MYSQL_TEXT_INDEX_LENGTH_MAX));
        } else {
            actionNougat.setDetailsData(StringUtils.isEmpty(detailData.toString()) ? PinfoConstant.DETAILS_EMPTY : detailData.toString());
        }
        String stackInfo = FileUtil.decodeData(infoAct.getStrTrigger());
        actionNougat.setStackInfo(StringUtils.isEmpty(stackInfo) ? PinfoConstant.DETAILS_EMPTY : stackInfo);
        actionNougat.setTaskId(taskData.getTaskId());
        BehaviorStageEnum behaviorStage = IPrivacyLogCrtlServiceImpl.changeIosBehaviorStage(infoAct.getStage());
        actionNougat.setType(behaviorStage == BehaviorStageEnum.BEHAVIOR_GRANT);
        actionNougat.setApiName(infoAct.getStrName());
        actionNougat.setBehaviorStage(behaviorStage);
        actionNougat.setIsPersonal(String.valueOf(PrivacyStatusEnum.getItem(nougat.getPersonal()).getValue()));
        // 设置主体名称和主体类型，包名
        iosActionExecutorHelper.setNougatExecutor(actionNougat, actionNougat.getStackInfo(), taskData.getIosExecutableName(),
                taskData.getAppName(), taskData.getPackageName(),
                taskData.getIosSdkApiMap(), taskData.getSuspiciousSdkMap(), taskData.getIosLibraryList(), taskData.getSdkLibraryClassList());
        return actionNougat;
    }

    public IOSRealTimeLog buildRealTimeLog(TPrivacyActionNougat nougat, String appName) {
        if (nougat == null) {
            throw new IjiamiRuntimeException("行为不存在");
        }
        IOSRealTimeLog actionLog = new IOSRealTimeLog();
        actionLog.setTaskId(nougat.getTaskId());
        actionLog.setActionId(nougat.getActionId());
        actionLog.setStrTrigger(nougat.getStackInfo());
        actionLog.setStrDesc(nougat.getActionName());
        actionLog.setStrInfo(nougat.getDetailsData());
        actionLog.setAppName(appName);
        actionLog.setActionTime(nougat.getActionTimeStamp());
        actionLog.setId(nougat.getId().toString());
        actionLog.setIsPersonal(PrivacyStatusEnum.getItem(Integer.parseInt(nougat.getIsPersonal())));
        actionLog.setExecutorType(nougat.getExecutorType());
        actionLog.setExecutor(nougat.getExecutor());
        actionLog.setPackageName(nougat.getPackageName());
        actionLog.setBehaviorStage(nougat.getBehaviorStage().getValue());
        actionLog.setStrTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nougat.getActionTime()));
        return actionLog;
    }
}
