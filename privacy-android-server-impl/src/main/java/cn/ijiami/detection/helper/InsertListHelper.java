package cn.ijiami.detection.helper;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InsertListHelper.java
 * @Description 批量写入数据库帮助类，提高mybatis批量写入性能
 * @createTime 2021年12月15日 14:25:00
 */
public class InsertListHelper {

    public static <T> void insertList(List<T> dataList, Consumer<List<T>> action) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            Lists.partition(dataList, 50).forEach(action);
        }
    }

}
