package cn.ijiami.detection.helper.bean;

import cn.ijiami.detection.utils.CommonUtil;
import org.jsoup.select.Elements;

import org.jsoup.nodes.Element;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyPolicyTextInfo.java
 * @Description 文件提取的隐私文本
 * @createTime 2023年07月05日 16:16:00
 */
public class PrivacyPolicyTextInfo {

    public final String fileName;
    public final String content;
    public final List<Element> sdkTables;

    public PrivacyPolicyTextInfo(String fileName, String content) {
        this.fileName = fileName;
        this.content = CommonUtil.removeExtraBlankLines(content);
        this.sdkTables = null;
    }

    public PrivacyPolicyTextInfo(String fileName, String content, List<Element> tables) {
        this.fileName = fileName;
        this.content = CommonUtil.removeExtraBlankLines(content);
        this.sdkTables = tables;
    }


}
