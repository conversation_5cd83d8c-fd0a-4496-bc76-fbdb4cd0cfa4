package cn.ijiami.detection.helper;

import cn.ijiami.framework.common.enums.HttpStatusEnum;
import cn.ijiami.framework.common.response.BaseResponse;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResponseHelper.java
 * @Description 返回状态码工具类
 * @createTime 2021年12月07日 11:33:00
 */
public class ResponseHelper {


    public static <T> BaseResponse<T> successData(String message, T data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setData(data);
        response.setMessage(message);
        return response;
    }

    public static BaseResponse<String> successMessage(String message) {
        BaseResponse<String> response = new BaseResponse<>();
        response.setMessage(message);
        return response;
    }

    public static BaseResponse<String> failure(String message) {
        BaseResponse<String> response = new BaseResponse<>();
        response.setStatus(HttpStatusEnum.FAIL.getValue());
        response.setMessage(message);
        return response;
    }

    public static <T> BaseResponse<T> success() {
        return new BaseResponse<T>();
    }

}
