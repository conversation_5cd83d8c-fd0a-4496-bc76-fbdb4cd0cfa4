package cn.ijiami.detection.helper;

import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.IntStream;

/**
 * 云手机本地手机切换
 *
 * <AUTHOR>
 * @date 2020-12-03 12:27
 */
public class CustomDetectHelper {

    private CustomDetectHelper() {

    }

    private static Logger             logger         = LoggerFactory.getLogger(CustomDetectHelper.class);
    private static CustomDetectHelper instance       = new CustomDetectHelper();
    /**
     * 默认存储位置
     */
    private        String             directory      = "E:/zywa/ijiami/";
    /**
     * 默认存储文件名称
     */
    private        String             fileName       = "pinfo_priority_local.json";
    /**
     * 是否有改变
     */
    private        boolean            haveChanged    = false;
    /**
     * 云手机模式下，特殊存储特殊用户，此部分优先使用本地手机
     */
    private        Map<Long, Long>    priorityLocals = new ConcurrentHashMap<>();

    public static CustomDetectHelper getInstance() {
        return instance;
    }

    public void init() {
        // 初始化存储路径
        this.directory = SpringMonitorHeart.commonProperties.getProperty("ijiami.framework.default.path");
        // 读取配置到内存中
        readPriorityLocalToMemory();
        logger.info("CustomDetectHelper 初始化使用本地设备到内存，数据信息：{}", priorityLocals);
    }

    /**
     * 应用停止时，将内存中的存储到文件中
     */
    public void destroy() {
        writeMemoryToPriorityLocal();
        logger.info("CustomDetectHelper 销毁，存储内存数据，数据信息：{}", priorityLocals);
    }

    /**
     * 添加用户
     *
     * @param userId
     */
    public void add(Long userId) {
        priorityLocals.put(userId, System.currentTimeMillis());
        logger.info("CustomDetectHelper 添加本地手机优先用户，用户id:{}", userId);
    }

    /**
     * 删除用户
     *
     * @param userId
     */
    public void remove(Long userId) {
        priorityLocals.remove(userId);
        logger.info("CustomDetectHelper 删除本地手机优先用户，用户id:{}", userId);
        writeMemoryToPriorityLocal();
        logger.info("CustomDetectHelper 删除本地手机优先用户，更新内存副本到文件中，更新内容：{}", priorityLocals);
    }

    /**
     * 获取所有定制用户
     *
     * @return
     */
    public Set<Long> getAllPriorityLocal() {
        return priorityLocals.keySet();
    }

    /**
     * 判断是否为本地优先用户
     *
     * @param userId 用户id
     * @return
     */
    public boolean isPriorityLocal(Long userId) {
        if (Objects.isNull(userId) || priorityLocals.size() == 0) {
            return false;
        }
        return priorityLocals.containsKey(userId);
    }

    /**
     * 获取存储的文件，没有则新建一个
     *
     * @return
     */
    private File getStorageFile() {
        String filePath = directory + fileName;
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.getMessage();
                logger.error("CustomDetectHelper 无法创建配置，文件地址：{}", filePath);
            }
        }
        return file;
    }

    /**
     * 读取配置的到内存中
     */
    private void readPriorityLocalToMemory() {
        File file = getStorageFile();
        String readStr = DynamicFileReaderHelper.readFileToString(file.getAbsolutePath());
        if (StringUtils.isEmpty(readStr)) {
            return;
        }
        List<Long> userIds = JSON.parseArray(readStr, Long.class);
        Long initializationTime = System.currentTimeMillis();
        for (Long userId : userIds) {
            priorityLocals.put(userId, initializationTime);
        }
    }

    /**
     * 内存数据写入到文件中
     */
    private void writeMemoryToPriorityLocal() {
        File file = getStorageFile();
        // 获取所有内容
        Set<Long> set = getAllPriorityLocal();
        // 读取文件内容
        String readStr = DynamicFileReaderHelper.readFileToString(file.getAbsolutePath());
        // 文件为空，存储内容为空，则不再写入
        if (CollectionUtils.isEmpty(set) && StringUtils.isEmpty(readStr)) {
            return;
        }
        // 将内容写入到文件中
        try {
            FileWriter writer = new FileWriter(file);
            writer.write(JSON.toJSONString(set));
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.getMessage();
        }
    }

    public static void main(String[] args) {
        CustomDetectHelper instance = CustomDetectHelper.getInstance();
        instance.readPriorityLocalToMemory();
        System.err.println("读取前：" + instance.getAllPriorityLocal());
        Set<Long> set = new HashSet<>();
        IntStream.iterate(0, n -> n + 1).limit(10).forEach(i -> {
            set.add(Long.valueOf(i));
        });
        File file = instance.getStorageFile();
        try {
            FileWriter writer = new FileWriter(file);
            writer.write(JSON.toJSONString(set));
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.getMessage();
        }
        instance.readPriorityLocalToMemory();
        System.err.println("读取后：" + instance.getAllPriorityLocal());
    }
}
