package cn.ijiami.detection.helper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import cn.ijiami.detection.bean.WaitingForCheckWord;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.utils.WordStringUtil;
import lombok.Data;
import net.sf.json.JSONArray;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SensitiveWordsHelper.java
 * @Description 个人隐私信息帮助类
 * @createTime 2021年12月13日 20:03:00
 */
public class SensitiveWordsHelper {

    private static final Pattern HAVE_IP_WORD_PATTERN = Pattern.compile("(^|[^a-zA-Z])(ip[^a-zA-Z]|address)", Pattern.CASE_INSENSITIVE);

    private static final Cache<String, String> ENHANCED_WORD_CACHE = CacheBuilder.newBuilder().recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();
    private static final Cache<String, String> DOUBLE_QUOTES_WORD_CACHE = CacheBuilder.newBuilder().recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();
    private static final Cache<String, String> ANGLE_BRACKETS_WORD_CACHE = CacheBuilder.newBuilder().recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();

    private static final List<Integer> IGNORE_HEIGHT_LIST = Arrays.asList(2560, 1440, 1920, 1080, 1280, 720, 320, 240, 480,
            400, 432, 640, 800, 768, 854, 960, 576, 520, 486, 440, 600, 1024, 4096, 3112, 2048, 1536, 2800, 2100, 3200,
            3840, 2160, 5120, 3200, 6400, 4096, 7680, 4320);

    public static List<SensitiveInfo> find(String request, String method, String sensitiveWords, boolean isSpec) {
        //关键字匹配
        String[] split = sensitiveWords.split("\\|");
        List<SensitiveInfo> codeList = new ArrayList<>(2);
        for (String s : split) {
        	Matcher matcher = null;
        	if(isSpec) {
        		matcher = Pattern.compile(enhancedWordsSpec(s), Pattern.CASE_INSENSITIVE).matcher(request);
        	}else {
        		matcher = Pattern.compile(enhancedWords(s), Pattern.CASE_INSENSITIVE).matcher(request);
        	}
            if (matcher.find()) {
                int begin = matcher.start(2);
                codeList.add(buildSensitiveCode(request, method, s, begin));
            }
            matchWord(request, method, codeList, s, wrapDoubleQuotes(s));
            matchWord(request, method, codeList, s, wrapAngleBrackets(s));
        }
        return codeList;
    }

    private static void matchWord(String request, String method, List<SensitiveInfo> codeList, String word, String regex) {
        Matcher matcher = Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(request);
        if (matcher.find()) {
            if (StringUtils.containsIgnoreCase(word, "height")) {
                if (SensitiveWordsHelper.isIgnoreHeight(matcher.group(1))) {
                    return;
                }
            } else {
                // 判断是否空值，有可能取到的是 "", {}, {"id":""}, [] 这几种都是空的
                if (SensitiveWordsHelper.isEmptyValues(matcher.group(1))) {
                    return;
                }
            }
            int begin = matcher.start();
            codeList.add(buildSensitiveCode(request, method, word, begin));
        }
    }

    /**
     * 增强敏感词匹配关键词，减少匹配
     * @param sensitiveWord
     * @return
     */
    public static String enhancedWords(String sensitiveWord) {
        String enhanced = ENHANCED_WORD_CACHE.getIfPresent(sensitiveWord);
        if (enhanced == null) {
        	enhanced = "(^|[;&?])(" + sensitiveWord + ")=";
            ENHANCED_WORD_CACHE.put(sensitiveWord, enhanced);
        }
        return enhanced;
    }
    
    /**
     * 增强敏感词匹配关键词，减少匹配
     * @param sensitiveWord
     * @return
     */
    public static String enhancedWordsSpec(String sensitiveWord) {
        String enhanced = ENHANCED_WORD_CACHE.getIfPresent(sensitiveWord);
        if (enhanced == null) {
            enhanced = "(^|\\s|[;&?:])(" + sensitiveWord + ")[=:]";
            ENHANCED_WORD_CACHE.put(sensitiveWord, enhanced);
        }
        return enhanced;
    }

    private static String wrapDoubleQuotes(String sensitiveWord){
        String doubleQuotes = DOUBLE_QUOTES_WORD_CACHE.getIfPresent(sensitiveWord);
        if (doubleQuotes == null) {
            doubleQuotes = "\"" + sensitiveWord + "\":((\\[\\S*?\\])|(\"\\S*?\")|(\\{\\S*?\\})|[^,}$]+)";
            DOUBLE_QUOTES_WORD_CACHE.put(sensitiveWord, doubleQuotes);
        }
        return doubleQuotes;
    }

    private static String wrapAngleBrackets(String sensitiveWord){
        String angleBrackets = ANGLE_BRACKETS_WORD_CACHE.getIfPresent(sensitiveWord);
        if (angleBrackets == null) {
            angleBrackets = "<" + sensitiveWord + ">(\\S+)<\\/" + sensitiveWord + ">";
            ANGLE_BRACKETS_WORD_CACHE.put(sensitiveWord, angleBrackets);
        }
        return angleBrackets;
    }

    public static List<SensitiveInfo> findName(String request, String sensitiveWords) {
        List<SensitiveInfo> codeList = new ArrayList<>(2);
        String[] words = sensitiveWords.split("\\|");
        for (String word:words) {
            if (StringUtils.isBlank(word)) {
                continue;
            }
            Matcher matcher = Pattern.compile("(^|[;&?])(NAME=\"" + escapeRegexStr(word) + "\")", Pattern.CASE_INSENSITIVE).matcher(request);
            if (matcher.find()) {
                int begin = matcher.start(2);
                codeList.add(buildSharedPrefCode(request, word, begin));
            }
        }
        return codeList;
    }

    private static final String[] REGEX_WORDS = {"\\", "$", "(", ")", "[", "]", "*", "+", ".", "?", "^", "{", "}", "|"};

    private static String escapeRegexStr(String str) {
        for (String word:REGEX_WORDS){
            if (str.contains(word)) {
                str = str.replace(word, "\\" + word);
            }
        }
        return str;
    }

    public static List<SensitiveInfo> findXmlName(String request, String sensitiveWords) {
        List<SensitiveInfo> codeList = new ArrayList<>(2);
        String[] words = sensitiveWords.split("\\|");
        for (String word:words) {
            if (StringUtils.isBlank(word)) {
                continue;
            }
            int begin = org.apache.commons.lang3.StringUtils.indexOfIgnoreCase(request, "name=\"" + word + "\"");
            if (begin >= 0) {
                codeList.add(buildSharedPrefCode(request, word, begin));
            }
        }
        return codeList;
    }

    public static SensitiveInfo buildSensitiveCode(String request, String method, String word, int begin) {
        String code = subCode(request, begin);
        SensitiveInfo info = new SensitiveInfo();
        info.setWord(word);
        info.setCode(StringUtils.isBlank(code) ? PinfoConstant.DETAILS_EMPTY : code);
        if (info.getCode().contains("Cookie: ")) {
            info.setMethod("Cookies");
        } else {
            info.setMethod(method);
        }
        return info;
    }

    public static SensitiveInfo buildSharedPrefCode(String request, String word, int begin) {
        SensitiveInfo sensitiveInfo = new SensitiveInfo();
        String code = subCode(request, begin);
        // 截取原字符串中的关键词，因为可能和word的大小写不一样
        String keyword = request.substring(begin, begin + word.length());
        String markOriginal = code.replace(keyword, "<em>" + keyword + "</em>");
        sensitiveInfo.setCode(StringUtils.isBlank(code) ? PinfoConstant.DETAILS_EMPTY : code);
        sensitiveInfo.setWord(word);
        sensitiveInfo.setMarkOriginal(StringUtils.isBlank(markOriginal) ? PinfoConstant.DETAILS_EMPTY : markOriginal);
        return sensitiveInfo;
    }

    public static String subCode(String request, int begin) {
        return WordStringUtil.converCharacters(getMatchString(begin, begin, request));
    }

    @Data
    public static class SensitiveInfo {

        private String word;
        private String code;
        private String method;
        private String markOriginal;

    }

    /**
     * 判断提取的内容为无效信息
     *
     * @param matcher       匹配到的信息
     * @param content       源数据
     * @param sensitive     敏感词
     * @return true(无效)/false(有效)
     */
    public static boolean isInvalidInfo(Matcher matcher, String content, TSensitiveWord sensitive, boolean checkPrefix) {
        // 身份证或手机号码往前一位或者往后一位为数字，则视为无效数据
        if ("身份证".equals(sensitive.getName()) || "电话号码".equals(sensitive.getName())) {
            int start = matcher.start();
            int end = matcher.end();
            if (start > 0) {
                char beforeStart = content.charAt(start - 1);
                if (Character.isLetterOrDigit(beforeStart)) {
                    return true;
                }
            }
            if (end < content.length()) {
                char afterEnd = content.charAt(end + 1);
                if (Character.isLetterOrDigit(afterEnd)) {
                    return true;
                }
            }
            int prefixStart = findIndex(content, matcher.start() - 2, 20, 10);
            int prefixEnd = matcher.end();
            if (checkPrefix && StringUtils.isNotBlank(sensitive.getSensitiveWords())) {
                Pattern wordsPattern = Pattern.compile(sensitive.getSensitiveWords(), Pattern.CASE_INSENSITIVE);
                // 判断匹配到的身份证或电话字符串前面的字符是否带有相应的标记，减少误报
                String paramName = content.substring(prefixStart, prefixEnd);
                if (!wordsPattern.matcher(paramName).find()) {
                    return true;
                }
            }
        }
        // 正则匹配的关键字
        String keyword = matcher.group();
        if ("IP 地址".equals(sensitive.getName())) {
            if (keyword.startsWith("1.") || keyword.startsWith("2.") || keyword.startsWith("3.") || keyword.startsWith("4.") || keyword.startsWith("5.")
                    || keyword.startsWith("6.") || keyword.startsWith("7.") || keyword.startsWith("8.") || keyword.startsWith("9.") || keyword.startsWith("0")
                    || keyword.contains("127.0.0.") || keyword.equals("*******") || keyword.startsWith("10.") || keyword.startsWith("192.168") || keyword
                    .endsWith("0.0.0")) {
                return true;
            }
            int start = findIndex(content, matcher.start() - 2, 20, 10);
            int end = matcher.end();
            // 判断匹配到的ip地址字符串前面的字符是否带有ip字符
            if (checkPrefix && !HAVE_IP_WORD_PATTERN.matcher(content.substring(start, end)).find()) {
                return true;
            }
            if (content.contains("Host: ")) {
                return true;
            }
        }
        if ("电子邮箱".equals(sensitive.getName())) {
            if (!keyword.endsWith(".com") && !keyword.endsWith(".co") && !keyword.endsWith(".cn") && !keyword.endsWith(".org")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找特定字符的索引
     *
     * @param input       输入的字符串
     * @param startIndex  起始索引
     * @param maxDepth    最大往前查找深度
     * @param defaultValue 如果未找到符合条件的字符，返回的默认值
     * @return 找到的索引或默认值
     */
    public static int findIndex(String input, int startIndex, int maxDepth, int defaultValue) {
        // 检查输入是否合法
        if (input == null || startIndex < 0 || startIndex >= input.length()) {
            return defaultValue;
        }

        // 定义需要查找的字符集合
        String targetChars = "=;\"' \t\n\r";

        // 计算实际的最大查找范围
        int minIndex = Math.max(0, startIndex - maxDepth);

        // 从 startIndex 开始向前查找
        for (int i = startIndex; i >= minIndex; i--) {
            char currentChar = input.charAt(i);
            if (targetChars.indexOf(currentChar) != -1) {
                return i; // 找到目标字符，返回索引
            }
        }

        // 如果未找到，返回默认值
        return defaultValue;
    }

    public static List<WaitingForCheckWord> buildWaitingForCheckWords(String[] words) {
        return Arrays.stream(words).map(w -> new WaitingForCheckWord(w, false)).collect(Collectors.toList());
    }

    /**
     * 需要跳过的身高敏感词，有些是屏幕高度
     * @param content
     * @return
     */
    public static boolean isIgnoreHeight(String content) {
        if (StringUtils.isBlank(content)) {
            return true;
        }
        List<String> values = new ArrayList<>();
        if (content.startsWith("{") && content.endsWith("}")) {
            values.addAll(getObjectValues(content, values));
        } else if (content.startsWith("[") && content.endsWith("]")) {
            values.addAll(getArrayValues(content, values));
        } else if (content.startsWith("\"") && content.endsWith("\"") && content.length() >= 2) {
            values.add(content.substring(1, content.length() - 1));
        } else {
            values.add(content);
        }
        if (values.isEmpty()) {
            return true;
        }
        return values.stream().allMatch(word -> {
            try {
                Integer height = Integer.parseInt(word);
                return  StringUtils.isBlank(word) ||IGNORE_HEIGHT_LIST.contains(height);
            } catch (NumberFormatException e) {
                return false;
            }
        });
    }

    private static List<String> getObjectValues(String content, List<String> values) {
    	try {
	        for (Object obj:JSONObject.fromObject(content).values()) {
	            if (obj instanceof JSONObject || obj instanceof JSONArray || obj instanceof JSONNull) {
	                continue;
	            }
	            values.add(obj.toString());
	        }
    	} catch (Exception e) {
			e.getMessage();
		}
        return values;
    }

    private static List<String> getArrayValues(String content, List<String> values) {
    	try {
	        for (Object obj:JSONArray.fromObject(content)) {
	            if (obj instanceof JSONObject || obj instanceof JSONArray || obj instanceof JSONNull) {
	                continue;
	            }
	            values.add(obj.toString());
	        }
        } catch (Exception e) {
			e.getMessage();
		}
        return values;
    }

    /**
     * 判断获取的敏感词文本是否都是空值
     * @param content
     * @return
     */
    public static boolean isEmptyValues(String content) {
        if (StringUtils.isBlank(content)) {
            return true;
        }
        List<String> values = new ArrayList<>();
        if (content.startsWith("{") && content.endsWith("}")) {
            values.addAll(getObjectValues(content, values));
        } else if (content.startsWith("[") && content.endsWith("]")) {
            values.addAll(getArrayValues(content, values));
        } else if (content.startsWith("\"") && content.endsWith("\"") && content.length() >= 2) {
            values.add(content.substring(1, content.length() - 1));
        } else {
            values.add(content);
        }
        if (values.isEmpty()) {
            return true;
        }
        return values.stream().allMatch(word -> {
            try {
                return StringUtils.isBlank(word);
            } catch (NumberFormatException e) {
                return false;
            }
        });
    }

    public static String getMatchString(Matcher matcher, String content) {
        return getMatchString(matcher, content, 0);
    }

    public static String getMatchString(Matcher matcher, String content, int matcherGroup) {
        int start = matcherGroup > 0 ? matcher.start(matcherGroup) : matcher.start();
        //关键字匹配到结束位置
        int end = matcherGroup > 0 ? matcher.end(matcherGroup) : matcher.end();
        return getMatchString(start, end, content);
    }


    public static String getMatchString(int start, int end, String content) {
        //截取字符开始位置
        int strLength = Math.max(start - 50, 0);
        //截取字符结束位置
        int endLength = Math.min(content.length() - 1, end + 50);
        //v2.5版本优化，code只取匹配到的前后各50个字符
        return content.substring(strLength,endLength).trim()
                .replaceAll("[\n\t\r]","");
    }
}
