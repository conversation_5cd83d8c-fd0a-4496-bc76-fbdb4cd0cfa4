package cn.ijiami.detection.helper;

import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.ijiami.detection.helper.DetectPointCommonHelper.addNoInvolvedImage;
import static cn.ijiami.detection.helper.DetectPointCommonHelper.checkRepeatedlyApplyPermission;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint20105Helper.java
 * @Description 检测点公共函数类
 * @createTime 2024年07月29日 15:55:00
 */
public class DetectPoint20105Helper {

    public static void judgeCompliance(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        // 不涉及 添加图片
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            List<ResultDataLogBO> disAgreeResultDataLogBOList = commonDetectInfo.getResultDataLogs().stream()
                    .filter(resultDataLogBO -> resultDataLogBO.getUiDumpResult() != null
                            && resultDataLogBO.getType() == ResultDataLogBoTypeEnum.PERMISSION_REJECT.type
                            && resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()).collect(Collectors.toList());
            for (ResultDataLogBO permissionResultDataLogBO : disAgreeResultDataLogBOList) {
                addNoInvolvedImage(commonDetectInfo, detectResult, permissionResultDataLogBO);
                if (StringUtils.equals(permissionResultDataLogBO.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                    break;
                }
            }
        }


        //判断是否只包含存储权限，如果是只是存在存储就不违规
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE && detectResult.getRequestPermissionNames()!= null &&
                detectResult.getRequestPermissionNames().size()==1 && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
            Set<String> set = detectResult.getRequestPermissionNames();
            set.remove(PermissionNameHelper.FILTERWORD);
            detectResult.setRequestPermissionNames(set);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }

        if(detectResult.getRequestPermissionNames() != null
                && !detectResult.getRequestPermissionNames().isEmpty()
                && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
            Set<String> set = detectResult.getRequestPermissionNames();
            set.remove(PermissionNameHelper.FILTERWORD);
            detectResult.setRequestPermissionNames(set);
        }
    }

}