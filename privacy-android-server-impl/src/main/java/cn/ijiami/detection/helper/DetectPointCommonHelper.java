package cn.ijiami.detection.helper;

import static cn.ijiami.detection.utils.CommonUtil.strAndContains;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPointCommonHelper.java
 * @Description 检测点公共函数类
 * @createTime 2024年07月29日 15:38:00
 */
@Slf4j
public class DetectPointCommonHelper {

    /**
     * 检查频繁申请权限
     *
     * @param commonDetectInfo
     * @param detectResult
     */
    public static void checkRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            checkIosRepeatedlyApplyPermission(commonDetectInfo, detectResult, Collections.emptyList(), false);
        } else {
            checkAndroidRepeatedlyApplyPermission(commonDetectInfo, detectResult, Collections.emptyList(), false);
        }
    }

    /**
     * 检查频繁申请权限
     * @param commonDetectInfo
     * @param detectResult
     */
    public static void checkRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult, boolean allowImageDuplication) {
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            checkIosRepeatedlyApplyPermission(commonDetectInfo, detectResult, Collections.emptyList(), allowImageDuplication);
        } else {
            checkAndroidRepeatedlyApplyPermission(commonDetectInfo, detectResult, Collections.emptyList(), allowImageDuplication);
        }
    }

    /**
     * 检查频繁申请权限，在权限拒绝阶段，权限拒绝后是否再次弹出申请。如果是app退出后重新进入的弹出申请以及点击功能按钮触发的弹出申请，不算违规。
     * @param commonDetectInfo
     * @param detectResult
     * @param ignorePermissionNames
     * @param allowImageDuplication 允许图片文字重复
     */
    public static void checkAndroidRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult, List<String> ignorePermissionNames, boolean allowImageDuplication) {
        // 遍历界面
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            //权限拒绝页面
            ResultDataLogBO refuseResult = commonDetectInfo.getResultDataLogs().get(index);
            if (refuseResult.getUiDumpResult() != null &&
                    refuseResult.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                String permissionText = refuseResult.getUiDumpResult().getPermissionText();
                if (ignorePermissionNames.stream().anyMatch(permissionText::contains)) {
                    continue;
                }
                // 非拒绝权限操作返回
                if (refuseResult.getType() != ResultDataLogBoTypeEnum.PERMISSION_REJECT.type) {
                    continue;
                }
                for (int reApplyIndex = index + 1; reApplyIndex < commonDetectInfo.getResultDataLogs().size(); reApplyIndex++) {
                    // 再次申请权限页面
                    ResultDataLogBO reApplyResult = commonDetectInfo.getResultDataLogs().get(reApplyIndex);
                    // 应用非一直前台运行break;
                    if (StringUtils.equals(reApplyResult.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                        break;
                    }
                    // 非拒绝权限操作返回
                    if (reApplyResult.getType() != ResultDataLogBoTypeEnum.PERMISSION_REJECT.type) {
                        break;
                    }
                    // 有操作页面的点击事件，不用再往下检查，因为不符合拒绝后再次弹出的条件。后续就算有同样的权限弹窗也很可能是该动作触发的
                    if (isClickPageEvent(reApplyResult)) {
                        break;
                    }
                    // 频繁申请权限
                    if (reApplyResult.getUiDumpResult() != null
                            && StringUtils.equals(reApplyResult.getUiDumpResult().getPermissionText(), permissionText)
                            // 如果前一个界面是登录，重复弹出的权限则是登录动作触发的
                            && !previousIsLoginUi(commonDetectInfo.getResultDataLogs(), reApplyIndex)) {
                        if (allowImageDuplication) {
                            addNoComplianceImageAllowDuplication(commonDetectInfo, detectResult, refuseResult);
                            addNoComplianceImageAllowDuplication(commonDetectInfo, detectResult, reApplyResult);
                        } else {
                            addNoComplianceImage(commonDetectInfo, detectResult, refuseResult);
                            addNoComplianceImage(commonDetectInfo, detectResult, reApplyResult);
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查频繁申请权限，在权限拒绝阶段，权限拒绝后是否再次弹出申请。如果是app退出后重新进入的弹出申请以及点击功能按钮触发的弹出申请，不算违规。
     * @param commonDetectInfo
     * @param detectResult
     * @param ignorePermissionNames
     * @param allowImageDuplication 允许图片文字重复
     */
    public static void checkIosRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult, List<String> ignorePermissionNames, boolean allowImageDuplication) {
        List<ResultDataLogBO> discoverPolicyPauseLogs = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> resultDataLogBO.getType() == ResultDataLogBoTypeEnum.PERMISSION_REJECT.type)
                .collect(Collectors.toList());
        // 遍历界面
        for (int index = 0; index < discoverPolicyPauseLogs.size(); index++) {
            //权限拒绝页面
            ResultDataLogBO refuseResult = discoverPolicyPauseLogs.get(index);
            if (refuseResult.getUiDumpResult() != null &&
                    refuseResult.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                String applyName = findApplyPermissionRegex(commonDetectInfo,
                        refuseResult.getUiDumpResult()).getApplyName();
                if (ignorePermissionNames.stream().anyMatch(applyName::contains)) {
                    continue;
                }
                for (int reApplyIndex = index + 1; reApplyIndex < discoverPolicyPauseLogs.size(); reApplyIndex++) {
                    // 再次申请权限页面
                    ResultDataLogBO reApplyResult = discoverPolicyPauseLogs.get(reApplyIndex);
                    if (reApplyResult.getUiDumpResult() == null) {
                        continue;
                    }
                    // 应用非一直前台运行break;
                    if (StringUtils.equals(reApplyResult.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                        break;
                    }
                    String uiText = reApplyResult.getUiDumpResult().getPermissionText();
                    if (!strAndContains(uiText, "打开", "前往", "开启")) {
                        continue;
                    }
                    String reApplyName = findApplyPermissionRegex(commonDetectInfo, reApplyResult.getUiDumpResult()).getApplyName();
                    // 频繁申请权限
                    if (StringUtils.equals(reApplyName, applyName)
                            // 如果前一个界面是登录，重复弹出的权限则是登录动作触发的
                            && !previousIsLoginUi(commonDetectInfo.getResultDataLogs(), reApplyIndex)) {
                        if (allowImageDuplication) {
                            addNoComplianceImageAllowDuplication(commonDetectInfo, detectResult, refuseResult);
                            addNoComplianceImageAllowDuplication(commonDetectInfo, detectResult, reApplyResult);
                        } else {
                            addNoComplianceImage(commonDetectInfo, detectResult, refuseResult);
                            addNoComplianceImage(commonDetectInfo, detectResult, reApplyResult);
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置不合规检测图片结果，允许文字重复
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    public static void addNoComplianceImageAllowDuplication(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        if (detectResult.getScreenshots() == null) {
            detectResult.setScreenshots(new HashSet<>());
        }
        if (detectResult.getScreenshotMd5s() == null) {
            detectResult.setScreenshotMd5s(new HashSet<>());
        }
        String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        String md5 = getMD5Str(resultDataLogBO.getUiDumpResult().getFullText());
        if (MiitLogKit.isFileExist(absolutePath)) {
            detectResult.getScreenshots().add(absolutePath);
            detectResult.getScreenshotMd5s().add(md5);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);

            // 获取拒绝权限名
            addRequestPermissionNameByUi(commonDetectInfo, resultDataLogBO.getUiDumpResult(), detectResult);
        }
    }

    /**
     * 设置不合规检测图片结果
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    public static void addNoComplianceImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        if (detectResult.getScreenshots() == null) {
            detectResult.setScreenshots(new HashSet<>());
        }
        if (detectResult.getScreenshotMd5s() == null) {
            detectResult.setScreenshotMd5s(new HashSet<>());
        }
        String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        String md5 = getMD5Str(resultDataLogBO.getUiDumpResult().getFullText());
        if (MiitLogKit.isFileExist(absolutePath) && !detectResult.getScreenshotMd5s().contains(md5)) {
            detectResult.getScreenshots().add(absolutePath);
            detectResult.getScreenshotMd5s().add(md5);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);

            // 获取拒绝权限名
            addRequestPermissionNameByUi(commonDetectInfo, resultDataLogBO.getUiDumpResult(), detectResult);
        }
    }

    /**
     * 设置不涉及检测图片结果
     *
     * @param commonDetectInfo
     * @param resultDataLogBO
     */
    public static void addNoInvolvedImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult, ResultDataLogBO resultDataLogBO) {
        if (detectResult.getScreenshots() == null) {
            detectResult.setScreenshots(new HashSet<>());
        }
        if (detectResult.getScreenshotMd5s() == null) {
            detectResult.setScreenshotMd5s(new HashSet<>());
        }
        String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        String md5 = getMD5Str(resultDataLogBO.getUiDumpResult().getFullText());
        if (MiitLogKit.isFileExist(absolutePath) && !detectResult.getScreenshotMd5s().contains(md5)) {
            detectResult.getScreenshots().add(absolutePath);
            detectResult.getScreenshotMd5s().add(md5);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
    }

//    public static String getMD5Str(String str) {
//        try {
//            // 生成一个MD5加密计算摘要
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            // 计算md5函数
//            md.update(str.getBytes());
//            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
//            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
//            return new BigInteger(1, md.digest()).toString(16);
//        } catch (Exception e) {
//            log.error("getMD5Str error", e);
//        }
//        return null;
//    }
    
    public static String getMD5Str(String str) {
        if (str == null) {
            return null; // 输入为空时直接返回 null
        }

        try {
            // 获取 MD5 摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 使用 UTF-8 编码将字符串转换为字节数组
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为 32 位十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 将每个字节转换为两位的十六进制表示
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补齐前导零
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            Logger logger = LoggerFactory.getLogger(DetectPointCommonHelper.class);
            logger.error("Error calculating MD5 for input: {}", str, e);
            return null;
        }
    }

    public static void addRequestPermissionNameByUi(CommonDetectInfo commonDetectInfo, UIDumpResult result, DetectResult detectResult) {
        if (result == null) {
            return;
        }
        if (StringUtils.isBlank(result.getPermissionText())) {
            return;
        }

        String name = findApplyPermissionRegex(commonDetectInfo, result).getApplyName();
        addRequestPermissionName(detectResult, name);
    }


    public static void addRequestPermissionName(DetectResult detectResult, String name) {
        if (detectResult.getRequestPermissionNames() == null) {
            detectResult.setRequestPermissionNames(new HashSet<>());
        }
        if (!StringUtils.isEmpty(name)) {
            detectResult.getRequestPermissionNames().add(name);
        }
    }

    /**
     * 是否操作页面的事件
     * @param resultDataLogBO
     * @return
     */
    public static boolean isClickPageEvent(ResultDataLogBO resultDataLogBO) {
        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(),
                    new TypeReference<ResultDataLogIosDetailsBO>() {
                    });
            if (detailsBO != null) {
                return !isApplyPermissionDialogButtonText(detailsBO);
            }
        }
        return false;
    }

    /**
     * 是否权限申请弹框的按钮文本
     * @param detailsBO
     * @return
     */
    public static boolean isApplyPermissionDialogButtonText(ResultDataLogIosDetailsBO detailsBO) {
        return org.apache.commons.lang3.StringUtils.equals(detailsBO.getText(), "拒绝")
                || org.apache.commons.lang3.StringUtils.equals(detailsBO.getText(), "允许");
    }

    /**
     * 前一个界面是否注册登录
     * @return
     */
    public static boolean previousIsLoginUi(List<ResultDataLogBO> resultDataLogBOList, int index) {
        for (int beginIndex=index-1; beginIndex>=0; beginIndex--) {
            ResultDataLogBO result = resultDataLogBOList.get(beginIndex);
            // 前一个有可能是数据，dump为空，需要继续往前找，直到找到第一个非数据的
            if (result.getUiDumpResult() != null) {
                return result.getUiDumpResult().getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue();
            }
        }
        return false;
    }

    public static TApplyPermission findApplyPermissionRegexByText(CommonDetectInfo commonDetectInfo, String text) {
        if (StringUtils.isBlank(text)) {
            return emptyApplyPermission();
        }
        Optional<TApplyPermission> permissionOpt = matchApplyPermission(commonDetectInfo, text);
        if (permissionOpt.isPresent()) {
            return permissionOpt.get();
        } else {
            TApplyPermission permission = emptyApplyPermission();
            permission.setApplyName(extractPermissionByText(commonDetectInfo, text));
            return permission;
        }
    }

    private static String extractPermissionByText(CommonDetectInfo commonDetectInfo, String text) {
        Matcher matcher;
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ANDROID) {
            matcher = getAndroidPermissionTextPattern(commonDetectInfo.getApkName()).matcher(text);
        } else if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            matcher = getIosPermissionTextPattern().matcher(text);
        } else {
            matcher = getAppletPermissionTextPattern().matcher(text);
        }
        return matcher.find() ? matcher.group(1) : StringUtils.EMPTY;
    }

    public static Pattern getAndroidPermissionTextPattern(String apkName) {
        return Pattern.compile("要允许" + apkName + "([\\s\\S]+)吗？");
    }

    public static Pattern getIosPermissionTextPattern() {
//        return Pattern.compile("允许“[\\s\\S]+”([\\s\\S]+)？");
        return Pattern.compile("允许“[\\s\\S]*?”([\\s\\S]*?)？");
    }

    public static Pattern getAppletPermissionTextPattern() {
        return Pattern.compile("获取([\\s\\S]+)[,，]");
    }

    private static Optional<TApplyPermission> matchApplyPermission(CommonDetectInfo commonDetectInfo, String text) {
        return commonDetectInfo.getApplyPermissions()
                .stream()
                .filter(regex -> regex.getTerminalType().equals(commonDetectInfo.getTerminalTypeEnum().getValue()))
                .filter(regex -> Pattern.compile(regex.getRegex()).matcher(text).find())
                .findFirst();
    }

    public static TApplyPermission findApplyPermissionRegex(CommonDetectInfo commonDetectInfo, UIDumpResult result) {
        if (Objects.isNull(result)) {
            return emptyApplyPermission();
        }
        String text = result.getPermissionText();
        if (StringUtils.isBlank(text)) {
            return emptyApplyPermission();
        }
        Optional<TApplyPermission> permissionOpt = matchApplyPermission(commonDetectInfo, text);
        if (permissionOpt.isPresent()) {
            return permissionOpt.get();
        } else {
            TApplyPermission permission = emptyApplyPermission();
            if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ALIPAY_APPLET && result.getUiComponentList() != null &&
                    !result.getUiComponentList().isEmpty()) {

                // 优先找控件里面的文本，避免把其它控件的文本也提取出来了
                for (UIComponent component:result.getUiComponentList()) {
                    if(StringUtils.isBlank(component.getText())) {
                        continue;
                    }
                    Matcher matcher = getAlipayAppletComponentPermissionTextPattern().matcher(component.getText());
                    if (matcher.find()) {
                        permission.setApplyName(matcher.group(1));
                        return permission;
                    }
                }
            }
            permission.setApplyName(extractPermissionByText(commonDetectInfo, text));
            return permission;
        }
    }

    private static TApplyPermission emptyApplyPermission() {
        TApplyPermission permission = new TApplyPermission();
        permission.setApplyName(StringUtils.EMPTY);
        permission.setRegex(StringUtils.EMPTY);
        return permission;
    }

    public static Pattern getAlipayAppletComponentPermissionTextPattern() {
        return Pattern.compile("获取([\\s\\S]+)");
    }

    public static boolean isIosPrivacyPermission(ResultDataLogBO resultDataLogBO) {
        if (Objects.isNull(resultDataLogBO.getUiDumpResult())) {
            return false;
        }
        return equalsButtonText(resultDataLogBO.getUiDumpResult(), "允许")
                && equalsButtonText(resultDataLogBO.getUiDumpResult(), "不允许")
                && !equalsUIText(resultDataLogBO.getUiDumpResult(), "网站上的活动")
                && !equalsUIText(resultDataLogBO.getUiDumpResult(), "发送通知");
    }

    /**
     * 为向你展示更契合需求的精彩内容、减少无关广告推荐，我们需要获取你的设备标识符(IDFA)
     * 想给您发送通知
     * @param result
     * @param text
     * @return
     */
    public static boolean equalsButtonText(UIDumpResult result, String text) {
        if (result == null) {
            return false;
        }
        return result.getUiComponentList().stream().anyMatch(uiComponent -> org.apache.commons.lang.StringUtils.contains(uiComponent.getText(), text));
    }


    /**
     * 为向你展示更契合需求的精彩内容、减少无关广告推荐，我们需要获取你的设备标识符(IDFA)
     * 想给您发送通知
     * @param result
     * @param text
     * @return
     */
    public static boolean equalsUIText(UIDumpResult result, String text) {
        if (result == null) {
            return false;
        }
        return org.apache.commons.lang.StringUtils.contains(result.getFullText(), text);
    }

    /**
     * 保存权限文件
     *
     * @param commonDetectInfo
     * @param detectResult
     */
    public static void savePermissionImage(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        List<ResultDataLogBO> disAgreeResultDataLogBOList = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> resultDataLogBO.getUiDumpResult() != null && (
                        resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()
                                || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.APPLY_PERMISSION.getValue()
                                || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.AGREE_PERMISSION.getValue())).collect(Collectors.toList());
        for (ResultDataLogBO permissionResultDataLogBO : disAgreeResultDataLogBOList) {
            addNoInvolvedImage(commonDetectInfo, detectResult, permissionResultDataLogBO);
        }
    }

    public static DetectResult buildNonInvolved(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        detectResult.setConclusion("该检测项未发现风险");
        detectResult.setScreenshots(new HashSet<>());
        return detectResult;
    }

    /**
     * 构建不合规
     */
    public static DetectResult buildNonCompliance(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        return detectResult;
    }

    public static DetectResult getBaseDetectResult(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = new DetectResult();
        detectResult.setTaskId(commonDetectInfo.getTaskId());
        detectResult.setItemNo(customDetectInfo.getItemNo());
        detectResult.setCount(0);
        return detectResult;
    }

    public static String getBaseTargetPrivacyPolicyText(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        String privacyPolicyText = commonDetectInfo.getPrivacyPolicyContent();
        Set<String> decideRuleKeys = customDetectInfo.getDecideRuleKeys();
        return MiitWordKit.defaultKeywordExtractionFromContent(privacyPolicyText, decideRuleKeys);
    }
    
}
