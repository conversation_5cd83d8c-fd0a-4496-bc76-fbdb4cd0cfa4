package cn.ijiami.detection.helper;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提取包名
 */
public class PackageNameExtractHelper {

    private final static Pattern fullPackageNamePattern = Pattern.compile("(^|<---)([a-zA-Z]+[0-9a-zA-Z_]*(\\.[a-zA-Z]+[0-9a-zA-Z_]*)*\\.[a-zA-Z]+[0-9a-zA-Z_]*)");

    private final static Pattern samplePackageNamePattern = Pattern.compile("(^|<---)([a-zA-Z]+[0-9a-zA-Z_]*(\\.[a-zA-Z]+[0-9a-zA-Z_]*){1,2})");

//    private final static Pattern twoLevelPackageNamePattern = Pattern.compile("^[a-zA-Z]+[0-9a-zA-Z_]*\\.[a-zA-Z]+[0-9a-zA-Z_]*");

    private final static Pattern shrinkingPackageNamePattern = Pattern.compile("^[0-9a-zA-Z_]\\.[0-9a-zA-Z_]$|^[0-9a-zA-Z_](\\.[0-9a-zA-Z_]){2,}(\\.[a-zA-Z]+[0-9a-zA-Z_]*)*$");

    private final static Pattern mergePackageNamePattern = Pattern.compile("^([a-zA-Z]+[0-9a-zA-Z_]*(\\.[a-zA-Z]+[0-9a-zA-Z_]+)*)(\\.[0-9a-zA-Z_])+$");

    /**
     * 提取完整的调用包名
     * 例如：java.io.File.renameTo(File.java:1434) 取 java.io.File.renameTo
     * @param stackInfo
     * @return
     */
    public static List<String> extractFullName(String stackInfo) {
        Matcher m = fullPackageNamePattern.matcher(stackInfo);
        List<String> packageNames = new ArrayList<>();
        while (m.find()) {
            String packageName = m.group(2);
            if (!packageNames.contains(packageName)) {
                packageNames.add(packageName);
            }
        }
        return packageNames;
    }

    /**
     * 提取aaa.bbb.ccc三个层级的调用包名
     * 例如：java.io.File.renameTo(File.java:1434) 只取 java.io.File
     * @param stackInfo
     * @return
     */
    public static List<String> extractSampleName(String stackInfo) {
        Matcher m = samplePackageNamePattern.matcher(stackInfo);
        List<String> packageNames = new ArrayList<>();
        while (m.find()) {
            String packageName = m.group(2);
            if (!packageNames.contains(packageName)) {
                packageNames.add(packageName);
            }
        }
        return packageNames;
    }

    /**
     * 提取aaa.bbb.ccc三个层级的调用包名
     * 例如：java.io.File.renameTo(File.java:1434) 只取 java.io.File
     * @return
     */
    public static String extractThreeLevelPackageName(String packageName) {
        return extractPackageName(packageName, 3);
    }

    /**
     * 提取aaa.bbb.ccc两个层级的调用包名
     * 例如：java.io.File.renameTo 只取 java.io
     * @param packageName
     * @return
     */
    public static String extractTwoLevelPackageName(String packageName) {
        return extractPackageName(packageName, 2);
    }

    public static String extractPackageName(String packageName, int level) {
        if (StringUtils.isEmpty(packageName) || level < 1) {
            return packageName;
        }
        String[] names = packageName.split("\\.");
        if (names.length > level) {
            StringJoiner joiner = new StringJoiner(".");
            for (int i=0; i<level; i++) {
                joiner.add(names[i]);
            }
            return joiner.toString();
        } else {
            return packageName;
        }
    }
    /**
     * 是否有压缩过的包名，前两个层级或者三个层级都是单字母
     * 例如：
     * c.a.b | c.a.b.start 都是属于压缩过的， 如果只有两层 c.a 那么也属于压缩过的
     * c.a.start | c.a.start.running 则不属于
     * @param packageName
     * @return
     */
    public static boolean isShrinkingPackageName(String packageName) {
        if (StringUtils.isBlank(packageName)) {
            return false;
        }
        Matcher m = shrinkingPackageNamePattern.matcher(packageName);
        return m.find();
    }


    /**
     * 提取需要合并的包名，就是最后一层是单字母的包名
     * 例如：
     * com.qq.a 提取出 com.aa
     * com.qq.local.a  提取出 com.qq.local
     * com.qq.local.store 不符合规则，返回空字符串
     * @param packageName
     * @return
     */
    public static String extractMergePackageName(String packageName) {
        if (StringUtils.isBlank(packageName)) {
            return "";
        }
        Matcher m = mergePackageNamePattern.matcher(packageName);
        if (m.find()) {
            return m.group(1);
        }
        return "";
    }
}
