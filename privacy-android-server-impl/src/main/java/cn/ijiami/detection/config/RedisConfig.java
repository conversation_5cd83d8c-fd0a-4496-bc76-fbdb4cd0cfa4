package cn.ijiami.detection.config;

import cn.ijiami.detection.VO.AndroidSensorLog;
import cn.ijiami.detection.VO.ChannelMonitoring;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * @date 2020/1/3 11:42
 */
@EnableCaching
@Configuration
public class RedisConfig {

    /**
     * redisTemplate 序列化使用的jdkSerializeable, 存储二进制字节码, 所以自定义序列化类
     *
     * @param redisConnectionFactory
     * @return
     */
    @Bean
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 设置value的序列化规则和 key的序列化规则
        redisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, RealTimeBehaviorLog> androidLogTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, RealTimeBehaviorLog> redisTemplate = new RedisTemplate<>();
        FastJsonRedisSerializer<RealTimeBehaviorLog> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(RealTimeBehaviorLog.class);
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, IOSRealTimeLog> iosLogTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, IOSRealTimeLog> redisTemplate = new RedisTemplate<>();
        FastJsonRedisSerializer<IOSRealTimeLog> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(IOSRealTimeLog.class);
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, RealTimeNetLog> netLogTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, RealTimeNetLog> redisTemplate = new RedisTemplate<>();
        FastJsonRedisSerializer<RealTimeNetLog> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(RealTimeNetLog.class);
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String, AndroidSensorLog> sensorLogTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, AndroidSensorLog> redisTemplate = new RedisTemplate<>();
        FastJsonRedisSerializer<AndroidSensorLog> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(AndroidSensorLog.class);
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(fastJsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RedisTemplate<String,ChannelMonitoring> channelMonitoringTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String,ChannelMonitoring> channelMonitoringTemplate = new RedisTemplate<>();
        FastJsonRedisSerializer<ChannelMonitoring> fastJsonRedisSerializer = new FastJsonRedisSerializer<>(ChannelMonitoring.class);
        channelMonitoringTemplate.setConnectionFactory(redisConnectionFactory);
        channelMonitoringTemplate.setValueSerializer(fastJsonRedisSerializer);
        channelMonitoringTemplate.setKeySerializer(new StringRedisSerializer());
        channelMonitoringTemplate.setHashValueSerializer(fastJsonRedisSerializer);
        channelMonitoringTemplate.setHashKeySerializer(new StringRedisSerializer());
        channelMonitoringTemplate.afterPropertiesSet();
        return channelMonitoringTemplate;
    }

}
