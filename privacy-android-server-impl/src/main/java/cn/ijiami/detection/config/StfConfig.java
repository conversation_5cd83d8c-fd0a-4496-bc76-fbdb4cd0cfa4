package cn.ijiami.detection.config;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.stf.STFClient;
import cn.ijiami.stf.STFProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StfConfig {

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Bean
    public STFClient stfClient() {
        STFProperties stfProperties = new STFProperties();
        stfProperties.setUrl(commonProperties.getProperty("ijiami.stf.url"));
        stfProperties.setToken(commonProperties.getProperty("ijiami.stf.token"));
        return new STFClient(stfProperties);
    }
}
