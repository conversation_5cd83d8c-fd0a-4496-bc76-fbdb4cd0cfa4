package cn.ijiami.detection.service.impl;

import org.springframework.stereotype.Service;

import cn.ijiami.detection.entity.TReportStore;
import cn.ijiami.detection.mapper.TReportStoreMapper;
import cn.ijiami.detection.service.api.IReportStoreService;

@Service
public class ReportStoreServiceImpl implements IReportStoreService {

    private final TReportStoreMapper reportStoreMapper;

    public ReportStoreServiceImpl(TReportStoreMapper reportStoreMapper) {
        this.reportStoreMapper = reportStoreMapper;
    }

    @Override
    public TReportStore findReport(String documentId,Integer type) {
        return reportStoreMapper.findByDocumentIdAndType(documentId,type);
    }
}
