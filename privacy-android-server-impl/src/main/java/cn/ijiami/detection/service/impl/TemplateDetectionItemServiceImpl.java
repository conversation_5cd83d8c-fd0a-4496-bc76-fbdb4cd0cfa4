package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.entity.TTemplateDetectionItem;
import cn.ijiami.detection.mapper.TTemplateDetectionItemMapper;
import cn.ijiami.detection.service.api.ITemplateDetectionItemService;

/**
 * 模板用户检测项中间表接口实现类
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class TemplateDetectionItemServiceImpl implements ITemplateDetectionItemService {
	@Autowired
	private TTemplateDetectionItemMapper detectionItemMapper;

	@Override
	public int saveTemplateDetectionItem(Long templateId, List<Long> detectionItemIds) {
		List<TTemplateDetectionItem> templateDetectionItems = new ArrayList<>();
		TTemplateDetectionItem detectionItem = null;
		if (!CollectionUtils.isEmpty(detectionItemIds)) {
			for (Long itemId : detectionItemIds) {
				detectionItem = new TTemplateDetectionItem();
				detectionItem.setTemplateId(templateId);
				detectionItem.setDetectionItemId(itemId);
				Date createTime = new Date();
				detectionItem.setCreateTime(createTime);
				detectionItem.setUpdateTime(createTime);
				templateDetectionItems.add(detectionItem);
			}
		}
		return detectionItemMapper.insertTemplateDetectionItem(templateDetectionItems);
	}

	@Override
	public int deleteDetectionItemByTemplateId(Long templateId) {
		return detectionItemMapper.deleteDetectionItemByTemplateId(templateId);
	}

}
