package cn.ijiami.detection.service.impl;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.CustomIosSdkClass;
import cn.ijiami.detection.VO.CustomSdkLibraryVO;
import cn.ijiami.detection.VO.CustomSdkSave;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.sdk.SdkInfoResponse;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryClass;
import cn.ijiami.detection.entity.TSdkLibraryLabel;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.entity.TSdkLibraryType;
import cn.ijiami.detection.entity.TSdkLibraryUpdate;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.SdkSourceEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionPermissionMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TSdkLibraryClassMapper;
import cn.ijiami.detection.mapper.TSdkLibraryLabelMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSdkLibraryPackageMapper;
import cn.ijiami.detection.mapper.TSdkLibraryTypeMapper;
import cn.ijiami.detection.mapper.TSdkLibraryUpdateMapper;
import cn.ijiami.detection.query.CustomSdkQuery;
import cn.ijiami.detection.service.ISdkService;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.excel.ExcelSDKParser;
import cn.ijiami.framework.common.enums.HttpStatusEnum;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.kit.utils.UuidUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_UPLOAD_SDK;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkServiceImpl.java
 * @Description sdk服务
 * @createTime 2023年04月24日 18:46:00
 */
@Component
public class SdkServiceImpl implements ISdkService {

    private static final Logger LOG = LoggerFactory.getLogger(SdkServiceImpl.class);

    private static final Long SDK_UPDATE_RECORD_ID = 1L;

    @Resource
    @Qualifier("redisDistributedLock")
    private DistributedLockService distributedLockService;

    @Resource
    private TSdkLibraryUpdateMapper sdkLibraryUpdateMapper;

    @Resource
    private TSdkLibraryMapper sdkLibraryMapper;

    @Resource
    private TActionPermissionMapper actionPermissionMapper;

    @Resource
    private TSdkLibraryPackageMapper sdkLibraryPackageMapper;

    @Resource
    private TSdkLibraryClassMapper sdkLibraryClassMapper;

    @Resource
    private TPermissionMapper permissionMapper;

    @Resource
    private TSdkLibraryLabelMapper sdkLibraryLabelMapper;

    @Resource
    private TSdkLibraryTypeMapper sdkLibraryTypeMapper;

    @Resource
    IjiamiCommonProperties commonProperties;
    @Resource
    ExcelSDKParser excelSDKParser;

    public void executeUpdateSdkInfo(String sdkServer) {
        try {
            if (!distributedLockService.tryLock(KEY_UPLOAD_SDK, TimeUnit.MINUTES.toMillis(15))) {
                LOG.info("有其他服务正在更新中");
                return;
            }
            TSdkLibraryUpdate updateRecord = sdkLibraryUpdateMapper.selectByPrimaryKey(SDK_UPDATE_RECORD_ID);
            if (updateRecord != null
                    && updateRecord.getUpdateTime() != null
                    && updateRecord.getUpdateTime().getTime() > System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5)) {
                LOG.info("更新时间未到，不需要更新SDK");
                return;
            }
            boolean isUpdate = false;
            boolean sdkNew = false;
            if (updateRecord == null) {
                updateRecord = new TSdkLibraryUpdate();
                updateRecord.setId(SDK_UPDATE_RECORD_ID);
                updateRecord.setIdentification(UuidUtil.randomNumber());
                updateRecord.setVersion("");
                isUpdate = false;
            } else {
                isUpdate = true;
            }
            String url = sdkServer + "/restSdk/exportSdk?identification=" + updateRecord.getIdentification() + "&version=" + updateRecord.getVersion();
            LOG.info("updateSdkInfo.url={}",url);
            String responseStr = HttpUtils.httpGetString(url, null, 1);
            SdkInfoResponse response = CommonUtil.jsonToBean(responseStr, new TypeReference<SdkInfoResponse>() {
            });
            if (response != null && response.getSuccess() && response.getResult()!=null && StringUtils.isNoneBlank(response.getResult().getVersion())) {
                LOG.info("updateSdkInfo 开始更新数据");
                addSdk(filterNotEmptyPacage(response.getResult().getAddList())); //过滤包名为空
                deleteSdk(filterNotEmptyPacage(response.getResult().getDeleteList()));
                updateSdk(filterNotEmptyPacage(response.getResult().getUpdateList()));
                updateRecord.setVersion(response.getResult().getVersion());
                updateRecord.setUpdateTime(new Date());
                sdkNew = true;
            }
            if (isUpdate) {
            	if(sdkNew) {
            		sdkLibraryUpdateMapper.updateByPrimaryKeySelective(updateRecord);
            	}
            } else {
                sdkLibraryUpdateMapper.insert(updateRecord);
            }
            LOG.info("updateSdkInfo 更新完成");
        } catch (Throwable throwable) {
            LOG.error("updateSdkInfo error", throwable);
        } finally {
            distributedLockService.unlock(KEY_UPLOAD_SDK);
        }
    }

    @Override
    public List<TSdkLibraryType> findSdkTypeList() {
        return sdkLibraryTypeMapper.selectAll();
    }

    @Override
    public List<TSdkLibraryLabel> findSdkBadLabelList() {
        return sdkLibraryLabelMapper.selectAll();
    }

    @Override
    public PageInfo<CustomSdkLibraryVO> findListByPage(IUser user, CustomSdkQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<TSdkLibrary> libraryList = sdkLibraryMapper.findCustomSdkByPage(query);
        PageInfo<TSdkLibrary> libraryPage = new PageInfo<>(libraryList);
        PageInfo<CustomSdkLibraryVO> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(libraryPage, resultPage);
        resultPage.setList(libraryList.stream().map(sdkLibrary -> {
            CustomSdkLibraryVO libraryVO = new CustomSdkLibraryVO();
            BeanUtils.copyProperties(sdkLibrary, libraryVO);
            libraryVO.setSdkDescribe(sdkLibrary.getDescribe());
            libraryVO.setSdkManufacturer(sdkLibrary.getManufacturer());
            libraryVO.setSource(sdkLibrary.getSource());
            if (StringUtils.isNotBlank(sdkLibrary.getPermissionCodes())) {
                libraryVO.setPermissionList(permissionMapper.findPermissionByPermissionCodes(sdkLibrary.getPermissionCodes()));
            }
            if (StringUtils.isNotBlank(sdkLibrary.getTypeName())) {
                TSdkLibraryType typeQuery = new TSdkLibraryType();
                typeQuery.setTypeName(sdkLibrary.getTypeName());
                libraryVO.setSdkType(sdkLibraryTypeMapper.selectOne(typeQuery));
            }
            if (Objects.nonNull(sdkLibrary.getBadLabelId())) {
                libraryVO.setBadLabel(sdkLibraryLabelMapper.selectByPrimaryKey(sdkLibrary.getBadLabelId()));
            }
            Long sameSdkCount = sdkLibraryMapper.countByNameAndVersion(
                    sdkLibrary.getName(),
                    sdkLibrary.getVersion(),
                    sdkLibrary.getTerminalType(),
                    sdkLibrary.getPackageList()
                            .stream()
                            .map(TSdkLibraryPackage::getPackageName)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList()));
            libraryVO.setIdentifier(Objects.isNull(sameSdkCount) || sameSdkCount == 1 ? "" : "重复数据");
            return libraryVO;
        }).collect(Collectors.toList()));
        return resultPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(IUser user, CustomSdkSave customSdkSave) {
        checkParams(customSdkSave);
        if (Objects.isNull(customSdkSave.getId()) || customSdkSave.getId() == 0) {
            TSdkLibrary sdkLibrary = new TSdkLibrary();
            setSdkLibraryInfo(sdkLibrary, customSdkSave);
            sdkLibraryMapper.insertSelective(sdkLibrary);
            LOG.info("save 新增SDK sdkId={} name={}", sdkLibrary.getId(), sdkLibrary.getName());
            createPackageName(sdkLibrary, customSdkSave, user);
            createClass(sdkLibrary, customSdkSave, user);
        } else {
            TSdkLibrary sdkLibrary = sdkLibraryMapper.selectByPrimaryKey(customSdkSave.getId());
            if (Objects.isNull(sdkLibrary)) {
                throw new IllegalArgumentException("sdk id 错误");
            }
            sdkLibrary.setId(customSdkSave.getId());
            setSdkLibraryInfo(sdkLibrary, customSdkSave);
            sdkLibraryMapper.updateByPrimaryKey(sdkLibrary);
            LOG.info("save 更新SDK sdkId={} name={}", sdkLibrary.getId(), sdkLibrary.getName());
            // 是否要更新包名信息
            updatePackageName(sdkLibrary, customSdkSave, user);
            updateClass(sdkLibrary, customSdkSave, user);
        }
    }

    private void createClass(TSdkLibrary sdkLibrary, CustomSdkSave customSdkSave, IUser user) {
        if (CollectionUtils.isEmpty(customSdkSave.getClassFunctionList())) {
            return;
        }
        customSdkSave.getClassFunctionList().forEach(c -> {
            TSdkLibraryClass insertData = makeClass(sdkLibrary.getId(), c, user);
            sdkLibraryClassMapper.insert(insertData);
            LOG.info("save 新增包名 sdkId={} class={} function={}", sdkLibrary.getId(), insertData.getClazz(), insertData.getFunction());
        });
    }

    private void updateClass(TSdkLibrary sdkLibrary, CustomSdkSave customSdkSave, IUser user) {
        if (CollectionUtils.isEmpty(customSdkSave.getClassFunctionList())) {
            return;
        }
        Example example = new Example(TSdkLibraryClass.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sdkId", customSdkSave.getId());
        // 不在返回信息中的包名需要删掉
        List<TSdkLibraryClass> classList = sdkLibraryClassMapper.selectByExample(example);
        classList.stream()
                .filter(c -> !newDataContains(customSdkSave, c))
                .forEach(c -> {
                    sdkLibraryClassMapper.deleteByPrimaryKey(c.getId());
                    LOG.info("save 删除类名 sdkId={} class={}", c.getSdkId(), c.getClazz());
                });
        // 不在数据库中的包名需要添加
        customSdkSave.getClassFunctionList()
                .stream()
                .filter(c -> !oldDataContains(classList, c))
                .forEach(c -> {
                    TSdkLibraryClass insertData = makeClass(sdkLibrary.getId(), c, user);
                    sdkLibraryClassMapper.insert(insertData);
                    LOG.info("save 新增类名 sdkId={} class={}", sdkLibrary.getId(), insertData.getClazz());
                });
    }

    private boolean newDataContains(CustomSdkSave customSdkSave, TSdkLibraryClass clazz) {
        return customSdkSave.getClassFunctionList()
                .stream()
                .anyMatch(c -> StringUtils.equals(c.getClazz(), clazz.getClazz()) && StringUtils.equals(c.getFunction(), clazz.getFunction()));
    }

    private boolean oldDataContains(List<TSdkLibraryClass> classList, CustomIosSdkClass clazz) {
        return classList
                .stream()
                .anyMatch(c -> StringUtils.equals(c.getClazz(), clazz.getClazz()) && StringUtils.equals(c.getFunction(), clazz.getFunction()));
    }

    private TSdkLibraryPackage makePackage(Long sdkId, String packageName, IUser user) {
        TSdkLibraryPackage insertData = new TSdkLibraryPackage();
        insertData.setSdkId(sdkId);
        insertData.setPackageName(packageName);
        insertData.setCreatedTime(new Date());
        insertData.setUpdateTime(new Date());
        insertData.setCreatedUserId(user.getUserId());
        insertData.setUpdateUserId(user.getUserId());
        return insertData;
    }

    private TSdkLibraryClass makeClass(Long sdkId, CustomIosSdkClass c, IUser user) {
        TSdkLibraryClass insertData = new TSdkLibraryClass();
        insertData.setSdkId(sdkId);
        insertData.setClazz(c.getClazz());
        insertData.setFunction(c.getFunction());
        insertData.setCreatedTime(new Date());
        insertData.setUpdateTime(new Date());
        insertData.setCreatedUserId(user.getUserId());
        insertData.setUpdateUserId(user.getUserId());
        return insertData;
    }

    private void createPackageName(TSdkLibrary sdkLibrary, CustomSdkSave customSdkSave, IUser user) {
        if (CollectionUtils.isEmpty(customSdkSave.getPackageList())) {
            return;
        }
        customSdkSave.getPackageList().forEach(p -> {
            TSdkLibraryPackage insertData = makePackage(sdkLibrary.getId(), p, user);
            sdkLibraryPackageMapper.insert(insertData);
            LOG.info("save 新增包名 sdkId={} packageName={}", sdkLibrary.getId(), insertData.getPackageName());
        });
    }

    private void updatePackageName(TSdkLibrary sdkLibrary, CustomSdkSave customSdkSave, IUser user) {
        if (CollectionUtils.isEmpty(customSdkSave.getPackageList())) {
            return;
        }
        Example example = new Example(TSdkLibraryPackage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sdkId", customSdkSave.getId());
        // 不在返回信息中的包名需要删掉
        List<TSdkLibraryPackage> packageList = sdkLibraryPackageMapper.selectByExample(example);
        packageList.stream()
                .filter(p -> !customSdkSave.getPackageList().contains(p.getPackageName()))
                .forEach(p -> {
                    sdkLibraryPackageMapper.deleteByPrimaryKey(p.getId());
                    LOG.info("save 删除包名 sdkId={} packageName={}", p.getSdkId(), p.getPackageName());
                });
        // 不在数据库中的包名需要添加
        customSdkSave.getPackageList()
                .stream()
                .filter(p -> packageList.stream().noneMatch(tp -> tp.getPackageName().equals(p)))
                .forEach(p -> {
                    TSdkLibraryPackage insertData = makePackage(sdkLibrary.getId(), p, user);
                    sdkLibraryPackageMapper.insert(insertData);
                    LOG.info("save 新增包名 sdkId={} packageName={}", insertData.getSdkId(), insertData.getPackageName());
                });
    }

    private void checkParams(CustomSdkSave customSdkSave) {
        if ((customSdkSave.getTerminalType() == TerminalTypeEnum.ANDROID.getValue() || customSdkSave.getTerminalType() == TerminalTypeEnum.HARMONY.getValue())
                && CollectionUtils.isEmpty(customSdkSave.getPackageList())) {
            throw new IllegalArgumentException("包名不能为空");
        }
        if (customSdkSave.getTerminalType() == TerminalTypeEnum.IOS.getValue()
                && CollectionUtils.isEmpty(customSdkSave.getClassFunctionList())) {
            throw new IllegalArgumentException("类名不能为空");
        }
        if (Objects.isNull(customSdkSave.getId()) && StringUtils.isNotBlank(customSdkSave.getUniqueNo())) {
            Example example = new Example(TSdkLibrary.class);
            example.createCriteria().andEqualTo("uniqueNo", customSdkSave.getUniqueNo());
            if (sdkLibraryMapper.selectCountByExample(example) > 0) {
                throw new IllegalArgumentException("唯一编码重复");
            }
        }
    }

    private void setSdkLibraryInfo(TSdkLibrary sdkLibrary, CustomSdkSave customSdkSave) {
        sdkLibrary.setName(customSdkSave.getName());
        sdkLibrary.setAlias(customSdkSave.getAlias());
        sdkLibrary.setVersion(customSdkSave.getVersion());
        if (StringUtils.isNotBlank(customSdkSave.getUniqueNo())) {
            sdkLibrary.setUniqueNo(customSdkSave.getUniqueNo());
        }
        sdkLibrary.setGroupId(customSdkSave.getGroupId());
        sdkLibrary.setArtifactId(customSdkSave.getArtifactId());
        sdkLibrary.setDownloadUrl(customSdkSave.getDownloadUrl());
        sdkLibrary.setDescribe(customSdkSave.getSdkDescribe());
        sdkLibrary.setTerminalType(customSdkSave.getTerminalType());
        sdkLibrary.setSdkMd5(customSdkSave.getSdkMd5());
        sdkLibrary.setCompileSdkVersion(customSdkSave.getCompileSdkVersion());
        sdkLibrary.setMinSdkVersion(customSdkSave.getMinSdkVersion());
        sdkLibrary.setNewVersionFeature(customSdkSave.getNewVersionFeature());
        sdkLibrary.setAppKey(customSdkSave.getAppKey());
        sdkLibrary.setApiUrl(customSdkSave.getApiUrl());
        sdkLibrary.setPrivacyUrl(customSdkSave.getPrivacyUrl());
        sdkLibrary.setWebsiteUrl(customSdkSave.getWebsiteUrl());
        sdkLibrary.setIpAddress(customSdkSave.getIp());
        sdkLibrary.setHost(customSdkSave.getHost());
        sdkLibrary.setBadLabelId(customSdkSave.getBadLabelId());
        if (Objects.nonNull(customSdkSave.getFirstPartyLibrary())) {
            sdkLibrary.setFirstPartyLibrary(customSdkSave.getFirstPartyLibrary());
        } else {
            sdkLibrary.setFirstPartyLibrary(false);
        }
        TSdkLibraryType type = sdkLibraryTypeMapper.selectByPrimaryKey(customSdkSave.getSdkTypeId());
        if (Objects.nonNull(type)) {
            sdkLibrary.setTypeName(type.getTypeName());
        } else{
            sdkLibrary.setTypeName(null);
        }
        sdkLibrary.setSdkPath(customSdkSave.getSdkPath());
        sdkLibrary.setChargeType(customSdkSave.getChargeType());
        sdkLibrary.setManufacturer(customSdkSave.getSdkManufacturer());
        sdkLibrary.setSdkManufacturerAddress(customSdkSave.getSdkManufacturerAddress());
        sdkLibrary.setSdkManufacturerWebsiteUrl(customSdkSave.getSdkManufacturerWebsiteUrl());
        if (CollectionUtils.isNotEmpty(customSdkSave.getPermissionCodeList())) {
            String codes = permissionMapper.findByPermissionCodeList(customSdkSave.getPermissionCodeList())
                    .stream()
                    .map(PermissionVO::getPermissionCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
            sdkLibrary.setPermissionCodes(codes);
        }
        sdkLibrary.setHotfix(false);
        sdkLibrary.setPackageName(null);
        if(customSdkSave.getSdkSource() != null && customSdkSave.getSdkSource() == SdkSourceEnum.CAICTAC.getValue()) {
        	sdkLibrary.setPackageName(customSdkSave.getPackageList().get(0));
        }
        sdkLibrary.setSource(customSdkSave.getSdkSource()==null?SdkSourceEnum.CUSTOM: SdkSourceEnum.getItem(customSdkSave.getSdkSource()));
        sdkLibrary.setCoordinate(customSdkSave.getCoordinate());
        sdkLibrary.setIosHeaderFileName(customSdkSave.getIosHeaderFileName());
    }

    @Override
    public void delete(IUser user, Long id) {
        Example example = new Example(TSdkLibrary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        List<Object> sources = Arrays.asList(SdkSourceEnum.CUSTOM.itemValue(), SdkSourceEnum.CAICTAC.itemValue());
        criteria.andIn("source", sources);
        sdkLibraryMapper.deleteByExample(example);
    }


    private List<SdkInfoResponse.ResultDTO.SdkDTO> filterNotEmptyPacage(List<SdkInfoResponse.ResultDTO.SdkDTO> sdkList) {
        if (CollectionUtils.isEmpty(sdkList)) {
            return Collections.emptyList();
        }
        return sdkList.stream().filter(sdk -> StringUtils.isNotBlank(sdk.getPackageName()) || sdk.getSdkPackageList()!= null).collect(Collectors.toList());
    }

    private void addSdk(List<SdkInfoResponse.ResultDTO.SdkDTO> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            LOG.info("不需要添加sdk");
            return;
        }
        Lists.partition(addList, 100).forEach(sdkList -> {
            List<String> packageList = sdkList.stream().map(SdkInfoResponse.ResultDTO.SdkDTO::getPackageName)
                    .collect(Collectors.toList());
            // 查询是否已经存在数据库中了
            List<TSdkLibrary> libraryList = sdkLibraryMapper.findBaseSdkInPackage(packageList);
            // 判断是否数据库里面已经存在了这个sdk
            for (SdkInfoResponse.ResultDTO.SdkDTO sdk : sdkList) {
                Optional<TSdkLibrary> libraryOptional = findTSdkLibrary(libraryList, sdk);
                if (libraryOptional.isPresent()) {
                    updateSdkInfoToDatabase(libraryOptional.get(), sdk);
                } else {
                    insertSdkInfoToDatabase(sdk);
                }
            }
        });
    }

    /**
     * 根据md5和version去判断是否同一个sdk数据，因为单纯靠md5去判断不准确，有可能重复
     *
     * @param libraryList
     * @param sdk
     * @return
     */
    private Optional<TSdkLibrary> findTSdkLibrary(List<TSdkLibrary> libraryList, SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        Optional<TSdkLibrary> opt = libraryList.stream().filter(sdkLibrary -> samePackageName(sdkLibrary, sdk)).findFirst();
        if (opt.isPresent()) {
            return opt;
        } else {
            return libraryList.stream().filter(sdkLibrary -> sameMd5(sdkLibrary, sdk)).findFirst();
        }
    }

    private boolean samePackageName(TSdkLibrary sdkLibrary, SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        return StringUtils.equals(sdkLibrary.getPackageName(), sdk.getPackageName())
                && StringUtils.equals(sdkLibrary.getVersion(), sdk.getSdkVersion());
    }

    private boolean sameMd5(TSdkLibrary sdkLibrary, SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        return StringUtils.equals(sdkLibrary.getSdkMd5(), sdk.getMd5())
                && StringUtils.equals(sdkLibrary.getVersion(), sdk.getSdkVersion());
    }

    private void deleteSdk(List<SdkInfoResponse.ResultDTO.SdkDTO> sdkList) {
        if (CollectionUtils.isEmpty(sdkList)) {
            LOG.info("不需要删除sdk");
            return;
        }
        Lists.partition(sdkList, 100).forEach(list -> {
            LOG.info("删除sdk={}", list);
            List<String> pacakgeList = list.stream().map(SdkInfoResponse.ResultDTO.SdkDTO::getPackageName)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pacakgeList)) {
                List<TSdkLibrary> sdkLibraryList = sdkLibraryMapper.findBaseSdkInPackage(pacakgeList);
                for (SdkInfoResponse.ResultDTO.SdkDTO sdk : list) {
                    Optional<TSdkLibrary> sdkLibraryOptional = findTSdkLibrary(sdkLibraryList, sdk);
                    // 存在数据库内的才能去删除
                    sdkLibraryOptional.ifPresent(this::deleteSdkLibrary);
                }
            }
        });
    }

    private void deleteSdkLibrary(TSdkLibrary sdkLibrary) {
        LOG.info("updateSdkInfo 删除sdk package={}", sdkLibrary.getPackageName());
        sdkLibraryMapper.deleteByPrimaryKey(sdkLibrary.getId());
        // 删除包名
        Example example = new Example(TSdkLibraryPackage.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sdkId", sdkLibrary.getId());
        sdkLibraryPackageMapper.deleteByExample(example);
    }

    private void updateSdk(List<SdkInfoResponse.ResultDTO.SdkDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            LOG.info("不需要更新sdk");
            return;
        }
        Lists.partition(updateList, 100).forEach(list -> {
            List<String> md5List = list.stream().map(SdkInfoResponse.ResultDTO.SdkDTO::getPackageName)
                    .collect(Collectors.toList());
            List<TSdkLibrary> sdkLibraryList = sdkLibraryMapper.findBaseSdkInPackage(md5List);
            for (SdkInfoResponse.ResultDTO.SdkDTO sdk : list) {
                Optional<TSdkLibrary> sdkLibraryOptional = findTSdkLibrary(sdkLibraryList, sdk);
                // 存在数据库内的才能去更新
                sdkLibraryOptional.ifPresent(sdkLibrary -> updateSdkInfoToDatabase(sdkLibrary, sdk));
            }
        });
    }

    private void insertSdkInfoToDatabase(SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        LOG.info("updateSdkInfo 新增sdk md5={}", sdk.getMd5());
        TSdkLibrary sdkLibrary = new TSdkLibrary();
        sdkLibrary.setPackageName(sdk.getPackageName());
        sdkLibrary.setName(sdk.getSdkName());
        sdkLibrary.setSdkMd5(sdk.getMd5());
        sdkLibrary.setUrl(sdk.getWebsiteUrl());
        sdkLibrary.setMinSdkVersion(strToVersion(sdk.getMinVersion()));
        sdkLibrary.setTargetSdkVersion(strToVersion(sdk.getTargetVersion()));
        sdkLibrary.setCompileSdkVersion(strToVersion(sdk.getCompileVersion()));
        sdkLibrary.setManufacturer("--");
        sdkLibrary.setDescribe(sdk.getDescription());
        sdkLibrary.setTypeName(sdk.getFuncClassify());
        sdkLibrary.setTerminalType(sdk.getTerminalType().getValue());
        if (Objects.nonNull(sdk.getSdkPermissionList())) {
            if (sdk.getSdkPermissionList().isEmpty()) {
                sdkLibrary.setPermissionCodes(null);
            } else {
                String codes = String.join(",", actionPermissionMapper.findCodeInName(sdk.getSdkPermissionList()));
                sdkLibrary.setPermissionCodes(codes);
            }
        }
        sdkLibrary.setRiskLevel(true);
        sdkLibrary.setVersion(sdk.getSdkVersion());
        if (Objects.nonNull(sdk.getHotfix())) {
            sdkLibrary.setHotfix(sdk.getHotfix() == BooleanEnum.TRUE.value);
        } else {
            sdkLibrary.setHotfix(false);
        }
        sdkLibrary.setSource(SdkSourceEnum.BASE);
        sdkLibraryMapper.insertSelective(sdkLibrary);
        // 是否要更新包名信息
        if (CollectionUtils.isNotEmpty(sdk.getSdkPackageList())) {
            // 不在数据库中的包名需要添加
            sdk.getSdkPackageList()
                    .stream()
                    .filter(StringUtils::isNotBlank)
                    .forEach(p -> {
                        TSdkLibraryPackage insertData = new TSdkLibraryPackage();
                        insertData.setSdkId(sdkLibrary.getId());
                        insertData.setPackageName(p);
                        sdkLibraryPackageMapper.insert(insertData);
                        LOG.info("updateSdkInfo 新增包名 sdkId={} packageName={}", insertData.getSdkId(), insertData.getPackageName());
                    });
        }
    }

    private void updateSdkInfoToDatabase(TSdkLibrary sdkLibrary, SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        // 更新sdk信息
        setUpdateInfo(sdkLibrary, sdk);
        sdkLibraryMapper.updateByPrimaryKeySelective(sdkLibrary);
        if (Objects.isNull(sdkLibrary.getPackageList())) {
            sdkLibrary.setPackageList(new ArrayList<>());
        }
        // 是否要更新包名信息
        if (CollectionUtils.isNotEmpty(sdk.getSdkPackageList())) {
            // 不在返回信息中的包名需要删掉
            sdkLibrary.getPackageList()
                    .stream()
                    .filter(p -> !sdk.getSdkPackageList().contains(p.getPackageName()))
                    .forEach(p -> {
                        sdkLibraryPackageMapper.deleteByPrimaryKey(p.getId());
                        LOG.info("updateSdkInfo 删除包名 sdkId={} packageName={}", p.getSdkId(), p.getPackageName());
                    });
            // 不在数据库中的包名需要添加
            sdk.getSdkPackageList()
                    .stream()
                    .filter(StringUtils::isNotBlank)
                    .filter(p -> sdkLibrary.getPackageList().stream().noneMatch(tp -> StringUtils.equalsIgnoreCase(tp.getPackageName(), p)))
                    .forEach(p -> {
                        TSdkLibraryPackage insertData = new TSdkLibraryPackage();
                        insertData.setSdkId(sdkLibrary.getId());
                        insertData.setPackageName(p);
                        sdkLibraryPackageMapper.insert(insertData);
                        LOG.info("updateSdkInfo 新增包名 sdkId={} packageName={}", insertData.getSdkId(), insertData.getPackageName());
                    });
        }
    }

    private void setUpdateInfo(TSdkLibrary sdkLibrary, SdkInfoResponse.ResultDTO.SdkDTO sdk) {
        LOG.info("updateSdkInfo 更新前 sdk={}", sdkLibrary);
        sdkLibrary.setPackageName(sdk.getPackageName());
        sdkLibrary.setManufacturer(sdk.getCompanyName());
        sdkLibrary.setName(sdk.getSdkName());
        sdkLibrary.setSdkMd5(sdk.getMd5());
        sdkLibrary.setUrl(sdk.getWebsiteUrl());
        sdkLibrary.setMinSdkVersion(strToVersion(sdk.getMinVersion()));
        sdkLibrary.setTargetSdkVersion(strToVersion(sdk.getTargetVersion()));
        sdkLibrary.setCompileSdkVersion(strToVersion(sdk.getCompileVersion()));
        sdkLibrary.setDescribe(sdk.getDescription());
        sdkLibrary.setTypeName(sdk.getFuncClassify());
        if (Objects.nonNull(sdk.getHotfix())) {
            sdkLibrary.setHotfix(sdk.getHotfix() == BooleanEnum.TRUE.value);
        }
        if (Objects.nonNull(sdk.getSdkPermissionList())) {
            if (sdk.getSdkPermissionList().isEmpty()) {
                sdkLibrary.setPermissionCodes(null);
            } else {
                String codes = String.join(",", actionPermissionMapper.findCodeInName(sdk.getSdkPermissionList()));
                sdkLibrary.setPermissionCodes(codes);
            }
        }
        sdkLibrary.setVersion(sdk.getSdkVersion());
        LOG.info("updateSdkInfo 更新后 sdk={}", sdkLibrary);
    }

    private Integer strToVersion(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            LOG.info("strToVersion error=" + str);
            return null;
        }
    }

    public BaseResponse<Object> uploadFileImport(IUser user, MultipartFile file) {
        BaseResponse<Object> response = new BaseResponse<>();
        LOG.info("上传文件 file={}", file.getName());
        String filePath = commonProperties.getFilePath() + File.separator + "default" + File.separator + file.getOriginalFilename();
        File temp = new File(filePath);
        try {
            file.transferTo(temp);
            if(!temp.exists()) {
                response.setStatus(HttpStatusEnum.FAIL.getValue());
                response.setMessage("上传失败,文件不存在");
                return response;
            }
            List<CustomSdkSave> sdkList = excelSDKParser.parseExcel(filePath);
            if(sdkList.isEmpty()) {
                response.setStatus(HttpStatusEnum.FAIL.getValue());
                response.setMessage("表格数据为空");
                return response;
            }
            sdkList.forEach(sdk -> {
                this.save(user, sdk);
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            temp.delete();
        }
        return response;
    }

}
