package cn.ijiami.detection.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.ijiami.detection.android.client.dto.AssetsStatisticalSummaryDTO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.android.client.dto.AssetsDetectionDTO;
import cn.ijiami.detection.android.client.dto.AssetsInfoDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDTO;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDetailDTO;
import cn.ijiami.detection.VO.statistics.AssetsTask;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesApp;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesAppLaw;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesAssetsInfo;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesDetail;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesLaw;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesLawItem;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportAssetsInfo;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportItem;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportLaw;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesStatistics;
import cn.ijiami.detection.VO.statistics.DetectionStatistics;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsDetail;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsLawItem;
import cn.ijiami.detection.VO.statistics.HomePageDailyStatistics;
import cn.ijiami.detection.VO.statistics.HomePageDetectionStatistics;
import cn.ijiami.detection.VO.statistics.HomePageLawStatistics;
import cn.ijiami.detection.VO.statistics.LawFalsePositivesStatistics;
import cn.ijiami.detection.VO.statistics.LawStatistics;
import cn.ijiami.detection.VO.statistics.RiskResult;
import cn.ijiami.detection.VO.statistics.SdkIssueStatistics;
import cn.ijiami.detection.VO.statistics.SdkRiskResult;
import cn.ijiami.detection.VO.statistics.SdkStatisticsDetail;
import cn.ijiami.detection.VO.statistics.SdkUsageStatistics;
import cn.ijiami.detection.entity.AssetsDetectionStatistics;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyLawsRegulations;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.StatisticsLawType;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.mapper.TAppLawsDectDetailMapper;
import cn.ijiami.detection.mapper.TAppLawsRiskCollectMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsRegulationsMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsResultMapper;
import cn.ijiami.detection.mapper.TPrivacyResultMarkMapper;
import cn.ijiami.detection.mapper.TSdkDectDetailMapper;
import cn.ijiami.detection.mapper.TSdkLawsDectDetailMapper;
import cn.ijiami.detection.android.client.param.AssetsDetailsParam;
import cn.ijiami.detection.android.client.param.AssetsInfoParam;
import cn.ijiami.detection.android.client.param.AssetsStatisticsParam;
import cn.ijiami.detection.query.DetectionDetailsQuery;
import cn.ijiami.detection.query.DetectionLawItemQuery;
import cn.ijiami.detection.query.DetectionStatisticsQuery;
import cn.ijiami.detection.query.HomePageDetectionStatisticsQuery;
import cn.ijiami.detection.query.SdkDetailsQuery;
import cn.ijiami.detection.service.api.StatisticsService;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.LawDetectResultUtils;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StatisticsServiceImpl.java
 * @Description 检测统计
 * @createTime 2022年07月01日 11:58:00
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TPrivacyLawsResultMapper privacyLawsResultMapper;

    @Autowired
    private TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;

    @Autowired
    private TAppLawsDectDetailMapper appLawsDectDetailMapper;

    @Autowired
    private TAppLawsRiskCollectMapper appLawsRiskCollectMapper;

    @Autowired
    private TSdkLawsDectDetailMapper sdkLawsDectDetailMapper;

    @Autowired
    private TSdkDectDetailMapper sdkDectDetailMapper;

    @Autowired
    private TPrivacyResultMarkMapper privacyResultMarkMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Override
    public AssetsStatisticalSummaryDTO assetsStatisticalSummary(AssetsStatisticsParam query) {
        AssetsStatisticalSummaryDTO assetsStatisticsVO = new AssetsStatisticalSummaryDTO();
        assetsStatisticsVO.setAssetsStatistics(assetsStatistics(query));
        assetsStatisticsVO.setDetectionTopList(detectionTopList(query));
        return assetsStatisticsVO;
    }

    @Override
    public AssetsStatisticsDTO assetsStatistics(AssetsStatisticsParam query) {
        LocalDate now = LocalDate.now();
        LocalDate monday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        long week = LocalDateTime.of(monday, LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        long month = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        LocalDate firstDayOfYear = now.with(TemporalAdjusters.firstDayOfYear());
        long year = LocalDateTime.of(firstDayOfYear, LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        AssetsStatisticsDTO statistics = new AssetsStatisticsDTO();
        statistics.setIncrementThisWeek(incrementByDateTime(query.getUserId(), terminalType, week));
        statistics.setIncrementThisMonth(incrementByDateTime(query.getUserId(), terminalType, month));
        statistics.setIncrementThisYear(incrementByDateTime(query.getUserId(), terminalType, year));
        statistics.setTerminalType(terminalType.getValue());
        // 资产总数
        statistics.setTotalCount(incrementByDateTime(query.getUserId(), terminalType, 0));
        return statistics;
    }

    private DetectFalsePositivesStatistics emptyDetectFalsePositivesStatistics(DetectionStatisticsQuery query) {
        DetectFalsePositivesStatistics statistics = new DetectFalsePositivesStatistics();
        statistics.setTerminalType(query.getTerminalType());
        return statistics;
    }

    private int incrementByDateTime(Long userId, TerminalTypeEnum terminalType, long dateTime) {
        Example example = new Example(TAssets.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andGreaterThanOrEqualTo("createTime", new Date(dateTime));
        criteria.andEqualTo("isDelete", StatusEnum.NORMAL.itemValue());
        criteria.andEqualTo("terminalType", terminalType);
        criteria.andEqualTo("createUserId", userId);
        return assetsMapper.selectCountByExample(example);
    }

    @Override
    public List<AssetsDetectionDTO> detectionTopList(AssetsStatisticsParam query) {
        // 如果有用户名，使用搜索到用户的信息
        return findDetectionCount(query.getUserId(), query, 10).stream().map(a -> {
            AssetsDetectionDTO statistics = new AssetsDetectionDTO();
            statistics.setDetectionCount(a.getDetectionCount());
            statistics.setAssetsName(a.getName());
            statistics.setVersion(a.getVersion());
            return statistics;
        }).sorted(Comparator.comparing(AssetsDetectionDTO::getDetectionCount).reversed()).collect(Collectors.toList());
    }

    private List<TAssets> findDetectionCount(Long userId, AssetsStatisticsParam query, Integer top) {
        return assetsMapper.detectionCountRank(userId,
                TerminalTypeEnum.getItem(query.getTerminalType()),
                query.getStartDate(),
                query.getEndDate(),
                query.getDetectionType(),
                query.getDetectionStatus(),
                query.getAssetsName(),
                top);
    }

    @Override
    public PageInfo<AssetsStatisticsDetailDTO> assetsDetailsByPage(AssetsDetailsParam query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        PageInfo<AssetsStatisticsDetailDTO> detailPageInfo = new PageInfo<>();
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        List<TAssets> rankPage = assetsMapper.assetsDetectionCountRank(query.getUserId(), terminalType,
                query.getStartDate(), query.getEndDate(), query.getDetectionType(), query.getDetectionStatus(), query.getAssetsName());
        PageInfo<TAssets> rankPageInfo = new PageInfo<>(rankPage);
        List<String> nameList = rankPage.stream().map(TAssets::getName).collect(Collectors.toList());
        BeanUtils.copyProperties(rankPageInfo, detailPageInfo);
        if (!nameList.isEmpty()) {
            List<AssetsDetectionStatistics> assetsList = assetsMapper.findSimpleByName(query.getUserId(), terminalType,
                    query.getStartDate(), query.getEndDate(), query.getDetectionType(), query.getDetectionStatus(), nameList);
            detailPageInfo.setList(makeAssetsStatisticsDetail(nameList, assetsList, terminalType, true));
        } else {
            detailPageInfo.setList(new ArrayList<>());
        }
        return detailPageInfo;
    }

    @Override
    public List<AssetsStatisticsDetailDTO> assetsDetailsAll(Long searchUserId, AssetsStatisticsParam query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        List<TAssets> rankPage = assetsMapper.assetsDetectionCountRank(searchUserId, terminalType,
                query.getStartDate(), query.getEndDate(), query.getDetectionType(), query.getDetectionStatus(), query.getAssetsName());
        List<String> nameList = rankPage.stream().map(TAssets::getName).collect(Collectors.toList());
        if (!nameList.isEmpty()) {
            List<AssetsDetectionStatistics> assetsList = assetsMapper.findSimpleByName(searchUserId, terminalType,
                    query.getStartDate(), query.getEndDate(), query.getDetectionType(), query.getDetectionStatus(), nameList);
            return makeAssetsStatisticsDetail(nameList, assetsList, terminalType, false);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<AssetsTask> assetsTaskAll(Long searchUserId, AssetsStatisticsParam query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        return assetsMapper.assetsTask(searchUserId, terminalType,
                query.getStartDate(), query.getEndDate(), query.getDetectionType(), query.getAssetsName());
    }

    @Override
    public DetectionStatistics detectionStatistics(Long searchUserId, DetectionStatisticsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        LawTypes lawTypes = buildLawTypes(terminalType, query.getLawType());
        DetectionStatistics statistics = new DetectionStatistics();
        statistics.setTerminalType(terminalType.getValue());
        statistics.setLaw164Statistics(makeLawStatistics(searchUserId, lawTypes.getLaw164(), query));
        statistics.setLaw191Statistics(makeLawStatistics(searchUserId, lawTypes.getLaw191(), query));
        statistics.setLaw35273Statistics(makeLawStatistics(searchUserId, lawTypes.getLaw35273(), query));
        statistics.setLaw41391Statistics(makeLawStatistics(searchUserId, lawTypes.getLaw41391(), query));
        List<SdkUsageStatistics> sdkUsageStatisticsList = sdkDectDetailMapper.findSdkUsageStatistics(searchUserId,
                ConstantsUtils.SDK_USAGE_STATISTICS_LIMIT, terminalType.getValue(), query.getStartDate(), query.getEndDate(), query.getAssetsName())
                .stream().map(s -> {
                    SdkUsageStatistics sdk = new SdkUsageStatistics();
                    sdk.setSdkName(s.getSdkName());
                    sdk.setSdkPackageName(s.getSdkPackage());
                    sdk.setCount(s.getUsageCount());
                    return sdk;
                }).collect(Collectors.toList());
        PageHelper.startPage(1, ConstantsUtils.SDK_USAGE_STATISTICS_LIMIT);
        List<SdkIssueStatistics> sdkIssueStatisticsList = sdkLawsDectDetailMapper.findRiskSdkByPage(searchUserId, lawTypes.toIntList(),
                        StringUtils.EMPTY, query.getStartDate(), query.getEndDate(), query.getAssetsName())
                .stream().map(s -> {
                    SdkIssueStatistics sdk = new SdkIssueStatistics();
                    sdk.setSdkName(s.getSdkName());
                    sdk.setSdkPackageName(s.getSdkPackage());
                    sdk.setCount(s.getRiskCount());
                    return sdk;
                }).collect(Collectors.toList());
        statistics.setSdkIssueStatisticsList(sdkIssueStatisticsList);
        statistics.setSdkUsageStatisticsList(sdkUsageStatisticsList);
        return statistics;
    }

    private LawTypes buildLawTypes(TerminalTypeEnum terminalType, Integer lawType) {
        LawTypes lawTypes = new LawTypes();
        if (terminalType == TerminalTypeEnum.ANDROID) {
            if (StatisticsLawType.LAW_164.getType().equals(lawType)) {
                lawTypes.setLaw164(PrivacyLawId.ANDROID_LAW_164);
            } else if (StatisticsLawType.LAW_191.getType().equals(lawType)) {
                lawTypes.setLaw191(PrivacyLawId.ANDROID_LAW_191);
            } else if (StatisticsLawType.LAW_35273.getType().equals(lawType)) {
                lawTypes.setLaw35273(PrivacyLawId.ANDROID_LAW_35273);
            } else if (StatisticsLawType.LAW_41391.getType().equals(lawType)) {
                lawTypes.setLaw41391(PrivacyLawId.ANDROID_LAW_41391);
            } else {
                lawTypes.setLaw164(PrivacyLawId.ANDROID_LAW_164);
                lawTypes.setLaw191(PrivacyLawId.ANDROID_LAW_191);
                lawTypes.setLaw35273(PrivacyLawId.ANDROID_LAW_35273);
                lawTypes.setLaw41391(PrivacyLawId.ANDROID_LAW_41391);
            }
        } else if (terminalType == TerminalTypeEnum.IOS) {
            if (StatisticsLawType.LAW_164.getType().equals(lawType)) {
                lawTypes.setLaw164(PrivacyLawId.IOS_LAW_164);
            } else if (StatisticsLawType.LAW_191.getType().equals(lawType)) {
                lawTypes.setLaw191(PrivacyLawId.IOS_LAW_191);
            } else if (StatisticsLawType.LAW_35273.getType().equals(lawType)) {
                lawTypes.setLaw35273(PrivacyLawId.IOS_LAW_35273);
            } else if (StatisticsLawType.LAW_41391.getType().equals(lawType)) {
                lawTypes.setLaw41391(PrivacyLawId.IOS_LAW_41391);
            } else {
                lawTypes.setLaw164(PrivacyLawId.IOS_LAW_164);
                lawTypes.setLaw191(PrivacyLawId.IOS_LAW_191);
                lawTypes.setLaw35273(PrivacyLawId.IOS_LAW_35273);
                lawTypes.setLaw41391(PrivacyLawId.IOS_LAW_41391);
            }
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            if (StatisticsLawType.LAW_164.getType().equals(lawType)) {
                lawTypes.setLaw164(PrivacyLawId.WECHAT_APPLET_LAW_164);
            } else if (StatisticsLawType.LAW_191.getType().equals(lawType)) {
                lawTypes.setLaw191(PrivacyLawId.WECHAT_APPLET_LAW_191);
            } else if (StatisticsLawType.LAW_35273.getType().equals(lawType)) {
                lawTypes.setLaw35273(PrivacyLawId.WECHAT_APPLET_LAW_35273);
            } else if (StatisticsLawType.LAW_41391.getType().equals(lawType)) {
                lawTypes.setLaw41391(PrivacyLawId.WECHAT_APPLET_LAW_41391);
            } else {
                lawTypes.setLaw164(PrivacyLawId.WECHAT_APPLET_LAW_164);
                lawTypes.setLaw191(PrivacyLawId.WECHAT_APPLET_LAW_191);
                lawTypes.setLaw35273(PrivacyLawId.WECHAT_APPLET_LAW_35273);
                lawTypes.setLaw41391(PrivacyLawId.WECHAT_APPLET_LAW_41391);
            }
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            if (StatisticsLawType.LAW_164.getType().equals(lawType)) {
                lawTypes.setLaw164(PrivacyLawId.ALIPAY_APPLET_LAW_164);
            } else if (StatisticsLawType.LAW_191.getType().equals(lawType)) {
                lawTypes.setLaw191(PrivacyLawId.ALIPAY_APPLET_LAW_191);
            } else if (StatisticsLawType.LAW_35273.getType().equals(lawType)) {
                lawTypes.setLaw35273(PrivacyLawId.ALIPAY_APPLET_LAW_35273);
            } else if (StatisticsLawType.LAW_41391.getType().equals(lawType)) {
                lawTypes.setLaw41391(PrivacyLawId.ALIPAY_APPLET_LAW_41391);
            } else {
                lawTypes.setLaw164(PrivacyLawId.ALIPAY_APPLET_LAW_164);
                lawTypes.setLaw191(PrivacyLawId.ALIPAY_APPLET_LAW_191);
                lawTypes.setLaw35273(PrivacyLawId.ALIPAY_APPLET_LAW_35273);
                lawTypes.setLaw41391(PrivacyLawId.ALIPAY_APPLET_LAW_41391);
            }
        } else if (terminalType == TerminalTypeEnum.HARMONY) {
            if (StatisticsLawType.LAW_164.getType().equals(lawType)) {
                lawTypes.setLaw164(PrivacyLawId.HARMONY_LAW_164);
            } else if (StatisticsLawType.LAW_191.getType().equals(lawType)) {
                lawTypes.setLaw191(PrivacyLawId.HARMONY_LAW_191);
            } else if (StatisticsLawType.LAW_35273.getType().equals(lawType)) {
                lawTypes.setLaw35273(PrivacyLawId.HARMONY_LAW_35273);
            } else if (StatisticsLawType.LAW_41391.getType().equals(lawType)) {
                lawTypes.setLaw41391(PrivacyLawId.HARMONY_LAW_41391);
            } else {
                lawTypes.setLaw164(PrivacyLawId.HARMONY_LAW_164);
                lawTypes.setLaw191(PrivacyLawId.HARMONY_LAW_191);
                lawTypes.setLaw35273(PrivacyLawId.HARMONY_LAW_35273);
                lawTypes.setLaw41391(PrivacyLawId.HARMONY_LAW_41391);
            }
        }
        return lawTypes;
    }

    private LawStatistics makeLawStatistics(Long userId, PrivacyLawId lawId, DetectionStatisticsQuery query) {
        if (Objects.isNull(lawId)) return null;
        LawStatistics statistics = new LawStatistics();
        statistics.setLawId(lawId.id);
        List<LawStatistics.AppIssue> appIssueList = appLawsRiskCollectMapper.findRiskAssets(userId,
                lawId.id,
                query.getStartDate(),
                query.getEndDate(),
                query.getAssetsName(),
                ConstantsUtils.DEFAULT_STATISTICS_LIMIT)
                .stream().map(a -> {
                    LawStatistics.AppIssue appIssue = new LawStatistics.AppIssue();
                    appIssue.setNonComplianceCount(a.getRiskCount());
                    appIssue.setAssetsId(a.getAssetsId());
                    appIssue.setAssetsName(a.getName());
                    appIssue.setVersion(a.getVersion());
                    return appIssue;
                }).collect(Collectors.toList());

        List<LawStatistics.LawItem> lawItemList = appLawsDectDetailMapper.findRiskLaw(userId,
                lawId.id,
                query.getStartDate(),
                query.getEndDate(),
                query.getAssetsName(),
                ConstantsUtils.DEFAULT_STATISTICS_LIMIT)
                .stream().map(a -> {
                    LawStatistics.LawItem lawItem = new LawStatistics.LawItem();
                    lawItem.setLawItemId(a.getLawsItemParentId());
                    lawItem.setLawItemName(a.getName());
                    lawItem.setNonComplianceCount(a.getRiskCount());
                    return lawItem;
                }).collect(Collectors.toList());
        statistics.setLawItemList(lawItemList);
        statistics.setAppIssueList(appIssueList);
        statistics.setDetectionTotalCount(detectionTotalCount(userId, lawId.id, query));
        statistics.setNonComplianceTotalCount(nonComplianceTotalCount(userId, lawId.id, query));
        return statistics;
    }

    private int detectionTotalCount(Long userId, Integer lawId, DetectionStatisticsQuery query) {
        return appLawsRiskCollectMapper.detectionTotalCount(userId, lawId, query.getStartDate(), query.getEndDate(), query.getAssetsName(), null);
    }

    private int nonComplianceTotalCount(Long userId, Integer lawId, DetectionStatisticsQuery query) {
        return appLawsDectDetailMapper.nonComplianceTotalCount(userId, lawId, query.getStartDate(), query.getEndDate(), query.getAssetsName(), null, null);
    }

    private int detectFalsePositivesTotalCount(Long userId, Integer lawId, DetectionStatisticsQuery query) {
        return privacyLawsResultMapper.detectFalsePositivesTotalCount(userId, lawId, query.getStartDate(), query.getEndDate(), query.getAssetsName(), null);
    }

    private LawFalsePositivesStatistics makeDetectFalsePositivesStatistics(Long userId, PrivacyLawId lawId, DetectionStatisticsQuery query) {
        if (Objects.isNull(lawId)) return null;
        LawFalsePositivesStatistics statistics = new LawFalsePositivesStatistics();
        statistics.setLawId(lawId.id);
        List<DetectFalsePositivesApp> appIssueList = privacyLawsResultMapper.findDetectFalsePositivesAssets(userId,
                        lawId.id,
                        query.getStartDate(),
                        query.getEndDate(),
                        query.getAssetsName(),
                        ConstantsUtils.DEFAULT_STATISTICS_LIMIT);

        List<DetectFalsePositivesLaw> lawItemList = privacyLawsResultMapper.findDetectFalsePositivesLawItem(userId,
                        lawId.id,
                        query.getStartDate(),
                        query.getEndDate(),
                        query.getAssetsName(),
                        ConstantsUtils.DEFAULT_STATISTICS_LIMIT);
        statistics.setLawItemList(lawItemList);
        statistics.setAppIssueList(appIssueList);
        statistics.setDetectionTotalCount(detectionTotalCount(userId, lawId.id, query));
        statistics.setDetectFalsePositivesTotalCount(detectFalsePositivesTotalCount(userId, lawId.id, query));
        return statistics;
    }

    @Override
    public PageInfo<DetectionStatisticsDetail> detectionDetailsByPage(Long searchUserId, DetectionDetailsQuery query) {
        // 如果有用户名，使用搜索到用户的信息
        if (Objects.isNull(searchUserId)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<DetectionStatisticsDetail> pageInfo = new PageInfo<>();
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        // 分页查询，相同名字的合并为一项，按照上传顺序排
        List<RiskResult> riskResultList = appLawsDectDetailMapper.findRiskAssetsByPage(searchUserId, query.getLawId(),
                query.getStartDate(), query.getEndDate(), query.getAssetsName());
        PageInfo<RiskResult> riskResultPage = new PageInfo<>(riskResultList);
        BeanUtils.copyProperties(riskResultPage, pageInfo);
        if (!riskResultList.isEmpty()) {
            List<String> nameList = riskResultList.stream().map(RiskResult::getName).collect(Collectors.toList());
            List<RiskResult> appVersionList = appLawsRiskCollectMapper.findRiskAssetsInName(searchUserId, query.getLawId(),
                    query.getStartDate(), query.getEndDate(), nameList);
            pageInfo.setList(makeDetectionStatisticsDetail(searchUserId, nameList, appVersionList, query.getStartDate(), query.getEndDate()));
        } else {
            pageInfo.setList(new ArrayList<>());
        }
        return pageInfo;
    }

    private List<DetectionStatisticsDetail> makeDetectionStatisticsDetail(Long userId, List<String> nameList, List<RiskResult> appVersionList,
                                                                          Date startDate, Date endDate) {
        Map<String, List<RiskResult>> map = appVersionList.stream()
                .collect(Collectors.groupingBy(RiskResult::getName));
        List<DetectionStatisticsDetail> detailList = new ArrayList<>();
        nameList.forEach(name -> {
            DetectionStatisticsDetail detail = new DetectionStatisticsDetail();
            detail.setAssetsName(name);
            detail.setVersionList(makDetectionStatisticsVersion(userId, map.get(name), startDate, endDate));
            detailList.add(detail);
        });
        return detailList;
    }

    private List<DetectionStatisticsDetail.Detection> queryDetectionDetailList(Long userId, String name, String version, Integer lawId, Date startDate, Date endDate) {
        List<DetectionStatisticsDetail.Detection> detectionList = new ArrayList<>();
        appLawsRiskCollectMapper.findRiskLawItem(userId, name, version, lawId, startDate, endDate)
                .stream()
                .collect(Collectors.groupingBy(RiskResult::getDetectionTime))
                .forEach((time, list) -> {
                    DetectionStatisticsDetail.Detection detection = new DetectionStatisticsDetail.Detection();
                    detection.setDetectionTime(time);
                    detection.setNonComplianceCount(list.size());
                    detection.setNonComplianceContentList(buildNonComplianceContent(list));
                    detectionList.add(detection);
                });
        return detectionList;
    }

    private List<DetectionStatisticsDetail.NonComplianceContent> buildNonComplianceContent(List<RiskResult> list) {
        return list.stream()
                .collect(Collectors.groupingBy(RiskResult::getLawsItemParentName))
                .entrySet()
                .stream()
                .map(entry -> {
                    DetectionStatisticsDetail.NonComplianceContent content = new DetectionStatisticsDetail.NonComplianceContent();
                    content.setLawName(entry.getKey());
                    content.setLawDescriptionList(entry.getValue().stream().map(RiskResult::getLawsItemName).collect(Collectors.toList()));
                    return content;
                }).collect(Collectors.toList());
    }

    @Override
    public DetectionStatisticsLawItem detectionLawItem(Long searchUserId, DetectionLawItemQuery query, Integer pageSize) {
        DetectionStatisticsLawItem lawItem = new DetectionStatisticsLawItem();
        lawItem.setLawName(queryLawName(query.getLawItemId()));
        lawItem.setLawItemId(query.getLawItemId());
        // 只获取第一页的内容
        if (pageSize != ConstantsUtils.STATISTICS_UNLIMITED) {
            PageHelper.startPage(1, pageSize);
        }
        List<DetectionStatisticsLawItem.Detail> lawDescriptionList = privacyLawsRegulationsMapper.selectByParentId(query.getLawItemId()).stream().map(r -> {
            DetectionStatisticsLawItem.Detail detail = new DetectionStatisticsLawItem.Detail();
            detail.setLawChildItemId(r.getId());
            detail.setLawDescription(r.getName());
            List<RiskResult> resultList = appLawsDectDetailMapper.findRiskAssetsByLawItemId(searchUserId, r.getId(),
                    query.getStartDate(), query.getEndDate(), query.getAssetsName());
            detail.setNonComplianceCount(resultList.size());
            detail.setAssetsList(buildAssetsInfo(searchUserId, r.getId(), new PageInfo<>(resultList)));
            return detail;
        }).collect(Collectors.toList());
        lawItem.setLawDescriptionList(lawDescriptionList);
        return lawItem;
    }

    private String queryLawName(Long lawItemId) {
        TPrivacyLawsRegulations regulationQuery = new TPrivacyLawsRegulations();
        regulationQuery.setId(lawItemId);
        return privacyLawsRegulationsMapper.selectOne(regulationQuery).getName();
    }

    private PageInfo<AssetsInfoDTO> buildAssetsInfo(Long userId, Long lawsItemId, PageInfo<RiskResult> resultList) {
        PageInfo<AssetsInfoDTO> assetsInfoPageInfo = new PageInfo<>();
        List<AssetsInfoDTO> assets = resultList.getList().stream().map(r -> {
            AssetsInfoDTO info = new AssetsInfoDTO();
            info.setAssetsName(r.getName());
            info.setDetectionCount(appLawsRiskCollectMapper.detectionCount(userId, r.getName(), r.getVersion(), r.getLawId()));
            info.setVersion(r.getVersion());
            info.setNonComplianceTotalCount(
                    appLawsDectDetailMapper.nonComplianceTotalCountByAssetsName(userId, r.getName(), r.getVersion(),
                            r.getLawId(), null));
            info.setCurrentNonComplianceCount(
                    appLawsDectDetailMapper.nonComplianceTotalCountByAssetsName(userId, r.getName(), r.getVersion(),
                            r.getLawId(), lawsItemId));
            info.setUserName(r.getUserName());
            info.setLogo(r.getLogo());
            return info;
        }).collect(Collectors.toList());
        BeanUtils.copyProperties(resultList, assetsInfoPageInfo);
        assetsInfoPageInfo.setList(assets);
        return assetsInfoPageInfo;
    }

    @Override
    public PageInfo<AssetsInfoDTO> detectionLawItemAssetsByPage(Long searchUserId, AssetsInfoParam query) {
        if (Objects.isNull(searchUserId)) {
            return PageInfo.emptyPageInfo();
        }
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<RiskResult> resultList = appLawsDectDetailMapper.findRiskAssetsByLawItemId(searchUserId, query.getLawChildItemId(),
                query.getStartDate(), query.getEndDate(), query.getAssetsName());
        return buildAssetsInfo(searchUserId, query.getLawChildItemId(), new PageInfo<>(resultList));
    }

    @Override
    public PageInfo<SdkStatisticsDetail> sdkDetailsByPage(Long searchUserId, SdkDetailsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        if (Objects.isNull(searchUserId)) {
            return PageInfo.emptyPageInfo();
        }
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        LawTypes lawTypes = buildLawTypes(terminalType, query.getLawType());
        PageInfo<SdkStatisticsDetail> pageInfo = new PageInfo<>();
        List<SdkRiskResult> resultList = sdkLawsDectDetailMapper.findRiskSdkByPage(searchUserId, lawTypes.toIntList(),
                query.getSdkName(), query.getStartDate(), query.getEndDate(), query.getAssetsName());
        PageInfo<SdkRiskResult> resultPageInfo = new PageInfo<>(resultList);
        BeanUtils.copyProperties(resultPageInfo, pageInfo);
        List<SdkStatisticsDetail> sdkDetails = resultList.stream().map(s -> {
            SdkStatisticsDetail detail = new SdkStatisticsDetail();
            detail.setSdkName(s.getSdkName());
            detail.setSdkPackageName(s.getSdkPackage());
            detail.setManufacturer(s.getSdkManufacturer());
            detail.setNonComplianceTotalCount(s.getRiskCount());
            detail.setLawItemList(querySdkLawItemDesc(searchUserId, lawTypes.toIntList(),
                    s.getSdkName(), query.getStartDate(), query.getEndDate(), query.getAssetsName()));
            return detail;
        }).collect(Collectors.toList());
        pageInfo.setList(sdkDetails);
        return pageInfo;
    }

    @Override
    public DetectFalsePositivesStatistics detectFalsePositivesStatistics(Long searchUserId, DetectionStatisticsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getAndValid(query.getTerminalType());
        if (Objects.isNull(searchUserId)) {
            return emptyDetectFalsePositivesStatistics(query);
        }
        LawTypes lawTypes = buildLawTypes(terminalType, query.getLawType());
        DetectFalsePositivesStatistics statistics = new DetectFalsePositivesStatistics();
        statistics.setTerminalType(terminalType.getValue());
        statistics.setLaw164Statistics(makeDetectFalsePositivesStatistics(searchUserId, lawTypes.getLaw164(), query));
        statistics.setLaw191Statistics(makeDetectFalsePositivesStatistics(searchUserId, lawTypes.getLaw191(), query));
        statistics.setLaw35273Statistics(makeDetectFalsePositivesStatistics(searchUserId, lawTypes.getLaw35273(), query));
        statistics.setLaw41391Statistics(makeDetectFalsePositivesStatistics(searchUserId, lawTypes.getLaw41391(), query));
        return statistics;
    }

    @Override
    public PageInfo<DetectFalsePositivesDetail> detectFalsePositivesByPage(Long searchUserId, DetectionDetailsQuery query) {
        if (Objects.isNull(searchUserId)) {
            return PageInfo.emptyPageInfo();
        }
        PageInfo<DetectFalsePositivesDetail> pageInfo = new PageInfo<>();
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        // 分页查询，相同名字的合并为一项，按照上传顺序排
        List<DetectFalsePositivesApp> riskResultList = privacyLawsResultMapper.findDetectFalsePositivesAssetsByPage(searchUserId, query.getLawId(),
                query.getStartDate(), query.getEndDate(), query.getAssetsName());
        PageInfo<DetectFalsePositivesApp> riskResultPage = new PageInfo<>(riskResultList);
        BeanUtils.copyProperties(riskResultPage, pageInfo);
        if (!riskResultList.isEmpty()) {
            List<String> nameList = riskResultList.stream().map(DetectFalsePositivesApp::getAssetsName).collect(Collectors.toList());
            List<DetectFalsePositivesAppLaw> appVersionList = privacyLawsResultMapper.findDetectFalsePositivesAssetsInName(searchUserId, query.getLawId(),
                    query.getStartDate(), query.getEndDate(), nameList);
            pageInfo.setList(makeDetectFalsePositivesDetail(searchUserId, nameList, appVersionList, query.getStartDate(), query.getEndDate()));
        } else {
            pageInfo.setList(new ArrayList<>());
        }
        return pageInfo;
    }

    private List<DetectFalsePositivesDetail> makeDetectFalsePositivesDetail(Long userId, List<String> nameList, List<DetectFalsePositivesAppLaw> appVersionList,
                                                                          Date startDate, Date endDate) {
        Map<String, List<DetectFalsePositivesAppLaw>> map = appVersionList.stream()
                .collect(Collectors.groupingBy(DetectFalsePositivesAppLaw::getAssetsName));
        List<DetectFalsePositivesDetail> detailList = new ArrayList<>();
        nameList.forEach(name -> {
            DetectFalsePositivesDetail detail = new DetectFalsePositivesDetail();
            detail.setAssetsName(name);
            detail.setVersionList(makDetectFalsePositivesDetailVersion(userId, map.get(name), startDate, endDate));
            detailList.add(detail);
        });
        return detailList;
    }

    private List<DetectFalsePositivesDetail.Version> makDetectFalsePositivesDetailVersion(Long userId, List<DetectFalsePositivesAppLaw> riskList, Date startDate, Date endDate) {
        return riskList.stream()
                .sorted(Comparator.comparing(DetectFalsePositivesAppLaw::getAssetsId).reversed()) // 后上传的排在前面
                .map(a -> {
                    // 合并相同版本的资产，有可能多个用户上传同一个版本的APP
                    DetectFalsePositivesDetail.Version version = new DetectFalsePositivesDetail.Version();
                    version.setVersion(a.getVersion());
                    // 找出最近一次检测时间
                    version.setDetectionList(queryDetectFalsePositivesDetailList(userId, a.getAssetsName(), a.getVersion(), a.getLawId(), startDate, endDate));
                    return version;
                }).collect(Collectors.toList());
    }

    private List<DetectFalsePositivesDetail.Detection> queryDetectFalsePositivesDetailList(Long userId, String name, String version, Integer lawId, Date startDate, Date endDate) {
        List<DetectFalsePositivesDetail.Detection> detectionList = new ArrayList<>();
        privacyLawsResultMapper.findDetectFalsePositivesLawItemDetail(userId, name, version, lawId, startDate, endDate)
                .stream()
                .collect(Collectors.groupingBy(RiskResult::getDetectionTime))
                .forEach((time, list) -> {
                    DetectFalsePositivesDetail.Detection detection = new DetectFalsePositivesDetail.Detection();
                    detection.setDetectionTime(time);
                    detection.setDetectFalsePositivesTotalCount(list.size());
                    detection.setDetectFalsePositivesList(buildDetectFalsePositives(list));
                    detectionList.add(detection);
                });
        return detectionList;
    }

    private List<DetectFalsePositivesDetail.DetectFalsePositives> buildDetectFalsePositives(List<RiskResult> list) {
        return list.stream()
                .collect(Collectors.groupingBy(RiskResult::getLawsItemParentName))
                .entrySet()
                .stream()
                .map(entry -> {
                    DetectFalsePositivesDetail.DetectFalsePositives content = new DetectFalsePositivesDetail.DetectFalsePositives();
                    content.setLawName(entry.getKey());
                    content.setLawDescriptionList(entry.getValue().stream().map(RiskResult::getLawsItemName).collect(Collectors.toList()));
                    return content;
                }).collect(Collectors.toList());
    }

    @Override
    public DetectFalsePositivesLawItem detectFalsePositivesLawItem(Long searchUserId, DetectionLawItemQuery query, Integer pageSize) {
        DetectFalsePositivesLawItem lawItem = new DetectFalsePositivesLawItem();
        lawItem.setLawName(queryLawName(query.getLawItemId()));
        lawItem.setLawItemId(query.getLawItemId());
        // 只获取第一页的内容
        if (pageSize != ConstantsUtils.STATISTICS_UNLIMITED) {
            PageHelper.startPage(1, pageSize);
        }
        List<DetectFalsePositivesLawItem.Detail> lawDescriptionList = privacyLawsRegulationsMapper.selectByParentId(query.getLawItemId()).stream().map(r -> {
            DetectFalsePositivesLawItem.Detail detail = new DetectFalsePositivesLawItem.Detail();
            detail.setLawChildItemId(r.getId());
            detail.setLawDescription(r.getName());
            List<RiskResult> resultList = privacyLawsResultMapper.findDetectFalsePositivesAssetsByLawItemId(searchUserId, r.getId(),
                    query.getStartDate(), query.getEndDate(), query.getAssetsName());
            detail.setDetectFalsePositivesCount(resultList.size());
            detail.setAssetsList(buildDetectFalsePositivesAssetsInfo(searchUserId, r.getId(), new PageInfo<>(resultList)));
            return detail;
        }).collect(Collectors.toList());
        lawItem.setLawDescriptionList(lawDescriptionList);
        return lawItem;
    }

    @Override
    public PageInfo<DetectFalsePositivesAssetsInfo> detectFalsePositivesLawItemAssetsByPage(Long searchUserId, AssetsInfoParam query) {
        if (Objects.isNull(searchUserId)) {
            return PageInfo.emptyPageInfo();
        }
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<RiskResult> resultList = privacyLawsResultMapper.findDetectFalsePositivesAssetsByLawItemId(searchUserId, query.getLawChildItemId(),
                query.getStartDate(), query.getEndDate(), query.getAssetsName());
        return buildDetectFalsePositivesAssetsInfo(searchUserId, query.getLawChildItemId(), new PageInfo<>(resultList));
    }

    @Override
    public List<DetectFalsePositivesReportAssetsInfo> findDetectFalsePositivesReport(Long searchUserId, DetectionStatisticsQuery query) {
        if (Objects.isNull(searchUserId)) {
            return Collections.emptyList();
        }
        LawTypes lawTypes = buildLawTypes(TerminalTypeEnum.getItem(query.getTerminalType()), query.getLawType());
        List<DetectFalsePositivesReportAssetsInfo> detailList = privacyLawsResultMapper.findDetectFalsePositivesReport(searchUserId,
                query.getAssetsName(),
                lawTypes.toIntList(),
                query.getStartDate(),
                query.getEndDate());
        // 设置动态的检测结论
        for (DetectFalsePositivesReportAssetsInfo assetsInfo:detailList) {
            for (DetectFalsePositivesReportLaw law:assetsInfo.getLawList()) {
                for (DetectFalsePositivesReportItem item:law.getItemList()) {
                    LawDetectResultUtils.setConclusionAndSuggestionBySceneTitle(item, item.getSceneTitle());
                    item.setMarkDescription(privacyResultMarkMapper.findLatestMarkDesc(item.getResultId()));
                }
            }
        }
        return detailList;
    }

    @Override
    public HomePageDetectionStatistics homePageDetectionStatistics(Long userId, HomePageDetectionStatisticsQuery query) {
        if (Objects.isNull(query.getStartDate()) || Objects.isNull(query.getEndDate())) {
            throw new IllegalArgumentException("缺少时间范围");
        }
        if (query.getEndDate().getTime() - query.getStartDate().getTime() <= 0) {
            throw new IllegalArgumentException("时间范围错误");
        }
        if (query.getEndDate().getTime() - query.getStartDate().getTime() > TimeUnit.DAYS.toMillis(365 * 5)) {
            throw new IllegalArgumentException("时间范围不能大于5年");
        }
        HomePageDetectionStatistics statistics = new HomePageDetectionStatistics();
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        if (Objects.isNull(terminalType)) {
            return statistics;
        }
        int fastCount = assetsMapper.assetsDetectionCount(userId, terminalType, query.getStartDate(),
                query.getEndDate(), TaskDetectionTypeEnum.FAST.getValue(), query.getPackageNameList());
        int deepCount = assetsMapper.assetsDetectionCount(userId, terminalType, query.getStartDate(),
                query.getEndDate(), TaskDetectionTypeEnum.DEPTH.getValue(), query.getPackageNameList());
        int aiCount = assetsMapper.assetsDetectionCount(userId, terminalType, query.getStartDate(),
                query.getEndDate(), TaskDetectionTypeEnum.AI.getValue(), query.getPackageNameList());
        statistics.setDetectionTotalCount(assetsMapper.detectionAssetsCount(userId, terminalType, query.getStartDate(),
                query.getEndDate(), query.getPackageNameList()));
        statistics.setFastDetectionCount(fastCount);
        statistics.setDeepDetectionCount(deepCount);
        statistics.setAIDetectionCount(aiCount);
        statistics.setNonComplianceTotalCount(appLawsDectDetailMapper.nonComplianceTotalCount(userId, null,
                query.getStartDate(), query.getEndDate(), null, terminalType, query.getPackageNameList()));
        List<HomePageLawStatistics> lawStatisticsList = new ArrayList<>();
        for (PrivacyLawId id:PrivacyLawId.getLawsByTerminalType(terminalType)) {
            lawStatisticsList.add(makeHomePageLawStatistics(userId, id, query.getStartDate(), query.getEndDate(), query.getPackageNameList()));
        }
        statistics.setLawStatisticsList(lawStatisticsList);
        statistics.setDailyStatisticsList(fillMissingDates(assetsMapper.assetsDailyDetection(userId, terminalType,
                query.getStartDate(), query.getEndDate(), query.getPackageNameList()), query.getStartDate(), query.getEndDate()));
        return statistics;
    }

    private HomePageLawStatistics makeHomePageLawStatistics(Long userId, PrivacyLawId lawId,
                                                            Date startDate, Date endDate, List<String> packageNameList) {
        HomePageLawStatistics statistics = new HomePageLawStatistics();
        if (PrivacyLawId.law164().contains(lawId)) {
            statistics.setLawType(StatisticsLawType.LAW_164);
        } else if (PrivacyLawId.law191().contains(lawId)) {
            statistics.setLawType(StatisticsLawType.LAW_191);
        } else if (PrivacyLawId.law35273().contains(lawId)) {
            statistics.setLawType(StatisticsLawType.LAW_35273);
        } else if (PrivacyLawId.law41391().contains(lawId)) {
            statistics.setLawType(StatisticsLawType.LAW_41391);
        }
        statistics.setDetectionTotalCount(appLawsRiskCollectMapper.detectionTotalCount(userId, lawId.id, startDate,
                endDate, null, packageNameList));
        statistics.setNonComplianceTotalCount(appLawsDectDetailMapper.nonComplianceTotalCount(userId, lawId.id, startDate,
                endDate, null, null, packageNameList));
        statistics.setDetectFalsePositivesTotalCount(privacyLawsResultMapper.detectFalsePositivesTotalCount(userId, lawId.id,
                startDate, endDate, null, packageNameList));
        return statistics;
    }

    private PageInfo<DetectFalsePositivesAssetsInfo> buildDetectFalsePositivesAssetsInfo(Long userId, Long lawsItemId, PageInfo<RiskResult> resultList) {
        PageInfo<DetectFalsePositivesAssetsInfo> assetsInfoPageInfo = new PageInfo<>();
        List<DetectFalsePositivesAssetsInfo> assets = resultList.getList().stream().map(r -> {
            DetectFalsePositivesAssetsInfo info = new DetectFalsePositivesAssetsInfo();
            info.setAssetsName(r.getName());
            info.setDetectionCount(appLawsRiskCollectMapper.detectionCount(userId, r.getName(), r.getVersion(), r.getLawId()));
            info.setVersion(r.getVersion());
            info.setDetectFalsePositivesTotalCount(
                    detectFalsePositivesTotalCountByLawItem(userId, r.getName(), r.getVersion(),
                            r.getTerminalType(), r.getLawId(), null));
            info.setCurrentDetectFalsePositivesCount(
                    detectFalsePositivesTotalCountByLawItem(userId, r.getName(), r.getVersion(),
                            r.getTerminalType(), r.getLawId(), lawsItemId));
            info.setUserName(r.getUserName());
            info.setLogo(r.getLogo());
            return info;
        }).collect(Collectors.toList());
        BeanUtils.copyProperties(resultList, assetsInfoPageInfo);
        assetsInfoPageInfo.setList(assets);
        return assetsInfoPageInfo;
    }

    private Integer detectFalsePositivesTotalCountByLawItem(Long userId, String name,
                      String version,
                      Integer terminalType,
                      Integer lawId,
                      Long lawsItemId) {
        Integer count = privacyLawsResultMapper.detectFalsePositivesTotalCountByLawItem(userId, name, version,
                terminalType, lawId, lawsItemId);
        return Objects.isNull(count) ? 0 : count;
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        appLawsDectDetailMapper.deleteByTaskId(taskId);
        appLawsRiskCollectMapper.deleteByTaskId(taskId);
        sdkDectDetailMapper.deleteByTaskId(taskId);
        sdkLawsDectDetailMapper.deleteByTaskId(taskId);
    }

    @Override
    public void deleteByAssetsId(Long assetsId) {
        appLawsDectDetailMapper.deleteByAssetsId(assetsId);
        appLawsRiskCollectMapper.deleteByAssetsId(assetsId);
        sdkDectDetailMapper.deleteByAssetsId(assetsId);
        sdkLawsDectDetailMapper.deleteByAssetsId(assetsId);
    }

    private List<String> querySdkLawItemDesc(Long userId, List<Integer> lawIds, String sdkName, Date startDate, Date endDate, String assetsName) {
        return sdkLawsDectDetailMapper.findRiskLawItem(userId, lawIds, sdkName, startDate, endDate, assetsName)
                .stream()
                .map(d -> d.getLawName() + lawsName(PrivacyLawId.getItem(d.getLawId())))
                .collect(Collectors.toList());
    }

    private String lawsName(PrivacyLawId lawId) {
        if (PrivacyLawId.law164().contains(lawId)) {
            return "（违反164）";
        } else if (PrivacyLawId.law191().contains(lawId)) {
            return "（违反191）";
        } else if (PrivacyLawId.law35273().contains(lawId)) {
            return "（违反35273）";
        } else if (PrivacyLawId.law41391().contains(lawId)) {
            return "（违反41391）";
        }
        return "";
    }

    private List<AssetsStatisticsDetailDTO> makeAssetsStatisticsDetail(List<String> nameList, List<AssetsDetectionStatistics> assetsList,
                                                                       TerminalTypeEnum terminalType, boolean haveDetectionTime) {
        Map<String, List<AssetsDetectionStatistics>> map = assetsList.stream()
                .collect(Collectors.groupingBy(AssetsDetectionStatistics::getName));
        List<AssetsStatisticsDetailDTO> detailList = new ArrayList<>();
        nameList.forEach(name -> {
            List<AssetsStatisticsDetailDTO> details = makeAssetsStatisticsVersion(map.get(name), terminalType, haveDetectionTime);
            // 是否有多个版本，多个版本需要构建一个数据
            AssetsStatisticsDetailDTO assetsStatisticsDetail;
            if (details.size() == 1) {
                assetsStatisticsDetail = details.get(0);
            } else {
                // 构建一个总的数据
                assetsStatisticsDetail = buildTotalAssetsStatisticsDetail(details);
            }
            detailList.add(assetsStatisticsDetail);
        });
        return detailList;
    }

    private AssetsStatisticsDetailDTO taskDetectionTime(AssetsStatisticsDetailDTO detail, TerminalTypeEnum terminalType) {
        Long staticDetectDuration;
        if (terminalType.isApplet()) {
            // 小程序的静态检测没有缓存，所以取最后一次检测时间
            staticDetectDuration = taskMapper.findAssetsLastTaskStaticDetectDuration(detail.getAssetsId());
        } else {
            // app的静态检测有缓存，第一次检测才是真是的时间，所以取最长的一次检测时间
            staticDetectDuration = taskMapper.findAssetsStaticDetectDuration(detail.getAssetsId());
        }
        if (Objects.isNull(staticDetectDuration) || staticDetectDuration == 0) {
            detail.setStaticDetectDuration(PinfoConstant.DETAILS_EMPTY);
        } else {
            detail.setStaticDetectDuration(DateUtils.getDurationTime(staticDetectDuration));
        }
        // 动态检测取最后一次检测时间
        Long dynamicDetectDuration = taskMapper.findAssetsLastTaskDynamicDetectDuration(detail.getAssetsId());
        if (Objects.isNull(dynamicDetectDuration) || dynamicDetectDuration == 0) {
            detail.setDynamicDetectDuration(PinfoConstant.DETAILS_EMPTY);
        } else {
            detail.setDynamicDetectDuration(DateUtils.getDurationTime(dynamicDetectDuration));
        }
        return detail;
    }

    private List<AssetsStatisticsDetailDTO> makeAssetsStatisticsVersion(List<AssetsDetectionStatistics> assetsList,
                                                                        TerminalTypeEnum terminalType, boolean haveDetectionTime) {
        // 合并相同版本的资产，有可能多个用户上传同一个版本的APP
        return assetsList.stream().map(a -> buildAssetsStatisticsDetail(a, terminalType, haveDetectionTime))
                .sorted(Comparator.comparing(AssetsStatisticsDetailDTO::getLastDetectionTime).reversed()) // 根据最后一次检测时间排序
                .collect(Collectors.toList());
    }

    private AssetsStatisticsDetailDTO buildTotalAssetsStatisticsDetail(List<AssetsStatisticsDetailDTO> detailList) {
        AssetsStatisticsDetailDTO latest = detailList.get(0);
        AssetsStatisticsDetailDTO detail = new AssetsStatisticsDetailDTO();
        detail.setId(UuidUtil.uuid());
        detail.setAssetsName(latest.getAssetsName());
        detail.setAssetsId(latest.getAssetsId());
        detail.setVersion(StringUtils.EMPTY);
        // 找出最近一次检测时间
        detail.setLastDetectionTime(latest.getLastDetectionTime());
        detail.setStaticDetectDuration(latest.getStaticDetectDuration());
        detail.setDynamicDetectDuration(latest.getDynamicDetectDuration());
        int totalDetectionCount = 0;
        int totalFastDetectionCount = 0;
        int totalDeepDetectionCount = 0;
        int totalFastDetectionCompletions = 0;
        int totalDeepDetectionCompletions = 0;
        int totalFastDetectionFailures = 0;
        int totalDeepDetectionFailures = 0;
        for (AssetsStatisticsDetailDTO d : detailList) {
            totalDetectionCount += d.getDetectionCount();
            totalFastDetectionCount += d.getFastDetectionCount();
            totalDeepDetectionCount += d.getDeepDetectionCount();
            totalFastDetectionCompletions += d.getFastDetectionCompletions();
            totalDeepDetectionCompletions += d.getDeepDetectionCompletions();
            totalFastDetectionFailures += d.getFastDetectionFailures();
            totalDeepDetectionFailures += d.getDeepDetectionFailures();
        }
        // 检测次数相加
        detail.setDetectionCount(totalDetectionCount);
        detail.setFastDetectionCount(totalFastDetectionCount);
        detail.setDeepDetectionCount(totalDeepDetectionCount);
        detail.setFastDetectionCompletions(totalFastDetectionCompletions);
        detail.setDeepDetectionCompletions(totalDeepDetectionCompletions);
        detail.setFastDetectionFailures(totalFastDetectionFailures);
        detail.setDeepDetectionFailures(totalDeepDetectionFailures);
        detail.setVersionList(detailList);
        return detail;
    }

    private AssetsStatisticsDetailDTO buildAssetsStatisticsDetail(AssetsDetectionStatistics assets, TerminalTypeEnum terminalType, boolean haveDetectionTime) {
        AssetsStatisticsDetailDTO version = new AssetsStatisticsDetailDTO();
        version.setId(UuidUtil.uuid());
        version.setAssetsId(assets.getAssetsId());
        version.setAssetsName(assets.getName());
        version.setVersion(assets.getVersion());
        // 找出最近一次检测时间
        version.setLastDetectionTime(assets.getLastDetectionTime());
        // 因为是同一个版本，检测次数相加
        version.setDetectionCount(assets.getDetectionCount());
        version.setFastDetectionCount(assets.getFastDetectionCount());
        version.setDeepDetectionCount(assets.getDeepDetectionCount());
        version.setFastDetectionCompletions(assets.getFastDetectionCompletions());
        version.setDeepDetectionCompletions(assets.getDeepDetectionCompletions());
        version.setFastDetectionFailures(assets.getFastDetectionFailures());
        version.setDeepDetectionFailures(assets.getDeepDetectionFailures());
        version.setVersionList(Collections.emptyList());
        if (haveDetectionTime) {
            taskDetectionTime(version, terminalType);
        }
        return version;
    }

    private List<DetectionStatisticsDetail.Version> makDetectionStatisticsVersion(Long userId, List<RiskResult> riskList, Date startDate, Date endDate) {
        return riskList.stream()
                .sorted(Comparator.comparing(RiskResult::getAssetsId).reversed()) // 后上传的排在前面
                .map(a -> {
                    // 合并相同版本的资产，有可能多个用户上传同一个版本的APP
                    DetectionStatisticsDetail.Version version = new DetectionStatisticsDetail.Version();
                    version.setVersion(a.getVersion());
                    // 找出最近一次检测时间
                    version.setDetectionList(queryDetectionDetailList(userId, a.getName(), a.getVersion(), a.getLawId(), startDate, endDate));
            return version;
        }).collect(Collectors.toList());
    }


    private static class LawTypes {
        PrivacyLawId law164;
        PrivacyLawId law191;
        PrivacyLawId law35273;
        PrivacyLawId law41391;

        public PrivacyLawId getLaw164() {
            return law164;
        }

        public void setLaw164(PrivacyLawId law164) {
            this.law164 = law164;
        }

        public PrivacyLawId getLaw191() {
            return law191;
        }

        public PrivacyLawId getLaw35273() {
            return law35273;
        }

        public void setLaw35273(PrivacyLawId law35273) {
            this.law35273 = law35273;
        }

        public PrivacyLawId getLaw41391() {
            return law41391;
        }

        public void setLaw41391(PrivacyLawId law41391) {
            this.law41391 = law41391;
        }

        public void setLaw191(PrivacyLawId law191) {
            this.law191 = law191;
        }

        public List<Integer> toIntList() {
            List<Integer> integers = new ArrayList<>(2);
            if (Objects.nonNull(law164)) {
                integers.add(law164.id);
            }
            if (Objects.nonNull(law191)) {
                integers.add(law191.id);
            }
            if (Objects.nonNull(law35273)) {
                integers.add(law35273.id);
            }
            if (Objects.nonNull(law41391)) {
                integers.add(law41391.id);
            }
            return integers;
        }
    }

    public List<HomePageDailyStatistics> fillMissingDates(List<HomePageDailyStatistics> list, Date startDate, Date endDate) {
        // 日期转换
        LocalDate start = convertToLocalDateViaInstant(startDate);
        LocalDate end = convertToLocalDateViaInstant(endDate);
        List<HomePageDailyStatistics> resultList = new ArrayList<>(list);
        // 遍历检查是否某一天的数据没有
        LocalDate currentDate = start;
        while (!currentDate.isAfter(end)) {
            String currentDateString = currentDate.toString();
            boolean found = false;
            for (HomePageDailyStatistics stats : list) {
                if (stats.getDetectionDate().equals(currentDateString)) {
                    found = true;
                    break;
                }
            }
            // 没有找到，创建空日期
            if (!found) {
                HomePageDailyStatistics emptyStats = new HomePageDailyStatistics();
                emptyStats.setDetectionDate(currentDateString);
                emptyStats.setDetectionTotalCount(0);
                emptyStats.setFastDetectionCount(0);
                emptyStats.setDeepDetectionCount(0);
                emptyStats.setNonComplianceTotalCount(0);
                resultList.add(emptyStats);
            }
            currentDate = currentDate.plusDays(1);
        }
        resultList.sort(Comparator.comparing(HomePageDailyStatistics::getDetectionDate).reversed());
        return resultList;
    }

    private LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
        return dateToConvert.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

}
