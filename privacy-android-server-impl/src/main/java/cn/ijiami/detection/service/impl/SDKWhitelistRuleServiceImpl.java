package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.enums.SDKWhitelistRuleType;
import cn.ijiami.detection.mapper.TSdkWhitelistRuleMapper;
import cn.ijiami.detection.service.api.SDKWhitelistRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class SDKWhitelistRuleServiceImpl implements SDKWhitelistRuleService {

    @Autowired
    private TSdkWhitelistRuleMapper tSdkWhitelistRuleMapper;

    private volatile List<Pattern> patternList;
    private volatile Set<String> packageNameList;
    private volatile List<String> packageNamePrefixList;
    private volatile long refreshTime = 0;

    @Override
    public boolean inWhitelist(String packageName) {
        checkRefresh();
        if (patternList.stream().anyMatch(p -> p.matcher(packageName).matches())) {
            return true;
        }
        if (packageNameList.contains(packageName)) {
            return true;
        }
        return packageNamePrefixList.stream().anyMatch(packageName::startsWith);
    }

    private void checkRefresh() {
        if (canRefresh()) {
            refreshData();
        }
    }

    private synchronized void refreshData() {
        if (canRefresh()) {
            patternList = tSdkWhitelistRuleMapper.findPatternByType(SDKWhitelistRuleType.PATTERN.getValue())
                    .stream().map(Pattern::compile).collect(Collectors.toList());
            packageNameList = new HashSet<>(tSdkWhitelistRuleMapper.findPackageNameByType(SDKWhitelistRuleType.PACKAGE_NAME.getValue()));
            packageNamePrefixList = packageNameList.stream().map(p -> p + ".").collect(Collectors.toList());
            refreshTime = System.currentTimeMillis();
        }
    }

    private boolean canRefresh() {
        return System.currentTimeMillis() - refreshTime > 10 * 60 * 1000;
    }
}
