package cn.ijiami.detection.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSdkLibraryPackageMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.UserSdkAliasUpdate;
import cn.ijiami.detection.VO.UserSdkAliasVO;
import cn.ijiami.detection.entity.TUserSdkAlias;
import cn.ijiami.detection.mapper.TUserSdkAliasMapper;
import cn.ijiami.detection.query.UserSdkAliasQuery;
import cn.ijiami.detection.service.api.UserSdkAliasService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserSdkAliasServiceImpl.java
 * @Description 用户sdk别名服务
 * @createTime 2023年01月12日 16:48:00
 */
@Slf4j
@Service
public class UserSdkAliasServiceImpl implements UserSdkAliasService {

    @Autowired
    private TUserSdkAliasMapper userSdkAliasMapper;

    @Autowired
    private TSdkLibraryPackageMapper sdkLibraryPackageMapper;

    @Override
    public PageInfo<UserSdkAliasVO> findAliasListByPage(Long userId, UserSdkAliasQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<UserSdkAliasVO> aliasList = userSdkAliasMapper.findUserSdkAlias(Collections.singletonList(userId), query.getName(), query.getSdkPackage());
        Example example = new Example(TSdkLibraryPackage.class);
        example.createCriteria()
                .andIn("sdkId", aliasList.stream().map(UserSdkAliasVO::getSdkId).collect(Collectors.toList()))
                .andIsNotNull("packageName");
        List<TSdkLibraryPackage> packageList = sdkLibraryPackageMapper.selectByExample(example);
        for (UserSdkAliasVO aliasVO:aliasList) {
            aliasVO.setSdkPackageList(
                    packageList
                            .stream()
                            .filter(p -> p.getSdkId().equals(aliasVO.getSdkId()))
                            .map(TSdkLibraryPackage::getPackageName)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .collect(Collectors.toList())
            );
        }
        return new PageInfo<>(aliasList);
    }

    @Override
    public void updateAlias(Long userId, UserSdkAliasUpdate update) {
        TUserSdkAlias query = new TUserSdkAlias();
        query.setUserId(userId);
        query.setSdkId(update.getSdkId());
        TUserSdkAlias alias = userSdkAliasMapper.selectOne(query);
        if (alias == null) {
            alias = new TUserSdkAlias();
            alias.setUserId(userId);
            alias.setSdkId(update.getSdkId());
            alias.setSdkAlias(update.getSdkAlias());
            alias.setCreateTime(new Date());
            alias.setUpdateTime(new Date());
            userSdkAliasMapper.insert(alias);
        } else {
            alias.setSdkAlias(update.getSdkAlias());
            alias.setUpdateTime(new Date());
            userSdkAliasMapper.updateByPrimaryKeySelective(alias);
        }
    }
}
