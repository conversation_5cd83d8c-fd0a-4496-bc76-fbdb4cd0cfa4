package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_ANALYSIS_TASK_DATA_PREFIX;
import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_UPLOAD_TASK_DATA_PREFIX;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.DETECTION_AUTO_WAITING;
import static cn.ijiami.detection.helper.IosActionLogConvertHelper.INFO_NET_RESPONSE_FLAG;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.enums.BooleanEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.similarity.EditDistance;
import org.apache.commons.text.similarity.JaccardDistance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.ijm.ios.RuntimeDetection.IosDynamicDataParser;
import com.ijm.ios.RuntimeDetection.data.InfoAct;
import com.ijm.ios.RuntimeDetection.data.InfoConfig;
import com.ijm.ios.RuntimeDetection.data.InfoData;
import com.ijm.ios.RuntimeDetection.data.InfoLog;
import com.ijm.ios.RuntimeDetection.data.IosBehaviorLogResult;
import com.ijm.ios.RuntimeDetection.enums.IosBehaviorStageEnum;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.aspect.DistributeLock;
import cn.ijiami.detection.bean.Pair;
import cn.ijiami.detection.bean.WaitingForCheckWord;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIosFrameworkLibrary;
import cn.ijiami.detection.entity.TIosPrivacyActionSdk;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSdkLibraryClass;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.lock.LockFailureAction;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.job.TaskSortThread;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TIosFrameworkLibraryMapper;
import cn.ijiami.detection.mapper.TIosPrivacyActionSdkMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatExtendMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsResultMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyItemMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyResultMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.parser.IosResultDataLogParser;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.service.api.IDynamicIOSActionDataService;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.StaticFunctionAnalysisService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.DateUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.TarGzUtil;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.utils.WordStringUtil;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.kit.utils.UuidUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import tk.mybatis.mapper.entity.Example;

@Service
public class DynamicIOSActionDataServiceImpl extends AbstractIosDynamicDetectionService<TaskDetailVO> implements IDynamicIOSActionDataService {

    private static Logger logger = LoggerFactory.getLogger(DynamicIOSActionDataServiceImpl.class);

    /**
     * 动态检测需要解析的文件组
     */
    public static final Map<IosBehaviorStageEnum, String> ANALYZE_FILE_MAP = new HashMap<>();
    /**
     * 深度检测需要解析的文件组
     */
    private static final Map<BehaviorStageEnum, String> MAMUAL_ANALYZE_FILE_MAP = new HashMap<>();

    public static final Set<IosBehaviorStageEnum> IOS = new HashSet<>();

    public static final List<String> RESULT_DATA_LOG_PATH = new ArrayList<>();

    //mysql数据库text、字符集utf8 文本最大可存储量
    public static final int MYSQL_TEXT_INDEX_LENGTH_MAX = 30000;

    /*
     * 隐私详情的第一张截图
     */
    public static final String PRIVACY_DETAIL_BEGIN_PREFIX = "Privacy_Detail_0";

    /**
     * html文件夹内隐私文本的文件名
     */
    public static final String PRIVACY_HTML_NAME = "privacy.html";


    /**
     *  需要跳过检查的文件
     */
    public static final String[] IGNORE_SENSITIVE_CHECK_SUFFIX = new String[]{".plist", ".mp4", ".mp3"};

    static {
        ANALYZE_FILE_MAP.put(IosBehaviorStageEnum.GRANT_STAGE, "result_01");
        ANALYZE_FILE_MAP.put(IosBehaviorStageEnum.REFUSE_STATGE, "result_02");
        ANALYZE_FILE_MAP.put(IosBehaviorStageEnum.FRONT_STAGE, "result_03");
        ANALYZE_FILE_MAP.put(IosBehaviorStageEnum.GROUND_STAGE, "result_04");
        IOS.add(IosBehaviorStageEnum.GRANT_STAGE);
        IOS.add(IosBehaviorStageEnum.REFUSE_STATGE);
        IOS.add(IosBehaviorStageEnum.FRONT_STAGE);
        IOS.add(IosBehaviorStageEnum.GROUND_STAGE);
        RESULT_DATA_LOG_PATH.add("result_01");
        RESULT_DATA_LOG_PATH.add("result_02");
        RESULT_DATA_LOG_PATH.add("result_03");

        MAMUAL_ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_FRONT, "shared_prefs");
        MAMUAL_ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GRANT, "shared_prefs_02");
        MAMUAL_ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GROUND, "shared_prefs_03");
        MAMUAL_ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_EXIT, "shared_prefs_04");
    }

    @Autowired
    TTaskMapper taskMapper;
    @Autowired
    IjiamiCommonProperties commonProperties;
    @Autowired
    ISendMessageService sendMessageService;
    @Autowired
    TaskSortThread taskSortThread;
    @Autowired
    TSensitiveWordMapper sensitiveWordMapper;
    @Autowired
    private TActionNougatMapper tActionNougatMapper;
    @Autowired
    private TPrivacyActionNougatMapper tPrivacyActionNougatMapper;

    @Autowired
    private TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;

    @Autowired
    private TAssetsMapper tAssetsMapper;
    @Autowired
    private TTaskMapper tTaskMapper;
    @Autowired
    private TPrivacySensitiveWordMapper tPrivacySensitiveWordMapper;
    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;
    @Autowired
    private IpUtil ipUtil;
    @Autowired
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    @Autowired
    private TPrivacyOutsideAddressMapper tPrivacyOutsideAddressMapper;

    @Value("${ijiami.logCrtl.config.path}")
    private String logCrtlConfigPath;

    @Value("${ijiami.privacyPolicy.mimTextSize:7}")
    private Integer privacyPolicyMinTextSize;

    @Value("${ijiami.privacyPolicy.mimTextLineSpacing:-4}")
    private Integer privacyPolicyMinTextLineSpacing;

    @Autowired
    private IScreenshotImageService screenshotImageService;

    @Autowired
    private IPrivacyLogCrtlServiceImpl privacyLogCrtlService;

    @Autowired
    private TPrivacyLawsDetailMapper privacyLawsDetailMapper;

    @Autowired
    private TPrivacyLawsResultMapper privacyLawsResultMapper;

    @Autowired
    private IosResultDataLogParser iosResultDataLogParser;

    @Autowired
    private TPrivacyPolicyResultMapper privacyPolicyResultMapper;

    @Autowired
    private TPrivacyPolicyItemMapper privacyPolicyItemMapper;

    @Autowired
    private TIosPrivacyActionSdkMapper iosPrivacyActionSdkMapper;

    @Autowired
    private TIosFrameworkLibraryMapper iosFrameworkLibraryMapper;

    @Autowired()
    @Qualifier("redisDistributedLock")
    private DistributedLockService distributedLockService;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private StaticFunctionAnalysisService staticFunctionAnalysisService;

    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

//    @Autowired
//    private SingleFastDfsFileService singleFastDfsFileService;
    
    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Value("${ijiami.tensorflow.checkboxModelPath:}")
    private String checkboxModelPath;

    @Value("${ijiami.tensorflow.checkedModelPath:}")
    private String checkedModelPath;

    @Override
    @DistributeLock(keyPrefix = KEY_UPLOAD_TASK_DATA_PREFIX, keyValue = "#task.taskId",
            lockFailureAction = LockFailureAction.CANCEL, timeoutMillis = 30 * 60 * 1000L)
    public void analysisAutoFromUpload(TTask task, MultipartFile data) {
        // 校验任务存在
        if (!checkDocumentValid(task)) {
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_WAITING) {
            logger.info("TaskId:{} 动态检测等待状态 进行状态转变", task.getTaskId());
            taskDAO.updateDynamicAutoWaiting(task, "", false);
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "AUTO", data);
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisIOSAutoInternal(task, file.getAbsolutePath(), StringUtils.EMPTY));
        } catch (IOException e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            logger.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        }
    }

    @Override
    @DistributeLock(keyPrefix = KEY_UPLOAD_TASK_DATA_PREFIX, keyValue = "#taskId",
            lockFailureAction = LockFailureAction.CANCEL, timeoutMillis = 30 * 60 * 1000L)
    public void analysisAutoAgain(Long taskId, MultipartFile data) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "AUTO", data);
            analysisIOSAutoInternal(task, file.getAbsolutePath(), task.getDataPath());
        } catch (IOException e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            logger.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        }
    }

    /**
     * 保存压缩包并返回
     *
     * @param md5    文件md5
     * @param taskId 任务ID
     * @param suffix 文件后缀
     * @return
     */
    @Override
    protected File saveFile(String md5, Long taskId, String suffix, MultipartFile data) throws IOException {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = new File(dynamicPath + md5 + "_" + taskId + "_" + suffix + ".tar.gz");
        if (data == null) {
            if (!file.exists()) {
                FileUtils.copyURLToFile(new URL(commonProperties.getProperty("ijiami.dynamic.data.download.path") + md5 + ".tar.gz"), file);
            }
        } else {
            data.transferTo(file);
            if (!file.getParentFile().exists()) {
                boolean mkdirs = file.getParentFile().mkdirs();
                if (!mkdirs) {
                    throw new FileNotFoundException("无法创建文件夹");
                }
            }
        }
        // 小于128k则认为检测结果数据包为空
        if (file.length() < 128 * 1024) {
            logger.info("ios自动检测解析入库 失败 数据包太小 fileLength={}", file.length());
            throw new IOException("检测结果数据为空");
        }
        return file;
    }

    /**
     * IOS快速解析入库
     * @param taskId
     * @param dataPath
     */

    @Override
    @DistributeLock(keyPrefix = KEY_UPLOAD_TASK_DATA_PREFIX, keyValue = "#taskId",
            lockFailureAction = LockFailureAction.CANCEL, timeoutMillis = 30 * 60 * 1000L)
    public void analysisIOSAutoFromWebSocket(Long taskId, String dataPath) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        File downloadFile = new File(
                commonProperties.getProperty("detection.tools.dynamic_path") + task.getMd5() + "_" + task.getTaskId() + "_AUTO" + ".tar.gz");
        HttpUtils.download(dataPath, downloadFile.getAbsolutePath(), null);
        // 小于128k则认为检测结果数据包为空
        if (downloadFile.length() < 128 * 1024) {
            logger.info("ios自动检测解析入库 失败 数据包太小 fileLength={}", downloadFile.length());
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "检测结果数据为空");
            return;
        }
        // 解析入库
        logger.info("ios自动检测解析入库 taskId={} filePath={}", task.getTaskId(), downloadFile.getAbsolutePath());
        executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisIOSAutoInternal(task, downloadFile.getAbsolutePath(), dataPath));
    }

    /**
     *
     * @param task
     * @param filePath
     * @param fastDfsDataPath 上传到文件服务器后的到的路径，如果没有则传空，会把dataPath的文件上传到文件服务器
     */
    private void analysisIOSAutoInternal(TTask task, String filePath, String fastDfsDataPath) {
        //存放动态检测文件包的路径
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        //解压后放的路径
        String uncompress = dynamicPath + "ios_AUTO_"+ task.getTaskId() +UuidUtil.uuid();
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(30))) {
            logger.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            updateTask.setDataPath(StringUtils.isEmpty(fastDfsDataPath) ? uploadDataFileToFastDfs(filePath) : fastDfsDataPath);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            //要解压的文件 (全路径)
            Path source = Paths.get(filePath);
            //解压到哪 (全路径）
            //该路径用于后面获取截图相关信息
            Path target = Paths.get(uncompress);

            //解压文件
            TarGzUtil.testDeCompressTarGzip(source,target);

            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            //配置文件
            String configPath = logCrtlConfigPath + "/config/config.json";
            //解析行为数据并入库
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisUncompress(taskDetailVo, filePath, configPath,uncompress);
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap, new ArrayList<>());
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);

            logger.info("ios自动检测解析入库 taskId={} 完成 开始修改任务状态", task.getTaskId());
            updateTaskInfoWhenAnalyzeFinish(task);
            logger.info("ios 动态检测完成 taskId={} 时间={}s", task.getTaskId(), (System.currentTimeMillis() - startTime) / 1000);
        } catch (IOException e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包不存在");
            logger.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        } catch (Throwable e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据解析失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, DetectionTypeEnum.DYNAMIC.getName() + "失败");
            logger.error(String.format("检测数据处理 - %s，数据解析异常:{}", DetectionTypeEnum.DYNAMIC.getName()), e);
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    private void updateTaskInfoWhenAnalyzeFinish(TTask task) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE);
        taskMapper.updateByPrimaryKeySelective(updateTask);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE.getValue());
        update.set("dynamic_detection_description", "静态函数检测中");
        update(paramMap, update);
        // 提交静态函数检测
        TAssets assets = tAssetsMapper.selectByPrimaryKey(task.getAssetsId());
        staticFunctionAnalysisService.startAnalysis(task, assets);
        taskSortThread.checkTaskSortListAsSoonASPossible();
        //检测回调第三方
        callBackMessageData(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, -1, task);
    }

    // 推送动态检测进度给第三方
    private void callBackMessageData(BroadcastMessageTypeEnum typeEnum, int progress, TTask task) {
        apiPushProgressServer.pushIosProgress(task, progress, typeEnum);
    }

    /**
     * ios解析包入库重试
     *
     * @param taskId
     */
    @Override
    public void analysisDataRetry(Long taskId) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        taskDAO.updateDynamicAutoDataProcess(task.getTaskId());
        // 获取mongodb的TaskDetailVO数据
        Integer detectionType = task.getDetectionType();
        String zipSuffix = TaskDetectionTypeEnum.isAuto(detectionType) ? "_AUTO" : "_MANUAL";
        //存放动态检测文件包的路径
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = new File(dynamicPath + task.getMd5() + "_" + task.getTaskId() + zipSuffix + ".tar.gz");
        //要解压的文件 (全路径)
        if (!file.exists()) {
            try {
                if (task.getDataPath() != null && task.getDataPath().startsWith("http")) {
                    FileUtils.copyURLToFile(new URL(task.getDataPath()), file);
                } else {
                    FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIntranetIp)), file);
                }
            } catch (IOException e) {
                logger.error("文件加载出错", e);
                return;
            }
        }
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            analysisIOSAutoInternal(task, file.getAbsolutePath(), task.getDataPath());
        } else {
            analyzingIOSManual(task, file.getAbsolutePath());
        }
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param filePath
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisUncompress(TaskDetailVO taskDetailVo, String filePath,String configPath, String uncompress) {
        // 清理行为、抓包数据、ip信息、SharedPrefs
        tPrivacyActionNougatMapper.deleteByTaskId(taskDetailVo.getTaskId());
        tPrivacySensitiveWordMapper.deleteByTaskId(taskDetailVo.getTaskId());
        tPrivacyOutsideAddressMapper.deleteByTaskId(taskDetailVo.getTaskId());
        privacySharedPrefsMapper.deleteByTaskId(taskDetailVo.getTaskId());
        List<TIosFrameworkLibrary> iosFrameworkLibraries = iosFrameworkLibraryMapper.selectAll();
        List<TSdkLibraryClass> sdkLibraryClassList = sdkLibraryClassMapper.selectAll();
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        Map<String, TIosPrivacyActionSdk> sdkApiVOMap = new HashMap<>();
        Map<String, SuspiciousSdkBO> suspiciousSdkMap = new HashMap<>();
        for(IosBehaviorStageEnum iosEnum:IOS){
            try {
                //解压后的一级文件目录
                File firstfile = new File(uncompress);
                if(firstfile.isDirectory()){
                    //所有二级文件目录
                    File[] secondfiles = firstfile.listFiles();
                    if(secondfiles!=null && secondfiles.length==1){
                        File secondFile = secondfiles[0];
                        if(secondFile.isDirectory()){
                            //存放图片的位置
                            String screenPath = secondFile.getCanonicalPath() + File.separator + ANALYZE_FILE_MAP.get(iosEnum) + File.separator +"Main"+ File.separator +"screenshot";
                            //拼装头文件，后面要拼存储个人信息的路径
                            String headPath = secondFile.getCanonicalPath().replace("\\","/");
                            DetectDataBO detectDataBO = analyzeAction(iosEnum, filePath, configPath, taskDetailVo,
                                    screenPath, headPath, sdkApiVOMap, suspiciousSdkMap, iosFrameworkLibraries, sdkLibraryClassList);
                            BehaviorStageEnum stageEnum = convertIosStage(iosEnum);
                            DetectDataBO dataBO = detectDataMap.get(stageEnum);
                            if (dataBO != null) {
                                dataBO.getPrivacyActionNougats().addAll(detectDataBO.getPrivacyActionNougats());
                                dataBO.getPrivacySharedPrefs().addAll(detectDataBO.getPrivacySharedPrefs());
                                dataBO.getPrivacySensitiveWords().addAll(detectDataBO.getPrivacySensitiveWords());
                                dataBO.getPrivacyOutsideAddresses().addAll(detectDataBO.getPrivacyOutsideAddresses());
                            } else {
                                detectDataMap.put(stageEnum, detectDataBO);
                            }
                        }
                    }
                }

            } catch (IOException e) {
                logger.info("文件路径不存在：[{}] e={}", uncompress, e);
            }
        }
        logger.info("检测数据解析，TaskId：[{}]，全部行为数据解析完成", taskDetailVo.getTaskId());
        List<TIosPrivacyActionSdk> insertSdkList = new ArrayList<>(sdkApiVOMap.values());
        if (!insertSdkList.isEmpty()) {
            logger.info("检测数据解析，TaskId：[{}]，开始保存数据", taskDetailVo.getTaskId());
            // 删除旧的数据
            Example example = new Example(TIosPrivacyActionSdk.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("taskId", taskDetailVo.getTaskId());
            iosPrivacyActionSdkMapper.deleteByExample(example);
            // 写入新的
            InsertListHelper.insertList(insertSdkList, iosPrivacyActionSdkMapper::insertList);
        }
        if (!suspiciousSdkMap.isEmpty()) {
            logger.info("检测数据解析，TaskId：[{}]，开始保存疑似sdk", taskDetailVo.getTaskId());
            updateSdkPermissionCodeAndInsertRecord(taskDetailVo.getTaskId(), new ArrayList<>(suspiciousSdkMap.values()), TerminalTypeEnum.IOS);
        }
        return detectDataMap;
    }

    private BehaviorStageEnum convertIosStage(IosBehaviorStageEnum stageEnum) {
        if (stageEnum == IosBehaviorStageEnum.GRANT_STAGE) {
            return BehaviorStageEnum.BEHAVIOR_GRANT;
        } else if (stageEnum == IosBehaviorStageEnum.REFUSE_STATGE) {
            return BehaviorStageEnum.BEHAVIOR_FRONT;
        } else if (stageEnum == IosBehaviorStageEnum.FRONT_STAGE) {
            return BehaviorStageEnum.BEHAVIOR_FRONT;
        } else if (stageEnum == IosBehaviorStageEnum.GROUND_STAGE) {
            return BehaviorStageEnum.BEHAVIOR_GROUND;
        } else {
            throw new IjiamiRuntimeException("unknown IosBehaviorStage");
        }
    }

    /**
     * 分析检测得到的数据
     * @param iosEnum
     * @param filePath
     * @param configPath
     * @param taskDetailVO
     */
    private DetectDataBO analyzeAction(IosBehaviorStageEnum iosEnum, String filePath, String configPath,
                                       TaskDetailVO taskDetailVO,String screenPath,String headPath,
                                       Map<String, TIosPrivacyActionSdk> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                       List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList) {
        logger.info("检测数据解析，任务ID：[{}]，资产名称：[{}]，解析阶段：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name(), iosEnum.getName());
        DetectDataBO detectDataBO = new DetectDataBO();
        //存放应用行为信息
        List<IosBehaviorLogResult> resultOfBehaviorSdk = new ArrayList<>();
        //存放通信行为信息
        List<IosBehaviorLogResult> resultOfNet = new ArrayList<>();
        //存放存储个人信息POIXMLDocument
        List<IosBehaviorLogResult> resultOfWrite = new ArrayList<>();

        try (IosDynamicDataParser iosDynamicDataParser = new IosDynamicDataParser(filePath, configPath)) {
            resultOfBehaviorSdk = iosDynamicDataParser.getBehaviorResultByStage(iosEnum);
            resultOfNet = iosDynamicDataParser.getNetBehaviorResultByStage(iosEnum);
            resultOfWrite = iosDynamicDataParser.getWriteBehaviorResultByStage(iosEnum);
        } catch (Exception e) {
            logger.info("调用jar包解析出错", e);
        }
        logger.info("检测数据解析，任务ID：[{}]，行为数据读取完成，解析阶段：[{}]", taskDetailVO.getTaskId(), iosEnum.getName());
        //查询资产
        TAssets tAssets = tAssetsMapper.selectByPrimaryKey(tTaskMapper.selectByPrimaryKey(taskDetailVO.getTaskId()).getAssetsId());

        Map<Long,String> screenshotMap = new HashMap<>();
        //获取截图时间搓跟filekey，避免每条行为数据都要再单独存储一次截图，直接先存截图再去比对行为入库，行为有数据才去存截图
        screenshotMap = screenshotImageService.savePictureAndReturnMap(screenPath);
        //分析应用行为数据并入库
        String executableName = CommonUtil.getIosExecutableName(taskDetailVO);
        detectDataBO.setPrivacyActionNougats(
                analysisActIos(resultOfBehaviorSdk,taskDetailVO.getTaskId(), executableName,
                        tAssets.getName(), tAssets.getPakage(), screenshotMap, sdkApiVOMap, suspiciousSdkMap));
        //分析通信行为数据、传输个人信息数据并入库
        Pair<List<TPrivacyOutsideAddress>, List<TPrivacySensitiveWord>> pair = analysisNet(resultOfNet, taskDetailVO.getTaskId(),
                tAssets.getName(),screenshotMap, tAssets.getPakage(), executableName, sdkApiVOMap, suspiciousSdkMap);
        //分析存储个人信息并入库
        detectDataBO.setPrivacySharedPrefs(saveSharedPrefs(taskDetailVO.getTaskId(),resultOfWrite,tAssets.getPakage(),
                tAssets.getName(),screenshotMap,headPath, executableName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList));
        detectDataBO.setPrivacyOutsideAddresses(pair.getKey());
        detectDataBO.setPrivacySensitiveWords(pair.getValue());
        logger.info("检测数据解析，任务ID：[{}]，行为数据解析完成，解析阶段：[{}]", taskDetailVO.getTaskId(), iosEnum.getName());
        return detectDataBO;
    }


    /**
     * 解析应用行为数据
     * @param results
     * @param taskId
     * @param appName
     * @param screenshotMap
     * @return
     */
    private List<TPrivacyActionNougat> analysisActIos(List<IosBehaviorLogResult> results, Long taskId,
                                                      String executableName,
                                                      String appName,
                                                      String packageName,
                                                      Map<Long,String> screenshotMap,
                                                      Map<String, TIosPrivacyActionSdk> sdkApiVOMap,
                                                      Map<String, SuspiciousSdkBO> suspiciousSdkMap) {
        List<TPrivacyActionNougat> tPrivacyActionNougatList = new ArrayList<>();
        if(CollectionUtils.isEmpty(results)){
            logger.info("BehaviorInfoAction - 行为数据初步解析，无数据");
            return tPrivacyActionNougatList;
        }
        List<TActionFilterGroupRegex> regexList = actionFilterGroupDao.findActionFilterGroupRegexList(taskId, TerminalTypeEnum.IOS);
        List<IosBehaviorLogResult> actResultList = results.stream().filter(result -> result.getConfigApi() != null
                && result.getBehaviorStage() != null && StringUtils.isNotBlank(result.getConfigApi().getDesc())).collect(Collectors.toList());
        logger.info("BehaviorInfoAction - 行为数据初步解析: {} 条", actResultList.size());
        List<TIosFrameworkLibrary> iosFrameworkLibraries = iosFrameworkLibraryMapper.selectAll();
        List<TSdkLibraryClass> sdkLibraryClassList = sdkLibraryClassMapper.selectAll();
        for (IosBehaviorLogResult result : actResultList) {
            TActionNougat tActionNougat = tActionNougatMapper.findByTerminalTypeAndActionId(result.getConfigApi().getDesc(),
                    TerminalTypeEnum.IOS.getValue());
            if (tActionNougat != null) {
                //v2.5版本只要string类型为空则按照--入库
                TPrivacyActionNougat tPrivacyActionNougat = new TPrivacyActionNougat();
                tPrivacyActionNougat.setActionId(tActionNougat.getActionId());
                tPrivacyActionNougat.setActionTime(DateUtils.formatDate(DateUtils.getDateTimeFormat(result.getTime()), "yyy-MM-dd HH:mm:ss"));
                tPrivacyActionNougat.setActionTimeStamp(result.getTime().getTime());
                // 获取详细数据
                List<String> detailData = getInfo(result);

                tPrivacyActionNougat.setDetailsData(StringUtils.isEmpty(detailData.toString())? PinfoConstant.DETAILS_EMPTY:detailData.toString());
                tPrivacyActionNougat.setStackInfo(StringUtils.isEmpty(result.getTrigger())?PinfoConstant.DETAILS_EMPTY:result.getTrigger());
                tPrivacyActionNougat.setTaskId(taskId);
                tPrivacyActionNougat.setType(false);
                tPrivacyActionNougat.setApiName(result.getApi_name());
                tPrivacyActionNougat.setBehaviorStage(changeIosBehaviorStage(result.getBehaviorStage()));
                // 设置主体名称和主体类型，包名
                setNougatExecutor(tPrivacyActionNougat, result.getTrigger(), executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, iosFrameworkLibraries, sdkLibraryClassList);
                // 行为过滤
                if (BehaviorInfoAction.filterActionGroupRegex(regexList, tPrivacyActionNougat.getActionId(),
                        tPrivacyActionNougat.getStackInfo(), tPrivacyActionNougat.getDetailsData())) {
                    continue;
                }
                tPrivacyActionNougatList.add(tPrivacyActionNougat);
            }
        }

        if (tPrivacyActionNougatList.size() == 0) {
            return tPrivacyActionNougatList;
        }
        /***新增行为触发次数（秒/次）开始****/
        // 按照每秒钟统计数据相同actionId的数据
        setNougatsCycleTrigger(tPrivacyActionNougatList, TerminalTypeEnum.IOS);
        // 分段批量写入，避免超出mysql的max_allowed_packet
        InsertListHelper.insertList(tPrivacyActionNougatList, tPrivacyActionNougatMapper::insertList);
        // 扩展表数据需要放到主表写入后有id了再写入，需要关联数据
        List<TPrivacyActionNougatExtend> extendList = tPrivacyActionNougatList.stream()
                .filter(nougat -> StringUtils.isNotBlank(nougat.getApiName()))
                .map(TPrivacyActionNougatExtend::make)
                .collect(Collectors.toList());
        if (!extendList.isEmpty()) {
            privacyActionNougatExtendMapper.insertList(extendList);
        }
        //入库截图信息
        screenshotImageService.saveDynamicBehaviorImg(taskId,screenshotMap,tPrivacyActionNougatList);
        logger.info("BehaviorInfoAction - 行为数据解析，获取到的符合要求的完整解析数据: {} 条", tPrivacyActionNougatList.size());
        return tPrivacyActionNougatList;
    }

    private List<String> getInfo(IosBehaviorLogResult result) {
        List<String> detailData = result.getInfo();
        LongSummaryStatistics lengthStatistics = detailData.stream().map(String::length).collect(Collectors.summarizingLong(l -> l));
        // 数据过长，需要缩减
        if (lengthStatistics.getSum() >= MYSQL_TEXT_INDEX_LENGTH_MAX) {
            // 需要减少的长度
            long reduceLength = lengthStatistics.getSum() - MYSQL_TEXT_INDEX_LENGTH_MAX;
            if (reduceLength < lengthStatistics.getMax()) {
                return detailData.stream().map(s -> {
                    if (s.length() == lengthStatistics.getMax()) {
                        return s.substring(0, Math.toIntExact(lengthStatistics.getMax() - reduceLength));
                    } else {
                        return s;
                    }
                }).collect(Collectors.toList());
            } else {
                // 要缩减的长度大于列表里最长的一项，缩减的数据量太多，不进行展示
                return Collections.singletonList("数据过长，请解析数据包查看");
            }
        } else {
            return detailData;
        }
    }

    /**
     * 通信行为和传输个人信息行为分析保存
     */
    private Pair<List<TPrivacyOutsideAddress>, List<TPrivacySensitiveWord>> analysisNet(List<IosBehaviorLogResult> results,
                                                                                        Long taskId, String appName, Map<Long,String> screenshotMap,
                                                                                        String packageName, String executableName,
                                                                                        Map<String, TIosPrivacyActionSdk> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap) {
        List<TPrivacyOutsideAddress> addressList = new ArrayList<>();
        List<TPrivacySensitiveWord> wordList = new ArrayList<>();
        if(CollectionUtils.isEmpty(results)){
            logger.info("HostIpaAction - 通讯行为和传输个人信息数据初步解析，无数据");
            return new Pair<>(addressList, wordList);
        }
        List<TIosFrameworkLibrary> iosFrameworkLibraries = iosFrameworkLibraryMapper.selectAll();
        List<TSdkLibraryClass> sdkLibraryClassList = sdkLibraryClassMapper.selectAll();
        logger.info("HostIpaAction - 通讯行为和传输个人信息数据初步解析：{} 条", results.size());
        for (IosBehaviorLogResult result : results) {
            if (result.getConfigApi() == null || result.getBehaviorStage() == null){
                continue;
            }
            // 无效的网络行为
            if (StringUtils.isBlank(result.getIp())
                    && StringUtils.isBlank(result.getUrl())
                    && StringUtils.isBlank(result.getDomainName())) {
                continue;
            }
            //目前ios没有返回端口号
            String port=null;
            //ip地址
            String ip = result.getIp();
            //域名
            String host = result.getDomainName();
            //函数调用栈
            String stackInfo = result.getTrigger();
            //详细信息
            String conetent = CommonUtil.checkBase64(result.getContent());
            //请求方式
            String requestMethod = result.getMethod();
            //url地址
            String url = result.getUrl();
            //协议
            String protocol = result.getOperate();
            //行为时间
            Date strTime = DateUtils.formatDate(DateUtils.getDateTimeFormat(result.getTime()), "yyy-MM-dd HH:mm:ss");
            //行为阶段   将ios的行为阶段转换成跟安卓一样的表示
            BehaviorStageEnum behaviorStage = changeIosBehaviorStage(result.getBehaviorStage());
            //域名 与host是一致的
            String domainName = result.getDomainName();
            //cookie  v2.5版本后面新增
            String cookie = getCookie(result.getCookie());
            //保存通信行为数据
            addressList.add(saveAddress(ip, host, taskId, appName, stackInfo,conetent, strTime,behaviorStage,
                    packageName,requestMethod,url,protocol,cookie, suspiciousSdkMap, port, executableName, sdkApiVOMap,
                    iosFrameworkLibraries, sdkLibraryClassList));
            //保存传输个人信息行为数据
            wordList.addAll(saveSensitiveWords(taskId, appName, conetent, stackInfo,requestMethod,url, domainName,
                    behaviorStage,screenshotMap,ip,packageName,protocol,strTime,cookie,port, executableName, sdkApiVOMap,
                    suspiciousSdkMap, iosFrameworkLibraries, sdkLibraryClassList));
        }
        //截图入库要放在后面
        screenshotImageService.saveImgOfOutsideData(taskId, screenshotMap, addressList);
        logger.info("HostIpaAction - 通讯行为数据解析,获取到的符合要求的完整解析数据: {} 条", addressList.size());
        logger.info("HostIpaAction - 传输个人信息数据解析,获取到的符合要求的完整解析数据: {} 条", wordList.size());
        return new Pair<>(addressList, wordList);
    }

    /**
     * 根据header文本去拿到cookie
     * @param content
     * @return
     */
    private String getCookie(String content) {
        try {
			if (StringUtils.isBlank(content)) {
			    return StringUtils.EMPTY;
			}
			String headerData = CommonUtil.checkBase64(content);
			JSONObject headers = JSONObject.fromObject(headerData);
			for (Object key:headers.keySet()) {
			    String keyStr = key.toString();
			    if (StringUtils.equalsIgnoreCase("Cookie", keyStr)) {
			        return headers.optString(keyStr, StringUtils.EMPTY);
			    }
			}
		} catch (Exception e) {
			e.getMessage();
		}
        return StringUtils.EMPTY;
    }

    /**
     * 构建通信行为实体并写入数据
     * @param ip             IP地址
     * @param host           域名地址
     * @param taskId         任务id
     * @param appName        应用名称
     * @param stackInfo      函数调用栈
     * @param detailsData    详细数据
     * @param strTime        行为发生时间
     * @param behaviorStage  行为阶段
     * @param packageName    包名
     * @param requestMethod  请求方式
     * @param url            url地址
     * @param protocol       协议
     */
    private TPrivacyOutsideAddress saveAddress(String ip, String host, Long taskId, String appName, String stackInfo, String detailsData, Date strTime,BehaviorStageEnum behaviorStage,
                                               String packageName,String requestMethod,String url,String protocol,
                                               String cookie, Map<String, SuspiciousSdkBO> suspiciousSdkMap, String port, String executableName,
                                               Map<String, TIosPrivacyActionSdk> sdkApiVOMap, List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList) {
        //v2.5版本只要string类型为空则按照--入库
        TPrivacyOutsideAddress tPrivacyOutsideAddress = new TPrivacyOutsideAddress();
        String city = ipUtil.getAddress(ip);
        // 境内外判断
        int isOutSide = IpUtil.isOutSide(city);
        tPrivacyOutsideAddress.setOutside(isOutSide);
        tPrivacyOutsideAddress.setCounter(1);
        tPrivacyOutsideAddress.setIp(StringUtils.isBlank(ip) ? "内部请求" : ip);
        tPrivacyOutsideAddress.setHost(StringUtils.isBlank(host) ? "内部请求" : host);
        tPrivacyOutsideAddress.setTaskId(taskId);
        tPrivacyOutsideAddress.setAddress(optDetails(city));
        tPrivacyOutsideAddress.setStackInfo(optDetails(stackInfo));
        // 是否响应数据
        if (INFO_NET_RESPONSE_FLAG.equals(requestMethod)) {
            tPrivacyOutsideAddress.setDetailsData(PinfoConstant.DETAILS_EMPTY);
            tPrivacyOutsideAddress.setResponseData(optDetails(detailsData));
        } else {
            tPrivacyOutsideAddress.setDetailsData(optDetails(detailsData));
            tPrivacyOutsideAddress.setResponseData(PinfoConstant.DETAILS_EMPTY);
        }
        tPrivacyOutsideAddress.setStrTime(strTime);
        tPrivacyOutsideAddress.setActionTime(strTime);
        tPrivacyOutsideAddress.setBehaviorStage(behaviorStage);
        tPrivacyOutsideAddress.setRequestMethod(optDetails(requestMethod));
        tPrivacyOutsideAddress.setUrl(optDetails(url));
        tPrivacyOutsideAddress.setProtocol(optDetails(protocol));
        tPrivacyOutsideAddress.setCookie(optDetails(cookie));
        //增加cookie标记，以便前台展示
        tPrivacyOutsideAddress.setCookieMark(StringUtils.isNotBlank(cookie)? BooleanEnum.TRUE.value : BooleanEnum.FALSE.value);
        tPrivacyOutsideAddress.setPort(optDetails(port));
        setNougatExecutor(tPrivacyOutsideAddress, stackInfo, executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList);
        tPrivacyOutsideAddressMapper.insert(tPrivacyOutsideAddress);
        return tPrivacyOutsideAddress;
    }

    /**
     * 分析网络抓包数据敏感词
     * @param taskId  任务id
     * @param appName 应用名称
     * @param conetent 详细数据数据
     * @param stackInfo 函数调用栈
     * @param method  请求方式
     * @param url     url地址
     * @param domainName    域名
     * @param behaviorStage  行为阶段
     * @param screenshotMap 截图集合
     * @param ips     ip地址
     * @param packageName  包名
     */
    public List<TPrivacySensitiveWord> saveSensitiveWords(Long taskId, String appName, String conetent, String stackInfo, String method, String url,
                                                          String domainName,BehaviorStageEnum behaviorStage, Map<Long,String> screenshotMap,String ips,
                                                          String packageName, String protocol,Date strTime,String cookie,String port,
                                                          String executableName, Map<String, TIosPrivacyActionSdk> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                                          List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> sdkLibraryClassList) {
        List<TPrivacySensitiveWord> privacySensitiveWords = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(conetent)) {
                return privacySensitiveWords;
            }
            List<TSensitiveWord> sensitiveWords = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue());
            //去除‘-’用于手机号码匹配
            conetent = conetent.replace("-", "");
            List<WaitingForCheckWord> waitingForCheckWords = new ArrayList<>();
            waitingForCheckWords.addAll(SensitiveWordsHelper.buildWaitingForCheckWords(conetent.split("@ijiami_data_boundary\n")));
            waitingForCheckWords.add(new WaitingForCheckWord(cookie, true));
            for (WaitingForCheckWord checkWord : waitingForCheckWords) {
                String content = checkWord.getContent();
                if (StringUtils.isBlank(content))
                    continue;
                //用于正则截取数据
                for (TSensitiveWord sensitiveWord : sensitiveWords) {
                    // base64的字符串就不需要往下执行正则表达式匹配，避免数据量过大导致匹配几个小时
                    if (org.apache.commons.codec.binary.Base64.isBase64(content)) {
                        continue;
                    }
                    if (!StringUtils.isBlank(sensitiveWord.getRegex())) {
                        //正则表达式匹配
                        Pattern pattern = Pattern.compile(sensitiveWord.getRegex());
                        Matcher matcher = pattern.matcher(content);
                        logger.info("regex={} request={}", sensitiveWord.getRegex(), content);
                        while (matcher.find()) {
                            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, content, sensitiveWord, true);
                            if (isInvalidInfo) {
                                continue;
                            }
                            String code = WordStringUtil.converCharacters(SensitiveWordsHelper.getMatchString(matcher, content));
                            if (code.contains("Cookie: ")) {
                                method = "Cookies";
                            }
                            saveTPrivacySensitiveWord(taskId, appName, matcher.group(), method, url, domainName, code,
                                    sensitiveWord, privacySensitiveWords, stackInfo, conetent,behaviorStage,ips,packageName,
                                    protocol,strTime,cookie,port, executableName, sdkApiVOMap, suspiciousSdkMap, libraryList, sdkLibraryClassList, checkWord.isCookie());
                        }
                    } else {
                        if (StringUtils.isBlank(sensitiveWord.getSensitiveWords())) {
                            continue;
                        }
                        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(content, method, sensitiveWord.getSensitiveWords(),false)) {
                            saveTPrivacySensitiveWord(taskId, appName, word.getWord(), word.getMethod(), url, domainName, word.getCode(), sensitiveWord,
                                    privacySensitiveWords, stackInfo, conetent,behaviorStage,ips,packageName,protocol,
                                    strTime,cookie,port, executableName, sdkApiVOMap, suspiciousSdkMap, libraryList, sdkLibraryClassList, checkWord.isCookie());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("关键词获取失败", e);
        }

        if (privacySensitiveWords.size() == 0) {
            return privacySensitiveWords;
        }
        tPrivacySensitiveWordMapper.insertList(privacySensitiveWords);
        //入库截图信息
        screenshotImageService.saveDynamicBehaviorImgSensitiveWord(taskId,screenshotMap, privacySensitiveWords);
        return privacySensitiveWords;
    }

    /**
     * 保存敏感词
     *
     * @param taskId                任务id
     * @param appName               基本信息
     * @param kwd                   关键词
     * @param method                请求方式
     * @param url                   接口
     * @param host                  域名
     * @param code                  代码段
     * @param sensitiveWord         关键词对象
     * @param privacySensitiveWords 结果
     * @param stackInfo             函数调用栈
     * @param detailsData           详细数据
     * @param behaviorStage         行为阶段枚举
     * @param ip                    ip地址
     * @param packageName           包名
     */
    private void saveTPrivacySensitiveWord(Long taskId, String appName, String kwd, String method, String url, String host, String code,
                                           TSensitiveWord sensitiveWord, List<TPrivacySensitiveWord> privacySensitiveWords, String stackInfo,
                                           String detailsData,BehaviorStageEnum behaviorStage,String ip,String packageName,String protocol,
                                           Date strTime,String cookie,String port, String executableName, Map<String, TIosPrivacyActionSdk> sdkApiVOMap,
                                           Map<String, SuspiciousSdkBO> suspiciousSdkMap, List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList,
                                           boolean isCookieWord)
            throws FileNotFoundException, IjiamiApplicationException {
        //v2.5版本只要string类型为空则按照--入库
        String city = ipUtil.getAddress(ip);
        TPrivacySensitiveWord privacySensitiveWord = new TPrivacySensitiveWord();
        privacySensitiveWord.setTaskId(taskId);
        privacySensitiveWord.setTypeId(sensitiveWord.getTypeId());
        privacySensitiveWord.setSensitiveWord(StringUtils.isEmpty(kwd)?PinfoConstant.DETAILS_EMPTY:kwd);
        privacySensitiveWord.setName(StringUtils.isEmpty(sensitiveWord.getName())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getName());
        privacySensitiveWord.setMethod(StringUtils.isEmpty(method)?PinfoConstant.DETAILS_EMPTY:method);
        privacySensitiveWord.setUrl(StringUtils.isEmpty(url)?PinfoConstant.DETAILS_EMPTY:url);
        privacySensitiveWord.setHost(StringUtils.isEmpty(host)?PinfoConstant.DETAILS_EMPTY:host);
        privacySensitiveWord.setAddress(StringUtils.isEmpty(host)?PinfoConstant.DETAILS_EMPTY:host);
        privacySensitiveWord.setCode(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY:code);
        privacySensitiveWord.setRiskLevel(sensitiveWord.getRiskLevel());
        privacySensitiveWord.setSuggestion(sensitiveWord.getSuggestion());
        privacySensitiveWord.setStackInfo(StringUtils.isEmpty(stackInfo)?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(stackInfo));
        privacySensitiveWord.setDetailsData(StringUtils.isEmpty(detailsData)?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(detailsData));
        privacySensitiveWord.setBehaviorStage(behaviorStage);
        privacySensitiveWord.setIp(StringUtils.isEmpty(ip)?PinfoConstant.DETAILS_EMPTY:ip);
        privacySensitiveWord.setAttributively(StringUtils.isEmpty(city)?PinfoConstant.DETAILS_EMPTY:city);
        privacySensitiveWord.setProtocol(StringUtils.isEmpty(protocol)?PinfoConstant.DETAILS_EMPTY:protocol);
        privacySensitiveWord.setActionTime(strTime);
        privacySensitiveWord.setCookie(StringUtils.isEmpty(cookie)?PinfoConstant.DETAILS_EMPTY:cookie);
        //增加cookie标识，以便前端展示cookie标签
        privacySensitiveWord.setCookieMark(StringUtils.isNotBlank(cookie)?1:null);
        privacySensitiveWord.setPort(StringUtils.isEmpty(port)?PinfoConstant.DETAILS_EMPTY:port);
        privacySensitiveWord.setCookieWord(isCookieWord);
        privacySensitiveWord.setPlaintextTransmission(isPlaintextTransmission(sensitiveWord).value);
        setNougatExecutor(privacySensitiveWord, stackInfo, executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList);
        privacySensitiveWords.add(privacySensitiveWord);
    }

    /**
     * 解析存储个人信息
     *
     * @param
     */
    public List<TPrivacySharedPrefs> saveSharedPrefs(Long taskId, List<IosBehaviorLogResult> results,String packageName,
                                                     String appName,Map<Long,String> screenshotMap,String headPath,
                                                     String executableName, Map<String, TIosPrivacyActionSdk> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                                     List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList) {
        List<TPrivacySharedPrefs> sharedPrefs = new ArrayList<>();
        if (CollectionUtils.isEmpty(results)) {
            logger.info("SharedPrefAction - 存储个人信息数据初步解析，无数据");
            return sharedPrefs;
        }
        logger.info("SharedPrefAction - 存储个人信息数据初步解析：{} 条", results.size());

        List<TSensitiveWord> sensitiveWords = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue());

        for (IosBehaviorLogResult result : results) {
            //解析出来的文件路径，可能会存在空路径
            String url =result.getUrl();
            if(StringUtils.isEmpty(url)){
                continue;
            }
            //截取路径,默认所有的存储信息发生的文件路径为var/
            int varIndex = url.indexOf("var");
            if (varIndex < 0 || org.apache.commons.lang3.StringUtils.endsWithAny(url, IGNORE_SENSITIVE_CHECK_SUFFIX)) {
                continue;
            }
            url = url.substring(varIndex);
            //由于原解析包中的：导致解析出错在解析时改成了_,故此处也要改成_
            url = CommonUtil.filterFileName(url);
            File file = new File(headPath+"/"+url);
            if(!file.exists() || file.isDirectory()){
                continue;
            }
            String content = CommonUtil.readFileToPrintString(file.getAbsolutePath());
            //用于正则匹配数据截取
            if (org.apache.commons.lang3.StringUtils.isBlank(content)) {
                continue;
            }
            String keyword = result.getOperate();
            if (StringUtils.contains(keyword, "\"write\"")) {
                continue;
            }

            for (TSensitiveWord sensitiveWord : sensitiveWords) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(sensitiveWord.getRegex())) {
                    //正则表达式匹配
                    Pattern pattern = Pattern.compile(sensitiveWord.getRegex());
                    Matcher matcher = pattern.matcher(content);
                    while (matcher.find()) {
                        boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, content, sensitiveWord, true);
                        if (isInvalidInfo) {
                            continue;
                        }
                        String code = SensitiveWordsHelper.getMatchString(matcher, content);
                        TPrivacySharedPrefs sharedPref = new TPrivacySharedPrefs();
                        //v2.5版本只要string类型为空则按照--入库
                        sharedPref.setTaskId(taskId);
                        sharedPref.setTypeId(sensitiveWord.getTypeId());
                        sharedPref.setName(StringUtils.isEmpty(sensitiveWord.getName())?PinfoConstant.DETAILS_EMPTY:sensitiveWord.getName());
                        sharedPref.setSensitiveId(sensitiveWord.getId());
                        sharedPref.setSensitiveWord(StringUtils.isEmpty(matcher.group())?PinfoConstant.DETAILS_EMPTY:matcher.group());
                        sharedPref.setContent(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY:code);
                        sharedPref.setPath(result.getUrl());
                        sharedPref.setActionTime(DateUtils.formatDate(DateUtils.getDateTimeFormat(result.getTime()), "yyy-MM-dd HH:mm:ss"));
                        sharedPref.setBehaviorStage(changeIosBehaviorStage(result.getBehaviorStage()));
                        // 保存堆栈数据
                        sharedPref.setStackInfo(StringUtils.isEmpty(result.getTrigger())?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(result.getTrigger()));
                        setNougatExecutor(sharedPref, result.getTrigger(), executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList);

                        sharedPrefs.add(sharedPref);
                    }
                } else {
                    //关键字匹配
                    if (org.apache.commons.lang3.StringUtils.isBlank(sensitiveWord.getSensitiveWords())) {
                        continue;
                    }
                    for (SensitiveWordsHelper.SensitiveInfo info : SensitiveWordsHelper.findName(content, sensitiveWord.getSensitiveWords())) {
                        TPrivacySharedPrefs sharedPref = new TPrivacySharedPrefs();
                        //v2.5版本只要string类型为空则按照--入库
                        sharedPref.setTaskId(taskId);
                        sharedPref.setTypeId(sensitiveWord.getTypeId());
                        sharedPref.setName(StringUtils.isEmpty(sensitiveWord.getName()) ? PinfoConstant.DETAILS_EMPTY : sensitiveWord.getName());
                        sharedPref.setSensitiveWord(info.getWord());
                        sharedPref.setSensitiveId(sensitiveWord.getId());
                        sharedPref.setContent(info.getCode());
                        sharedPref.setPath(result.getUrl());
                        // 保存堆栈数据
                        sharedPref.setStackInfo(StringUtils.isEmpty(result.getTrigger())?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(result.getTrigger()));
                        sharedPref.setActionTime(DateUtils.formatDate(DateUtils.getDateTimeFormat(result.getTime()), "yyy-MM-dd HH:mm:ss"));
                        sharedPref.setBehaviorStage(BehaviorStageEnum.getItem(result.getBehaviorStage().getCode()));
                        setNougatExecutor(sharedPref, result.getTrigger(), executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList);
                        sharedPrefs.add(sharedPref);
                    }
                }
            }
        }
        savePrivacySharedPrefs(taskId, sharedPrefs, screenshotMap);
        logger.info("SharedPrefAction - 存储个人信息数据解析,获取到的符合要求的完整解析数据: {} 条", sharedPrefs.size());
        return sharedPrefs;
    }

    /**
     * 存储个人信息入库
     * @param sharedPrefs 存储个人信息
     */
    private void savePrivacySharedPrefs(Long taskId, List<TPrivacySharedPrefs> sharedPrefs,Map<Long,String> screenshotMap) {
        if (sharedPrefs != null && sharedPrefs.size() > 0) {
            privacySharedPrefsMapper.insertList(sharedPrefs);
            //截图入库
            screenshotImageService.saveImgOfSharedData(taskId,screenshotMap,sharedPrefs);
        }
    }

    /**
     * ios行为阶段转为安卓行为阶段表示
     * @param iosEnum
     * @return
     */
    public static BehaviorStageEnum changeIosBehaviorStage(IosBehaviorStageEnum iosEnum){
        BehaviorStageEnum behaviorStage=null;
        if(iosEnum !=null){
            /**
             * ios授权前行为1 对应于 安卓授权前行为1
             * ios权限拒绝行为2、iOS前台运行行为3  对应于 安卓前台行为2
             * ios后台运行行为4  对应于  安卓后台行为3
             * ios没有应用退出行为
             */
            if(iosEnum == IosBehaviorStageEnum.GRANT_STAGE){
                behaviorStage = BehaviorStageEnum.BEHAVIOR_GRANT;
            }else if(iosEnum == IosBehaviorStageEnum.REFUSE_STATGE || iosEnum == IosBehaviorStageEnum.FRONT_STAGE){
                behaviorStage = BehaviorStageEnum.BEHAVIOR_FRONT;
            }else if(iosEnum == IosBehaviorStageEnum.GROUND_STAGE){
                behaviorStage = BehaviorStageEnum.BEHAVIOR_GROUND;
            }
        }
        return behaviorStage;
    }

    /**
     * ios深度检测解析重试
     *
     * @param task
     * @param filePath 压缩包
     */
    private void analyzingIOSManual(TTask task, String filePath) {
        //存放动态检测文件包的路径
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        //解压后放的路径
        String uncompress = dynamicPath + "ios_MANUAL_" + task.getTaskId() + UuidUtil.uuid();
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(30))) {
            logger.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            //要解压的文件 (全路径)
            Path source = Paths.get(filePath);
            //解压到哪 (全路径）
            //该路径用于后面获取截图相关信息
            Path target = Paths.get(uncompress);

            //解压文件
            TarGzUtil.testDeCompressTarGzip(source, target);

            //查询资产
            TAssets tAssets = tAssetsMapper.selectByPrimaryKey(tTaskMapper.selectByPrimaryKey(task.getTaskId()).getAssetsId());
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            //存储个人信息
            try {
                Map<BehaviorStageEnum, List<File>> sharedPrefsFile = new HashMap<>();
                for (Map.Entry<BehaviorStageEnum, String> entry:MAMUAL_ANALYZE_FILE_MAP.entrySet()) {
                    String sharedPrefsDir = uncompress + "/" + entry.getValue();
                    sharedPrefsFile.put(entry.getKey(), cn.hutool.core.io.FileUtil.loopFiles(sharedPrefsDir));
                }
                privacyLogCrtlService.analysisIosManualTaskData(task.getTaskId(), tAssets.getName(), sharedPrefsFile, tAssets.getPakage());
            } catch (Exception e) {
                logger.info("存储个人信息解析文件异常:" + e.getMessage());
            }
        } catch (Throwable e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, "数据解析失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, DetectionTypeEnum.MANUAL.getName() + "失败");
            logger.error("检测数据处理 - " + DetectionTypeEnum.MANUAL.getName() + "，数据解析异常:", e);
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
        }
    }

    //ͨlog日志转成 infoact结构体
    private static InfoData log2Act(String filePath,String taskId){
        InfoData infoData=new InfoData();
        File file = new File(filePath);
        if(!file.exists()){
            return null;
        }
        if(!file.isDirectory()){
            return null;
        }
        File[] lists=file.listFiles();
        for(File f:lists){
            String text=CommonUtil.readFileToString(f.getAbsolutePath());
            String[] json = text.split("\n");
            if(json.length==0){
                return null;
            }
            for(String strLogLine:json){
                try{
                    JSONObject jsonObject= new JSONObject();
                    try{
                        jsonObject = JSONObject.fromObject(strLogLine);
                    }catch(Exception e){
                        continue;
                    }
                    //获取当前api名
                    String strCurApiName = jsonObject.getString(InfoLog.STR_API_NAME);

                    InfoAct infoAct = new InfoAct();

                    //taskid 为当前ctrl taskid
                    infoAct.setStrTaskId(taskId);

                    infoAct.setStrName(strCurApiName);
                    infoAct.setStrType(jsonObject.getString(InfoConfig.STR_TYPE));
                    infoAct.setStrDesc(jsonObject.getString(InfoConfig.STR_DESC));

                    JSONArray jsonStrInfoAry = jsonObject.getJSONArray(InfoLog.STR_INFO);

                    infoAct.setStrInfo(jsonStrInfoAry.toString());
                    infoAct.setStrTime(jsonObject.getString(InfoLog.STR_TIME));
                    infoAct.setStrName(jsonObject.getString(InfoLog.STR_API_NAME));
                    infoAct.setStrTrigger(jsonObject.getString(InfoLog.STR_TRIGGER));

                    //如果有权限
                    if (true == jsonObject.getBoolean(InfoConfig.STR_HAS_PERMISSION))
                    {
                        JSONArray jAryStrPer =  jsonObject.getJSONArray(InfoConfig.STR_PER_NAME);
                        JSONArray jAryStrDesc = jsonObject.getJSONArray(InfoConfig.STR_PER_DESC);

                        //
                        Map<String, String> mapStrPer = new HashMap<String, String>();

                        //
                        int nSize = jAryStrDesc.size();
                        for (int i = 0; i < nSize; i++)
                        {
                            String strCurPerName = jAryStrPer.getString(i);
                            String strCurPerDesc = jAryStrDesc.getString(i);

                            mapStrPer.put(strCurPerName, strCurPerDesc);
                        }

                        //权限字段赋值
                        infoAct.setBoolHasPermission(true);
                        infoAct.setMapstrPermission(mapStrPer);
                    }
                    else
                    {
                        infoAct.setBoolHasPermission(false);
                    }

                    infoData.addAct(infoAct);
                }catch(Exception e){
                    e.getStackTrace();
                }
            }

        }

        return infoData;
    }


    private CommonDetectInfo analysisMiitDetectResult(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap,
                                                      List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS) {
        // 深度检测直接返回
        if (!TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            return null;
        }
        File file = new File(uncompress);
        if (file.listFiles() != null && file.listFiles().length == 1) {
            File innerDir = file.listFiles()[0];
            privacyLawsDetailMapper.deleteByTaskId(task.getTaskId());
            privacyLawsResultMapper.deleteByTaskId(task.getTaskId());
            appLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
            appLawsRiskCollectMapper.deleteByTaskId(task.getTaskId());
            sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
            sdkLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
            privacyLawsConclusionActionMapper.deleteByTaskId(task.getTaskId());
            List<SdkVO> sdkVOList = getSdkList(task);
            CommonDetectInfo commonDetectInfo = buildIosDetectInfo(task, innerDir.getAbsolutePath(), detectDataMap);
            
            //查询检测项
            TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
            for (PrivacyLawId lawId: getDetectionLawIds(extend, task.getTerminalType())) {
                analysisLaw(task, sdkVOList, commonDetectInfo, suspiciousSdkBehaviorVOS, lawId);
            }
            saveSdkDectDetail(task, sdkVOList);
            return commonDetectInfo;
        } else {
            return null;
        }
    }

    /**
     * 重新录入sdk统计数据
     * @param task
     */
    @Transactional
    public void restoreSdkDectDetail(TTask task) {
        logger.info("重新录入sdk统计数据");
        sdkLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        saveSdkDectDetail(task, sdkVOList);
    }

    @Override
    public void analysisManual(Long taskId, MultipartFile data) {
        privacyLogCrtlService.uploadIosDepthDetectionSharedPrefs(taskId, data);
    }

    private void analysisPrivacyCheck(TTask task, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, CommonDetectInfo commonDetectInfo) {
        if (commonDetectInfo == null) return;
        TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());
        // 数据更新，需要taskId
        taskDetail.setTaskId(task.getTaskId());
        // 清除历史记录
        privacyPolicyResultMapper.deleteByTaskId(taskDetail.getTaskId());

        Map<String, TPrivacyPolicyItem> itemMap = new HashMap<>();
        for (TPrivacyPolicyItem privacyPolicyItem : privacyPolicyItemMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue())) {
            itemMap.put(privacyPolicyItem.getItem_no(), privacyPolicyItem);
        }

        List<TPrivacyPolicyResult> policyResultList = new ArrayList<>();
        // 快速检测才检测
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            //个人信息保护政策检测
            policyResultList.add(buildPrivacyPolicyDetailResult(taskDetail, commonDetectInfo, itemMap));
        }
        privacyPolicyResultMapper.insertList(policyResultList);
    }

    private CommonDetectInfo buildIosDetectInfo(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        logger.info("TaskId:{} buildIosDetectInfo uncompress={}", task.getTaskId(), uncompress);
        // 组装公共参数
        CommonDetectInfo commonDetectInfo = buildCommonDetectInfo(task, uncompress, detectDataMap);

        List<ResultDataLogBO> resultDataLogBOList = new ArrayList<>();
        RESULT_DATA_LOG_PATH.forEach(path -> {
            // 从uncomperss中解析出的数据
            resultDataLogBOList.addAll(iosResultDataLogParser.parser(uncompress + File.separator + path + File.separator + "resultDataLog", commonDetectInfo.getApkName()));
        });
        commonDetectInfo.setResultDataLogs(resultDataLogBOList);

        List<PrivacyPolicyTextInfo> htmlPrivacyDetailList = new ArrayList<>();
        List<ResultDataLogBO> privacyResults = new ArrayList<>();
        for (ResultDataLogBO resultDataLogBO : resultDataLogBOList) {
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                privacyResults.add(resultDataLogBO);
                // ios的界面信息可能通过携带的url解析出html页面信息
                if (Objects.nonNull(resultDataLogBO.getUiDumpResult()) && Objects.nonNull(resultDataLogBO.getUiDumpResult().getPolicyTextInfoList())) {
                    htmlPrivacyDetailList.addAll(resultDataLogBO.getUiDumpResult().getPolicyTextInfoList());
                }
            }
        }
        RESULT_DATA_LOG_PATH.forEach(path -> {
            // 从uncomperss中解析出的数据
            htmlPrivacyDetailList.addAll(getSubPagePrivacyDetailByHtmlDir(uncompress + File.separator + path + File.separator + "html"));
        });
        TAssets assets = assetsMapper.getAssetByTaskId(task.getTaskId());
        // 先解析清单，要把清单从隐私政策中排除
        if (StringUtils.isNotBlank(assets.getThirdPartyShareListPath())) {
            analysisCheckListByUpload(assets, commonDetectInfo);
        } else {
            analysisCheckListByUi(commonDetectInfo, privacyResults, htmlPrivacyDetailList);
        }
        if (StringUtils.isNotBlank(assets.getPrivacyPolicyPath())) {
            // 用户有上传隐私文件
            analysisUserUploadPrivacyPolicy(assets, commonDetectInfo);
        } else if (privacyResults.isEmpty() && htmlPrivacyDetailList.isEmpty()) {
            commonDetectInfo.setHasPrivacyPolicy(false);
            commonDetectInfo.setPrivacyPolicyImg(null);
            commonDetectInfo.setPrivacyPolicyContent(null);
        } else {
            analysisDetectPrivacyPolicy(task, commonDetectInfo, privacyResults, htmlPrivacyDetailList);
        }
        // 没有单独的第三方SDK清单，尝试从隐私政策中解析
        analysisCheckListByPolicyContent(commonDetectInfo);
        commonDetectInfo.setTensorflowCheckBoxModelPath(checkboxModelPath);
        commonDetectInfo.setTensorflowCheckedModelPath(checkedModelPath);
        return commonDetectInfo;
    }

    private void analysisDetectPrivacyPolicy(TTask task,
                                             CommonDetectInfo commonDetectInfo,
                                             List<ResultDataLogBO> privacyResults,
                                             List<PrivacyPolicyTextInfo> htmlPrivacyDetailList) {
        commonDetectInfo.setHasPrivacyPolicy(true);
        // 隐私政策详情文件
        List<ResultDataLogBO> privacyDetailResults = new ArrayList<>();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && privacyResult.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()) {
                privacyDetailResults.add(privacyResult);
            }
        }
        // 没详情文件 取所有的
        if (privacyDetailResults.size() == 0) {
            privacyDetailResults.addAll(privacyResults);
        }
        // 隐私文本拼接 取最长文本图片
        Pair<String, String> privacyPolicy = getPrivacyPolicyByResultDataLogBO(privacyDetailResults);
        String privacyImagePath = privacyPolicy.getKey();
        String privacyPolicyContent = privacyPolicy.getValue();
        // IOS的数据需要去找html文件中是否有privacy.html文件，如果有的话privacy.html里的内容就是更详细的隐私文本
        Optional<PrivacyPolicyTextInfo> privacyPolicyContentByHtmlDirOpt = htmlPrivacyDetailList
                .stream().filter(info -> info.fileName.equals(PRIVACY_HTML_NAME)).findFirst();
        if (privacyPolicyContentByHtmlDirOpt.isPresent()) {
            String privacyPolicyContentByHtmlDir = privacyPolicyContentByHtmlDirOpt.get().content;
            // privacy.html的文本远大于界面解析出的隐私文本内容
            if (privacyPolicyContentByHtmlDir.length() - privacyPolicyContent.length() > privacyPolicyContent.length() / 2) {
                privacyPolicyContent = privacyPolicyContentByHtmlDir;
            } else {
                // Jaccard 计算文本相似度
                EditDistance<Double> distance = new JaccardDistance();
                double similarity = distance.apply(privacyPolicyContent, privacyPolicyContentByHtmlDir);
                // 相似度很高，那么以html页面的内容为准，因为ResultDataLogBO里解析出的文本可能是OCR或者界面元素提取的，内容可能会有缺失
                if (similarity < 0.2) {
                    privacyPolicyContent = privacyPolicyContentByHtmlDir;
                }
            }
        }
        // 剩下的就是子页面的html内容
        List<PrivacyPolicyTextInfo> subPageList = htmlPrivacyDetailList.stream()
                .filter(info -> !info.fileName.equals(PRIVACY_HTML_NAME)).collect(Collectors.toList());
        commonDetectInfo.setPrivacyPolicyContent(mergePrivacyPolicySubPage(privacyPolicyContent, subPageList));
        uploadPrivacyPolicyImage(task, commonDetectInfo, privacyImagePath);
        setPrivacyPolicyNlp(commonDetectInfo);
    }

    private Pair<String, String> getPrivacyPolicyByResultDataLogBO(List<ResultDataLogBO> privacyDetailResults) {
        String privacyImagePath = null;
        int maxLength = 0;
        StringBuilder privacyPolicyContentBuilder = new StringBuilder();
        Set<String> addLines = new HashSet<>();
        for (ResultDataLogBO privacyResult : privacyDetailResults) {
            if (privacyResult.getUiDumpResult() != null && StringUtils.isNotBlank(privacyResult.getUiDumpResult().getFullText())) {
                // 隐私详情截图的第一张，清空已经收集的文本。因为有可能xml里面没有隐私详情文本，所以要加上文本长度判断
                if (privacyResult.getImgPath().contains(PRIVACY_DETAIL_BEGIN_PREFIX)
                        && org.apache.commons.lang3.StringUtils.length(privacyResult.getUiDumpResult().getFullText()) > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                    privacyPolicyContentBuilder = new StringBuilder();
                    addLines.clear();
                }
                String[] uiList = privacyResult.getUiDumpResult().getFullText().split("\n");
                for (String string : uiList) {
                    if(org.apache.commons.lang3.StringUtils.isNoneBlank(string) && string.startsWith("。")){
                        string = string.replaceFirst("。", "");
                    }
                    if(!addLines.contains(string)) {
                        addLines.add(string);
                        privacyPolicyContentBuilder.append(string).append("\n");
                    }
                }
                // 如果是需要ocr的隐私详情截图，有可能有多张截图。只取第一张图片
                if (privacyResult.getImgPath().contains(PRIVACY_DETAIL_BEGIN_PREFIX)
                        && org.apache.commons.lang3.StringUtils.length(privacyResult.getUiDumpResult().getFullText()) > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                    maxLength = Integer.MAX_VALUE;
                    privacyImagePath = privacyResult.getImgPath();
                } else if (org.apache.commons.lang3.StringUtils.length(privacyResult.getUiDumpResult().getFullText()) > maxLength) {
                    maxLength = privacyResult.getUiDumpResult().getFullText().length();
                    privacyImagePath = privacyResult.getImgPath();
                }
            }
        }
        return new Pair<>(privacyImagePath, privacyPolicyContentBuilder.toString());
    }

    @Override
    protected void analysisAutoOldFile(TTask task, String dataPath) {

    }

    @Override
    protected void analysisAutoInternal(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {

    }

    @Override
    protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {

    }

    @Override
    protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception {

    }

    @Override
    protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath) {

    }

}
