package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_ANALYSIS_TASK_DATA_PREFIX;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;
import static cn.ijiami.detection.utils.CommonUtil.analysisErrorMsg;
import static cn.ijiami.detection.utils.CommonUtil.containsItemNo;
import static cn.ijiami.detection.utils.CommonUtil.getCellAsString;
import static cn.ijiami.detection.utils.CommonUtil.getFileExtName;
import static cn.ijiami.detection.utils.SdkUtils.removeSdkSuffix;
import static cn.ijiami.detection.utils.StreamUtils.distinctByKey;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipFile;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.VO.ActionFrequencyVO;
import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.UserSdkAliasVO;
import cn.ijiami.detection.VO.detection.ScreenshotImage;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.bean.AppletBaseResultContentDTO;
import cn.ijiami.detection.bean.DetectionItem;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAppLawsDectDetail;
import cn.ijiami.detection.entity.TAppLawsRiskCollect;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TCustomLawsRegulationsItem;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyLawsBasis;
import cn.ijiami.detection.entity.TPrivacyLawsConclusionAction;
import cn.ijiami.detection.entity.TPrivacyLawsDetail;
import cn.ijiami.detection.entity.TPrivacyLawsPrivacyPolicyActionKeyWords;
import cn.ijiami.detection.entity.TPrivacyLawsRegulations;
import cn.ijiami.detection.entity.TPrivacyLawsResult;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSdkCheckList;
import cn.ijiami.detection.entity.TSdkDectDetail;
import cn.ijiami.detection.entity.TSdkLawsDectDetail;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSuspiciousSdk;
import cn.ijiami.detection.entity.TSuspiciousSdkLibrary;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.entity.TTaskExtend;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionItemIdEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.LawDetailDataTypeEnum;
import cn.ijiami.detection.enums.LawResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyLawsBasisCategoryEnum;
import cn.ijiami.detection.enums.PrivacyPolicyItemNoEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultCategoryEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.StatisticsStatus;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.DownloadZipFailException;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.job.TaskSortThread;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TAppLawsDectDetailMapper;
import cn.ijiami.detection.mapper.TAppLawsRiskCollectMapper;
import cn.ijiami.detection.mapper.TApplyPermissionMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TCustomLawsRegulationsItemMapper;
import cn.ijiami.detection.mapper.TOdpCompanyMapper;
import cn.ijiami.detection.mapper.TOdpCompanyProductMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatExtendMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsBasisMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsConclusionActionMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsPrivacyPolicyKeyWordsMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsRegulationsMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsResultMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyItemMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyResultMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSdkCheckListMapper;
import cn.ijiami.detection.mapper.TSdkDectDetailMapper;
import cn.ijiami.detection.mapper.TSdkLawsDectDetailMapper;
import cn.ijiami.detection.mapper.TSdkLibraryClassMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.TUserSdkAliasMapper;
import cn.ijiami.detection.miit.DetectPointManager;
import cn.ijiami.detection.miit.callback.DefaultDetectCallback;
import cn.ijiami.detection.miit.callback.IDetectCallback;
import cn.ijiami.detection.miit.constants.DetectPointIdentifyItems;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.ActionNetwork;
import cn.ijiami.detection.miit.domain.ActionOutSide;
import cn.ijiami.detection.miit.domain.ActionSharedPerfs;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.nlp.ONlpServer;
import cn.ijiami.detection.nlp.result.ONlpResponse;
import cn.ijiami.detection.service.api.AIToolService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.IStorageLogService;
import cn.ijiami.detection.service.api.ITaskDataService;
import cn.ijiami.detection.service.api.ITaskReportService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.service.api.OcrService;
import cn.ijiami.detection.service.api.vo.AISdkExtractVO;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.PdfToTextFileUtil;
import cn.ijiami.detection.utils.SDKExtractor;
import cn.ijiami.detection.utils.SdkUtils;
import cn.ijiami.detection.utils.SeleniumUtils;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import tk.mybatis.mapper.entity.Example;

public abstract class AbstractDynamicDetectionService<T extends TaskDetailVO> extends BaseDetectionMongoDBDAOImpl<T> {

    private static Logger logger = LoggerFactory.getLogger(AbstractDynamicDetectionService.class);

    @Autowired
    TOdpCompanyProductMapper odpCompanyProductMapper;

    @Autowired
    TOdpCompanyMapper odpCompanyMapper;

    @Resource
    TPrivacyLawsBasisMapper privacyLawsBasisMapper;

    @Resource
    TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;

    @Autowired
    TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    TPrivacyLawsResultMapper privacyLawsResultMapper;

    @Autowired
    IBaseFileService fileService;

    @Autowired
    TApplyPermissionMapper      applyPermissionMapper;

    @Autowired
    TSuspiciousSdkLibraryMapper suspiciousSdkLibraryMapper;

    @Autowired
    TSuspiciousSdkMapper suspiciousSdkMapper;

    @Autowired
    TAppLawsDectDetailMapper appLawsDectDetailMapper;

    @Autowired
    TAppLawsRiskCollectMapper appLawsRiskCollectMapper;

    @Autowired
    TSdkLawsDectDetailMapper sdkLawsDectDetailMapper;

    @Autowired
    TSdkDectDetailMapper sdkDectDetailMapper;

    @Autowired
    TAssetsMapper assetsMapper;

    @Lazy
    @Autowired
    ITaskService taskService;

    @Autowired
    TActionNougatMapper actionNougatMapper;

    @Autowired
    TUserSdkAliasMapper userSdkAliasMapper;

    @Autowired
    TPrivacyLawsConclusionActionMapper privacyLawsConclusionActionMapper;

    @Autowired
    TPrivacyLawsPrivacyPolicyKeyWordsMapper tPrivacyLawsPrivacyPolicyKeyWordsMapper;
    
    @Autowired
    IjiamiCommonProperties commonProperties;
    
    @Autowired
    SingleFastDfsFileService singleFastDfsFileService;

    @Autowired
    IStorageLogService storageLogService;

    @Autowired
    TPrivacyLawsDetailMapper privacyLawsDetailMapper;

    @Autowired
    TPrivacyPolicyResultMapper privacyPolicyResultMapper;

    @Autowired
    TPrivacyPolicyItemMapper privacyPolicyItemMapper;

    @Autowired
    TTaskMapper taskMapper;

    @Autowired
    TTaskExtendMapper taskExtendMapper;

    @Autowired
    DetectionConfigService detectionConfigService;

    @Autowired
    IScreenshotImageService screenshotImageService;

    @Autowired
    ITaskDataService taskDataService;

    @Autowired
    TPrivacyActionNougatMapper privacyActionNougatMapper;

    @Autowired
    TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;

    @Autowired
    TPrivacySensitiveWordMapper privacySensitiveWordMapper;

    @Autowired
    TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

    @Autowired
    TPrivacySharedPrefsMapper privacySharedPrefsMapper;

    @Autowired
    DataSourceTransactionManager dataSourceTransactionManager;

    @Lazy
    @Autowired
    ITaskReportService taskReportService;

    @Autowired
    TaskSortThread taskSortThread;

    @Autowired
    ISendMessageService sendMessageService;

    @Autowired()
    @Qualifier("redisDistributedLock")
    DistributedLockService distributedLockService;

    @Value("${ijiami.privacy.html.regexp}")
    String privacyHtmlRegex;

    @Value("${fastDFS.intranet.ip}")
    String fastDFSIntranetIp;

    @Value("${ijiami.privacyPolicy.mimTextSize:7}")
    Integer privacyPolicyMinTextSize;

    @Value("${ijiami.privacyPolicy.mimTextLineSpacing:-4}")
    Integer privacyPolicyMinTextLineSpacing;

    @Value("${policy.identify.url:/temp}")
    String privacyPolicyIdentifyUrl;

    @Value("${ijiami.third-party-sdk-table.sdk-name.regex:(产品|SDK|第三方)[\\S\\s]*(名称|名字)}")
    String thirdPartySdkTableSdkNameRegex;

    @Value("${ijiami.third-party-sdk-table.purpose.regex:场景|功能|使用目的}")
    String thirdPartySdkTablePurposeRegex;

    @Value("${ijiami.third-party-sdk-table.permission.regex:个人信息|权限|信息[\\S\\s]*收集|收集[\\S\\s]*信息}")
    String thirdPartySdkTablePermissionRegex;

    @Value("${ijiami.third-party-sdk-table.company.regex:公司[\\S\\s]*(名称|名字|主体)}")
    String thirdPartySdkTableCompanyRegex;

    @Value("${ijiami.third-party-sdk-table.policy-link.regex:隐私[\\S\\s]*(地址|链接|政策)}")
    String thirdPartySdkTablePolicyLinkRegex;

    @Autowired
    OcrService ocrService;

    @Autowired
    TSdkCheckListMapper sdkCheckListMapper;

    @Autowired
    TaskDAO taskDAO;

    @Autowired
    IPrivacyDetectionService privacyDetectionService;

    @Autowired
    SeleniumUtils seleniumUtils;

    @Autowired
    TCustomLawsRegulationsItemMapper customLawsRegulationsItemMapper;

    @Autowired
    DetectPointManager detectPointManager;

    @Autowired
    TSdkLibraryClassMapper sdkLibraryClassMapper;

    @Autowired
    AIToolService aiToolService;

    private static final List<String> CONCLUSION_ITEM_LIST = Arrays.asList("10105", "10106", "10203", "10207");

    /**
     * 根据行为ID组装行为标题
     *
     * @return
     */
    //行为中一个参数的情况，例子：APP未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在${actionNames}，非服务所必需且无合理应用场景，超出实现产品或服务的业务功能所必需的最低频率。
    String actionId1[] = {"10101", "10102", "10103", "10104", "10105", "10106", "10201", "10203"};
    //权限行为情况
    String actionId3[] = {"20101", "20102", "20103", "20104", "20105", "20106", "20107", "20108"};
    //涉及到阶段两个参数的 例子：APP运行时，未向用户告知${parameter1}的目的，向用户索取当前服务场景未使用到的${parameter2}等权限，且用户拒绝授权后，应用退出或关闭相关功能，无法正常使用。
    String actionId2[] = {"10205", "10206", "10207", "10208"};
    //行为中涉及到固定频率情况-一个参数在结论，一个参数在整改建议
    String actionId4[] = {"10202", "10204"};
    //涉及到权限情况 例子：APP未见向用户告知且未经用户同意，存在将${IMEI/设备MAC地址/软件安装}列表等个人信息发送给${友盟/极光/个推}等第三方SDK的行为。两个参数
    String actionId5[] = {"10301", "10302"};

    String actionSdk[] = {"10104", "10105", "10106", "10203", "10207", "10208", "10204"};

    protected void setConclusionAction(TPrivacyLawsResult tPrivacyLawsDetail, List<TPrivacyLawsDetail> details, DetectResult detectResult,
                                       List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS){
        String itemNo = tPrivacyLawsDetail.getItemNo();
        if(StringUtils.isBlank(itemNo) || details== null || details.size()==0) {
            return;
        }

        Set<String> setSdkBO = new HashSet<>();
        if (CollectionUtils.isNotEmpty(suspiciousSdkBehaviorVOS)) {
            for (SuspiciousSdkBehaviorVO sup : suspiciousSdkBehaviorVOS) {
                if(sup==null) {
                    continue;
                }
                if(sup.getExecutorType()==1) {
                    continue;
                }
                setSdkBO.add(sup.getPackageName());
            }
        }

        //疑似SDK
        List<String> subSdkList = null;
        if(setSdkBO.size()>0) {
            subSdkList = new ArrayList<>(setSdkBO);
        }

        List<Long> actionIds = new ArrayList<>();
        //行为详情中获取对应的检测项ID的行为数据
        for (TPrivacyLawsDetail detail : details) {
            if(!itemNo.equals(detail.getItemNo())) {
                continue;
            }
            if(detail.getActionId()==null) {
                continue;
            }
            actionIds.add(detail.getActionId());
        }
        List<TPrivacyLawsConclusionAction> actionList = new ArrayList<>();
        StringBuilder sf = new StringBuilder();
        List<String> list1 = Arrays.asList(actionId1); // 10104 10105 10106  10203
        List<String> list2 = Arrays.asList(actionId2); // 10207  10208
        List<String> list3 = Arrays.asList(actionId3);
        List<String> list4 = Arrays.asList(actionId4); // 10204
        List<String> list5 = Arrays.asList(actionId5);
        List<String> sdkItemList = Arrays.asList(actionSdk);
        //一个参数的情况
        if (actionIds.size() > 0 && containsItemNo(list1, itemNo)) {
            if (containsItemNo(CONCLUSION_ITEM_LIST, itemNo)) {
                getConclusionInfo(actionList, details, actionIds, itemNo, detectResult.getTaskId());
                // 164号文动态建议文本填充的行为名称
                sf.append(getActionTypeWord(actionIds, itemNo));
            } else if (sdkItemList.contains(itemNo)) {
                sf.append(getSDKInfo(details, actionIds, itemNo));
            } else {
                sf.append(getActionTypeWord(actionIds, itemNo));
            }
        }

        //涉及到固定频率情况和两个参数情况
        if (containsItemNo(list2, itemNo)) {
            List<ActionAnalyse> analyseList = detectResult.getAnalysisResult();
            if (analyseList == null || analyseList.size() == 0) {
                return;
            }
            List<Long> frequencyActionIds = new ArrayList<>();
            Set<String> behaviorStageWord = new HashSet<>();
            for (ActionAnalyse actionAnalyse : analyseList) {
                frequencyActionIds.add(actionAnalyse.getActionId());
                behaviorStageWord.add(actionAnalyse.getBehaviorStage().getName());
            }
            if(frequencyActionIds.size()>0) {
                if (containsItemNo(CONCLUSION_ITEM_LIST, itemNo)) {
                    getConclusionInfo(actionList, details, actionIds, itemNo, detectResult.getTaskId());
                    // 164号文动态建议文本填充的行为名称
                    String work = getActionTypeWord(frequencyActionIds, itemNo);
                    if (StringUtils.isNoneBlank(work)) {
                        sf.append(StringUtils.join(behaviorStageWord.toArray(), "、"));
                        sf.append("|");
                        sf.append(work);
                    }
                } else if (sdkItemList.contains(itemNo)) {
                    String work = getSDKInfo(details, actionIds, itemNo);
                    sf.append(StringUtils.join(behaviorStageWord.toArray(), "、"));
                    sf.append("|");
                    sf.append(work);
                } else {
                    String work = getActionTypeWord(frequencyActionIds, itemNo);
                    if (StringUtils.isNoneBlank(work)) {
                        sf.append(StringUtils.join(behaviorStageWord.toArray(), "、"));
                        sf.append("|");
                        sf.append(work);
                    }
                }
            }
        }

        //权限行为情况
        if (containsItemNo(list3, itemNo) && detectResult.getRequestPermissionNames() != null) {
            List<String> pNameList = new ArrayList<>(detectResult.getRequestPermissionNames());
            sf.append(StringUtils.join(pNameList, "、"));
        }

        //例子： 每2s读取一次位置信息
        if (containsItemNo(list4, itemNo)) {
            List<ActionAnalyse> analyseList = detectResult.getAnalysisResult();
            if (analyseList == null || analyseList.size() == 0) {
                return;
            }

            Set<String> analyseSfSet = new HashSet<>();
            for (ActionAnalyse actionAnalyse : analyseList) {
                ActionFrequencyVO vo = actionAnalyse.getActionFrequency();
                if (vo == null) {
                    continue;
                }
                StringBuilder analyseSf = new StringBuilder();
                List<Long> frequencyActionIds = new ArrayList<>();
                frequencyActionIds.add(actionAnalyse.getActionId());
                String work;
                if (containsItemNo(CONCLUSION_ITEM_LIST, itemNo)) {
                    getConclusionInfo(actionList, details, actionIds, itemNo, detectResult.getTaskId());
                    // 164号文动态建议文本填充的行为名称
                    work = getActionTypeWord(frequencyActionIds, itemNo);
                } else if (sdkItemList.contains(itemNo)) {
                    work = getSDKInfo(details, actionIds, itemNo);
                } else {
                    work = getActionTypeWord(frequencyActionIds, itemNo);
                }

                if(StringUtils.isBlank(work)) {
                    continue;
                }
                analyseSf.append("每");
                analyseSf.append(vo.getInterval());
                analyseSf.append("秒读取一次");
                analyseSf.append(work);
                analyseSfSet.add(analyseSf.toString());
            }
            if(!analyseSfSet.isEmpty()) {
                sf.append(StringUtils.join(analyseSfSet, "、"));
                sf.append("|");
                if (sdkItemList.contains(itemNo)) {
                    sf.append(getSDKInfo(details, actionIds, itemNo));
                } else {
                    sf.append(getActionTypeWord(actionIds, itemNo));
                }
            }
        }

        if (actionIds.size() > 0 && containsItemNo(list5, itemNo)) {
            String typeWord = getActionTypeWord(actionIds, itemNo);
            List<ActionAnalyse> analyseList = detectResult.getAnalysisResult();
            if (analyseList == null || analyseList.size() == 0) {
                return;
            }
            Set<String> analyseSDK = new HashSet<>();
            for (ActionAnalyse analyse : analyseList) {
                if (StringUtils.isBlank(analyse.getExecutor())) {
                    continue;
                }

                if(analyse.getExecutor().trim().contains(",")) {
                    List<String> executorList = Arrays.asList(analyse.getExecutor().trim().split(","));
                    analyseSDK.addAll(filterSuspectedSdk(executorList, subSdkList));
                    continue;

                }

                analyseSDK.add(analyse.getExecutor().trim());
            }

            if(analyseSDK.size()>0) {
                sf.append(typeWord);
                sf.append("|");
                sf.append(StringUtils.join(analyseSDK, "、"));
            }
        }

        if(StringUtils.isNoneBlank(sf.toString())){
            tPrivacyLawsDetail.setSceneTitle(sf.toString());
        }
        if (!actionList.isEmpty()) {
            privacyLawsConclusionActionMapper.insertList(actionList);
        }
    }

    private String getSDKInfo(List<TPrivacyLawsDetail> details, List<Long> actionIds,String itemNo){

        if(details== null || details.size()==0) {
            return null;
        }
        List<TPrivacyLawsPrivacyPolicyActionKeyWords> actionList = getActionKeyWords(actionIds, itemNo);
        if(actionList.size() == 0) {
            return null;
        }

        Set<String> actionTypeWord = new HashSet<>();
        for (TPrivacyLawsDetail detail : details) {
            if(!itemNo.equals(detail.getItemNo())) {
                continue;
            }
            if(detail.getActionId()==null) {
                continue;
            }

            for (TPrivacyLawsPrivacyPolicyActionKeyWords a : actionList) {
                if(!detail.getActionId().equals(a.getActionId())) {
                    continue;
                }
                String word = detail.getExecutor()+"获取"+a.getActionTypeName();
                if(actionTypeWord.contains(word)) {
                    continue;
                }
                actionTypeWord.add(word);
                break;
            }
        }


        if(actionTypeWord.size() == 0) {
            return null;
        }
        return StringUtils.join(actionTypeWord.toArray(), "、");

    }

    private void getConclusionInfo(List<TPrivacyLawsConclusionAction> conclusionActionList,
                                                                 List<TPrivacyLawsDetail> details,
                                                                 List<Long> actionIds, String itemNo, Long taskId){
        details.stream()
                .filter(d -> itemNo.equals(d.getItemNo()))
                .filter(d -> d.getActionId() != null)
                .collect(Collectors.groupingBy(TPrivacyLawsDetail::getBehaviorStage))
                // 根据行为阶段分组
                .forEach((stage, stageList) -> {
                    // 根据行为id分组
                    stageList.stream().collect(Collectors.groupingBy(TPrivacyLawsDetail::getActionId))
                            .forEach((actionId, list) -> {
                                if (!list.isEmpty()) {
                                    String actionName = list.get(0).getActionAlias();
                                    addToConclusionAction(conclusionActionList, list, actionName, stage, itemNo, taskId);
                                }
                            });
                });
    }

    private void addToConclusionAction(List<TPrivacyLawsConclusionAction> conclusionActionList,
                                       List<TPrivacyLawsDetail> detailList, String actionName,
                                       Integer stage, String itemNo, Long taskId) {
        Optional<TPrivacyLawsConclusionAction> actionOptional = conclusionActionList
                .stream()
                .filter(c -> c.getAction().equals(actionName) && c.getItemNo().equals(itemNo) && c.getTaskId().equals(taskId))
                .findFirst();
        // 如果行为名称已经存在，sdk名称追加进去
        if (actionOptional.isPresent()) {
            TPrivacyLawsConclusionAction conclusionAction = actionOptional.get();
            String sdkName = detailList
                    .stream()
                    .flatMap(d -> Arrays.stream(d.getExecutor().split(",")))
                    .distinct()
                    .filter(executor -> !StringUtils.contains(conclusionAction.getSdkName(), executor))
                    .collect(Collectors.joining(","));
            conclusionAction.setSdkName(String.join(",", conclusionAction.getSdkName(), sdkName));
        } else {
            TPrivacyLawsConclusionAction conclusionAction = new TPrivacyLawsConclusionAction();
            String sdkName = detailList.stream()
                    .flatMap(d -> Arrays.stream(d.getExecutor().split(",")))
                    .distinct()
                    .collect(Collectors.joining(","));
            conclusionAction.setBehaviorStage(BehaviorStageEnum.getItem(stage));
            conclusionAction.setSdkName(sdkName);
            conclusionAction.setAction(actionName);
            conclusionAction.setItemNo(itemNo);
            conclusionAction.setTaskId(taskId);
            conclusionActionList.add(conclusionAction);
        }
    }

    private TPrivacyLawsRegulations getTPrivacyLawsRegulationsByItems(String itemNo){
        TPrivacyLawsRegulations regulation = new TPrivacyLawsRegulations();
        regulation.setItemNo(itemNo);
        return privacyLawsRegulationsMapper.selectOne(regulation);
    }

    private List<String> filterSuspectedSdk(List<String> executorList,List<String> subSdkList){
        if(subSdkList ==null || subSdkList.size()==0 || executorList.size()==0) {
            return executorList;
        }
        List<String> newList = new ArrayList<>();
        for (String string : executorList) {
            if(subSdkList.contains(string)){
                continue;
            }
            newList.add(string);
        }
        return newList;
    }

    private String getActionTypeWord(List<Long> actionIds, String itemNo){
        Set<String> actionTypeWord = getActionTypeNames(actionIds, itemNo);
        if(actionTypeWord.size() == 0) {
            return null;
        }
        return StringUtils.join(actionTypeWord.toArray(), "、");
    }

    private Set<String> getActionTypeNames(List<Long> actionIds, String itemNo) {
        return getActionKeyWords(actionIds, itemNo)
                .stream().map(TPrivacyLawsPrivacyPolicyActionKeyWords::getActionTypeName).collect(Collectors.toSet());
    }

    private List<TPrivacyLawsPrivacyPolicyActionKeyWords> getActionKeyWords(List<Long> actionIds, String itemNo) {
        List<TPrivacyLawsPrivacyPolicyActionKeyWords> wordsList = new ArrayList<>();
        List<TPrivacyLawsBasis> basisList = privacyLawsBasisMapper.findByItemNo(itemNo);
        for (TPrivacyLawsBasis basis:basisList) {
            if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.PERSONAL_ACTIONS) {
                // 查询涉及个人信息的行为类型名
                wordsList.addAll(tPrivacyLawsPrivacyPolicyKeyWordsMapper.findPersonalActionKeyWords()
                        .stream()
                        .filter(words -> actionIds.contains(words.getActionId()))
                        .collect(Collectors.toList()));
            } else if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ALL_ACTIONS) {
                // 查询所有的行为类型名
                wordsList.addAll(tPrivacyLawsPrivacyPolicyKeyWordsMapper.findAllActionKeyWords()
                        .stream()
                        .filter(words -> actionIds.contains(words.getActionId()))
                        .collect(Collectors.toList()));
            } else if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ACTION_ID && actionIds.contains(basis.getActionId())){
                TPrivacyLawsPrivacyPolicyActionKeyWords words = tPrivacyLawsPrivacyPolicyKeyWordsMapper.findKeyWordsByActionId(basis.getActionId());
                if (words != null && StringUtils.isNotBlank(words.getActionTypeName())) {
                    wordsList.add(words);
                }
            }
        }
        return wordsList;
    }

    protected static Function<TPrivacyActionNougat, Long> groupFunction() {
        return b -> b.getActionTime().getTime() / 1000;
    }

    protected boolean getTPrivacyLawsResult(String itemNo, Long taskId){
        TPrivacyLawsResult regulation = new TPrivacyLawsResult();
        regulation.setItemNo(itemNo);
        regulation.setTaskId(taskId);
        return privacyLawsResultMapper.selectCount(regulation) > 0;
    }

    /**
     * @param detectResult
     * @return
     */
    protected TPrivacyLawsResult buildTPrivacyLawsResult(DetectResult detectResult) {
        TPrivacyLawsResult result = new TPrivacyLawsResult();
        result.setTaskId(detectResult.getTaskId());
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        result.setItemNo(detectResult.getItemNo());
        result.setResultStatus(LawResultStatusEnum.getItem(detectResult.getComplianceStatus().getValue()));
        return result;
    }

    /**
     * 构建行为详情信息
     *
     * @param actionAnalyse
     * @param detectResult
     * @param keyWords
     * @return
     */
    protected TPrivacyLawsDetail buildLawsDetailAction(ActionAnalyse actionAnalyse, DetectResult detectResult, String keyWords) {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setActionAlias(actionAnalyse.getActionName());
        detail.setActionId(actionAnalyse.getActionId());
        detail.setTriggerNum(actionAnalyse.getFrequency());
        detail.setTriggerTime(new Date(actionAnalyse.getActionTimeMillis() * 1000));
        detail.setBehaviorStage(actionAnalyse.getBehaviorStage().getValue());
        detail.setOldPolicySnippet(keyWords);
        detail.setDataType(LawDetailDataTypeEnum.ACTION.getValue());
        detail.setExecutorType(actionAnalyse.getExecutorType());
        detail.setExecutor(actionAnalyse.getExecutor());
        detail.setPackageName(actionAnalyse.getPackageName());
        detail.setSdkIds(actionAnalyse.getSdkIds());
        detail.setDataId(actionAnalyse.getDataId());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());

        ActionFrequencyVO frequency = actionAnalyse.getActionFrequency();
        if (frequency != null) {
            detail.setIntervalTime(frequency.getInterval());
        }
        return detail;
    }

    /**
     * 构建行为详情信息
     *
     * @param detectResult
     * @param keyWords
     * @return
     */
    protected TPrivacyLawsDetail buildLawsSensitiveWord(ActionNetwork actionNetwork, DetectResult detectResult, String keyWords) {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setIp(actionNetwork.getIp());
        detail.setHost(actionNetwork.getHost());
        detail.setPort(actionNetwork.getPort());
        detail.setCookie(actionNetwork.getCookie());
        detail.setCode(actionNetwork.getCode());
        detail.setAttributively(actionNetwork.getAttributively());
        detail.setActionId(actionNetwork.getActionId());
        detail.setBehaviorStage(actionNetwork.getBehaviorStage().getValue());
        detail.setOldPolicySnippet(keyWords);
        detail.setDataType(LawDetailDataTypeEnum.TRANSMISSION.getValue());
        detail.setExecutorType(actionNetwork.getExecutorType());
        detail.setExecutor(actionNetwork.getExecutor());
        detail.setPackageName(actionNetwork.getPackageName());
        detail.setSdkIds(actionNetwork.getSdkIds());
        detail.setTriggerTime(actionNetwork.getActionTime());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    /**
     * 构建行为详情信息
     *
     * @param detectResult
     * @param keyWords
     * @return
     */
    protected TPrivacyLawsDetail buildLawsSensitivePrefs(ActionSharedPerfs actionSharedPerfs, DetectResult detectResult, String keyWords) {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setActionId(actionSharedPerfs.getActionId());
        detail.setBehaviorStage(actionSharedPerfs.getBehaviorStage().getValue());
        detail.setOldPolicySnippet(keyWords);
        detail.setDataType(LawDetailDataTypeEnum.SHARED_PREFS.getValue());
        detail.setCode(actionSharedPerfs.getCode());
        detail.setExecutorType(actionSharedPerfs.getExecutorType());
        detail.setExecutor(actionSharedPerfs.getExecutor());
        detail.setPackageName(actionSharedPerfs.getPackageName());
        detail.setSdkIds(actionSharedPerfs.getSdkIds());
        detail.setTriggerTime(actionSharedPerfs.getActionTime());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    /**
     * 构建行为详情信息
     *
     * @param detectResult
     * @param keyWords
     * @return
     */
    protected TPrivacyLawsDetail buildLawsOutsideAddress(ActionOutSide actionOutSide, DetectResult detectResult, String keyWords) {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setIp(actionOutSide.getIp());
        detail.setHost(actionOutSide.getHost());
        detail.setPort(actionOutSide.getPort());
        detail.setCookie(actionOutSide.getCookie());
        detail.setOutside(actionOutSide.getOutside());
        detail.setActionId(actionOutSide.getActionId());
        detail.setBehaviorStage(actionOutSide.getBehaviorStage()==null?BehaviorStageEnum.BEHAVIOR_FRONT.getValue():actionOutSide.getBehaviorStage().getValue());
        detail.setOldPolicySnippet(keyWords);
        detail.setDataType(LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue());
        detail.setExecutorType(actionOutSide.getExecutorType());
        detail.setExecutor(actionOutSide.getExecutor());
        detail.setPackageName(actionOutSide.getPackageName());
        detail.setSdkIds(actionOutSide.getSdkIds());
        detail.setTriggerTime(actionOutSide.getActionTime());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    /**
     * 构建文本信息
     *
     * @param detectResult
     * @param keyWords
     * @return
     */
    protected TPrivacyLawsDetail buildLawsDetailText(DetectResult detectResult, String keyWords) {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setOldPolicySnippet(keyWords);
        detail.setPrivacyPolicySnippet(detectResult.getPrivacyPolicyFragment());
        detail.setDataType(LawDetailDataTypeEnum.TXT_IMAG.getValue());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    /**
     * 构建图片信息
     *
     * @param detectResult
     * @param keyWords
     * @param filePath
     * @return
     * @throws Exception
     */
    protected TPrivacyLawsDetail buildLawsDetailImage(DetectResult detectResult, String keyWords, String filePath) throws Exception {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setOldPolicySnippet(keyWords);
        File file = new File(filePath);
        String imageUploadMethod = commonProperties.getProperty("image.upload.method");//判断是否需要上传到文件服务器
		if(StringUtils.isNoneBlank(imageUploadMethod) && imageUploadMethod.equals("fdfs")) {
	        if(!filePath.toLowerCase().contains(".png") && !filePath.toLowerCase().contains(".jpg")) {
                File newFile = new File(filePath+".png");
	        	file.renameTo(newFile);
	        	filePath = newFile.getPath();
	        }
	        FileVO fileVO = uploadFileImage(detectResult.getTaskId(), filePath);
	        if (fileVO == null) {
                detail.setFileKey(uploadFileToDisk(file));
	        } else {
	        	if (StringUtils.isNoneBlank(fileVO.getFileUrl())) {
	        		detail.setFileKey(fileVO.getFileUrl());
	        	} else {
	        		logger.error("文件上传失败 fileUrl为空 filePath={}", filePath);
                    detail.setFileKey(uploadFileToDisk(file));
	        	}
	        }
		} else {
	        detail.setFileKey(uploadFileToDisk(file));
		}
        detail.setDataType(LawDetailDataTypeEnum.TXT_IMAG.getValue());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    private String uploadFileToDisk(File file) throws Exception {
        FileVO fileVO = fileService.uploadFile(new MockMultipartFile(file.getName(), new FileInputStream(file)));
        if (StringUtils.isBlank(fileVO.getFileKey())) {
            logger.error("文本保存到磁盘失败 fileKey为空 filePath={}", file.getPath());
            return StringUtils.EMPTY;
        }
        return fileVO.getFileKey();
    }

    protected FileVO uploadFileImage(Long taskId, String filePath) {
        FileVO fileVO = new FileVO();
        try {
            File file = new File(filePath);
            // 文件扩展名
            FileInputStream inputStream = new FileInputStream(file);

            fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
            fileVO.setInputStream(inputStream);
            fileVO.setFileSize(file.length());
            fileVO.setFileName(file.getName());
            fileVO.setFilePath(filePath);
            fileVO = singleFastDfsFileService.instance().upload(fileVO);
            if (StringUtils.isBlank(fileVO.getFileUrl())) {
                file.delete();
            } else {
                storageLogService.saveStorageLogByTaskFile(taskId, fileVO.getFileUrl());
            }
        } catch (Exception e) {
            e.getMessage();
        }
        return fileVO;
    }
    
    /**
     * 构建隐私政策截图地址
     *
     * @param detectResult
     * @param keyWords
     * @param fileKey
     * @return
     * @throws Exception
     */
    protected TPrivacyLawsDetail buildLawsPrivacyImage(DetectResult detectResult, String keyWords, String fileKey) throws Exception {
        TPrivacyLawsDetail detail = new TPrivacyLawsDetail();
        detail.setTaskId(detectResult.getTaskId());
        detail.setItemNo(detectResult.getItemNo());
        detail.setOldPolicySnippet(keyWords);

        logger.info("文件上传 fileKey={}", fileKey);
        String imageUploadMethod = commonProperties.getProperty("image.upload.method");//判断是否需要上传到文件服务器
		if(StringUtils.isNoneBlank(imageUploadMethod) && imageUploadMethod.equals("fdfs")) {
			try {
				cn.ijiami.base.common.file.entity.File img = fileService.findFileByFileKey(detectResult.getPrivacyScreenshot());
				if(img!= null) {
					String filePath = commonProperties.getFilePath() + img.getFilePath();
					File file = new File(filePath);
		            File newFile = new File(filePath+".png");
		            if(!filePath.toLowerCase().contains(".png") && !filePath.toLowerCase().contains(".jpg")) {
		            	file.renameTo(newFile);
		            	filePath = filePath+".png";
		            }
					FileVO fileVO = uploadFileImage(detectResult.getTaskId(), filePath);
					if (fileVO != null && StringUtils.isNoneBlank(fileVO.getFileUrl())) {
						fileKey = fileVO.getFileUrl();
					} else {
                        logger.error("文件上传失败 fileUrl为空 filePath={}", filePath);
                    }
				} else {
                    logger.error("文件上传失败 找不到图片 privacyScreenshot={}", detectResult.getPrivacyScreenshot());
                }
			} catch (Exception e) {
				logger.error("文件上传失败 {}", e.getMessage(), e);
			}
		}
        detail.setFileKey(fileKey);
        detail.setDataType(LawDetailDataTypeEnum.TXT_IMAG.getValue());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        return detail;
    }

    /**
     * 拼接隐私子页面
     *
     * @param subPagePrivacyDetailList
     * @return
     */
    protected String mergePrivacyPolicySubPage(String privacyContentBuilder, List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        /**
         * subPrivacyPageSet保存添加过的页数据，用来排除重复，比result.toString().contains这种形式要准确，减少误去重
         * 例如：前面内容已有 “《用户服务协议》（简称“本协议”）是用户（简称“您”）与公司就下载、安装、注册、登录、使用（以下统称“使用”）本产品，并获得本产品提供的相关服务所订立的协议”
         * 后面又来了单独一行 “《用户服务协议》”就会被排除掉
         */
        String firstPage = privacyContentBuilder == null ? "" : privacyContentBuilder;
        Set<String> firstPageLines = Arrays.stream(firstPage.split("\n")).filter(StringUtils::isNoneBlank).collect(Collectors.toSet());
        Set<String> subPrivacyPageSet = new HashSet<>();
        StringBuilder totalSubPageBuilder = new StringBuilder();
        // 子页面的隐私详情加在最后面
        for (PrivacyPolicyTextInfo privacyDetail : subPagePrivacyDetailList) {
            StringJoiner pageBuilder = new StringJoiner("\n");
            String[] lineList = privacyDetail.content.split("\n");
            if (lineList.length == 0) {
                continue;
            }
            // 第一行是url
            String pageUrl = lineList[0];
            for (int i = 1; i < lineList.length; i++) {
                String line = lineList[i];
                // 空行不添加
                if (StringUtils.isNotBlank(line)) {
                    pageBuilder.add(line);
                }
            }
            String subPrivacyPage = pageBuilder.toString();
            // 如果子页面跟第一页的隐私内容很相似，不用添加到子页面里
            if (containsLines(firstPageLines, getSubPageLines(subPrivacyPage))) {
                // 如果这个子页面的文本量比第一页的隐私内容多20%，那么判断子页面的内容比较丰富，替换为第一页
                if (subPrivacyPage.length() - firstPage.length() > (firstPage.length() * 0.2)) {
                    firstPage = subPrivacyPage;
                }
                continue;
            }
            // 把子页面添加进去
            if (StringUtils.isNotBlank(subPrivacyPage) && subPrivacyPageSet.contains(subPrivacyPage)) {
                continue;
            }
            subPrivacyPageSet.add(subPrivacyPage);
            addPageDivider(totalSubPageBuilder);
            totalSubPageBuilder.append(pageUrl).append("\n").append(pageBuilder);
        }
        if (totalSubPageBuilder.length() > 0) {
            StringBuilder totalPrivacyPolicyBuilder = new StringBuilder(firstPage);
            addPageDivider(totalPrivacyPolicyBuilder);
            totalPrivacyPolicyBuilder.append(totalSubPageBuilder);
            return totalPrivacyPolicyBuilder.toString();
        } else {
            return firstPage;
        }
    }

    private Collection<String> getSubPageLines(String subPrivacyPage) {
        return Arrays.stream(subPrivacyPage.split("\n"))
                .filter(StringUtils::isNoneBlank)
                .collect(Collectors.toList());
    }

    protected boolean containsLines(Set<String> lines1, Collection<String> lines2) {
        long equalsCount = lines2.stream().filter(lines1::contains).count();
        return equalsCount > (lines2.size() * 0.8);
    }

    protected List<PrivacyPolicyTextInfo> getSubPagePrivacyDetailByHtmlDir(String dir) {
        return PrivacyPolicyHtmlHelper.getSubPagePrivacyDetailByHtmlDir(dir, privacyHtmlRegex, thirdPartySdkTableSdkNameRegex, thirdPartySdkTablePermissionRegex);
    }

    protected List<PrivacyPolicyTextInfo> getPdfTextByPdfDir(String dir) {
        File privacyPdfDir = new File(dir);
        if (!privacyPdfDir.exists() || !privacyPdfDir.isDirectory()) {
            return Collections.emptyList();
        }
        File[] privacyPdfFiles = privacyPdfDir.listFiles();
        if (Objects.isNull(privacyPdfFiles)) {
            return Collections.emptyList();
        }
        List<PrivacyPolicyTextInfo> pdfTextList = new ArrayList<>();
        for (File pdf : privacyPdfFiles) {
            try {
                String privacyContent = PdfToTextFileUtil.getPdfFileText(pdf.getAbsolutePath());
                if (StringUtils.isNotBlank(privacyContent)) {
                    pdfTextList.add(new PrivacyPolicyTextInfo(pdf.getName(), privacyContent));
                }
            } catch (Exception e) {
                logger.info("pdf解析失败 path={}", pdf.getAbsolutePath());
            }
        }
        return pdfTextList;
    }

    /**
     * 多个隐私页面直接的分割线
     *
     * @param builder
     */
    protected void addPageDivider(StringBuilder builder) {
        if (builder.length() > 0) {
            builder.append("\n-----------------------------------------------------------------------------\n");
        }
    }

    protected void updateSuspiciousSdkLibraryCodesAndInsertRecord(Long taskId, List<SuspiciousSdkBO> suspiciousSdkBOList, Map<Long, TActionNougat> actionNougats,
                                                                  Map<String, TPermission> permissionMap, TerminalTypeEnum terminalTypeEnum) {
        List<String> suspiciousPackageNameList = suspiciousSdkBOList.stream()
                .map(sdkBO -> sdkBO.getSuspiciousSdk().getPackageName())
                .distinct()
                .collect(Collectors.toList());
        if (suspiciousPackageNameList.isEmpty()) {
            return;
        }
        Map<String, TSuspiciousSdkLibrary> libraryMap = suspiciousSdkLibraryMapper.findInPackageName(suspiciousPackageNameList)
                .stream().collect(Collectors.toMap(TSuspiciousSdkLibrary::getPackageName, Function.identity(), (key1, key2) -> key2));
        // 更新疑似SDK数据库
        suspiciousSdkBOList.stream()
                .collect(Collectors.groupingBy(sdkBo -> sdkBo.getSuspiciousSdk().getPackageName()))
                .forEach((packageName, sdkBoList) -> {
                    TSuspiciousSdkLibrary library = libraryMap.get(packageName);
                    if (library == null) {
                        // 疑似SDK库里面没有，创建一条记录
                        library = new TSuspiciousSdkLibrary();
                        library.setName(packageName);
                        library.setPackageName(packageName);
                        library.setTerminalType(terminalTypeEnum.getValue());
                        library.setPermissionCodes(getSuspiciousSdkPermissionCode("", permissionMap,
                                actionNougats, sdkBoList));
                        suspiciousSdkLibraryMapper.insert(library);
                        libraryMap.put(packageName, library);
                    } else {
                        String oldPermissionCode = Objects.isNull(library.getPermissionCodes()) ? "" : library.getPermissionCodes();
                        String updateCodes = getSuspiciousSdkPermissionCode(oldPermissionCode, permissionMap,
                                actionNougats, sdkBoList);
                        library.setTerminalType(terminalTypeEnum.getValue());
                        // 如果SDK库里有，判断是否有新增权限
                        if (StringUtils.isNotBlank(updateCodes)
                                && !updateCodes.equals(library.getPermissionCodes())) {
                            library.setPermissionCodes(updateCodes);
                            library.setUpdateTime(new Date());
                            suspiciousSdkLibraryMapper.updateByPrimaryKey(library);
                        }
                    }
                });
        // 写入疑似sdk记录
        insertPrivacySuspiciousSdks(taskId, suspiciousSdkBOList, libraryMap);
    }

    protected String getSuspiciousSdkPermissionCode(String oldPermissionCode, Map<String, TPermission> permissionMap,
                                                    Map<Long, TActionNougat> actionNougats, List<SuspiciousSdkBO> suspiciousSdkBOList) {
        List<String> permissionCodes = new ArrayList<>();
        suspiciousSdkBOList
                .stream()
                .filter(sdkBo -> sdkBo.getPrivacyActionNougat() instanceof TPrivacyActionNougat)
                .map(sdkBo -> (TPrivacyActionNougat) sdkBo.getPrivacyActionNougat())
                .filter(distinctByKey(TPrivacyActionNougat::getActionId)) // 过滤掉重复的行为id，相同的id意味着有相同的行为权限
                .forEach(nougat -> {
                    TActionNougat actionNougat = actionNougats.get(nougat.getActionId());
                    if (Objects.isNull(actionNougat)) {
                        return;
                    }
                    TPermission permission = permissionMap.get(actionNougat.getActionPermission());
                    if (Objects.isNull(permission)) {
                        return;
                    }
                    if (!permissionCodes.contains(permission.getPermissionCode())
                            && !oldPermissionCode.contains(permission.getPermissionCode())) {
                        permissionCodes.add(permission.getPermissionCode());
                    }
                });
        if (permissionCodes.isEmpty()) {
            return oldPermissionCode;
        } else {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(oldPermissionCode)) {
                permissionCodes.add(0, oldPermissionCode);
            }
            StringJoiner joiner = new StringJoiner(",");
            permissionCodes.forEach(joiner::add);
            return joiner.toString();
        }
    }

    /**
     * 写入疑似sdk记录
     *
     * @param taskId
     * @param suspiciousSdkBOs
     * @param libraryMap
     */
    protected void insertPrivacySuspiciousSdks(Long taskId, List<SuspiciousSdkBO> suspiciousSdkBOs, Map<String, TSuspiciousSdkLibrary> libraryMap) {
        List<TSuspiciousSdk> suspiciousSdks = new ArrayList<>(suspiciousSdkBOs.size());
        suspiciousSdkBOs.stream()
                .filter(distinctByKey(sdkBO -> sdkBO.getSuspiciousSdk().getPackageName()))
                .forEach(sdkBO -> {
            TSuspiciousSdkLibrary library = libraryMap.get(sdkBO.getSuspiciousSdk().getPackageName());
            TSuspiciousSdk sdk = new TSuspiciousSdk();
            sdk.setTaskId(sdkBO.getSuspiciousSdk().getTaskId());
            sdk.setExecutor(sdkBO.getSuspiciousSdk().getExecutor());
            sdk.setExecutorType(sdkBO.getSuspiciousSdk().getExecutorType());
            if (library != null) {
                sdk.setSuspiciousSdkLibraryId(library.getId());
            } else {
                logger.error("写入疑似SDK记录时找不到疑似SDK的id taskId={} packageName={}", sdk.getTaskId(),
                        sdkBO.getSuspiciousSdk().getPackageName());
            }
            suspiciousSdks.add(sdk);
        });
        if (CollectionUtils.isNotEmpty(suspiciousSdks)) {
            // 清空旧数据
            suspiciousSdkMapper.deleteByTaskId(taskId);
            InsertListHelper.insertList(suspiciousSdks, suspiciousSdkMapper::insertList);
        }
    }

    protected TAppLawsDectDetail buildAppLawsDectDetail(TTask task, Integer lawId, String itemNo) {
        TPrivacyLawsRegulations regulationQuery = new TPrivacyLawsRegulations();
        regulationQuery.setItemNo(itemNo);
        TPrivacyLawsRegulations regulations = privacyLawsRegulationsMapper.selectOne(regulationQuery);
        TAppLawsDectDetail detail = new TAppLawsDectDetail();
        detail.setAssetsId(task.getAssetsId());
        detail.setLawsItemId(regulations.getId());
        detail.setLawId(lawId);
        detail.setLawsItemParentId(regulations.getParentId());
        detail.setTaskId(task.getTaskId());
        detail.setLawsItemName(regulations.getName());
        detail.setTerminalType(task.getTerminalType().getValue());
        detail.setUserId(task.getCreateUserId());
        detail.setStatus(StatisticsStatus.LAST.value);
        detail.setCreateTime(new Date());
        return detail;
    }

    protected List<SdkVO> getSdkList(TTask task) {
        try {
            return taskService.getSdkList(task.getApkDetectionDetailId(), task.getTaskId());
        } catch (IjiamiApplicationException e) {
            e.getMessage();
            return Collections.emptyList();
        }
    }

    protected void saveAppDectDetail(TTask task, PrivacyLawId lawId, List<TAppLawsDectDetail> appLawsDectDetailList, List<TSdkLawsDectDetail> sdkLawsDectDetailList) {
        TAppLawsRiskCollect collect = new TAppLawsRiskCollect();
        collect.setAssetsId(task.getAssetsId());
        collect.setTaskId(task.getTaskId());
        collect.setTerminalType(task.getTerminalType().getValue());
        collect.setDetectionTime(task.getTaskStarttime());
        collect.setLawId(lawId.id);
        collect.setRiskCount(appLawsDectDetailList.size());
        collect.setStatus(StatisticsStatus.LAST.value);
        collect.setCreateTime(new Date());
        collect.setUserId(task.getCreateUserId());
        appLawsRiskCollectMapper.insert(collect);
        InsertListHelper.insertList(appLawsDectDetailList, appLawsDectDetailMapper::insertList);
        InsertListHelper.insertList(sdkLawsDectDetailList, sdkLawsDectDetailMapper::insertList);
    }

    protected void saveSdkDectDetail(TTask task, List<SdkVO> sdkVOList) {
        InsertListHelper.insertList(buildSdkDectDetailList(task, sdkVOList), sdkDectDetailMapper::insertList);
    }

    private List<TSdkDectDetail> buildSdkDectDetailList(TTask task, List<SdkVO> sdkVOList) {
        return sdkVOList.stream().filter(distinctByKey(SdkVO::getName)).map(sdkVO -> {
            TSdkDectDetail sdkDectDetail = new TSdkDectDetail();
            if (PinfoConstant.SUSPICIOUS_TYPE_NAME.equals(sdkVO.getTypeName())) {
                // 疑似sdk
                sdkDectDetail.setSdkId(PinfoConstant.SUSPICIOUS_SDK_ID);
                sdkDectDetail.setSdkName(sdkVO.getName());
                sdkDectDetail.setSdkPackage(DETAILS_EMPTY);
                sdkDectDetail.setSdkManufacturer(DETAILS_EMPTY);
            } else {
                sdkDectDetail.setSdkId(sdkVO.getId());
                sdkDectDetail.setSdkName(sdkVO.getName());
                sdkDectDetail.setSdkPackage(sdkVO.getPackageName());
                sdkDectDetail.setSdkManufacturer(sdkVO.getManufacturer());
            }
            sdkDectDetail.setStatus(StatisticsStatus.LAST.value);
            sdkDectDetail.setUserId(task.getCreateUserId());
            sdkDectDetail.setTaskId(task.getTaskId());
            sdkDectDetail.setAssetsId(task.getAssetsId());
            sdkDectDetail.setTerminalType(task.getTerminalType().getValue());
            return sdkDectDetail;
        }).collect(Collectors.toList());
    }

    protected List<TPrivacyActionNougat> getActionNougatList(Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        return detectDataMap
                .values()
                .stream()
                .flatMap(d -> d.getPrivacyActionNougats().stream())
                .collect(Collectors.toList());
    }

    protected List<TSdkLawsDectDetail> buildSdkLawsDectDetailList(TTask task, Integer lawId, String itemNo,
                                                                  List<TPrivacyLawsDetail> details,
                                                                  Function<String, Optional<SdkVO>> findSdk) {
        TPrivacyLawsRegulations regulations = queryRegulation(itemNo);
        return details.stream()
                .filter(d -> ExecutorTypeEnum.SDK.getValue().equals(d.getExecutorType()))
                .filter(d -> StringUtils.isNotBlank(d.getExecutor()))
                .filter(d -> itemNo.equals(d.getItemNo()))
                .flatMap(d -> Arrays.stream(d.getExecutor().split(",")))
                .distinct()
                .map(sdkName -> buildSdkLawsDectDetail(task, lawId, regulations, sdkName, findSdk))
                .collect(Collectors.toList());
    }

    protected TPrivacyLawsRegulations queryRegulation(String itemNo) {
        TPrivacyLawsRegulations regulationQuery = new TPrivacyLawsRegulations();
        regulationQuery.setItemNo(itemNo);
        return privacyLawsRegulationsMapper.selectOne(regulationQuery);
    }

    protected TSdkLawsDectDetail buildSdkLawsDectDetail(TTask task, Integer lawId, TPrivacyLawsRegulations regulations,
                                                        String sdkName, Function<String, Optional<SdkVO>> findSdk) {
        TSdkLawsDectDetail detail = new TSdkLawsDectDetail();
        detail.setLawId(lawId);
        Optional<SdkVO> sdkLibrary = findSdk.apply(sdkName);
        if (sdkLibrary.isPresent()) {
            if (PinfoConstant.SUSPICIOUS_TYPE_NAME.equals(sdkLibrary.get().getTypeName())) {
                // 疑似sdk
                detail.setSdkId(PinfoConstant.SUSPICIOUS_SDK_ID);
                detail.setSdkName(sdkName);
                detail.setSdkPackage(DETAILS_EMPTY);
                detail.setSdkManufacturer(DETAILS_EMPTY);
            } else {
                detail.setSdkId(sdkLibrary.get().getId());
                detail.setSdkName(sdkName);
                detail.setSdkPackage(sdkLibrary.get().getPackageName());
                detail.setSdkManufacturer(sdkLibrary.get().getManufacturer());
            }
        } else {
            // 疑似sdk
            detail.setSdkId(PinfoConstant.SUSPICIOUS_SDK_ID);
            detail.setSdkName(sdkName);
            detail.setSdkPackage(DETAILS_EMPTY);
            detail.setSdkManufacturer(DETAILS_EMPTY);
        }
        detail.setUserId(task.getCreateUserId());
        detail.setLawsItemId(regulations.getId());
        detail.setLawsItemName(regulations.getName());
        detail.setLawsItemParentId(regulations.getParentId());
        detail.setTaskId(task.getTaskId());
        detail.setAssetsId(task.getAssetsId());
        detail.setTerminalType(task.getTerminalType().getValue());
        detail.setStatus(StatisticsStatus.LAST.value);
        detail.setCreateTime(new Date());
        return detail;
    }

    protected void setNougatsCycleTrigger(List<TPrivacyActionNougat> privacyActionNougats, TerminalTypeEnum terminalTypeEnum) {
        Set<Long> actionIds = actionNougatMapper.findByTerminalType(terminalTypeEnum.getValue())
                .stream()
                .filter(a -> a.getPersonal() == PrivacyStatusEnum.YES.getValue())
                .map(TActionNougat::getActionId)
                .collect(Collectors.toSet());
        MiitActionKit.setActionCycle(privacyActionNougats, actionIds);
    }


    /**
     * 解析用户上传的隐私文件
     * @param assets
     * @param commonDetectInfo
     */
    protected void analysisUserUploadPrivacyPolicy(TAssets assets, CommonDetectInfo commonDetectInfo) {
        commonDetectInfo.setHasPrivacyPolicy(true);
        String privacyPolicyFileUrl = fastDFSIntranetIp + "/" + assets.getPrivacyPolicyPath();
        // 目前只支持上传txt类型文件，直接读取文本数据就行，不用读文件后再解析
        String content = HttpUtils.httpGetString(privacyPolicyFileUrl, Collections.emptyMap());
        commonDetectInfo.setPrivacyPolicyContent(content);
    }

    protected CustomDetectInfo makeCustomDetectInfo(String itemNo, Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys) {
        Set<Long> actionIds = new HashSet<>();
        Set<String> keysWords = new HashSet<>();
        Map<Long, String> actionKey = new HashMap<>(16);
        Map<Long, String> actionTypeName = new HashMap<>(16);
        List<TPrivacyLawsBasis> lawsBases = itemNoWithActionAndKeys.get(itemNo);
        lawsBases.forEach(basis -> {
            // 有可能actionId在关键词表没有数据，需要先加入到行为id集合中，避免漏加
            if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ACTION_ID) {
                actionIds.add(basis.getActionId());
            }
            getLawsBaseKeyWords(basis).forEach(words -> {
                if (Objects.nonNull(words.getActionId())) {
                    actionIds.add(words.getActionId());
                }
                if (Objects.nonNull(words.getKeyWords())) {
                    keysWords.add(words.getKeyWords());
                }
                if (Objects.nonNull(words.getActionId()) && Objects.nonNull(words.getKeyWords())) {
                    actionKey.put(words.getActionId(), words.getKeyWords());
                    actionTypeName.put(words.getActionId(), words.getActionTypeName());
                }
            });
        });
        Map<String, String> itemCheckWord = new HashMap<>();
        String point_10107_agree = commonProperties.getProperty("point_10107_agree");
        String point_10107_refuse = commonProperties.getProperty("point_10107_refuse");
        try {
            point_10107_agree = new String(point_10107_agree.getBytes("iso-8859-1"), "utf-8");
            point_10107_refuse = new String(point_10107_refuse.getBytes("iso-8859-1"), "utf-8");
        } catch (Exception e1) {
            e1.getMessage();
        }

        itemCheckWord.put("point_10107_agree", point_10107_agree);
        itemCheckWord.put("point_10107_refuse", point_10107_refuse);
        itemCheckWord.put("self_starting_white_list", commonProperties.getProperty("self_starting_white_list"));

        CustomDetectInfo customDetectInfo = new CustomDetectInfo();
        customDetectInfo.setItemNo(itemNo);
        customDetectInfo.setDecideRuleActionIds(actionIds);
        customDetectInfo.setDecideRuleKeys(keysWords);
        customDetectInfo.setActionWithKeyRegex(actionKey);
        customDetectInfo.setActionTypeNames(actionTypeName);
        customDetectInfo.setKeyWordRegex(itemCheckWord);
        return customDetectInfo;
    }

    public List<TPrivacyLawsPrivacyPolicyActionKeyWords> getLawsBaseKeyWords(TPrivacyLawsBasis basis) {
        if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.PERSONAL_ACTIONS) {
            // 涉及个人信息行为
            return tPrivacyLawsPrivacyPolicyKeyWordsMapper.findPersonalActionKeyWords();
        } else if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ALL_ACTIONS) {
            // 所有行为
            return tPrivacyLawsPrivacyPolicyKeyWordsMapper.findAllActionKeyWords();
        } else if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ACTION_ID) {
            TPrivacyLawsPrivacyPolicyActionKeyWords words = tPrivacyLawsPrivacyPolicyKeyWordsMapper.findKeyWordsByActionId(basis.getActionId());
            return Objects.isNull(words) ? Collections.emptyList() : Collections.singletonList(words);
        } else if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ITEM_NO){
            TPrivacyLawsPrivacyPolicyActionKeyWords words = tPrivacyLawsPrivacyPolicyKeyWordsMapper.findKeyWordsByItemNo(basis.getItemNo());
            return Objects.isNull(words) ? Collections.emptyList() : Collections.singletonList(words);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 保存164法规检测结果
     *
     * @param detectResultList
     * @param lawMap
     */
    protected void saveLawDetectData(TTask task,
                                     List<SdkVO> sdkVOList, List<DetectResult> detectResultList,
                                     Map<String, List<TPrivacyLawsBasis>> lawMap,
                                     List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS, PrivacyLawId lawId) {
        if (detectResultList == null) {
            return;
        }
        List<TPrivacyLawsResult> results = new ArrayList<>();
        List<TPrivacyLawsDetail> details = new ArrayList<>();
        List<TAppLawsDectDetail> appLawsDectDetailList = new ArrayList<>();
        List<TSdkLawsDectDetail> sdkLawsDectDetailList = new ArrayList<>();
        for (DetectResult detectResult : detectResultList) {
            try {
                TPrivacyLawsResult result = buildTPrivacyLawsResult(detectResult);

                List<ActionAnalyse> analysisResult = detectResult.getAnalysisResult();
                List<TPrivacyLawsBasis> privacyLawsBasisList = lawMap.get(result.getItemNo());
                Set<String> keyWordList = privacyLawsBasisList
                        .stream()
                        .flatMap(basis -> getLawsBaseKeyWords(basis).stream().map(TPrivacyLawsPrivacyPolicyActionKeyWords::getKeyWords))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                String keyWords = String.join("|", keyWordList);
                // 保存行为
                if (analysisResult != null) {
                    for (ActionAnalyse actionAnalyse : analysisResult) {
                        details.add(buildLawsDetailAction(actionAnalyse, detectResult, keyWords));
                    }
                }

                // 保存通讯传输
                if (detectResult.getSensitiveWordResult() != null) {
                    for (ActionNetwork actionNetwork : detectResult.getSensitiveWordResult()) {
                        details.add(buildLawsSensitiveWord(actionNetwork, detectResult, keyWords));
                    }
                }
                // 保存储存个人信息
                if (detectResult.getSensitivePrefsResult() != null) {
                    for (ActionSharedPerfs actionNetwork : detectResult.getSensitivePrefsResult()) {
                        details.add(buildLawsSensitivePrefs(actionNetwork, detectResult, keyWords));
                    }
                }
                // 保存传输个人信息
                if (detectResult.getOutsideAddressResult() != null) {
                    for (ActionOutSide actionOutSide : detectResult.getOutsideAddressResult()) {
                        details.add(buildLawsOutsideAddress(actionOutSide, detectResult, keyWords));
                    }
                }
                // 保存文本
                if (StringUtils.isNotBlank(detectResult.getPrivacyPolicyFragment())) {
                    details.add(buildLawsDetailText(detectResult, keyWords));
                }

                // 保存图片
                if (CollectionUtils.isNotEmpty(detectResult.getScreenshots())) {
                    for (String screenshotPath : detectResult.getScreenshots()) {
                        details.add(buildLawsDetailImage(detectResult, keyWords, screenshotPath));
                    }
                }
                // 保存隐私政策截图地址
                if (StringUtils.isNotBlank(detectResult.getPrivacyScreenshot())) {
                    details.add(buildLawsPrivacyImage(detectResult, keyWords, detectResult.getPrivacyScreenshot()));
                }
                //不合规情况替换场景标题
                if (result.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                    setConclusionAction(result, details, detectResult, suspiciousSdkBehaviorVOS);
                }
                // 保存整改建议和检测结论
                if (result.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                    result.setSuggestion(detectResult.getSuggestion());
                    result.setConclusion(detectResult.getConclusion());
                }
                if (result.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                    appLawsDectDetailList.add(buildAppLawsDectDetail(task, lawId.id, detectResult.getItemNo()));
                    sdkLawsDectDetailList.addAll(buildSdkLawsDectDetailList(task, lawId.id, detectResult.getItemNo(), details,
                            sdkName -> sdkVOList.stream().filter(sdk -> StringUtils.equals(sdk.getName(), sdkName)).findFirst()));
                }
                results.add(result);
            } catch (Exception e) {
                logger.error("saveLawDetectData detectResult:{}", JSON.toJSONString(detectResult));
                logger.error("saveLawDetectData error", e);
            }
        }

        if (results.size() > 0) {
            for (TPrivacyLawsResult tPrivacyLawsDetail : results) {
                if (getTPrivacyLawsResult(tPrivacyLawsDetail.getItemNo(), tPrivacyLawsDetail.getTaskId())) {
                    continue;
                }
                tPrivacyLawsDetail.setLawId(Integer.valueOf(lawId.id.toString()));
                privacyLawsResultMapper.insert(tPrivacyLawsDetail);
            }
        }

        if (details.size() > 0) {
            for (TPrivacyLawsDetail tPrivacyLawsDetail : details) {
                privacyLawsDetailMapper.insert(tPrivacyLawsDetail);
            }
        }
        saveAppDectDetail(task, lawId, appLawsDectDetailList, sdkLawsDectDetailList);
    }

    private boolean noneImage(List<TPrivacyLawsDetail> details) {
        return details.stream().noneMatch(d -> d.getDataType() == LawDetailDataTypeEnum.TXT_IMAG.getValue() && StringUtils.isNotBlank(d.getFileKey()));
    }

    protected CommonDetectInfo buildCommonDetectInfo(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        // 查询行为数据
        List<TActionNougat> actionNougats = actionNougatMapper.findByTerminalType(task.getTerminalType().getValue());
        Map<Long, TActionNougat> actionNougatMap = actionNougats.stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        List<TApplyPermission> applyPermissions = applyPermissionMapper.findByTerminalType(task.getTerminalType().getValue());
        Map<String, LawDetectResultVO> lawsRegulationsMap = privacyLawsRegulationsMapper.selectByTerminalType(task.getTerminalType().getValue())
                .stream().collect(Collectors.toMap(LawDetectResultVO::getItemNo, Function.identity(), (entity1, entity2) -> entity1));
        // 查询推送类sdk
        Set<Long> pushSdkIds = sdkLibraryMapper.findByTypeName("推送");

        // 查询资产信息
        // 校验任务是否为数据包解析失败
        TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());

        CommonDetectInfo commonDetectInfo = new CommonDetectInfo();
        commonDetectInfo.setTaskId(task.getTaskId());
        commonDetectInfo.setApkDetectionDetailId(task.getApkDetectionDetailId());
        commonDetectInfo.setPrivacyDetectionService(privacyDetectionService);
        commonDetectInfo.setApkPackageName(taskDetail.getApk_package());
        commonDetectInfo.setApkName(taskDetail.getApk_name());
        commonDetectInfo.setApkTargetSdkVersion(CommonUtil.findTargetSdkVersion(taskDetail));
        commonDetectInfo.setFilePath(uncompress);
        commonDetectInfo.setPrivacyPolicyMinTextLineSpacing(privacyPolicyMinTextLineSpacing);
        commonDetectInfo.setPrivacyPolicyMinTextSize(privacyPolicyMinTextSize);
        commonDetectInfo.setActionNougatMap(actionNougatMap);
        commonDetectInfo.setDetectDataMap(detectDataMap);
        commonDetectInfo.setSdkTypePushIds(pushSdkIds);
        commonDetectInfo.setTerminalTypeEnum(task.getTerminalType());
        commonDetectInfo.setApplyPermissions(applyPermissions);
        commonDetectInfo.setLawsRegulationsMap(lawsRegulationsMap);
        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
            // 查询用户和管理员设定的sdk别名
            List<UserSdkAliasVO> sdkAliasList = userSdkAliasMapper.findLawSdkAlias(task.getCreateUserId());
            Map<String, List<String>> sdkAliasMap = new HashMap<>();
            sdkAliasList.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getSdkAlias()))
                    .peek(sdkAlias -> sdkAlias.setSdkName(removeSdkSuffix(sdkAlias.getSdkName())))
                    .forEach(sdkAlias -> {
                        List<String> sdkAlliasSplitList = Arrays.stream(sdkAlias.getSdkAlias().split("、"))
                                .map(SdkUtils::removeSdkSuffix)
                                .collect(Collectors.toCollection(ArrayList::new));
                        sdkAliasMap.put(sdkAlias.getSdkName(), sdkAlliasSplitList);
                    });
            List<TSdkLibrary> sdkList = sdkLibraryMapper.findByTerminalType(task.getTerminalType().getValue());
            sdkList.stream()
                    // sdk名字去掉sdk后缀
                    .peek(sdkLibrary -> sdkLibrary.setName(removeSdkSuffix(sdkLibrary.getName())))
                    .forEach(sdkLibrary -> {
                // sdk自带的别名
                if (StringUtils.isNotBlank(sdkLibrary.getAlias()) && !StringUtils.equalsIgnoreCase(sdkLibrary.getName(), removeSdkSuffix(sdkLibrary.getAlias()))) {
                    List<String> aliasList = sdkAliasMap.get(sdkLibrary.getName());
                    if (Objects.isNull(aliasList)) {
                        aliasList = new ArrayList<>();
                    }
                    aliasList.addAll(Arrays.stream(sdkLibrary.getAlias().split("、"))
                            .map(SdkUtils::removeSdkSuffix)
                            .collect(Collectors.toList()));
                    sdkAliasMap.put(sdkLibrary.getName(), aliasList);
                }
            });
            commonDetectInfo.setSdkAliasMap(sdkAliasMap);
            commonDetectInfo.setSdkList(keepMaxVersionSdk(sdkList));
        }
        return commonDetectInfo;
    }

    /**
     * 相同名字的sdk只保留最新版本的
     * @param sdkList
     */
    private List<TSdkLibrary> keepMaxVersionSdk(List<TSdkLibrary> sdkList) {
        List<TSdkLibrary> newSdkList = new ArrayList<>();
        Map<String, List<TSdkLibrary>> group = sdkList.stream().collect(Collectors.groupingBy(TSdkLibrary::getName));
        for (Map.Entry<String, List<TSdkLibrary>> entry:group.entrySet()) {
            if (entry.getValue().size() == 1) {
                newSdkList.add(entry.getValue().get(0));
            } else {
                TSdkLibrary maxVersionSdk = entry.getValue().get(0);
                for (TSdkLibrary s:entry.getValue()) {
                    if (SdkUtils.compareSdkVersion(s, maxVersionSdk)) {
                        maxVersionSdk = s;
                    }
                }
                newSdkList.add(maxVersionSdk);
            }
        }
        return newSdkList;
    }

    protected void updateTaskInfoWhenAnalyzeFinish(TTask task, DetectionTypeEnum detectionType) {
        updateTaskInfoWhenAnalyzeFinish(task, detectionType, 0L);
    }

    /**
     * 完成分析时更新任务信息
     *
     * @param task
     * @param detectionType
     */
    protected void updateTaskInfoWhenAnalyzeFinish(TTask task, DetectionTypeEnum detectionType, Long analysisTakesTime) {
        switch (detectionType) {
            case DYNAMIC:
                taskDAO.updateDynamicSuccess(task.getTaskId(), analysisTakesTime);
                break;
            case MANUAL:
                taskDAO.updateManualSuccess(task.getTaskId());
                break;
            case LAW:
                taskDAO.updateLawSuccess(task.getTaskId());
                break;
        }
    }

    protected boolean checkDocumentValid(TTask task) {
        // 校验详情存在
        String documentId = task.getApkDetectionDetailId();
        TaskDetailVO taskDetailVo = findById(documentId);
        if (Objects.isNull(taskDetailVo)) {
            return false;
        }
        // 存入任务对应资产的MD5
        task.setMd5(taskDetailVo.getMd5());
        return true;
    }

    /**
     * 上传隐私政策图片
     *
     * @param task
     * @param commonDetectInfo
     * @param privacyImagePath
     */
    protected void uploadPrivacyPolicyImage(TTask task, CommonDetectInfo commonDetectInfo, String privacyImagePath) {
        if (StringUtils.isBlank(privacyImagePath)) {
            return;
        }
        String imgPath = commonDetectInfo.getFilePath() + File.separator + privacyImagePath;
        // 对隐私界面截图进行ocr识别
        commonDetectInfo.setPrivacyPolicyImgOcrResult(ocrService.extractText(imgPath));
        try {
            String imageUploadMethod = commonProperties.getProperty("image.upload.method");//判断是否需要上传到文件服务器
            if (org.apache.commons.lang.StringUtils.isNotBlank(imageUploadMethod) && imageUploadMethod.equals("fdfs")) {
                String filePath = imgPath;
                File file = new File(filePath);
                File newFile = new File(filePath + ".png");
                if (!filePath.toLowerCase().contains(".png") && !filePath.toLowerCase().contains(".jpg") && !filePath.toLowerCase().contains(".jpeg")) {
                    file.renameTo(newFile);
                    filePath = filePath + ".png";
                }
                FileVO fileVO = uploadFileImage(task.getTaskId(), filePath);
                commonDetectInfo.setPrivacyPolicyImg(fileVO.getFileUrl());
            } else {
                File file = new File(imgPath);
                FileVO fileVO = fileService.uploadFile(new MockMultipartFile(file.getName(), new FileInputStream(file)));
                logger.info("TaskId:{} 隐私政策截图保存 fileKey={}", task.getTaskId(), fileVO.getFileKey());
                commonDetectInfo.setPrivacyPolicyImg(fileVO.getFileKey());
            }
        } catch (Exception e) {
            logger.error("隐私政策截图保存异常，任务ID：{}，异常信息：{}", task.getTaskId(), e);
        }
    }

    protected void setPrivacyPolicyNlp(CommonDetectInfo commonDetectInfo) {
        commonDetectInfo.setOcrService(ocrService);
        if (StringUtils.isNotBlank(privacyPolicyIdentifyUrl) && StringUtils.isNotBlank(commonDetectInfo.getPrivacyPolicyContent())) {
            ONlpServer oNlpServer = new ONlpServer(privacyPolicyIdentifyUrl);
            ONlpResponse oNlpResponse = oNlpServer.process(commonDetectInfo.getPrivacyPolicyContent(),
                    DetectPointIdentifyItems.allItems().stream().map(item -> item.nlpItemName).collect(Collectors.toList()));
            commonDetectInfo.setNlpResponse(oNlpResponse);
        }
    }

    /**
     * 校验任务是否有效
     *
     * @param taskId
     * @return 无效返回为:null
     */
    protected TTask checkTaskValid(Long taskId) {
        // 校验任务存在
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            logger.info("TaskId:{} mysql数据库内无任务数据", taskId);
            return null;
        }
        // 校验详情存在
        String documentId = task.getApkDetectionDetailId();
        TaskDetailVO taskDetailVo = findById(documentId);
        if (Objects.isNull(taskDetailVo)) {
            logger.info("TaskId:{} mongodb数据库内无任务数据 documentId={}", taskId, documentId);
            return null;
        }
        // 存入任务对应资产的MD5
        task.setMd5(taskDetailVo.getMd5());
        return task;
    }

    protected void analysisCheckListByPolicyContent(CommonDetectInfo commonDetectInfo) {
        logger.info("没有单独的第三方SDK清单，尝试从隐私政策中解析");
        if (isCheckListEmpty(commonDetectInfo.getThirdPartySharingChecklist()) && StringUtils.isNotBlank(commonDetectInfo.getPrivacyPolicyContent())) {
            if (!StringUtils.containsIgnoreCase(commonDetectInfo.getPrivacyPolicyContent(), "sdk")) {
                logger.info("隐私政策中没有SDK信息");
                return;
            }
            // 优先调用ai解析
            List<AISdkExtractVO> aiSdkExtractVOList = aiToolService.sdkExtract(commonDetectInfo.getPrivacyPolicyContent());
            if (!aiSdkExtractVOList.isEmpty()) {
                commonDetectInfo.setThirdPartySharingChecklist(convertAISdkExtractIntoCheckList(Optional.of(aiSdkExtractVOList)));
            } else {
                List<String> sdkNameList = SDKExtractor.extractSdkName(commonDetectInfo.getPrivacyPolicyContent());
                // 2个以上才算SDK列表
                if (sdkNameList.size() >= 2) {
                    CheckList checkList = SDKExtractor.extractSDKInformation(commonDetectInfo.getPrivacyPolicyContent(), sdkNameList);
                    if (!checkList.getRowList().isEmpty() || StringUtils.isNotBlank(checkList.getFullText())) {
                        commonDetectInfo.setThirdPartySharingChecklist(checkList);
                    }
                }
            }
        }
    }

    private boolean isCheckListEmpty(CheckList checkList) {
        return Objects.isNull(checkList) || (CollectionUtils.isEmpty(checkList.getRowList()) && StringUtils.isBlank(checkList.getFullText()));
    }

    protected void analysisCheckListByUpload(TAssets assets, CommonDetectInfo commonDetectInfo) {
        File file = new File(commonProperties.getFilePath() + File.separator + "default" + File.separator + UuidUtil.uuid() + "." +
                getFileExtName(assets.getThirdPartyShareListPath()));
        try {
            CheckList checkList = new CheckList();
            List<CheckList.Row> rowList = new ArrayList<>();
            String privacyPolicyFileUrl = fastDFSIntranetIp + "/" + assets.getThirdPartyShareListPath();
            FileUtils.copyURLToFile(new URL(privacyPolicyFileUrl), file);
            //得到文件流
            InputStream in = new FileInputStream(file.getAbsoluteFile());
            Workbook workbook = new XSSFWorkbook(in);
            //获取第一张表
            Sheet sheet = workbook.getSheetAt(0);
            StringJoiner fullTextJoiner = new StringJoiner("\n");
            //得到第二行
            for (int i = 2; i < sheet.getLastRowNum() + 1; i++) {
                Row row = sheet.getRow(i);
                String terminal = getCellAsString(row.getCell(4));
                // 空表示匹配所有平台
                if (StringUtils.isEmpty(terminal) || isTarget(terminal, commonDetectInfo.getTerminalTypeEnum()) || DETAILS_EMPTY.equals(terminal)) {
                    String sdkName = getCellAsString(row.getCell(0));
                    String sdkPackage = getCellAsString(row.getCell(1));
                    String purpose = getCellAsString(row.getCell(2));
                    String company = getCellAsString(row.getCell(3));
                    String sdkDesc = getCellAsString(row.getCell(5));
                    String link = getCellAsString(row.getCell(6));
                    if (StringUtils.isNotBlank(sdkName)) {
                        CheckList.Row checkListRow = new CheckList.Row();
                        checkListRow.setSdkName(sdkName);
                        checkListRow.setSdkPackage(sdkPackage);
                        checkListRow.setPermission(sdkDesc);
                        checkListRow.setCompany(company);
                        checkListRow.setPurpose(purpose);
                        checkListRow.setPrivacyPolicyLink(link);
                        rowList.add(checkListRow);
                        fullTextJoiner.add(sdkName).add(sdkPackage).add(sdkDesc);
                    }
                }
            }
            checkList.setFullText(fullTextJoiner.toString());
            checkList.setRowList(rowList);
            commonDetectInfo.setThirdPartySharingChecklist(checkList);
        } catch (Exception e) {
            logger.error("解析第三方SDK清单失败", e);
        } finally {
            file.delete();
        }
    }

    private static boolean isTarget(String terminal, TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return StringUtils.containsAnyIgnoreCase(terminal, "android", "安卓");
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return StringUtils.containsAnyIgnoreCase(terminal, "ios", "苹果");
        } else if (terminalTypeEnum.isApplet()) {
            return StringUtils.containsAnyIgnoreCase(terminal, "小程序");
        } else if (terminalTypeEnum == terminalTypeEnum.HARMONY) {
            return StringUtils.containsAnyIgnoreCase(terminal, "鸿蒙");
        } else {
            return false;
        }
    }

    private boolean isThirdPartyContent(String text) {
        return headContainsStr(text, "第三方\\S*(SDK|信息)(共享)?(清单|列表|目录)");
    }

    private boolean isPersonalContent(String text) {
        return headContainsStr(text, "个人信息(收集)?(清单|列表)");
    }

    protected void analysisCheckListByUi(CommonDetectInfo commonDetectInfo,
                                         List<ResultDataLogBO> privacyDetailResults,
                                         List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        // 第三方SDK共享清单提取
        // 优先使用ai进行分析
        List<List<AISdkExtractVO>> aiSdkExtractVOList = aiSdkExtract(privacyDetailResults, subPagePrivacyDetailList);
        if (!aiSdkExtractVOList.isEmpty()) {
            // 取最长的内容
            Optional<List<AISdkExtractVO>> sdkExtractVOList = aiSdkExtractVOList.stream().max(Comparator.comparingInt(List::size));
            commonDetectInfo.setThirdPartySharingChecklist(convertAISdkExtractIntoCheckList(sdkExtractVOList));
        } else {
            commonDetectInfo.setThirdPartySharingChecklist(extractCheckList(privacyDetailResults, subPagePrivacyDetailList));
        }
        // 个人信息收集清单提取
        List<ResultDataLogBO> personalList = privacyDetailResults.stream().filter(log -> {
            return Objects.nonNull(log.getUiDumpResult()) && isPersonalContent(log.getUiDumpResult().getFullText());
        }).collect(Collectors.toList());
        if (!personalList.isEmpty()) {
            privacyDetailResults.removeAll(personalList);
            ResultDataLogBO uiLog = getMaximumTextLength(personalList);
            CheckList checkList = new CheckList();
            checkList.setFullText(uiLog.getUiDumpResult().getFullText());
            commonDetectInfo.setPersonalInfoCollectionChecklist(checkList);
        }
    }

    private List<List<AISdkExtractVO>> aiSdkExtract(List<ResultDataLogBO> privacyDetailResults,
                              List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        List<List<AISdkExtractVO>> aiSdkExtractVOList = subPagePrivacyDetailList.stream()
                .map(info -> aiToolService.sdkExtract(info.content))
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (aiSdkExtractVOList.isEmpty()) {
            // 从隐私文本界面里提取
            List<List<AISdkExtractVO>> aiSdkExtractVOList2 = privacyDetailResults.stream().filter(log -> {
                        return Objects.nonNull(log.getUiDumpResult()) && isThirdPartyContent(log.getUiDumpResult().getFullText());
                    }).map(info -> aiToolService.sdkExtract(info.getUiDumpResult().getFullText()))
                    .filter(CollectionUtils::isNotEmpty)
                    .collect(Collectors.toList());
            logger.info("aiSdkExtractVOList2 {}", aiSdkExtractVOList2.size());
            return aiSdkExtractVOList2;
        }
        logger.info("aiSdkExtractVOList {}", aiSdkExtractVOList.size());
        return aiSdkExtractVOList;
    }

    @NotNull
    private static CheckList convertAISdkExtractIntoCheckList(Optional<List<AISdkExtractVO>> sdkExtractVOList) {
        CheckList checkList = new CheckList();
        checkList.setFullText("");
        List<CheckList.Row> rowList = new ArrayList<>();
        if (sdkExtractVOList.isPresent()) {
            for (AISdkExtractVO vo: sdkExtractVOList.get()) {
                CheckList.Row row = new CheckList.Row();
                row.setSdkName(vo.getSdkName());
                row.setCompany(vo.getSdkCompany());
                row.setPermission(DETAILS_EMPTY);
                row.setPurpose(vo.getUsagePurpose());
                rowList.add(row);
            }
        }
        checkList.setRowList(rowList);
        logger.info("AI提取第三方SDK清单 {}", rowList.size());
        return checkList;
    }

    private CheckList extractCheckList(List<ResultDataLogBO> privacyDetailResults, List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        // html页面中有sdk表格详细的
        List<PrivacyPolicyTextInfo> htmlSdkTableList = subPagePrivacyDetailList.stream().filter(info -> {
            return CollectionUtils.isNotEmpty(info.sdkTables);
        }).collect(Collectors.toList());
        // html页面中没有sdk表格，但是有第三方sdk共享清单文本的
        List<PrivacyPolicyTextInfo> htmlSdkTextList = subPagePrivacyDetailList.stream().filter(info -> {
            return !htmlSdkTableList.contains(info) && isThirdPartyContent(info.content);
        }).collect(Collectors.toList());
        // 优先取html的表格数据，准确性比较高
        if (!htmlSdkTableList.isEmpty()) {
            PrivacyPolicyTextInfo page = getMaximumTextLengthPage(htmlSdkTableList);
            CheckList checkList = new CheckList();
            checkList.setFullText(page.content);
            analysisSdkTable(page.sdkTables, checkList);
            logger.info("正则提取html中第三方SDK清单表格");
            return checkList;
        }
        if (!htmlSdkTextList.isEmpty()) {
            PrivacyPolicyTextInfo page = getMaximumTextLengthPage(htmlSdkTextList);
            CheckList checkList = new CheckList();
            checkList.setFullText(page.content);
            logger.info("正则提取html中第三方SDK清单文本");
            return checkList;
        }
        // 界面中有第三方sdk共享清单文本的
        List<ResultDataLogBO> thirdPartyUIList = privacyDetailResults.stream().filter(log -> {
            return Objects.nonNull(log.getUiDumpResult()) && isThirdPartyContent(log.getUiDumpResult().getFullText());
        }).collect(Collectors.toList());
        if (!thirdPartyUIList.isEmpty()) {
            privacyDetailResults.removeAll(thirdPartyUIList);
            ResultDataLogBO uiLog = getMaximumTextLength(thirdPartyUIList);
            CheckList checkList = new CheckList();
            checkList.setFullText(uiLog.getUiDumpResult().getFullText());
            logger.info("正则提取界面文本中第三方SDK清单文本");
            return checkList;
        }
        logger.info("正则提取第三方SDK清单为空");
        return null;
    }

    private void analysisSdkTable(List<Element> tables, CheckList checkList) {
        if (tables == null) {
            return;
        }
        List<CheckList.Row> rowList = new ArrayList<>();
        for (Element table : tables) {
            Elements tr = table.select("tr");
            int headerIndex = -1;
            int sdkNameIndex = -1;
            int purposeIndex = -1;
            List<Integer> sdkDescIndexs = new ArrayList<>();
            int companyIndex = -1;
            int linkIndex = -1;
            // 找到头部这一行
            for (int i = 0; i < tr.size(); i++) {
                Element row = tr.get(i);
                Elements headers = row.select("th");
                if (headers.isEmpty()) {
                    headers = row.select("td");
                }
                if (headerIndex < 0) {
                    for (int j = 0; j < headers.size(); j++) {
                        Element td = headers.get(j);
                        if (Pattern.compile(thirdPartySdkTableSdkNameRegex).matcher(td.text()).find()) {
                            sdkNameIndex = j;
                        } else if (Pattern.compile(thirdPartySdkTablePurposeRegex).matcher(td.text()).find()) {
                            purposeIndex = j;
                        } else if (Pattern.compile(thirdPartySdkTablePermissionRegex).matcher(td.text()).find()) {
                            sdkDescIndexs.add(j);
                        } else if (Pattern.compile(thirdPartySdkTableCompanyRegex).matcher(td.text()).find()) {
                            companyIndex = j;
                        } else if (Pattern.compile(thirdPartySdkTablePolicyLinkRegex).matcher(td.text()).find()) {
                            linkIndex = j;
                        }
                        // 有sdk名字和sdk权限描述信息时，说明SDK表格的表头找到了
                        if (sdkNameIndex >= 0 && !sdkDescIndexs.isEmpty()) {
                            headerIndex = i;
                        }
                    }
                } else {
                    String sdkName = getElementStr(headers, sdkNameIndex);
                    String purpose = getElementStr(headers, purposeIndex);
                    StringJoiner sdkDesc = new StringJoiner("\n");
                    for (Integer index : sdkDescIndexs) {
                        sdkDesc.add(getElementStr(headers, index));
                    }
                    String company = getElementStr(headers, companyIndex);
                    String link = getElementStr(headers, linkIndex);
                    if (canAddRow(rowList, sdkName)) {
                        CheckList.Row checkListRow = new CheckList.Row();
                        checkListRow.setSdkName(sdkName);
                        checkListRow.setPermission(sdkDesc.toString());
                        checkListRow.setCompany(company);
                        checkListRow.setPurpose(purpose);
                        checkListRow.setPrivacyPolicyLink(link);
                        rowList.add(checkListRow);
                    }
                }
            }
        }
        if (!rowList.isEmpty()) {
            checkList.setRowList(rowList);
        }
    }

    private boolean canAddRow(List<CheckList.Row> rowList, String sdkName) {
        if (StringUtils.isBlank(sdkName)) {
            return false;
        }
        return rowList.stream().noneMatch(row -> row.getSdkName().equals(sdkName));
    }

    private String getElementStr(Elements elements, int index) {
        if (index >= 0 && index < elements.size()) {
            return elements.get(index).text();
        } else {
            return DETAILS_EMPTY;
        }
    }

    private PrivacyPolicyTextInfo getMaximumTextLengthPage(List<PrivacyPolicyTextInfo> textInfoList) {
        // 先获取有sdk表格的页面
        Optional<PrivacyPolicyTextInfo> haveTables = textInfoList.stream()
                .filter(info -> CollectionUtils.isNotEmpty(info.sdkTables))
                .max(Comparator.comparingInt(result -> StringUtils.length(result.content)));
        return haveTables.orElseGet(() -> textInfoList.stream()
                .max(Comparator.comparingInt(result -> StringUtils.length(result.content)))
                .orElseThrow(() -> new IjiamiRuntimeException("logBOList is empty")));
    }

    private ResultDataLogBO getMaximumTextLength(List<ResultDataLogBO> logBOList) {
        return logBOList.stream()
                .max(Comparator.comparingInt(result -> StringUtils.length(result.getUiDumpResult().getFullText())))
                .orElseThrow(() -> new IjiamiRuntimeException("logBOList is empty"));
    }

    private boolean headContainsStr(String content, String... regexList) {
        int end = (int) (content.length() * 0.1f);
        for (String regex : regexList) {
            if (Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(content.substring(0, end)).find()) {
                return true;
            }
        }
        return false;
    }

    protected TPrivacyPolicyResult buildPrivacyPolicyDetailResult(TaskDetailVO taskDetail, CommonDetectInfo commonDetectInfo, Map<String, TPrivacyPolicyItem> itemMap) {
        TPrivacyPolicyResult policyResult = new TPrivacyPolicyResult();
        policyResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
        policyResult.setTaskId(taskDetail.getTaskId());
        policyResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.PRIVACY_POLICY_DETAIL.itemNo).getId());
        if (StringUtils.isNotEmpty(commonDetectInfo.getPrivacyPolicyContent())) {
            policyResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
            policyResult.setDetailResult(commonDetectInfo.getPrivacyPolicyContent());
            if (StringUtils.isNotBlank(commonDetectInfo.getPrivacyPolicyImg())) {
                policyResult.setFileKey(commonDetectInfo.getPrivacyPolicyImg());
            }
        } else {
            policyResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
        }
        sdkCheckListMapper.deleteByTaskId(taskDetail.getTaskId());
        if (Objects.nonNull(commonDetectInfo.getThirdPartySharingChecklist())) {
            TSdkCheckList checkList = new TSdkCheckList();
            checkList.setOriginalText(commonDetectInfo.getThirdPartySharingChecklist().getFullText());
            checkList.setSdkList(CommonUtil.beanToJson(commonDetectInfo.getThirdPartySharingChecklist().getRowList()));
            checkList.setTaskId(commonDetectInfo.getTaskId());
            sdkCheckListMapper.insert(checkList);
        }
        return policyResult;
    }

    /**
     * 异常时更新任务信息
     *
     * @param task
     * @param errorMsg
     * @param detectionType
     */
    protected void updateTaskInfoWhenException(TTask task, TTaskData taskData, DetectionTypeEnum detectionType, String errorMsg) {
        taskDataService.analysisTaskDataFailure(taskData.getId());
        updateTaskInfoWhenException(task, detectionType, errorMsg);
    }

    /**
     * 异常时更新任务信息
     *
     * @param task
     * @param errorMsg
     * @param detectionType
     */
    protected void updateTaskInfoWhenException(TTask task, DetectionTypeEnum detectionType, String errorMsg) {
        // 查询最新的任务状态
        TTask newTask = taskMapper.selectByPrimaryKey(task.getTaskId());
        switch (detectionType) {
            case DYNAMIC:
                taskDAO.updateDynamicFailure(newTask, errorMsg);
                break;
            case MANUAL:
                taskDAO.updateManualFailure(newTask, errorMsg);
                break;
            case LAW:
                taskDAO.updateLawFailure(newTask, errorMsg);
                break;
        }
    }

    protected void updateTaskVersion(String documentId, String version, String logo) {
        if (StringUtils.isBlank(version)) {
            return;
        }
        try {
            TaskDetailVO taskDetailVO = findById(documentId);
            if (taskDetailVO.getDetection_result() == null || org.apache.commons.lang.StringUtils.isBlank(taskDetailVO.getDetection_result().toString())) {
                return;
            }
            List<DetectionItem<?>> itemList = CommonUtil.jsonToBean(taskDetailVO.getDetection_result().toString(), new TypeReference<List<DetectionItem<?>>>() {
            });
            Update update = new Update();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("_id", documentId);
            if (itemList != null && !itemList.isEmpty()) {
                itemList
                        .stream()
                        .filter(item -> item.getDetectionItemId().equals(DetectionItemIdEnum.BASE.getValue()))
                        .findFirst()
                        .ifPresent(item -> {
                            AppletBaseResultContentDTO base = (AppletBaseResultContentDTO) item.getResultContent();
                            base.setVersionName(version);
                        });
                update.set("detection_result", itemList);
                update.set("apk_version", version);
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(logo)) {
                update.set("apk_logo", logo);
            }
            update(paramMap, update);
        } catch (Exception e) {
            logger.error("更新版本号和log失败", e);
        }
    }

    //重新爬取一次链接
    protected void getUrlPrivacyContent(String uncompress){
        List<String> list = cn.ijiami.detection.utils.SeleniumUtils.getUrl(uncompress);
        if(list==null || list.size()==0) {
            return;
        }
        list.forEach(str->{
            try {
                seleniumUtils.getPrivacyByPythonScript(str, uncompress);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        });
    }



    protected List<PrivacyLawId> getDetectionLawIds(TTaskExtendVO extend, TerminalTypeEnum terminalTypeEnum) {
        if (StringUtils.isNotBlank(extend.getDetectionLaws())) {
            return Arrays.stream(extend.getDetectionLaws().split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(Integer::parseInt)
                    .map(PrivacyLawId::getItem)
                    .collect(Collectors.toList());
        } else {
            return PrivacyLawId.getLawsByTerminalType(terminalTypeEnum);
        }
    }


    protected List<TPrivacyLawsBasis> findTaskPrivacyLawsBasis(Long taskId, PrivacyLawId lawId) {
        List<TPrivacyLawsBasis> regulations = privacyLawsBasisMapper.selectItemNoInfoByLawId(lawId.id);
        TTaskExtendVO tTaskExtend = taskExtendMapper.findTaskByTaskId(taskId);
        if (Objects.nonNull(tTaskExtend)) {
            if (Objects.nonNull(tTaskExtend.getCustomLawsGroupId()) && tTaskExtend.getCustomLawsGroupId() > 0) {
                logger.info("检测的任务有自定义法规 taskId={} groupId={}", taskId, tTaskExtend.getCustomLawsGroupId());
                Example example = new Example(TCustomLawsRegulationsItem.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("groupId", tTaskExtend.getCustomLawsGroupId());
                criteria.andEqualTo("status", StatusEnum.NORMAL.itemValue());
                Set<Long> regulationsIdList = customLawsRegulationsItemMapper.selectByExample(example)
                        .stream().map(TCustomLawsRegulationsItem::getRegulationsId).collect(Collectors.toSet());
                return regulations.stream().filter(regulation -> regulationsIdList.contains(regulation.getId())).collect(Collectors.toList());
            }
        }
        return regulations;
    }

    protected void analysisLaw(TTask task, List<SdkVO> sdkVOList, CommonDetectInfo commonDetectInfo,
                               List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS, PrivacyLawId lawId) {
        // 查询检测项及检测关键词
        List<TPrivacyLawsBasis> regulations = findTaskPrivacyLawsBasis(task.getTaskId(), lawId);

        List<String> itemNos = regulations.stream().map(TPrivacyLawsBasis::getItemNo).distinct().collect(Collectors.toList());
        Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys = regulations.stream().collect(Collectors.groupingBy(TPrivacyLawsBasis::getItemNo));
        List<DetectResult> detectResults = new ArrayList<>();
        itemNos.forEach(itemNo -> {
            CustomDetectInfo customDetectInfo = makeCustomDetectInfo(itemNo, itemNoWithActionAndKeys);
            // 设置回调
            IDetectCallback callback = new DefaultDetectCallback();
            AbstractDetectPoint instance = detectPointManager.getInstance(itemNo);
            instance.startDetect(commonDetectInfo, customDetectInfo, callback);
            detectResults.add(callback.getResult());
        });
        // 保存结果
        saveLawDetectData(task, sdkVOList, detectResults,
                itemNoWithActionAndKeys, suspiciousSdkBehaviorVOS, lawId);
    }

    protected boolean isOnlySavePersonalBehavior(Long taskId) {
        Example example = new Example(TTaskExtend.class);
        example.createCriteria().andEqualTo("taskId", taskId);
        TTaskExtend taskExtend = taskExtendMapper.selectOneByExample(example);
        if (Objects.isNull(taskExtend)) {
            return false;
        } else if (Objects.isNull(taskExtend.getOnlySavePersonalBehavior())) {
            return false;
        } else {
            return taskExtend.getOnlySavePersonalBehavior();
        }
    }

    protected void setScreenshotImage(Long taskId, DetectDataBO data, String uncompress, TerminalTypeEnum terminalTypeEnum) {
        //根据任务id获取到手动截图信息(放外层优化性能）
        List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
        screenshotImages.forEach(action -> {
            logger.info("setScreenshotImage action=" + action);
        });
        //根据截图地址获取截图时间搓与filekey的map，避免每次存储一条行为记录都要再存一次图片
        Map<Long, String> screenshotMap = screenshotImageService.saveScreenPictureReturnMap(uncompress + "/" + "screenshot");
        screenshotMap.forEach((key, value) -> {
            logger.info("setScreenshotImage key=" + key + " value=" + value);
        });
        Map<Long, TActionNougat> actionNougatMap = actionNougatMapper.findByTerminalType(terminalTypeEnum.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        if (CollectionUtils.isNotEmpty(data.getPrivacyActionNougats())) {
            //不确定完整检测里面有没有自动截图的数据也把此方法加上
            screenshotImageService.saveDynamicBehaviorImg(taskId, screenshotMap, data.getPrivacyActionNougats());
            //新增完整检测截图入库方法  2021/6/22
            screenshotImageService.saveManualBehaviorImg(taskId, data.getPrivacyActionNougats(), screenshotImages, actionNougatMap);
        }
        if (CollectionUtils.isNotEmpty(data.getPrivacyOutsideAddresses())) {
            screenshotImageService.saveImgOfOutsideData(taskId, screenshotMap, data.getPrivacyOutsideAddresses());
            screenshotImageService.saveManualBehaviorImgOutside(taskId, data.getPrivacyOutsideAddresses(), screenshotImages);
        }
        if (CollectionUtils.isNotEmpty(data.getPrivacySensitiveWords())) {
            screenshotImageService.saveDynamicBehaviorImgSensitiveWord(taskId, screenshotMap, data.getPrivacySensitiveWords());
            screenshotImageService.saveManualBehaviorImgSensitiveWord(taskId, data.getPrivacySensitiveWords(), screenshotImages);
        }
        if (CollectionUtils.isNotEmpty(data.getPrivacySharedPrefs())) {
            screenshotImageService.saveImgOfSharedData(taskId, screenshotMap, data.getPrivacySharedPrefs());
            screenshotImageService.saveManualBehaviorImgShared(taskId, data.getPrivacySharedPrefs(), screenshotImages);
        }
    }

    /**
     * 判断任务的分段数据是否完整
     * @param task
     * @param callback
     * @throws Exception
     */
    public void obtainAndVerifyTaskData(TTask task, ObtainTaskDataCallback callback) throws Exception {
        List<TTaskData> dataList = taskDataService.findTaskData(task.getTaskId());
        if (taskDataService.validateTaskStagedData(task, DynamicAutoSubStatusEnum.DATA_PROCESS, dataList)) {
            callback.execute(dataList);
        } else {
            logger.info("TaskId:{} 任务数据不完整 不进行最终解析", task.getTaskId());
        }
    }

    /**
     * 判断任务的分段数据全部解析完成
     * @param task
     * @param callback
     * @throws Exception
     */
    public void allTaskDataAnalysisSuccess(TTask task, ObtainTaskDataCallback callback) throws Exception {
        List<TTaskData> dataList = taskDataService.findTaskData(task.getTaskId());
        if (taskDataService.allTaskDataAnalysisSuccess(task.getTerminalType(), dataList)) {
            callback.execute(dataList);
        } else {
            logger.info("TaskId:{} 任务数据不完整 不进行最终解析", task.getTaskId());
        }
    }

    public String uploadDataFileToFastDfs(String dataPath) {
        try {
            FileVO uploadFile = FileVOUtils.convertFileVOByFile(new File(dataPath));
            FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
            String shellIpaPath = fastDfsFile.getFilePath();
            if (org.apache.commons.lang3.StringUtils.isBlank(shellIpaPath)) {
                logger.error("检测数据上传文件失败 shellIpaPath为空");
            }
            return shellIpaPath;
        } catch (IOException | IjiamiApplicationException e) {
            logger.error("检测数据上传文件失败", e);
        }
        return StringUtils.EMPTY;
    }

    public String saveFilePath(String md5, Long taskId, String suffix) {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        return dynamicPath + md5 + "_" + taskId + "_" + suffix + ".zip";
    }

    protected void deleteOldData(Long taskId) {
        // 清理行为、抓包数据、ip信息、SharedPrefs
        // 扩展表要比主表先删，数据有关联
        privacyActionNougatExtendMapper.deleteByTaskId(taskId);
        privacyActionNougatMapper.deleteByTaskId(taskId);
        privacySensitiveWordMapper.deleteByTaskId(taskId);
        privacyOutsideAddressMapper.deleteByTaskId(taskId);
        privacySharedPrefsMapper.deleteByTaskId(taskId);
    }

    protected boolean isAllZipFiles(File file) {
        File[] files = file.listFiles();
        if (files == null) {
            return false;
        }
        for (File f:files) {
            if (!StringUtils.endsWithIgnoreCase(f.getName(), ".zip")) {
                return false;
            }
        }
        return true;
    }

    public static Integer extractStage(String fileName) {
        // 正则表达式匹配字符串_数值_AUTO_数值
        String regex = "^[^_]+_\\d+_AUTO_(\\d+)\\.zip$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(fileName);

        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        } else {
            return null; // 如果没有匹配到，返回null
        }
    }

    protected Map<Long, String> getScreenShotMap(IdbStagedDataEnum stagedData, String parentUncompress) {
        String uncompress;
        //截图图片的地址
        if (stagedData == IdbStagedDataEnum.NONE) {
            uncompress = parentUncompress + "/" + "screenshot";
        } else {
            uncompress = parentUncompress + "/" + "screenshot_" + stagedData.getValue();
        }
        return screenshotImageService.saveScreenPictureReturnMap(uncompress);
    }

    protected void taskDataRollback(TTask task, DynamicAutoSubStatusEnum subStatus) {
        TTask updateTask = new TTask();
        IdbStagedDataEnum stagedData = taskDataService.previousStage(
                task,
                IdbStagedDataEnum.getItem(subStatus.getValue()));
        logger.info("分段的数据异常 当前阶段={} 退回到={}", subStatus.getName(), stagedData.getName());
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDynamicSubStatus(DynamicAutoSubStatusEnum.getItem(stagedData.getValue()));
        taskMapper.updateByPrimaryKeySelective(updateTask);
    }

    public interface ObtainTaskDataCallback {
        void execute(List<TTaskData> taskDataList) throws Exception;
    }


    protected void analysisAutoTotalFile(TTask task, String uncompress) {
        File uncompressFile = new File(uncompress);
        if (isAllZipFiles(uncompressFile)) {
            deleteOldData(task.getTaskId());
            List<TTaskData> taskDataList = taskDataService.findTaskData(task.getTaskId());
            for (File zipFile:uncompressFile.listFiles()) {
                Integer stageValue = extractStage(zipFile.getName());
                if (stageValue == null) {
                    throw new IjiamiRuntimeException("数据包名错误 " + zipFile.getName());
                }
                Optional<TTaskData> taskDataOptional = taskDataList.stream().filter(taskData -> taskData.getDynamicSubStatus().getValue() == stageValue).findFirst();
                if (taskDataOptional.isPresent()) {
                    taskDataService.resetTaskDataStatus(taskDataOptional.get().getId());
                    analysisAutoInternal(task, taskDataOptional.get(), IdbStagedDataEnum.getItem(stageValue), zipFile.getAbsolutePath());
                } else {
                    throw new IjiamiRuntimeException("阶段数据不完整");
                }
            }
        } else {
            analysisAutoOldFile(task, uncompress);
        }
    }

    protected void analysisAutoStagedFile(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {
        if (stageEnum == IdbStagedDataEnum.NONE) {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            String uncompress = CommonUtil.uncompress(dataPath, dynamicPath + UuidUtil.uuid());
            analysisAutoOldFile(task, uncompress);
        } else {
            analysisAutoInternal(task, taskData, stageEnum, dataPath);
        }
    }

    abstract protected void analysisAutoOldFile(TTask task, String dataPath);

    protected void analysisAutoInternal(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {
        if (stageEnum == null || stageEnum.getValue() > IdbStagedDataEnum.BEHAVIOR_EXIT.getValue()) {
            logger.info("TaskId:{} stag={} 数据阶段错误", task.getTaskId(), stageEnum);
            return;
        }
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId() + ":" + stageEnum.itemValue();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            logger.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            taskDataService.analyzingTaskData(taskData.getId());
            analysisStageData(task, taskData, stageEnum, dataPath);
            finalizeTaskIfComplete(task);
        } catch (UncompressFailException | DownloadZipFailException e) {
            updateTaskInfoWhenException(task, taskData, DetectionTypeEnum.DYNAMIC, "数据包异常");
            logger.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        } catch (Throwable e) {
            logger.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, taskData, DetectionTypeEnum.DYNAMIC, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            IpUtil.ipMap.clear();
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    /**
     * 所有阶段德数据解析完成，进行一些需要把所有阶段数据整合一起的数据解析
     * @param task
     * @throws Exception
     */
    private void finalizeTaskIfComplete(TTask task) throws Exception {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            logger.info("TaskId:{} key={} 最后一步检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            allTaskDataAnalysisSuccess(task, taskDataList -> {
                // 最终数据解析
                analysisFinalStage(task, taskDataList);
                // 生成默认报告
                buildReport(task.getTaskId());
            });
        } finally {
            distributedLockService.unlock(lockKey);
        }
    }

    public void analysisDataRetry(Long taskId) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        taskDAO.updateDynamicAutoDataProcess(task.getTaskId());
        String zipSuffix;
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            zipSuffix = "_AUTO";
        } else {
            zipSuffix = "_MANUAL";
        }
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        try {
            if (StringUtils.isNotBlank(task.getDataPath())) {
                File file = new File(dynamicPath + task.getMd5() + "_" + task.getTaskId() + zipSuffix + ".zip");
                if (!file.exists()) {
                    try {
                        if (task.getDataPath() != null && task.getDataPath().startsWith("http")) {
                            FileUtils.copyURLToFile(new URL(task.getDataPath()), file);
                        } else {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIntranetIp)), file);
                        }
                    } catch (IOException e) {
                        logger.error("数据包下载失败", e);
                    }
                }
                if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
                    String uncompress = CommonUtil.uncompress(file.getAbsolutePath(), dynamicPath + UuidUtil.uuid());
                    analysisAutoTotalFile(task, uncompress);
                } else {
                    analysisManualInternal(task, file.getAbsolutePath(), task.getDataPath());
                }
            } else {
                String uncompress = dynamicPath + UuidUtil.uuid();
                obtainAndVerifyTaskData(task, taskDataList -> {
                    deleteOldData(task.getTaskId());
                    // 先重置分段任务数据的状态
                    for (TTaskData taskData:taskDataList) {
                        taskDataService.resetTaskDataStatus(taskData.getId());
                    }
                    // 分段任务数据进行解析
                    for (TTaskData taskData:taskDataList) {
                        File stagedZip = new File(uncompress, UuidUtil.uuid() + ".zip");
                        if (!stagedZip.exists()) {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                        }
                        analysisAutoInternal(task, taskData, IdbStagedDataEnum.getItem(taskData.getDynamicSubStatus().getValue()), stagedZip.getAbsolutePath());
                    }
                });
            }
        } catch (Exception e) {
            logger.error("检测数据处理 - {}，数据下载异常:{}", "检测失败", e.getMessage());
        }
    }

    protected File saveFile(String md5, Long taskId, String suffix, MultipartFile data) throws IOException {
        File file = new File(saveFilePath(md5, taskId, suffix));
        if (data == null) {
            if (!file.exists()) {
                FileUtils.copyURLToFile(new URL(commonProperties.getProperty("ijiami.dynamic.data.download.path") + md5 + ".zip"), file);
            }
        } else {
            if (!file.getParentFile().exists()) {
                boolean mkdirs = file.getParentFile().mkdirs();
                if (!mkdirs) {
                    throw new FileNotFoundException("无法创建文件夹");
                }
            }
            data.transferTo(file);
        }
        return file;
    }

    public void analysisAutoAgain(Long taskId, MultipartFile data) {
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            logger.info("TaskId:{} 任务不存在", taskId);
            return;
        }
        try {
            File file = saveFile(task.getMd5(), taskId, "AUTO", data);
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            String uncompress = CommonUtil.uncompress(file.getAbsolutePath(), dynamicPath + UuidUtil.uuid());
            analysisAutoTotalFile(task, uncompress);
        } catch (Exception e) {
            logger.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    abstract protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath);

    abstract protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception;

    abstract protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath);
    

    protected void sendMessage(TTask task, BroadcastMessageTypeEnum typeEnum, String message) {
        sendMessageService.sendTaskStatusBroadcast(typeEnum, message, task);
    }

    public void buildReport(Long taskId) {
        privacyDetectionService.buildAndUploadDefaultReport(taskId);
    }

    protected Map<BehaviorStageEnum, DetectDataBO> getManualDetectionData(Long taskId) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>();
        DetectDataBO detectDataBO = new DetectDataBO();

        Example queryActionNougat = new Example(TPrivacyActionNougat.class);
        queryActionNougat.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.selectByExample(queryActionNougat);
        detectDataBO.setPrivacyActionNougats(nougatList);

        Example queryOutsideAddress = new Example(TPrivacyOutsideAddress.class);
        queryOutsideAddress.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacyOutsideAddress> privacyOutsideAddresses = privacyOutsideAddressMapper.selectByExample(queryOutsideAddress);
        detectDataBO.setPrivacyOutsideAddresses(privacyOutsideAddresses);

        Example querySensitiveWord = new Example(TPrivacySensitiveWord.class);
        querySensitiveWord.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacySensitiveWord> sensitiveInserts = privacySensitiveWordMapper.selectByExample(querySensitiveWord);
        detectDataBO.setPrivacySensitiveWords(sensitiveInserts);

        Example querySharedPrefs = new Example(TPrivacySharedPrefs.class);
        querySharedPrefs.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacySharedPrefs> shareInserts = privacySharedPrefsMapper.selectByExample(querySharedPrefs);
        detectDataBO.setPrivacySharedPrefs(shareInserts);

        detectDataMap.put(BehaviorStageEnum.BEHAVIOR_FRONT, detectDataBO);
        return detectDataMap;
    }

    public void insertManualScreenshotImageByTasId(Long taskId, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, TerminalTypeEnum terminalType) {
        screenshotImageService.deleteByTaskId(taskId);
        Map<Long, TActionNougat> actionNougatMap = actionNougatMapper.findByTerminalType(terminalType.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            // 计算行为触发次数（秒/次）
            setNougatsCycleTrigger(entry.getValue().getPrivacyActionNougats(), terminalType);
            entry.getValue().getPrivacyActionNougats().stream()
                    .filter(action -> Objects.nonNull(action.getNumberAction()) && Objects.nonNull(action.getTriggerCycleTime()))
                    .forEach(action -> {
                        privacyActionNougatMapper.updateCycleTrigger(action.getId(), action.getNumberAction(), action.getTriggerCycleTime());
                    });
            //根据任务id获取到手动截图信息
            List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
            screenshotImageService.saveManualBehaviorImg(taskId, entry.getValue().getPrivacyActionNougats(), screenshotImages, actionNougatMap);
            // 通讯行为数据
            screenshotImageService.saveManualBehaviorImgOutside(taskId, entry.getValue().getPrivacyOutsideAddresses(), screenshotImages);
            // 传输个人信息
            screenshotImageService.saveManualBehaviorImgSensitiveWord(taskId, entry.getValue().getPrivacySensitiveWords(), screenshotImages);
            // 储存个人信息
            screenshotImageService.saveManualBehaviorImgShared(taskId, entry.getValue().getPrivacySharedPrefs(), screenshotImages);
        }
    }

    protected File downloadFile(String fastDfsDataPath) throws IOException {
        File file = new File(commonProperties.getFilePath() + File.separator + "default" +
                File.separator + UuidUtil.uuid() + ".zip");
        int retries = 3;
        long expectedMinSize = 96; // 最小文件大小阈值(字节)
        while (retries-- > 0) {
            try {
                String url = UriUtils.getHttpUrl(fastDfsDataPath, fastDFSIntranetIp);
                logger.info("下载 url: {}", url);
                // 下载文件
                FileUtils.copyURLToFile(new URL(url), file);
                // 检查1: 文件是否存在
                if (!file.exists()) {
                    throw new IOException("下载文件不存在");
                }
                // 检查2: 文件大小是否合理
                long fileSize = file.length();
                if (fileSize == 0) {
                    throw new IOException("下载文件为空 (0字节)");
                }
                if (fileSize < expectedMinSize) {
                    throw new IOException(String.format(
                            "下载文件大小异常 (%.2f KB)，小于最小阈值 (%.2f KB)",
                            fileSize/1024.0, expectedMinSize/1024.0));
                }
                // 检查3: ZIP文件完整性验证
                try (ZipFile zipFile = new ZipFile(file)) {
                    if (zipFile.size() == 0) {
                        throw new IOException("ZIP文件内容为空");
                    }
                } catch (IOException e) {
                    throw new IOException("下载的文件不是有效的ZIP文件", e);
                }
                // 所有检查通过
                return file;
            } catch (Exception e) {
                // 删除可能损坏的文件
                FileUtils.deleteQuietly(file);
                if (retries == 0) {
                    throw new IOException(String.format("文件下载失败，重试次数用尽。最后错误: %s", e.getMessage()), e);
                }
                try {
                    Thread.sleep(1000); // 等待后重试
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
        throw new IOException("文件下载失败，未知错误");
    }
}
