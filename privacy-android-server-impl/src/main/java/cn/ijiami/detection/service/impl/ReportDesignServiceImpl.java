package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.ijiami.detection.entity.TTaskAutoReport;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.mapper.TTaskAutoReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.entity.TReportDesign;
import cn.ijiami.detection.mapper.TReportDesignMapper;
import cn.ijiami.detection.service.api.IReportDesignService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import tk.mybatis.mapper.entity.Example;

/**
 * 报告配置接口服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class ReportDesignServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IReportDesignService {
    private final TReportDesignMapper reportDesignMapper;
    private final TTaskAutoReportMapper taskAutoReportMapper;

    public ReportDesignServiceImpl(TReportDesignMapper reportDesignMapper, TTaskAutoReportMapper taskAutoReportMapper) {
        this.reportDesignMapper = reportDesignMapper;
        this.taskAutoReportMapper = taskAutoReportMapper;
    }

    @Override
    public TReportDesign findReportDesign(TReportDesign reportDesign) {
        return reportDesignMapper.selectOne(reportDesign);
    }

    @Override
    public TReportDesign addReportDesign(TReportDesign reportDesign) throws IjiamiApplicationException {
        int num = reportDesignMapper.insertSelective(reportDesign);
        if (num == 0) {
            throw new IjiamiApplicationException("报告模板添加失败");
        }
        return reportDesign;
    }

    @Override
    public TReportDesign updateReportDesign(TReportDesign reportDesign) throws IjiamiApplicationException {
        Long reportId = reportDesign.getId();
        if (reportId == null) {
            throw new IjiamiApplicationException("报告ID不能为空");
        }
        int num = reportDesignMapper.updateByPrimaryKeySelective(reportDesign);
        if (num == 0) {
            throw new IjiamiApplicationException("修改失败");
        }
        TReportDesign result = reportDesignMapper.selectByPrimaryKey(reportId);
        log.info("报告模板修改成功，设置缓存报告失效");
        taskAutoReportMapper.deleteByTaskUser(result.getCreateUserId(), result.getTerminalType().getValue());
        return result;
    }

    @Override
    public PageInfo<TReportDesign> findReportDesignList(TReportDesign reportDesign) {
        if (reportDesign.getPage() != null && reportDesign.getRows() != null) {
            PageHelper.startPage(reportDesign.getPage(), reportDesign.getRows());
        }
        List<TReportDesign> reportDesignList = reportDesignMapper.select(reportDesign);
        if (CollectionUtils.isEmpty(reportDesignList)) {
            reportDesignList = copyReport(reportDesign.getCreateUserId());
        }
        return new PageInfo<>(reportDesignList);
    }

    @Override
    public List<TReportDesign> getReportDesignList(TReportDesign reportDesign) {
        List<TReportDesign> reportDesignList = reportDesignMapper.select(reportDesign);
        if (CollectionUtils.isEmpty(reportDesignList)) {
            return copyReport(reportDesign.getCreateUserId());
        }
        return reportDesignList;
    }

    /**
     * 复制报告模板
     *
     * @param userId 用户id
     */
    private List<TReportDesign> copyReport(Long userId) {
        // 获取基础模板，创建用户为1的即为基础模板
        Example example = new Example(TReportDesign.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("createUserId", 1);
        List<TReportDesign> reportTemplates = reportDesignMapper.selectByExample(example);
        // 创建当前用户的新模板
        List<TReportDesign> reportDesignList = new ArrayList<>();
        for (TReportDesign reportTemplate : reportTemplates) {
            reportTemplate.setId(null);
            reportTemplate.setCreateUserId(userId);
            reportTemplate.setUpdateUserId(userId);
            reportTemplate.setCreateTime(new Date());
            reportTemplate.setUpdateTime(new Date());
            reportDesignList.add(reportTemplate);
        }
        // 保存新创建一份模板信息给当前用户
        reportDesignMapper.insertList(reportDesignList);
        return reportDesignList;
    }
}
