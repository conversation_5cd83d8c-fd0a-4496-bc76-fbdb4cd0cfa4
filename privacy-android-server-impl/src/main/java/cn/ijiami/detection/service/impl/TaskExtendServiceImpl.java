package cn.ijiami.detection.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.android.client.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;

import cn.hutool.http.HttpUtil;
import cn.ijiami.detection.VO.ApiPushResultVO;
import cn.ijiami.detection.VO.CountLawDetectResult;
import cn.ijiami.detection.VO.PushProgressVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskExtend;
import cn.ijiami.detection.entity.TTaskQueue;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.TTaskQueueMapper;
import cn.ijiami.detection.service.api.IMiitDetectService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.ITaskExtendService;
import tk.mybatis.mapper.entity.Example;

/**
 * 检测任务扩展表
 *
 * <AUTHOR>
 */
@Service
public class TaskExtendServiceImpl implements ITaskExtendService {

    private static final Logger LOG = LoggerFactory.getLogger(TaskExtendServiceImpl.class);
    private final TTaskExtendMapper taskExtendMapper;
    private final RestTemplate restTemplate;
    private final TTaskMapper taskMapper;
    private final TTaskQueueMapper taskQueueMapper;
    private final IMiitDetectService miitDetectService;
    @Lazy
    @Resource
    private IPrivacyDetectionService privacyDetectionService;

    public TaskExtendServiceImpl(TTaskExtendMapper taskExtendMapper, RestTemplate restTemplate, TTaskMapper taskMapper, TTaskQueueMapper taskQueueMapper,
                                 IMiitDetectService miitDetectService) {
        this.taskExtendMapper = taskExtendMapper;
        this.restTemplate = restTemplate;
        this.taskMapper = taskMapper;
        this.taskQueueMapper = taskQueueMapper;
        this.miitDetectService = miitDetectService;
    }

    @Override
    public void save(TTaskExtendVO dto) {
        Example example = new Example(TTaskExtend.class);
        if (dto.getTaskId() != null) {
            example.createCriteria().andEqualTo("taskId", dto.getTaskId());
        }
        example.createCriteria().andEqualTo("bussinessId", dto.getBussinessId());
        TTaskExtend taskExtend = taskExtendMapper.selectOneByExample(example);
        if (taskExtend == null) {
            taskExtend = new TTaskExtend();
            BeanUtils.copyProperties(dto, taskExtend);
            taskExtend.setPushCount(0);
            taskExtend.setCreateTime(new Date());
            taskExtendMapper.insertSelective(taskExtend);
        } else {
            if (StringUtils.isNoneBlank(dto.getCallbackUrl())) {
                taskExtend.setCallbackUrl(dto.getCallbackUrl());
            }
            if (dto.getPushStatus() != null) {
                taskExtend.setPushStatus(dto.getPushStatus());
            }
            if (dto.getPushCount() != null) {
                taskExtend.setPushCount(dto.getPushCount());
            }
            taskExtend.setUpdateTime(new Date());
            if (StringUtils.isNotBlank(dto.getMessage())) {
                taskExtend.setMessage(dto.getMessage());
            }
            taskExtend.setActionFilterGroupId(dto.getActionFilterGroupId());
            taskExtend.setCustomLawsGroupId(dto.getCustomLawsGroupId());
            taskExtendMapper.updateByPrimaryKeySelective(taskExtend);
        }
    }

    @Override
    public void pushData(Long taskId) {
        try {
            TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(taskId);

            if (extend == null || StringUtils.isBlank(extend.getCallbackUrl())) {
                updatePushStatus(taskId, PushStatusEnum.PUSH_OVER);
                return;
            }

            TTask task = taskMapper.selectByPrimaryKey(taskId);
            if (task == null) {
                updatePushStatus(taskId, PushStatusEnum.PUSH_BEYOND);
                return;
            }

            if(task.getIsApi()!=null && task.getIsApi()== PageOrApiOrKafkaEnum.IS_PAGE.getValue()){
                updatePushStatus(taskId, PushStatusEnum.PUSH_BEYOND);
                return;
            }
            
            LOG.info("结果回调通知.bussinessId={},动态={},静态={}", extend.getBussinessId(), extend.getDynamicStatus().getValue(), extend.getTaskTatus().getValue());
            
            if(task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN || task.getTaskTatus() == DetectionStatusEnum.DETECTION_IN){
            	LOG.info("检测未完成，不回调结果.bussinessId={},动态={},静态={}", extend.getBussinessId(), extend.getDynamicStatus().getValue(), extend.getTaskTatus().getValue());
            	return;
            }
            
            if (extend.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED || extend.getTaskTatus() != DetectionStatusEnum.DETECTION_OVER) {
                PushProgressVO push = new PushProgressVO();
               
                if (extend.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                    push.setDetectionType(DetectionTypeEnum.DYNAMIC);
                }
                if (extend.getTaskTatus() == DetectionStatusEnum.DETECTION_STOP) {
                    push.setDetectionType(DetectionTypeEnum.STATIC);
                }
                push.setPushProgress(PushProgressEnum.PUSH_ERROR);
                ApiPushResultVO vo = getBodyResult(extend.getBussinessId(), push);
                LOG.info("检测异常回调.bussinessId={},动态={},静态={}", extend.getBussinessId(), extend.getDynamicStatus().getValue(), extend.getTaskTatus().getValue());
                String response = pushMessage(vo, extend.getCallbackUrl(), extend.getBussinessId());
                response = "[检测异常通知]" + response;
                if (StringUtils.isBlank(response) && response.contains("success")) {
                    //更新推送状态
                    updatePushStatus(taskId, PushStatusEnum.PUSH_OVER, response);
                } else {
                    //更新推送状态
                    updatePushStatus(taskId, PushStatusEnum.PUSH_STOP, response);
                }
            } else {

                if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                    //腾讯业务获取检测结果方法  //摘要
                    CountLawDetectResult lawResult = miitDetectService.findLawDetectResultByTaskId(1L, task.getTaskId());
                    if (lawResult == null) {
                        LOG.info("腾讯业务获取检测结果方法164号文结果为空.bussinessId={}", extend.getBussinessId());
                        return;
                    }
                }

                //详情
                JSONObject vo = privacyDetectionService.getDetectionForTencentDetail(task);

                if (StringUtils.isBlank(vo.getString("behaviorDataFile"))) {
                    LOG.info("行为文件地址为空.bussinessId={}", extend.getBussinessId());
                    return;
                }

                ApiPushResultVO pushVo1 = new ApiPushResultVO();
                pushVo1.setBussinessId(extend.getBussinessId());
                pushVo1.setResultType(3);
                pushVo1.setData(vo);

                String response2 = pushMessage1(pushVo1, extend.getCallbackUrl(), extend.getBussinessId());
                LOG.info("详情推送response2.bussinessId={},result={},文件地址={}", extend.getBussinessId(), response2, vo.get("behaviorDataFile"));
                if (StringUtils.isNotBlank(response2) && (response2.contains("200") || response2.contains("success"))
                        && (response2.contains("200") || response2.contains("success"))) {
                    //更新推送状态
                    updatePushStatus(taskId, PushStatusEnum.PUSH_OVER, response2);
                } else {
                    //更新推送状态
                    updatePushStatus(taskId, PushStatusEnum.PUSH_STOP, response2);
                }
            }
        } catch (Exception e) {
            e.getMessage();
        }
    }

    /**
     * 构建body实列内容
     *
     * @param bussinessId
     * @param progress
     * @return
     */
    private ApiPushResultVO getBodyResult(String bussinessId, PushProgressVO progress) {
        ApiPushResultVO pushResult = new ApiPushResultVO();
        pushResult.setBussinessId(bussinessId);
        pushResult.setResultType(1);
        pushResult.setData(progress);
        return pushResult;
    }

    private <T> String pushMessage(T o, String url, String bussinessId) {
        LOG.info("开始推送bussinessId={},URL={}", bussinessId, url);
//        LOG.info(JSONObject.toJSONString(o));
        String body = "error";
        com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
        try {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            body = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(o);
            LOG.info("开始推送bussinessId={},URL={},json={}",bussinessId,url,body);
        } catch (JsonProcessingException e) {
            e.getMessage();
        }
        
//        String response = HttpUtil.post(url, JSONObject.toJSONString(o));
        String response = HttpUtil.post(url, body);
        LOG.info("推送返回结果bussinessId={},pushMessage={}", bussinessId, response);
        return response;
    }

    private <T> String pushMessage1(T o, String url, String bussinessId) {
        String response = null;
		try {
			LOG.info("开始推送bussinessId={},URL={}", bussinessId, url);
			String obj = JSON.toJSONString(o, SerializerFeature.DisableCircularReferenceDetect);
			response = HttpUtil.post(url, obj);
			LOG.info("推送返回结果bussinessId={},pushMessage={}", bussinessId, response);
		} catch (Exception e) {
			LOG.error("推送返回结果bussinessId={},message={}", bussinessId, e.getMessage());
		}
        return response;
    }

    @Override
    public int updatePushStatus(Long taskId, PushStatusEnum pushStatus, String message) {

        TTaskExtend extend = new TTaskExtend();
        extend.setTaskId(taskId);
        extend = taskExtendMapper.selectOne(extend);

        if (extend == null) {
            return 0;
        }
        extend.setMessage(message);
        extend.setPushStatus(pushStatus.getValue());
        extend.setUpdateTime(new Date());
//        if (StringUtils.isBlank(extend.getCallbackUrl())) {
//            extend.setPushStatus(PushStatusEnum.PUSH_OVER.getValue());
//        }
        if (pushStatus == PushStatusEnum.PUSH_STOP) {
            extend.setPushCount(extend.getPushCount() == null ? 1 : (extend.getPushCount() + 1));
        }
        if (pushStatus == PushStatusEnum.PUSH_BEYOND) {
            extend.setPushCount(3);
        }
        return taskExtendMapper.updateByPrimaryKeySelective(extend);
    }

    @Override
    public int updatePushStatus(Long taskId, PushStatusEnum pushStatus) {

        TTaskExtend extend = new TTaskExtend();
        extend.setTaskId(taskId);
        extend = taskExtendMapper.selectOne(extend);

        if (extend == null) {
            return 0;
        }

        extend.setPushStatus(pushStatus.getValue());
        extend.setUpdateTime(new Date());
//        if (StringUtils.isBlank(extend.getCallbackUrl())) {
//            extend.setPushStatus(PushStatusEnum.PUSH_OVER.getValue());
//        }
        if (pushStatus == PushStatusEnum.PUSH_STOP) {
            extend.setPushCount(extend.getPushCount() == null ? 1 : (extend.getPushCount() + 1));
        }
        if (pushStatus == PushStatusEnum.PUSH_BEYOND) {
            extend.setPushCount(3);
        }
        return taskExtendMapper.updateByPrimaryKeySelective(extend);
    }

    @Override
    public TTask getTaskByBussinessId(String bussinessId) {
        if(StringUtils.isBlank(bussinessId)) {
            return null;
        }
        return taskExtendMapper.findTaskByBussinessId(bussinessId);
    }

    @Override
    public TTaskExtendVO findTaskByTaskId(Long taskId) {
        return taskExtendMapper.findTaskByTaskId(taskId);
    }

    @Override
    public TTaskQueue taskQueueSave(TTaskQueue queue) {
        if (queue.getId() == null) {
            queue.setCreateTime(new Date());
            queue.setQueueStatus(1);
            taskQueueMapper.insertSelective(queue);
        } else {
            taskQueueMapper.updateByPrimaryKey(queue);
        }
        return queue;
    }

    @Override
    public TTask getTaskByBussinessIdDynamicDesc(String bussinessId) {
        if (StringUtils.isBlank(bussinessId)) {
            return null;
        }
        return taskExtendMapper.findTaskByBussinessIdDynamicDesc(bussinessId);
    }

}
