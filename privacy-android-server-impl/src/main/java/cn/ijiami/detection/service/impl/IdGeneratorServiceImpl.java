package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.service.api.IdGeneratorService;
import cn.ijiami.detection.utils.IdWorker;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class IdGeneratorServiceImpl implements IdGeneratorService {

    @Value("${ijiami.snowflake.worker.id}")
    private int workId;

    private IdWorker idWorker;

    @PostConstruct
    public void init() {
        idWorker = new IdWorker(workId, 0);
    }

    @Override
    public long nextId() {
        return idWorker.nextId();
    }

}