package cn.ijiami.detection.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.PerformanceMonitorOVO;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SendEmailService {

    @Autowired
    private IjiamiCommonProperties commonProperties;

    static class MailAuthenticator extends  Authenticator{

        public String username = "";
        public String password = "";

        public MailAuthenticator() { }

        protected PasswordAuthentication getPasswordAuthentication() {
            return new PasswordAuthentication(username, password);
        }

    }

    /**
     * 发送监控预警
     */
    public boolean sendAlert(PerformanceMonitorOVO performance,List<String> receives){
        String user = commonProperties.getProperty("spring.mail.username");
        String password = commonProperties.getProperty("spring.mail.password");
        String host = commonProperties.getProperty("spring.mail.host");
        String sendName = "个人信息检测平台";
//        List<String> to = Arrays.asList("<EMAIL>");// 收件人
        String content = "<p>个人信息安全检测平台异常告警：<br/>尊敬的客户，您好：<br/>根据系统监控，我们发现以下情况：</p>"
        + "<p style=\"font-weight: bold;\">手机监控指标如下：</p>"
        + "<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
        + "<tr>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">总设备数</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">使用设备数</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">使用率</td>"
        + "</tr>"
        + "<tr>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getCloudPhoneNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getCloudPhoneUsedNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getCloudPhoneUsedPercent()+"%</td>"
        + "</tr>"
        + "</table>"

        + "<br/><p style=\"font-weight: bold;\">检测指标如下：</p>"
        + "<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
        + "<tr>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下发任务数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">排队下载中数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下载失败数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务排队待检测数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测中数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测失败数量</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成排队解析入库数量</td>"
        + "</tr>"
        + "<tr>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDistributeTaskNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getQueueUpNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getQueueUpFailNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDetectionFinishNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getTaskWaitNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getTaskDetectingNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getTaskFailNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getTaskWarehousingNum()+"</td>"
        + "</tr>"
        + "</table>"

        + "<br/><p style=\"font-weight: bold;\">检测耗时指标如下：</p>"
        + "<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
        + "<tr>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成数</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测时间小于30分钟比例</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测时间30~40分钟比例</td>"
        + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测时间大于40分钟比例</td>"
        + "</tr>"
        + "<tr>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDetectionFinishNum()+"</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDetectTimeLowPercent()+"%</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDetectTimeMediumPercent()+"%</td>"
        + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+performance.getDetectTimeHighPercent()+"%</td>"
        + "</tr>"
        + "</table>"
        + "<p><br/>如果您有任何问题或需要进一步的信息，请随时与工作人员联系，我们将竭尽全力为您提供帮助与支持。<br/>感谢您的理解与配合</p>";
        return sendMail(user,password,host,sendName,receives,"监控告警",content);
    }

    /**
     * 发送邮件
     * @param user 发件人邮箱
     * @param password 授权码（注意不是邮箱登录密码）
     * @param host
     * @param sendName 发件人昵称
     * @param receives 接收者邮箱
     * @param title 邮件主题
     * @param content 邮件内容
     * @return success 发送成功 failure 发送失败
     */
    public boolean sendMail(String user, String password, String host, String sendName, List<String> receives, String title, String content) {
        if(CollectionUtils.isEmpty(receives)){
            return false;
        }
        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port",465);
        props.put("mail.smtp.ssl.enable",true);
        MailAuthenticator auth = new MailAuthenticator();
        auth.username = user;
        auth.password = password;
        Session session = Session.getInstance(props, auth);
//        session.setDebug(true);
        try {
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(user,sendName));
            for (String receive : receives) {
                message.addRecipient(Message.RecipientType.TO,new InternetAddress(receive));
            }
            message.setSubject(title);
            MimeBodyPart bodyPart = new MimeBodyPart(); // 正文
            bodyPart.setContent(content, "text/html;charset=utf-8");
            Multipart mp = new MimeMultipart(); // 整个邮件：正文+附件
            mp.addBodyPart(bodyPart);
            message.setContent(mp);
            message.setSentDate(new Date());
            message.saveChanges();
//            Transport trans = session.getTransport("smtp");
            Transport.send(message);
//                System.out.println(message.toString());
        } catch (Exception e){
            e.getMessage();
            return false;
        }
        return true;
    }


//    public String send(String user,String password,String host,String from,String to,DataTaskCount dataTask){
//        String subject = "爱加密个人信息检测系统-腾讯业务";
//        //邮箱内容
//        StringBuffer sb = new StringBuffer();
//        sb.append("<table width=\"990\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td height=\"20\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td><img src=\"http://gtms03.alicdn.com/tps/i3/TB19RpLLFXXXXavXFXX_lM71XXX-640-30.png\" width=\"640\" height=\"30\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,30\"></td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td>"
//                + "<img src=\"https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=2564503653,1288824358&fm=58&s=69628C5284E07D115A5D0D560200D0F3&bpow=121&bpoh=75\" width=\"104\" height=\"54\" alt=\"爱加密\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"104,24\"></td></tr><tr><td height=\"106\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"380\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td height=\"18\" valign=\"bottom\" align=\"left\" style=\"font-size: 18.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;font-weight: bold;\">"
//                + "尊敬的客户：</td></tr><tr><td height=\"19\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td width=\"380\" valign=\"top\" align=\"left\" style=\"font-size: 14.0px;line-height: 30.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;\"><p>您好！</p>"
//                + "<p>当日检测数据统计情况如下</p>"
//                + "<p>每天下发任务量（未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getA()+"</a></p>"
//                + "<p>每天下发任务量（去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getB()+"</a></p>"
//                + "<p>每天完成检测量（静态，未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getC()+"</a></p>"
//                + "<p>每天完成检测量（静态，去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getD()+"</a></p>"
//                + "<p>每天完成检测量（动态，未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getE()+"</a></p>"
//                + "<p>每天完成检测量（动态，去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getF()+"</a></p>"
//                + "<p>每天完成检测数据推送（未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getG()+"</a></p>"
//                + "<p>每天完成检测数据推送（去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getH()+"</a></p>"
//                + "<p>每天检测失败数据（未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getI()+"</a></p>"
//                + "<p>每天检测失败数据（去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getJ()+"</a></p>"
//
//                + "<p>每天待检测量（静态，未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getK()+"</a></p>"
////				+ "<p>每天待检测量（静态，去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getL()+"</a></p>"
//                + "<p>每天待检测量（动态，未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+dataTask.getL()+"</a></p>"
////				+ "<p>每天待检测量（动态，去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">100</a></p>"
////				+ "<p>每天检测超时任务量（未去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">100</a></p>"
////				+ "<p>每天检测超时任务量（去重）：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">100</a></p>"
//                + "<p><br/>请相关人员确认。（若已处理请忽略此信息）</p><p>"
//                + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </p></td></tr><tr><td height=\"29\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td><td width=\"190\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr><tr><td><img src=\"http://gtms01.alicdn.com/tps/i1/TB1AQtGLFXXXXc4XFXXjuwj0FXX-640-114.png\" width=\"640\" height=\"114\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,114\"></td></tr></tbody></table></td></tr><tr><td height=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td valign=\"top\" align=\"center\" style=\"font-size: 14.0px;line-height: 28.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "此信是由爱加密系统发出，系统不接受回信，请勿回复"
//                + "</td></tr><tr><td><table><tbody><tr><td width=\"170\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td valign=\"top\" align=\"center\" style=\"line-height: 18.0px;font-size: 14.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "如有疑问请联系系统管理员，业务咨询客服热线：</td><td><img src=\"http://gtms02.alicdn.com/tps/i2/TB1JVh6LFXXXXaNXXXXoq9FFpXX-18-18.png\" width=\"18\" height=\"18\" border=\"0\" style=\"vertical-align: top;\" orignsize=\"18,18\"></td><td style=\"font-size: 14.0px;color: rgb(0,195,194);line-height: 18.0px;\">4000-618-110</td></tr></tbody></table></td></tr><tr><td height=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table>");
//        String res = null;
//        try {
//            res = sendMails(user, password, host, from, to,
//                    subject, sb.toString());
//            System.out.println(res);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return res;
//    }

    /**
     * 回调系统异常
     * @param to
     * @param url
     * @param businessId
     * @param message
     * @param appName
     * @param md5
     * @param version
     */
//    public void sendResultback(List<String> to,String url,String businessId,String message,String appName,String md5,String version){
//        String subject = "检测结果回调异常-爱加密个人信息检测系统(腾讯业务系统)";
//        String user = "<EMAIL>";
//        String password = "xxx";
//        String host = "smtphz.qiye.163.com";
//        String from = "<EMAIL>";
//
//        //邮箱内容
//        StringBuffer sb = new StringBuffer();
//        sb.append("<table width=\"990\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td height=\"20\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td><img src=\"http://gtms03.alicdn.com/tps/i3/TB19RpLLFXXXXavXFXX_lM71XXX-640-30.png\" width=\"640\" height=\"30\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,30\"></td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td>"
//                + "<img src=\"https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=2564503653,1288824358&fm=58&s=69628C5284E07D115A5D0D560200D0F3&bpow=121&bpoh=75\" width=\"104\" height=\"54\" alt=\"爱加密\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"104,24\"></td></tr><tr><td height=\"106\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"380\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td height=\"18\" valign=\"bottom\" align=\"left\" style=\"font-size: 18.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;font-weight: bold;\">"
//                + "尊敬的客户：</td></tr><tr><td height=\"19\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td width=\"380\" valign=\"top\" align=\"left\" style=\"font-size: 14.0px;line-height: 30.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;\"><p>您好！</p>"
//                + "<p>检测结果回调系统异常：</p>"
//                + "<p>回调地址：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+url+"</a></p>"
//                + "<p>业务ID：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+businessId+"</a></p>"
//                + "<p>错误信息：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+message+"</a></p>"
//                + "<p><br/>请相关人员确认。（若已处理请忽略此信息）</p><p>"
//                + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </p></td></tr><tr><td height=\"29\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td><td width=\"190\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr><tr><td><img src=\"http://gtms01.alicdn.com/tps/i1/TB1AQtGLFXXXXc4XFXXjuwj0FXX-640-114.png\" width=\"640\" height=\"114\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,114\"></td></tr></tbody></table></td></tr><tr><td height=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td valign=\"top\" align=\"center\" style=\"font-size: 14.0px;line-height: 28.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "此信是由爱加密系统发出，系统不接受回信，请勿回复"
//                + "</td></tr><tr><td><table><tbody><tr><td width=\"170\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td valign=\"top\" align=\"center\" style=\"line-height: 18.0px;font-size: 14.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "如有疑问请联系系统管理员，业务咨询客服热线：</td><td><img src=\"http://gtms02.alicdn.com/tps/i2/TB1JVh6LFXXXXaNXXXXoq9FFpXX-18-18.png\" width=\"18\" height=\"18\" border=\"0\" style=\"vertical-align: top;\" orignsize=\"18,18\"></td><td style=\"font-size: 14.0px;color: rgb(0,195,194);line-height: 18.0px;\">4000-618-110</td></tr></tbody></table></td></tr><tr><td height=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table>");
//        sendMail(user, password, host, from, to,subject, sb.toString());
//    }
//
//
//    public void sendPrivacyFail(List<String> to,String url,String businessId,String message,String appName,String md5,String version){
//        String subject = "获取应用宝隐私政策异常-爱加密个人信息检测系统(腾讯业务系统)";
//        String user = "<EMAIL>";
//        String password = "xxx";
//        String host = "smtphz.qiye.163.com";
//        String from = "<EMAIL>";
//
//        //邮箱内容
//        StringBuffer sb = new StringBuffer();
//        sb.append("<table width=\"990\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#efefef\"><tbody><tr><td height=\"20\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td><img src=\"http://gtms03.alicdn.com/tps/i3/TB19RpLLFXXXXavXFXX_lM71XXX-640-30.png\" width=\"640\" height=\"30\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,30\"></td></tr><tr><td><table width=\"640\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td>"
//                + "<img src=\"https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=2564503653,1288824358&fm=58&s=69628C5284E07D115A5D0D560200D0F3&bpow=121&bpoh=75\" width=\"104\" height=\"54\" alt=\"爱加密\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"104,24\"></td></tr><tr><td height=\"106\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td><table width=\"420\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td width=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td><table width=\"380\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" bgcolor=\"#ffffff\"><tbody><tr><td height=\"18\" valign=\"bottom\" align=\"left\" style=\"font-size: 18.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;font-weight: bold;\">"
//                + "尊敬的客户：</td></tr><tr><td height=\"19\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td width=\"380\" valign=\"top\" align=\"left\" style=\"font-size: 14.0px;line-height: 30.0px;color: rgb(0,0,0);font-family: &quot;Microsoft YaHei&quot;;\"><p>您好！</p>"
//                + "<p>获取应用宝隐私政策异常：</p>"
//                + "<p>隐私政策链接：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+url+"</a></p>"
//                + "<p>业务ID：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+businessId+"</a></p>"
//                + "<p>错误信息(隐私内容不全)：<a class=\"calNotifyLink\" date=\"1569114000000\" href=\"javascript:;\" style=\"color:#fe6600;border-bottom: dashed 1px #999;text-decoration: none;cursor: pointer;\">"+message+"</a></p>"
//                + "<p><br/>请相关人员确认。（若已处理请忽略此信息）</p><p>"
//                + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </p></td></tr><tr><td height=\"29\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td><td width=\"190\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td></tr><tr><td><img src=\"http://gtms01.alicdn.com/tps/i1/TB1AQtGLFXXXXc4XFXXjuwj0FXX-640-114.png\" width=\"640\" height=\"114\" border=\"0\" style=\"display: block;vertical-align: top;\" orignsize=\"640,114\"></td></tr></tbody></table></td></tr><tr><td height=\"30\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr><tr><td valign=\"top\" align=\"center\" style=\"font-size: 14.0px;line-height: 28.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "此信是由爱加密系统发出，系统不接受回信，请勿回复"
//                + "</td></tr><tr><td><table><tbody><tr><td width=\"170\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td><td valign=\"top\" align=\"center\" style=\"line-height: 18.0px;font-size: 14.0px;color: rgb(148,148,148);font-family: &quot;Microsoft YaHei&quot;;\">"
//                + "如有疑问请联系系统管理员，业务咨询客服热线：</td><td><img src=\"http://gtms02.alicdn.com/tps/i2/TB1JVh6LFXXXXaNXXXXoq9FFpXX-18-18.png\" width=\"18\" height=\"18\" border=\"0\" style=\"vertical-align: top;\" orignsize=\"18,18\"></td><td style=\"font-size: 14.0px;color: rgb(0,195,194);line-height: 18.0px;\">4000-618-110</td></tr></tbody></table></td></tr><tr><td height=\"40\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table></td><td width=\"175\" style=\"line-height: 0;font-size: 0;\">&nbsp;</td></tr></tbody></table>");
//        sendMail(user, password, host, from, to,subject, sb.toString());
//    }


//    public boolean sendMailTaskData(String user,String password,String host,String from,List<String> to){
//
//        DateFormat df = new SimpleDateFormat("yyyy年MM月dd日 HH时 ");
//        String startTime = df.format(new Date());
//        String subject = "【非常重要】"+startTime+"应用宝、小米检测引擎检测情况如下";
//        //邮箱内容
//        StringBuffer sb = new StringBuffer();
//        DecimalFormat d = new DecimalFormat("#.00");
//        sb.append("<p>尊敬的客户，您好：</p>"
//                + "<P><SPAN style=\"padding-left: 30px;color: red\">【非常重要】</SPAN>爱加密</SPAN>检测引擎检测指标详情如下：</SPAN></P>"
//
//                + "<p style=\"font-weight: bold;\">小米检测指标如下：</p>"
//                +"<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
//                + "<tr>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下发任务数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">排队下载中数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下载失败数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务排队待检测数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测中数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测失败数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成排队解析入库数量</td>"
//                + "</tr>"
//                + "<tr>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+9+"</td>"
//                + "</tr>"
//                + "</table>"
//
//                + "<br/><p style=\"font-weight: bold;\">小米检测耗时指标如下：</p>"
//                +"<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
//                + "<tr>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">总数</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">30分钟内</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">30~40分钟</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">大于40分钟</td>"
//                + "</tr>"
//                + "<tr>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+7+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+7+"("+44+"%)</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+7+"("+44+"%)</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+7+"("+44+"%)</td>"
//                + "</tr>"
//                + "</table>"
//
//                + "<br/><p style=\"font-weight: bold;\">应用宝检测指标如下：</p>"
//                +"<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">"
//                + "<tr>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下发任务数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">排队下载中数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">下载失败数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务排队待检测数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测中数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">任务检测失败数量</td>"
//                + "   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测完成排队解析入库数量</td>"
//                + "</tr>"
//                + "<tr>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+3+"</td>"
//                + "</tr>"
//                + "</table>"
//                + "<p><br/>请安排人员排查。（若已处理请忽略此信息）</p><p>"
//        );
//        boolean res = false;
//        try {
//            res = sendMail(user, password, host, from, to,subject, sb.toString());
//            System.out.println(res);
//        } catch (Exception e) {
//            e.getMessage();
//        }
//        return res;
//    }


//    public String sendMailTaskDataTimeOutOneHours(String user,String password,String host,String from,String to,List<SendMailVO> dataTask){
//
//        DateFormat df = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分 ");
//        String startTime = df.format(new Date());
//        String subject = "【小米超60分钟没出结果数据】"+startTime+"小米检测引擎检测超时情况如下";
//        //邮箱内容
//        StringBuffer sb = new StringBuffer();
//        sb.append("<p>尊敬的客户，您好：</p>");
//        sb.append("<P><SPAN style=\"padding-left: 30px;color: red\">【检测超时】</SPAN>爱加密</SPAN>检测超时情况如下：</SPAN></P>");
//        sb.append("<p style=\"font-weight: bold;\">小米检测超时如下：</p>");
//        sb.append("<table style=\"table-layout: fixed;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;margin: 0 auto;\">");
//        sb.append("<tr>");
//        sb.append("   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">应用名称</td>");
//        sb.append("   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">MD5</td>");
//        sb.append("   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">业务ID</td>");
//        sb.append("   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">检测耗时</td>");
//        sb.append("   <td style=\"font-weight: bold;width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">当前任务状态</td>");
//        sb.append("</tr>");
//
//        for (SendMailVO vo : dataTask) {
//            sb.append("<tr>");
//            sb.append("   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+vo.getName()+"</td>");
//            sb.append("   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+vo.getMd5()+"</td>");
//            sb.append("   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+vo.getBussinessId()+"</td>");
//            sb.append("   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+vo.getTime()+"分钟</td>");
//            sb.append("   <td style=\"width: 100%;border-collapse: collapse;border-style: solid;border-width: 1px;padding: 0.3em 1em;\">"+DynamicAutoStatusEnum.getItem(vo.getStatus()).getName()+"</td>");
//            sb.append("</tr>");
//        }
//        sb.append("</table>");
//        sb.append("<p><br/>请安排人员排查。（若已处理请忽略此信息）</p><p>");
//
//        String res = null;
//        try {
//            res = sendMails(user, password, host, from, to,
//                    subject, sb.toString());
//            System.out.println(res);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return res;
//    }
}
