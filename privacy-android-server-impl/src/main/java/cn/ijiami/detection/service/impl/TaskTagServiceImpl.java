package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.ApiTaskTagInfoVO;
import cn.ijiami.detection.VO.TaskTagInfoVO;
import cn.ijiami.detection.VO.TaskTagVO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.APITaskTagQuery;
import cn.ijiami.detection.service.api.ITaskTagService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskTagServiceImpl.java
 * @Description 任务标签
 * @createTime 2022年08月08日 18:18:00
 */
@Service
public class TaskTagServiceImpl implements ITaskTagService {

    @Autowired
    private TAssetsMapper tAssetsMapper;

    @Autowired
    private TTaskMapper tTaskMapper;

    @Autowired
    private TTaskTagDictMapper tTaskTagDictMapper;

    @Autowired
    private TTaskTagListMapper tTaskTagListMapper;

    @Autowired
    private TTaskTagInfoMapper tTaskTagInfoMapper;

    @Value("${fastDFS.ip}")
    private String fastDFSIp;

    @Override
    public TaskTagInfoVO getTaskTagList(Long taskId) {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        TTaskTagDict dictQuery = new TTaskTagDict();
        dictQuery.setTerminalType(task.getTerminalType());
        List<TTaskTagDict> dictList = tTaskTagDictMapper.select(dictQuery);
        TTaskTagList query = new TTaskTagList();
        query.setTaskId(taskId);
        List<TTaskTagList> tagList = tTaskTagListMapper.select(query);
        TTaskTagInfo info = tTaskTagInfoMapper.selectByPrimaryKey(taskId);
        TAssets assets = tAssetsMapper.selectByPrimaryKey(task.getAssetsId());

        TaskTagInfoVO infoVO = new TaskTagInfoVO();
        infoVO.setTaskId(taskId);
        infoVO.setAssetsName(assets.getName());
        infoVO.setVersion(assets.getVersion());
        if (Objects.nonNull(info)) {
            infoVO.setDescription(info.getDescription());
        } else {
            infoVO.setDescription(PinfoConstant.DETAILS_EMPTY);
        }
        infoVO.setTagList(dictList.stream().map(dict -> {
            TaskTagVO tagVO = new TaskTagVO();
            tagVO.setTagId(dict.getTagId());
            tagVO.setTagName(dict.getTagName());
            // 是否已选中
            tagVO.setSelected(tagList.stream().anyMatch(tag -> tag.getTagId().equals(dict.getTagId())));
            return tagVO;
        }).collect(Collectors.toList()));
        return infoVO;
    }

    @Transactional
    @Override
    public void saveTaskTag(Long taskId, String description, List<Long> tagIdList) {
        TTaskTagList query = new TTaskTagList();
        query.setTaskId(taskId);
        List<TTaskTagList> tagList = tTaskTagListMapper.select(query);
        List<TTaskTagList> removeList = tagList.stream().filter(tag -> !tagIdList.contains(tag.getTagId())).collect(Collectors.toList());
        List<TTaskTagList> addList = tagIdList.stream().filter(tagId -> tagList.stream().noneMatch(tag -> tag.getTagId().equals(tagId)))
                .map(tagId -> {
                    TTaskTagList taskTag = new TTaskTagList();
                    taskTag.setTaskId(taskId);
                    taskTag.setTagId(tagId);
                    return taskTag;
                }).collect(Collectors.toList());
        TTaskTagInfo infoQuery = new TTaskTagInfo();
        infoQuery.setTaskId(taskId);
        Optional<TTaskTagInfo> tagInfo = tTaskTagInfoMapper.select(infoQuery).stream().findFirst();
        if (tagInfo.isPresent()) {
            tagInfo.get().setDescription(description);
            tagInfo.get().setUpdateTime(new Date());
            tTaskTagInfoMapper.updateByPrimaryKey(tagInfo.get());
        } else {
            TTaskTagInfo info = new TTaskTagInfo();
            info.setTaskId(taskId);
            info.setDescription(description);
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            tTaskTagInfoMapper.insert(info);
        }
        InsertListHelper.insertList(addList, tTaskTagListMapper::insertList);
        removeList.forEach(tag -> tTaskTagListMapper.delete(tag));
    }

    @Override
    public PageInfo<ApiTaskTagInfoVO> findTaskTagByPage(APITaskTagQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<ApiTaskTagInfoVO> infoList = tTaskTagInfoMapper.findInfoByPage(query.getTerminalTypeEnum(), query.getTagIdList());
        return new PageInfo<>(infoList);
    }

}
