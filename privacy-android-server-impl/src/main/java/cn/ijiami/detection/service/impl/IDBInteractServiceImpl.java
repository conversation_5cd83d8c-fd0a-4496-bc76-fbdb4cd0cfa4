package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.ijiami.detection.VO.PersonalActionVO;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.idb.TokenResultVO;
import cn.ijiami.detection.entity.TInteractKey;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.TInteractKeyMapper;
import cn.ijiami.detection.query.idb.TokenQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IDBInteractService;
import cn.ijiami.detection.service.api.IStorageLogService;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IDBInteractServiceImpl.java
 * @Description
 * @createTime 2022年01月12日 16:52:00
 */
@Service
public class IDBInteractServiceImpl implements IDBInteractService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private TInteractKeyMapper interactKeyMapper;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private IStorageLogService storageLogService;

    @Autowired
    private TActionNougatMapper actionNougatMapper;

    @Override
    public boolean invalidToken(String token) {
        return StringUtils.isNotBlank(cacheService.get(token));
    }

    @Override
    public TokenResultVO genToken(TokenQuery query) {
        TInteractKey matchKey = new TInteractKey();
        matchKey.setAccessKey(query.getAccessKey());
        matchKey.setSecretKey(query.getSecretKey());
        matchKey.setStatus(StatusEnum.NORMAL.itemValue());
        if (interactKeyMapper.selectCount(matchKey) == 0) {
            throw new IjiamiRuntimeException("accessKey or secretKey illegal");
        }
        String token = UuidUtil.uuid().replace("-", "");
        long expiresIn = TimeUnit.MINUTES.toMillis(10L);
        cacheService.set(token, query.getAccessKey(), expiresIn, TimeUnit.MILLISECONDS);
        TokenResultVO resultVO = new TokenResultVO();
        resultVO.setToken(token);
        resultVO.setExpiresIn(expiresIn);
        return resultVO;
    }

    @Override
    public String uploadFile(MultipartFile multipartFile, long expireIn) throws IjiamiApplicationException {
        String fileName = multipartFile.getOriginalFilename().replaceAll(" ", "").trim();
        File saveFile = new File(commonProperties.getProperty("ijiami.framework.file.path"), UuidUtil.uuid() + ConstantsUtils.FILE_NAME_SEPARATOR + fileName);
        try {
            FileVO fileVO;
            try {
                multipartFile.transferTo(saveFile);
                fileVO = FileVOUtils.convertFileVOByFile(saveFile);
            } catch (IOException | IllegalStateException e) {
                throw new IjiamiApplicationException("保存文件失败");
            }
            // 上传图片至文件服务器
            FileVO uploadFile = singleFastDfsFileService.instance().storeFile(fileVO);
            if (StringUtils.isBlank(uploadFile.getFilePath())) {
                throw new IjiamiApplicationException("图片上传失败");
            }
            // 图片类型的文件，默认储存2个小时
            if (expireIn > 0) {
                storageLogService.saveStorageLogByExpired(uploadFile.getFilePath(), expireIn);
            }
            return commonProperties.getProperty("fastDFS.ip") + "/" + uploadFile.getFilePath();
        } finally {
            if (saveFile.exists()) {
                saveFile.delete();
            }
        }
    }

    @Override
    public String findActionIdOfPersinal(Integer terminalType) {
        List<PersonalActionVO> actionList = new ArrayList<>();
        //个人信息数据
        actionList.addAll(actionNougatMapper.selectPersonalAction(terminalType));
        //非个人信息的数据
        if(actionList.size() > 0){
            actionList.add(new PersonalActionVO(32001L,"应用自启动"));
            actionList.add(new PersonalActionVO(10009L,"获取移动网络运营商的名字"));
            actionList.add(new PersonalActionVO(17007L,"获取SD卡根目录"));
            actionList.add(new PersonalActionVO(17001L,"尝试写入SDCard卡数据"));
            actionList.add(new PersonalActionVO(17002L,"尝试读取SDCard卡数据"));
            actionList.add(new PersonalActionVO(17003L,"尝试写入SDCard卡数据"));
            actionList.add(new PersonalActionVO(17004L,"尝试读取SDCard卡数据"));
            actionList.add(new PersonalActionVO(17005L,"打开文件读取流"));
            actionList.add(new PersonalActionVO(17006L,"尝试写入SDCard数据"));
        }
        JSONObject json = new JSONObject();
        json.put("actionNougatList",actionList);
        return json.toJSONString();
    }
}
