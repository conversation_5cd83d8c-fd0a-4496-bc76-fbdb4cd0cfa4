package cn.ijiami.detection.service.impl;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.ijiami.detection.service.api.AIToolService;
import cn.ijiami.detection.service.api.vo.AISdkExtractResponse;
import cn.ijiami.detection.service.api.vo.AISdkExtractVO;
import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AIToolServiceImpl.java
 * @Description ai对sdk提取
 * @createTime 2025年01月20日 11:17:00
 */
@Slf4j
@Service
public class AIToolServiceImpl implements AIToolService {

    @Value("${ijiami.manager.ai.url:}")
    private String aiServerUrl;

    @Override
    public List<AISdkExtractVO> sdkExtract(String text) {
        String baseUrl = CommonUtil.extractBaseUrl(aiServerUrl);
        if (StringUtils.isBlank(baseUrl)) {
            log.info("ai服务url不存在");
            return Collections.emptyList();
        }
        try (HttpResponse httpResponse = HttpRequest.post(baseUrl + "/privacy_policy/sdk/information_extraction")
                .timeout(HttpGlobalConfig.getTimeout())
                .form("text", text)
                .execute()) {
            if (httpResponse.getStatus() == HttpStatus.HTTP_OK) {
                AISdkExtractResponse response = CommonUtil.jsonToBean(httpResponse.body(), AISdkExtractResponse.class);
                if (response != null && response.getData() != null) {
                    return response.getData();
                } else {
                    return Collections.emptyList();
                }
            } else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("sdk提取异常", e);
            return Collections.emptyList();
        }
    }

}
