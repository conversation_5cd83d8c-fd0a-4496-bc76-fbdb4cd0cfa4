package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.IosDetectStackInfo;
import cn.ijiami.detection.bean.IosExecutable;
import cn.ijiami.detection.service.api.IosSdkLibraryService;
import com.ocpframework.sdk.detect.enginer.DetectEnginer;
import com.ocpframework.sdk.detection.vo.DiscernVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IosSdkLibraryServiceImpl implements IosSdkLibraryService {

    @Value("${ijiami.sdk.library.path}")
    private String iosSdkLibraryPath;

    private DetectEnginer enginer;

    @PostConstruct
    private void init() {
        enginer = new DetectEnginer();
        enginer.loadDbData(iosSdkLibraryPath);
    }

    @Override
    public List<DiscernVO> findSdkLibraryInStackInfoList(List<IosDetectStackInfo> stackInfoList) {
        return stackInfoList.stream()
                .map(stackInfo -> {
                    try {
                        return enginer.startDetect(stackInfo.getId(), stackInfo.getStackInfo());
                    } catch (Exception e) {
                        // 报错说明匹配失败，没有数据
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public DiscernVO findSdkLibraryByStackInfo(String stackInfo) {
        try {
            return enginer.startDetect("1", stackInfo);
        } catch (Exception e) {
            log.error("ios 查找sdk失败 {}", e.getMessage(), e);
            DiscernVO discernVO = new DiscernVO();
            discernVO.setDumpApis(Collections.emptyList());
            discernVO.setId(StringUtils.EMPTY);
            return discernVO;
        }
    }
}
