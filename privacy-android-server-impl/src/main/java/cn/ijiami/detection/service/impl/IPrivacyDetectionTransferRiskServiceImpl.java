package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_POLICY;
import static cn.ijiami.detection.constant.PinfoConstant.BAISHENG_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.BASE_INFO_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.NEW_ANDROID_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.NEW_IOS_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.OLD_ANDROID_ITEMS;
import static cn.ijiami.detection.constant.PinfoConstant.OLD_IOS_ITEMS;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.VO.PersonalInfoRiskDetailVO;
import cn.ijiami.detection.VO.PrivacyDetectionResultVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.XiaomiAppStoreDetectItemVO;
import cn.ijiami.detection.VO.detection.AppStoreComplianceCheckVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.detection.statistical.ChildItemCommonCountVO;
import cn.ijiami.detection.VO.detection.statistical.DetectionChildResultDetailVO;
import cn.ijiami.detection.VO.detection.statistical.ItemCountVO;
import cn.ijiami.detection.VO.sdk.SdkContrastVo;
import cn.ijiami.detection.entity.TDetectionItem;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TSdkCheckList;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TXiaomiAppStoreDetectItem;
import cn.ijiami.detection.entity.TXiaomiAppStoreDetectResult;
import cn.ijiami.detection.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.PrivacyDetectionResultEnum;
import cn.ijiami.detection.enums.PrivacyPolicyItemNoEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultCategoryEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultStatusEnum;
import cn.ijiami.detection.enums.SdkContrastResultEnum;
import cn.ijiami.detection.enums.SdkSourceEnum;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TDetectionItemMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyItemMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyResultMapper;
import cn.ijiami.detection.mapper.TSdkCheckListMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectItemMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectResultMapper;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.IPrivacyDetectionTransferRiskService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.RiskUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @describtion 个人隐私检测  个人信息传输风险 service实现类
 * @date 2019/3/25
 */
@Service
public class IPrivacyDetectionTransferRiskServiceImpl implements IPrivacyDetectionTransferRiskService {

    private static final Logger LOG = LoggerFactory.getLogger(IPrivacyDetectionTransferRiskServiceImpl.class);

    public static final String ITEM_0806_SAFE_DESC = "静态检测暂未发现应用存在境外IP地址。";

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private TDetectionItemMapper tDetectionItemMapper;

    @Autowired
    private ICommonMongodbService mongodbService;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private TPrivacyPolicyResultMapper privacyPolicyResultMapper;

    @Autowired
    private TTaskMapper tTaskMapper;

    @Autowired
    private TPrivacyPolicyItemMapper privacyPolicyItemMapper;
    @Lazy
    @Autowired
    private IPrivacyDetectionService privacyDetectionService;

    @Autowired
    private TSdkCheckListMapper sdkCheckListMapper;

    @Autowired
    private TXiaomiAppStoreDetectItemMapper xiaomiAppStoreDetectItemMapper;

    @Autowired
    private TXiaomiAppStoreDetectResultMapper xiaomiAppStoreDetectResultMapper;

    @Autowired()
    @Qualifier("redisDistributedLock")
    private DistributedLockService distributedLockService;

    @Autowired
    private ITaskService taskService;

    @Resource
    private TSdkLibraryMapper sdkLibraryMapper;

    @Override
    public PrivacyDetectionResultVO getResultFor(String documentId, String itemNo, Integer terminalType) {
        PrivacyDetectionResultVO resultVO = null;
        if (terminalType == 1) {
            List<PrivacyDetectionResultVO> list = getResultForList(documentId, terminalType);
            if (list == null || list.size() == 0) {
                return resultVO;
            }
            for (PrivacyDetectionResultVO privacyDetectionResultVO : list) {
				if("itemNo".equals(privacyDetectionResultVO.getItemNo())) {
					return privacyDetectionResultVO;
				}
			}
    	}else {
    		resultVO = getResult(documentId, itemNo);
            if (resultVO != null) {
                List<DetectionItemVO> detectionItems = tDetectionItemMapper.selectByItemNoVO(itemNo, terminalType);
                resultVO.setItemNo(itemNo);
                resultVO.setPurpose(detectionItems.get(0).getPurpose());
                resultVO.setSolution(detectionItems.get(0).getSolution());
                resultVO.setName(detectionItems.get(0).getName());
                resultVO.setTypeName(detectionItems.get(0).getDetectionItemName());
                if (resultVO.getResult() == null) {
                    resultVO.setResult("未知");
                }
            }
    	}
        return resultVO;
    }

    @Override
    public boolean haveResult(String documentId,List<String> itemNoList) {
        TaskDetailVO taskDetailVO = mongoTemplate.findById(documentId, TaskDetailVO.class,"taskDetailVO");
        return RiskUtils.haveRiskItem(taskDetailVO, itemNoList);
    }

    @Override
    public boolean haveResult(TaskDetailVO taskDetailVO,List<String> itemNoList) {
        return RiskUtils.haveRiskItem(taskDetailVO, itemNoList);
    }

    @Override
	public List<PrivacyDetectionResultVO> getResultForList(String documentId, Integer terminalType) {
    	List<PrivacyDetectionResultVO> list = getResultList(documentId);
    	if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
    	for (PrivacyDetectionResultVO privacyDetectionResultVO : list) {
            List<DetectionItemVO> detectionItems = tDetectionItemMapper.selectByItemNoVO(privacyDetectionResultVO.getItemNo(), terminalType);
            if (!detectionItems.isEmpty()) {
                privacyDetectionResultVO.setItemNo(privacyDetectionResultVO.getItemNo());
                privacyDetectionResultVO.setPurpose(detectionItems.get(0).getPurpose());
                privacyDetectionResultVO.setSolution(detectionItems.get(0).getSolution());
                privacyDetectionResultVO.setName(detectionItems.get(0).getName());
                privacyDetectionResultVO.setTypeName(detectionItems.get(0).getDetectionItemName());
                if (privacyDetectionResultVO.getResult() == null) {
                    privacyDetectionResultVO.setResult("未知");
                }
            }
		}
		return list;
	}

    @Override
    public PrivacyDetectionResultVO getResultFor0803(String documentId) {
        PrivacyDetectionResultVO resultVO = getResult(documentId, "0803");
        if (resultVO != null) {
            TDetectionItem detectionItem = tDetectionItemMapper.selectByItemNo("0803", 1).get(0);
            resultVO.setPurpose(detectionItem.getPurpose());
            resultVO.setSolution(detectionItem.getSolution());
        }
        return resultVO;
    }

    @Override
    public PrivacyDetectionResultVO getResultFor0705(String documentId) {
        PrivacyDetectionResultVO resultVO = getResult(documentId, "0705");
        if (resultVO != null) {
            TDetectionItem detectionItem = tDetectionItemMapper.selectByItemNo("0705", 1).get(0);
            resultVO.setPurpose(detectionItem.getPurpose());
            resultVO.setSolution(detectionItem.getSolution());
        }
        return resultVO;
    }

    @Override
    public Map<String, Object> getTransferDetail(String documentId) {
        TaskDetailVO taskDetailVO = mongodbService.findByDocumentId(documentId);
        PrivacyDetectionResultVO vo1 = null;
        PrivacyDetectionResultVO vo2 = null;
        PrivacyDetectionResultVO vo3 = null;
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
//            vo1 = getResultFor(documentId, "0806", TerminalTypeEnum.ANDROID.getValue());
//            vo2 = getResultFor(documentId, "0803", TerminalTypeEnum.ANDROID.getValue());
//            vo3 = getResultFor(documentId, "0705", TerminalTypeEnum.ANDROID.getValue());
            List<PrivacyDetectionResultVO> list = getResultForList(documentId, TerminalTypeEnum.ANDROID.getValue());
        	if(list != null && list.size()>0) {
        		for (PrivacyDetectionResultVO privacyDetectionResultVO : list) {
					if("0806".equals(privacyDetectionResultVO.getItemNo())){
						vo1 = privacyDetectionResultVO;
					}
					if("0803".equals(privacyDetectionResultVO.getItemNo())){
						vo2 = privacyDetectionResultVO;
					}
					if("0705".equals(privacyDetectionResultVO.getItemNo())){
						vo3 = privacyDetectionResultVO;
					}
				}
        	}

        } else {
            vo1 = getResultFor(documentId, "0602", TerminalTypeEnum.IOS.getValue());
            vo2 = getResultFor(documentId, "0601", TerminalTypeEnum.IOS.getValue());
            vo3 = getResultFor(documentId, "0205", TerminalTypeEnum.IOS.getValue());
        }

        List<PrivacyDetectionResultVO> list = new ArrayList<>();
        list.add(vo1);
        list.add(vo2);
        list.add(vo3);
        boolean flag = false;//false  代表没有风险
        for (PrivacyDetectionResultVO vo : list) {
            if (vo != null && !"正常".equals(vo.getResult())) {
                flag = true;
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("flag", flag);
        map.put("list", list);
        return map;
    }

    @Override
    public PersonalInfoRiskDetailVO getPersonalInfoRiskDetail(String documentId) {
        TTask task = tTaskMapper.findByDocumentId(documentId);
        TaskDetailVO taskDetailVO = mongoTemplate.findById(documentId, TaskDetailVO.class,"taskDetailVO");
        List<PrivacyDetectionResultVO> list = new ArrayList<>();
        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            if (haveResult(taskDetailVO, NEW_IOS_ITEMS)) {
                for (String itemNo: NEW_IOS_ITEMS) {
                    list.add(getResultFor(documentId, itemNo, task.getTerminalType().getValue()));
                }
            } else {
                for (String itemNo: OLD_IOS_ITEMS) {
                    list.add(getResultFor(documentId, itemNo, task.getTerminalType().getValue()));
                }
            }
        } else {
            list = getResultForList(documentId, task.getTerminalType().getValue());
        }
        List<TDetectionItem> itemInfoList = tDetectionItemMapper.findByTerminalType(task.getTerminalType().getValue());
        boolean flag = false;//false  代表没有风险
        for (PrivacyDetectionResultVO vo : list) {
            if (vo != null && !"正常".equals(vo.getResult())) {
                flag = true;
            }
            for (TDetectionItem detectionItem : itemInfoList) {
                if (vo !=null && StringUtils.equals(detectionItem.getItemNo(), vo.getItemNo())) {
                    vo.setGrade(detectionItem.getGrade());
                    vo.setDetectionItemType(detectionItem.getDetectionItemType());
                    break;
                }
            }
        }
        List<String> disableAIList = new ArrayList<>();
        List<DetectionChildResultDetailVO> detectionResultDetailVOList;
        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
            disableAIList.addAll(OLD_ANDROID_ITEMS.stream().filter(item -> !NEW_ANDROID_ITEMS.contains(item)).collect(Collectors.toList()));
            detectionResultDetailVOList = privacyDetectionService.getDetectionDetail(documentId);
        } else if (task.getTerminalType() == TerminalTypeEnum.HARMONY){
            detectionResultDetailVOList = privacyDetectionService.getHarmonyDetectionDetail(documentId);
        } else if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            disableAIList.addAll(OLD_IOS_ITEMS.stream().filter(item -> !NEW_IOS_ITEMS.contains(item)).collect(Collectors.toList()));
            detectionResultDetailVOList = Collections.emptyList();
        } else {
            detectionResultDetailVOList = Collections.emptyList();
        }
        disableAI(list, disableAIList);
        if(detectionResultDetailVOList!=null && detectionResultDetailVOList.size()>0) {
            for (PrivacyDetectionResultVO res : list) {
                for (DetectionChildResultDetailVO child : detectionResultDetailVOList) {
                    if(res.getItemNo().equals(child.getItemNo())) {
                        res.setDetail2List(child.getDetail2List());
                        res.setDetailList(child.getDetailList());
                        res.setSafe_num(child.getSafe_num());
                        res.setUnsafe_num(child.getUnsafe_num());
                        res.setTotal_num(child.getTotal_num());
                    }
                }
            }
        }
        return PersonalInfoRiskDetailVO.create(task.getTaskId(), flag, list, task.getTerminalType());
    }

    private void disableAI(List<PrivacyDetectionResultVO> list, List<String> disenableList) {
        for (PrivacyDetectionResultVO resultVOS:list) {
            resultVOS.setAiEnabled(!disenableList.contains(resultVOS.getItemNo()));
        }
    }

    @Override
    public Optional<TPrivacyPolicyResult> privacyPolicyDetail(Long taskId) {
        List<TPrivacyPolicyResult> result = privacyPolicyResultMapper.findByTaskId(taskId);
        return findByItemNo(result, PrivacyPolicyItemNoEnum.PRIVACY_POLICY_DETAIL);
    }

    @Override
    public List<TPrivacyPolicyResult> getLawInfoRiskDetail(String documentId) {
        long taskId = tTaskMapper.findTaskIdByDocumentId(documentId);
        List<TPrivacyPolicyResult> result = privacyPolicyResultMapper.findByTaskId(taskId);
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        // 静态检测未完成直接返回
        if (task.getTaskTatus() != DetectionStatusEnum.DETECTION_OVER) {
            return result;
        }
        TSdkCheckList record = new TSdkCheckList();
        record.setTaskId(taskId);
        List<TSdkCheckList> sdkCheckList = sdkCheckListMapper.select(record);
        if (!sdkCheckList.isEmpty()) {
            Optional<TPrivacyPolicyResult> policyDetail = findByItemNo(result, PrivacyPolicyItemNoEnum.PRIVACY_POLICY_DETAIL);
            if (policyDetail.isPresent()) {
                String sdkJson = sdkCheckList.get(0).getSdkList();
                if (StringUtils.isNotBlank(sdkJson)) {
                    List<CheckList.Row> rowList = CommonUtil.jsonToBean(sdkJson, new TypeReference<List<CheckList.Row>>() {});
                    if (!CollectionUtils.isEmpty(rowList)) {
                        policyDetail.get().setThirdPartySharingChecklist(rowList);
                    } else {
                        policyDetail.get().setThirdPartySharingText(sdkCheckList.get(0).getOriginalText());
                    }
                } else {
                    policyDetail.get().setThirdPartySharingText(sdkCheckList.get(0).getOriginalText());
                }
            }
        }
        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
            // 静态检测完成 一揽子授权是否存在
            Optional<TPrivacyPolicyResult> optional = findByItemNo(result, PrivacyPolicyItemNoEnum.PERMISSION);
            if (!optional.isPresent()) {
                String key = KEY_POLICY + taskId;
                try {
                    if (distributedLockService.tryLock(key, TimeUnit.SECONDS.toMillis(5))) {
                        // 获得锁后再检查一次
                        if (findByItemNo(privacyPolicyResultMapper.findByTaskId(taskId), PrivacyPolicyItemNoEnum.PERMISSION).isPresent()) {
                            TPrivacyPolicyResult permissionResult = buildPrivacyPolicyItem0001(taskId, documentId, task.getTerminalType().getValue());
                            privacyPolicyResultMapper.insert(permissionResult);
                            result.add(permissionResult);
                        }
                    }
                } finally {
                    distributedLockService.unlock(key);
                }
            }
        }
        return result;
    }

    @Override
    public AppStoreComplianceCheckVO getAppStoreComplianceCheck(Long taskId) {
        AppStoreComplianceCheckVO storeComplianceCheckVO = new AppStoreComplianceCheckVO();
        List<TPrivacyPolicyResult> tPrivacyPolicyResultList = privacyPolicyResultMapper.findAppStoreByTaskId(taskId);
        // 填充小米应用商店专项检测数据
        Optional<TPrivacyPolicyResult> xiaomiAppStoreOptional = findByItemNo(tPrivacyPolicyResultList, PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_SPECIALIZED);
        if (xiaomiAppStoreOptional.isPresent()) {
            TPrivacyPolicyResult xiaomiAppStoreInfoResult = xiaomiAppStoreOptional.get();
            xiaomiAppStoreInfoResult.setXiaomiAppStoreDetectItemList(buildXiaomiAppstoreDetect(taskId));
        }
        storeComplianceCheckVO.setCheckList(tPrivacyPolicyResultList);
        TTask task = taskService.findById(taskId);
        if (Objects.nonNull(task) && task.getTerminalType() == TerminalTypeEnum.IOS) {
            storeComplianceCheckVO.setUndeclaredList(taskService.getIosSDKList(task.getApkDetectionDetailId(), taskId).getUndeclaredList());
            storeComplianceCheckVO.setAppStorePrivacyApiList(privacyDetectionService.getAppStorePrivacyApiList(task));
        }
        return sdkContrastList(storeComplianceCheckVO, task);
    }
    
    
    
    ///////////////////////////////aaaaaaaaaaaaaaaaa////////////////////////
    
    
    private AppStoreComplianceCheckVO sdkContrastList(AppStoreComplianceCheckVO vo, TTask task) {
        if (task == null) return vo;

        // 获取需要对比的检查项 0017
        Optional<TPrivacyPolicyResult> optional = vo.getCheckList().stream()
                .filter(item -> PrivacyPolicyItemNoEnum.SDK_CONTRAST.itemNo.equals(item.getItemNo()))
                .findFirst();
        if (!optional.isPresent()) return vo;

        TPrivacyPolicyResult result = optional.get();
        List<SdkVO> sdkVoList = getSdkListSafely(task);
        if (sdkVoList == null || sdkVoList.isEmpty()) return vo;

        List<SdkContrastVo> contrastList = new ArrayList<>();

        // 包名对比：包名相同但名称不同
        processPackageNameContrast(sdkVoList, result, contrastList, task);

        // 名称对比：名称相同但包名不同，并获取名称映射
        Map<String, List<TSdkLibrary>> nameMap = processSdkNameContrast(sdkVoList, result, contrastList, task);

        // 完全匹配对比：名称和包名全部相同
        processFullMatchContrast(sdkVoList, result, contrastList, nameMap);

        // 更新结果
        result.setSdkContrastList(contrastList);
        updateCheckListStatus(result, contrastList);
        updateCheckList(vo, result);

        return vo;
    }

    // 修改后的方法，返回名称映射
    private Map<String, List<TSdkLibrary>> processSdkNameContrast(List<SdkVO> sdkVoList,
                                            TPrivacyPolicyResult result,
                                            List<SdkContrastVo> contrastList, TTask task) {
        // 获取所有APP的SDK名称
        Set<String> allSdkNames = sdkVoList.stream()
                .map(SdkVO::getName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (allSdkNames.isEmpty()) return Collections.emptyMap();

        // 根据名称查询全国库
        List<TSdkLibrary> nationalByName = sdkLibraryMapper.findInSdkNameSource(
                new ArrayList<>(allSdkNames),
                SdkSourceEnum.CAICTAC.getValue(),
                task.getTerminalType().getValue()
        );

        // 建立名称映射（名称 -> 全国库SDK列表）
        Map<String, List<TSdkLibrary>> nameMap = nationalByName.stream()
                .collect(Collectors.groupingBy(
                        TSdkLibrary::getName,
                        Collectors.toList()
                ));

        // 遍历APP中的每个SDK
        sdkVoList.forEach(appSdk -> {
            String appName = appSdk.getName();
            if (appName == null) return;

            // 找到名称相同的全国库SDK
            List<TSdkLibrary> matchedNational = nameMap.getOrDefault(appName, Collections.emptyList());

            matchedNational.forEach(nationalSdk -> {
                Set<String> nationalPackages = getNationalSdkPackages(nationalSdk);
                Set<String> appPackages = getAppSdkPackages(appSdk);

                // 名称相同但包名完全不同
                if (Collections.disjoint(nationalPackages, appPackages)) {
                    contrastList.add(createContrastVo(
                            appSdk,
                            nationalSdk,
                            "名称匹配",
                            result,
                            SdkContrastResultEnum.ERROR
                    ));
                }
            });
        });

        return nameMap;
    }

    // 新增方法：处理完全匹配的合规情况
    private void processFullMatchContrast(List<SdkVO> sdkVoList,
                                          TPrivacyPolicyResult result,
                                          List<SdkContrastVo> contrastList,
                                          Map<String, List<TSdkLibrary>> nameMap) {
        sdkVoList.forEach(appSdk -> {
            String appName = appSdk.getName();
            if (appName == null) return;

            Set<String> appPackages = getAppSdkPackages(appSdk);
            if (appPackages.isEmpty()) return;

            List<TSdkLibrary> nationalSdks = nameMap.getOrDefault(appName, Collections.emptyList());
            nationalSdks.forEach(nationalSdk -> {
                Set<String> nationalPackages = getNationalSdkPackages(nationalSdk);
                if (nationalPackages.equals(appPackages)) {
                    contrastList.add(createContrastVo(
                            appSdk,
                            nationalSdk,
                            "完全匹配",
                            result,
                            SdkContrastResultEnum.OK
                    ));
                }
            });
        });
    }

    // 修改后的方法，支持传入结果类型
    private SdkContrastVo createContrastVo(SdkVO appSdk,
                                           TSdkLibrary nationalSdk,
                                           String matchType,
                                           TPrivacyPolicyResult result,
                                           SdkContrastResultEnum resultEnum) {
        SdkContrastVo vo = new SdkContrastVo();
        vo.setSdkName(appSdk.getName());
        vo.setSdkPackageName(String.join(",", getAppSdkPackages(appSdk)));
        vo.setOfficialSdkName(nationalSdk.getName());
        vo.setOfficialSdkPackageName(String.join(",", getNationalSdkPackages(nationalSdk)));
        vo.setMatchType(matchType);
        vo.setResult(resultEnum);
        vo.setRule("精准匹配");
        return vo;
    }

    // 修改后的状态更新逻辑
    private void updateCheckListStatus(TPrivacyPolicyResult result,
                                       List<SdkContrastVo> contrastList) {
        boolean hasError = contrastList.stream()
                .anyMatch(vo -> SdkContrastResultEnum.ERROR.equals(vo.getResult()));
        if (hasError) {
            result.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
        } else if (!contrastList.isEmpty()) {
            result.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
        }
    }
    
    //////////////////////////////bbbbbbbbbbbbbbbbb////////////////////////


//    private AppStoreComplianceCheckVO sdkContrastList(AppStoreComplianceCheckVO vo, TTask task) {
//        if (task == null) return vo;
//
//        // 获取需要对比的检查项 0017
//        Optional<TPrivacyPolicyResult> optional = vo.getCheckList().stream()
//                .filter(item -> PrivacyPolicyItemNoEnum.SDK_CONTRAST.itemNo.equals(item.getItemNo()))
//                .findFirst();
//        if (!optional.isPresent()) return vo;
//
//        TPrivacyPolicyResult result = optional.get();
//        List<SdkVO> sdkVoList = getSdkListSafely(task);
//        if (sdkVoList == null || sdkVoList.isEmpty()) return vo;
//
//        List<SdkContrastVo> contrastList = new ArrayList<>();
//
//        // 包名对比：包名相同但名称不同
//        processPackageNameContrast(sdkVoList, result, contrastList, task);
//
//        // 名称对比：名称相同但包名不同
//        processSdkNameContrast(sdkVoList, result, contrastList, task);
//
//        // 更新结果
//        result.setSdkContrastList(contrastList);
//        updateCheckListStatus(result, contrastList);
//        updateCheckList(vo, result);
//
//        return vo;
//    }
//
    // 安全获取SDK列表
    private List<SdkVO> getSdkListSafely(TTask task) {
        try {
            return privacyDetectionService.getSDKList(task.getApkDetectionDetailId());
        } catch (IjiamiApplicationException e) {
            LOG.error("获取SDK列表失败", e);
            return Collections.emptyList();
        }
    }

    // 包名对比处理
    private void processPackageNameContrast(List<SdkVO> sdkVoList,
                                            TPrivacyPolicyResult result,
                                            List<SdkContrastVo> contrastList, TTask task) {
        // 获取所有包名（包含多包名）
        Set<String> allPackageNames = sdkVoList.stream()
                .filter(sdk -> sdk.getPackageList() != null)
                .flatMap(sdk -> sdk.getPackageList().stream())
                .map(TSdkLibraryPackage::getPackageName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (allPackageNames.isEmpty()) return;

        // 根据包名查询全国库
        List<TSdkLibrary> nationalByPackage = sdkLibraryMapper.findInPackageNameSource(
                new ArrayList<>(allPackageNames),
                SdkSourceEnum.CAICTAC.getValue(),
                task.getTerminalType().getValue()
        );

        // 建立包名映射（包名 -> 全国库SDK列表）
        Map<String, List<TSdkLibrary>> packageMap = nationalByPackage.stream()
                .filter(sdk -> sdk.getPackageList() != null)
                .flatMap(sdk -> sdk.getPackageList().stream()
                        .map(pkg -> new AbstractMap.SimpleEntry<>(pkg.getPackageName(), sdk)))
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Entry::getKey,
                        Collectors.mapping(Entry::getValue, Collectors.toList())
                ));

        // 遍历APP中的每个SDK
        sdkVoList.forEach(appSdk -> {
            // 获取当前SDK的所有包名
            Set<String> appPackages = getAppSdkPackages(appSdk);

            appPackages.forEach(pkg -> {
                // 找到匹配的全国库SDK
                List<TSdkLibrary> matchedNational = packageMap.getOrDefault(pkg, Collections.emptyList());

                matchedNational.forEach(nationalSdk -> {
                    // 包名相同但名称不同
                    if (!nationalSdk.getName().equals(appSdk.getName())) {
                        contrastList.add(createContrastVo(
                                appSdk,
                                nationalSdk,
                                "包名匹配",
                                result
                        ));
                    }
                });
            });
        });
    }
//
//    // 名称对比处理
//    private void processSdkNameContrast(List<SdkVO> sdkVoList,
//                                        TPrivacyPolicyResult result,
//                                        List<SdkContrastVo> contrastList, TTask task) {
//        // 获取所有APP的SDK名称
//        Set<String> allSdkNames = sdkVoList.stream()
//                .map(SdkVO::getName)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toSet());
//
//        if (allSdkNames.isEmpty()) return;
//
//        // 根据名称查询全国库
//        List<TSdkLibrary> nationalByName = sdkLibraryMapper.findInSdkNameSource(
//                new ArrayList<>(allSdkNames),
//                SdkSourceEnum.CAICTAC.getValue(),
//                task.getTerminalType().getValue()
//        );
//
//        // 建立名称映射（名称 -> 全国库SDK列表）
//        Map<String, List<TSdkLibrary>> nameMap = nationalByName.stream()
//                .collect(Collectors.groupingBy(
//                        TSdkLibrary::getName,
//                        Collectors.toList()
//                ));
//
//        // 遍历APP中的每个SDK
//        sdkVoList.forEach(appSdk -> {
//            String appName = appSdk.getName();
//            if (appName == null) return;
//
//            // 找到名称相同的全国库SDK
//            List<TSdkLibrary> matchedNational = nameMap.getOrDefault(appName, Collections.emptyList());
//
//            matchedNational.forEach(nationalSdk -> {
//                // 获取全国库SDK的所有包名
//                Set<String> nationalPackages = getNationalSdkPackages(nationalSdk);
//                // 获取APP SDK的所有包名
//                Set<String> appPackages = getAppSdkPackages(appSdk);
//
//                // 名称相同但包名完全不同
//                if (Collections.disjoint(nationalPackages, appPackages)) {
//                    contrastList.add(createContrastVo(
//                            appSdk,
//                            nationalSdk,
//                            "名称匹配",
//                            result
//                    ));
//                }
//            });
//        });
//    }
//
    //  获取APP SDK的包名集合
    private Set<String> getAppSdkPackages(SdkVO sdk) {
        return sdk.getPackageList() == null ?
                Collections.emptySet() :
                sdk.getPackageList().stream()
                        .map(TSdkLibraryPackage::getPackageName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
    }

    // 获取全国SDK的包名集合
    private Set<String> getNationalSdkPackages(TSdkLibrary sdk) {
        return sdk.getPackageList() == null ?
                Collections.emptySet() :
                sdk.getPackageList().stream()
                        .map(TSdkLibraryPackage::getPackageName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
    }

    // 创建对比VO对象
    private SdkContrastVo createContrastVo(SdkVO appSdk,
                                           TSdkLibrary nationalSdk,
                                           String matchType,
                                           TPrivacyPolicyResult result) {
        SdkContrastVo vo = new SdkContrastVo();
        vo.setSdkName(appSdk.getName());
        vo.setSdkPackageName(String.join(",", getAppSdkPackages(appSdk)));

        // 全国库信息
        vo.setOfficialSdkName(nationalSdk.getName());
        vo.setOfficialSdkPackageName(String.join(",", getNationalSdkPackages(nationalSdk)));

        // 对比信息
        vo.setMatchType(matchType);
        vo.setResult(SdkContrastResultEnum.ERROR);
        vo.setRule("精准匹配");

        // 更新整体状态为风险
        result.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);

        return vo;
    }

    // 更新检查列表状态
//    private void updateCheckListStatus(TPrivacyPolicyResult result,
//                                       List<SdkContrastVo> contrastList) {
//        if (!contrastList.isEmpty()) {
//            result.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
//        }
//    }

    // 更新检查列表
    private void updateCheckList(AppStoreComplianceCheckVO vo, TPrivacyPolicyResult newResult) {
        List<TPrivacyPolicyResult> checkList = vo.getCheckList();
        int index = checkList.indexOf(newResult);
        if (index >= 0) {
            checkList.set(index, newResult);
        }
        vo.setCheckList(checkList);
    }

    protected List<XiaomiAppStoreDetectItemVO> buildXiaomiAppstoreDetect(Long taskId) {
        TXiaomiAppStoreDetectItem query = new TXiaomiAppStoreDetectItem();
        query.setStatus(StatusEnum.NORMAL.itemValue());
        List<TXiaomiAppStoreDetectItem> detectItems = xiaomiAppStoreDetectItemMapper.select(query);
        TXiaomiAppStoreDetectResult resultQuery = new TXiaomiAppStoreDetectResult();
        resultQuery.setTaskId(taskId);
        List<TXiaomiAppStoreDetectResult> detectResults = xiaomiAppStoreDetectResultMapper.select(resultQuery);
        List<XiaomiAppStoreDetectItemVO> itemVOList = new ArrayList<>();
        for (TXiaomiAppStoreDetectItem item:detectItems) {
            XiaomiAppStoreDetectItemVO itemVO = new XiaomiAppStoreDetectItemVO();
            itemVO.setName(item.getName());
            itemVO.setDescription(item.getDescription());
            itemVO.setItemKey(item.getItemKey());
            itemVO.setApplyRole(item.getApplyRole());
            itemVO.setProperties(item.getProperties());
            itemVO.setPurpose(item.getPurpose());
            Optional<TXiaomiAppStoreDetectResult> result = detectResults.stream().filter(r -> r.getItemId().equals(item.getId())).findFirst();
            if (result.isPresent()) {
            	itemVO.setStackInfo(result.get().getStackInfo());
                itemVO.setHavePersonalAction(result.get().getHavePersonalAction());
            } else {
                itemVO.setHavePersonalAction(false);
            }
            itemVOList.add(itemVO);
        }
        return itemVOList;
    }

    private boolean havePersonalAction(List<XiaomiAppStoreDetectItemVO> detectItems) {
        for (XiaomiAppStoreDetectItemVO item:detectItems) {
            if (item.getHavePersonalAction()) {
                return true;
            }
        }
        return false;
    }

    private Optional<TPrivacyPolicyResult> findByItemNo(List<TPrivacyPolicyResult> result, PrivacyPolicyItemNoEnum itemNoEnum) {
        return result.stream().filter(r -> StringUtils.equals(r.getItemNo(), itemNoEnum.itemNo)).findFirst();
    }

    private TPrivacyPolicyResult buildPrivacyPolicyItem0001(Long taskId, String documentId, int terminalType) {
        // 新增一揽子授权
        Map<String, TPrivacyPolicyItem> itemMap = new HashMap<>();
        for (TPrivacyPolicyItem privacyPolicyItem : privacyPolicyItemMapper.findByTerminalType(terminalType)) {
            itemMap.put(privacyPolicyItem.getItem_no(), privacyPolicyItem);
        }
        TaskDetailVO taskDetailVo = mongodbService.findByDocumentId(documentId);
        TPrivacyPolicyResult permissionResult = new TPrivacyPolicyResult();
        permissionResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
        permissionResult.setTaskId(taskId);
        permissionResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.PERMISSION.itemNo).getId());
        int targetSdkVersion = 0;
        net.sf.json.JSONArray array = net.sf.json.JSONArray.fromObject(taskDetailVo.getDetection_result());
        for (int i = 0; i < array.size(); i++) {
            try {
                net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(array.get(i));
                // 获取基础信息
                if (json.get("detection_item_id") != null && json.get("detection_item_id").equals("0101")) {
                    String result_content = json.getString("result_content");
                    result_content = result_content.replaceAll("NumberLong", "");
                    result_content = result_content.replaceAll("\\(", "");
                    result_content = result_content.replaceAll("\\)", "");
                    net.sf.json.JSONObject childJson = net.sf.json.JSONObject.fromObject(result_content);
                    if(childJson.get("targetSdkVersion") != null) {
                        targetSdkVersion = childJson.getInt("targetSdkVersion");
                    }
                    break;
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        if (targetSdkVersion >= 23) {
            permissionResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
        } else {
            permissionResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
        }
        permissionResult.setDetailResult("该应用targetSdkVersion=" + targetSdkVersion);
        return permissionResult;
    }

    /**
     * @param documentId 文档id
     * @param itemNo     检测项编号
     * @return cn.ijiami.detection.VO.PrivacyDetectionResultVO
     * @description 根据文档id和检测项编号 查询检测结果
     * <AUTHOR>
     * @date 2019/3/25
     */
    private PrivacyDetectionResultVO getResult(String documentId, String itemNo) {
        TaskDetailVO taskDetailVO = mongodbService.findByDocumentId(documentId);
        PrivacyDetectionResultVO vo = null;
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
            Aggregation aggregation = Aggregation.newAggregation(Aggregation.project("_id", "detection_result"),
                    Aggregation.unwind("$detection_result"),
                    Aggregation.match(Criteria.where("_id").is(new ObjectId(documentId)).and("detection_result.detection_item_id").is(itemNo)));
            AggregationResults<ItemCountVO> result = mongoTemplate.aggregate(aggregation, "taskDetailVO",
                    ItemCountVO.class);

            JSONObject object = JSONObject.fromObject(result.getRawResults());
            JSONArray array = object.getJSONArray("results");

            //result 数组不为空 长度大于0的情况  检查项才有结果数据
            if (array != null && array.size() > 0) {
                JSONObject job = array.getJSONObject(0).getJSONObject("detection_result");
                if (job != null) {
                    vo = new PrivacyDetectionResultVO();
                    int status = job.getInt("status");
                    vo.setResult(status == PrivacyDetectionResultEnum.RISK.getStatus() ? "存在风险" : "安全");
                    if (itemNo.equals("0806") && status == PrivacyDetectionResultEnum.SAFE.getStatus()) {
                        vo.setDetailDescription(ITEM_0806_SAFE_DESC);
                    } else {
                        vo.setDetailDescription(job.getString("describe"));
                    }
                    Object flag = job.get("result_content");
                    if (!flag.equals("")) {
                        JSONObject job1 = job.getJSONObject("result_content");
                        if (itemNo.equals("0806")) {  //0806检测项 ip地址集合
                            if (job1 != null) {
                                JSONArray array1 = job1.getJSONArray("ip_info");
                                List<ChildItemCommonCountVO> list = new LinkedList<>();
                                if (array1.size() > 0) {
                                    for (int i = 0; i < array1.size(); i++) {
                                        JSONObject obj = array1.getJSONObject(i);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                                        ChildItemCommonCountVO item = new ChildItemCommonCountVO();
                                        item.setColunm1(obj.getString("ip"));
                                        item.setColunm2(obj.getString("location"));
                                        item.setColunm3(String.valueOf(obj.getInt("count")));
                                        list.add(item);
                                    }
                                }
                                vo.setDetails(list);
                            }
                        } else if (itemNo.equals("0803")) {  //0803检测项 风险出现次数
                            if (job1 != null) {
                                vo.setOccurrenceNumber(job1.getString("unsafe_num") + "次");
                            }
                        }
                    } else if (itemNo.equals("0803")) {
                        vo.setOccurrenceNumber("0次");
                    }
                }
            }
        } else {
            com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(taskDetailVO.getDetection_result().toString());
            vo = new PrivacyDetectionResultVO();
            for (int k = 0; k < jsonArray.size(); k++) {
                com.alibaba.fastjson.JSONObject json = jsonArray.getJSONObject(k);
                if (json.get("detection_item_id") != null && json.get("detection_item_id").equals(itemNo)) {
                    vo.setResult(PrivacyDetectionResultEnum.SAFE.getStatus().equals(json.getInteger("status")) ? "安全" : "存在风险");
                    if (json.containsKey("result_content")) {
                        Object resultContent = json.get("result_content");
                        if (resultContent instanceof com.alibaba.fastjson.JSONArray) {
                            com.alibaba.fastjson.JSONArray array = (com.alibaba.fastjson.JSONArray)resultContent;
                            vo.setDetailDescription(array.toJSONString());
                        } else {
                            vo.setDetailDescription(resultContent.toString());
                        }
                    }
                }
            }
        }
        return vo;
    }

    private List<PrivacyDetectionResultVO> getResultList(String documentId) {
        TaskDetailVO taskDetailVO = mongodbService.findByDocumentId(documentId);
        List<PrivacyDetectionResultVO> listVO = new ArrayList<>();
        if(taskDetailVO.getDetection_result() == null || StringUtils.isBlank(taskDetailVO.getDetection_result().toString())) {
        	return null;
        }
        String detection_result = taskDetailVO.getDetection_result().toString();
        if(StringUtils.isBlank(detection_result)) {
    	   return null;
        }
        com.alibaba.fastjson.JSONArray detection_result_array = com.alibaba.fastjson.JSONArray.parseArray(detection_result);
        if(detection_result_array==null || detection_result_array.size()==0) {
        	return null;
        }
        List<String> androidItems;
        if (haveResult(taskDetailVO, NEW_ANDROID_ITEMS)) {
            androidItems = NEW_ANDROID_ITEMS;
        } else {
            androidItems = OLD_ANDROID_ITEMS;
        }
        String mark = commonProperties.getProperty("custom.mark");
        for (int j = 0; j < detection_result_array.size(); j++) {
        	try {
				com.alibaba.fastjson.JSONObject item_result_json = detection_result_array.getJSONObject(j);

				if (item_result_json != null) {
                    String itemNo = item_result_json.getString("detection_item_id");
                    String itemName = item_result_json.getString("detection_item_name");
					PrivacyDetectionResultVO  vo = new PrivacyDetectionResultVO();
				    int status = item_result_json.getInteger("status");
				    vo.setResult(status == PrivacyDetectionResultEnum.RISK.getStatus() ? "存在风险" : "安全");
                    if (StringUtils.equals(itemNo, "0806") && status == PrivacyDetectionResultEnum.SAFE.getStatus()) {
                        vo.setDetailDescription(ITEM_0806_SAFE_DESC);
                    } else {
                        vo.setDetailDescription(item_result_json.getString("describe"));
                    }
				    Object flag = item_result_json.get("result_content");
                    if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
                        // 百胜定制为快餐外卖
                        if (StringUtils.equals(mark, "baisheng")) {
                            if (!BAISHENG_ITEMS.contains(itemNo)) {
                                continue;
                            }
                        } else {
                            if (!androidItems.contains(itemNo)) {
                                continue;
                            }
                        }
                    } else {
                        // 应用基本信息检测项不需要
                        if (BASE_INFO_ITEMS.contains(itemNo)) {
                            continue;
                        }
                    }
				    if (!flag.equals("")) {
				        com.alibaba.fastjson.JSONObject job1 = null;
				        try {
				        	if(item_result_json.get("result_content") != null) {
				        		job1 = item_result_json.getJSONObject("result_content");
				        	}
				        } catch (Exception e) {
				            System.out.println(e.getMessage());
				        }
				        if (itemNo.equals("0806")) {  //0806检测项 ip地址集合
				            if (job1 != null) {
				                com.alibaba.fastjson.JSONArray array1 = job1.getJSONArray("ip_info");
				                List<ChildItemCommonCountVO> list = new LinkedList<>();
				                if (array1.size() > 0) {
				                    for (int i = 0; i < array1.size(); i++) {
				                        com.alibaba.fastjson.JSONObject obj = array1.getJSONObject(i);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
				                        ChildItemCommonCountVO item = new ChildItemCommonCountVO();
				                        item.setColunm1(obj.getString("ip"));
				                        item.setColunm2(obj.getString("location"));
				                        item.setColunm3(String.valueOf(obj.getIntValue("count")));
				                        list.add(item);
				                    }
				                }
				                vo.setDetails(list);
				            }
				        } else if (itemNo.equals("0803")) {  //0803检测项 风险出现次数
				            if (job1 != null) {
				                vo.setOccurrenceNumber(job1.getString("unsafe_num") + "次");
				            }
				        } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.WECHAT_APPLET.getValue() && job1 != null) {
                            com.alibaba.fastjson.JSONArray riskList = job1.getJSONArray("riskList");
                            if (riskList != null) {
                                vo.setDetail2List(riskList.toJavaList(String.class));
                            }
                        }
				    } else if (itemNo.equals("0803")) {
				        vo.setOccurrenceNumber("0次");
				    }
				    vo.setItemNo(itemNo);
				    listVO.add(vo);
				}
			} catch (Exception e) {
				e.getMessage();
			}
		}
        return listVO;
    }
}
