package cn.ijiami.detection.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

import cn.hutool.core.codec.Base64Encoder;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.query.ThreePartyAuth;
import cn.ijiami.detection.service.api.IThreePartyAuthService;
import cn.ijiami.detection.utils.AESUitls;
import cn.ijiami.detection.utils.HttpClient;
import cn.ijiami.detection.utils.PlatformConstant;

/**
 * 第三方自动接入服务实现
 *
 * <AUTHOR>
 * @date 2020/3/27 10:51
 **/
@Service
public class ThreePartyAuthServiceImpl implements IThreePartyAuthService {

    private static final Logger LOG = LoggerFactory.getLogger(ThreePartyAuthServiceImpl.class);

    private final static String CLIENT_ID = new Base64Encoder().encode("client:client".getBytes());

    @Value("${ijiami.server.remoteHost}")
    private String remoteHost;
    @Value("${ijiami.server.tokenHost}")
    private String tokenHost;
    @Value("${ijiami.server.redirectHost}")
    private String redirectHost;
    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Override
    public void auth(ThreePartyAuth threePartyAuth, HttpServletRequest request, HttpServletResponse response) {
        ServletOutputStream output = null;
        try {
            output = response.getOutputStream();
            String authHandler = authHandler(threePartyAuth, request, response);
            if (StringUtils.isNotBlank(authHandler)) {
                LOG.info(authHandler);
                output.write(authHandler.getBytes());
                output.flush();
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (null != output) {
                try {
                    output.close();
                } catch (IOException e) {
                }
            }
        }
    }

    private String authHandler(ThreePartyAuth threePartyAuth, HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        String result = null;
        // 根据接口参数，先来一波校验，判断token,是否存在该用户，密码是否正确
        long t = Long.valueOf(threePartyAuth.getT());
        Date now = new Date();
        Date date = new Date(t / 1000);
        // 定义日期实例
        Calendar dd = Calendar.getInstance();
        // 设置日期起始时间
        dd.setTime(date);
        // 进行当前日期秒数加60
        dd.add(Calendar.MINUTE, 10);
        if (now.after(dd.getTime())) {
            result = "errorMessage:Param 'T' time out!";
            return result;
        }

        String encrypt = AESUitls.decrypt(threePartyAuth.getTk(), threePartyAuth.getT());
        if (!isJson(encrypt)) {
            result = "errorMessage:Decrypt token error!";
            return result;
        }

        JSONObject parseObject = JSON.parseObject(encrypt);
        String userName = parseObject.getString("u");
        String pwd = parseObject.getString("p");

        if (StringUtils.isBlank(userName) || StringUtils.isBlank(pwd)) {
            result = "errorMessage:username or password cannot be null!";
            return result;
        }
        // 自己调用自己，生成一个token
        Map<String, String> headerMap = Maps.newTreeMap();
        headerMap.put("Authorization", "Basic " + CLIENT_ID);
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");
        Map<String, Object> paramsMap = Maps.newTreeMap();
        paramsMap.put("username", userName);
        paramsMap.put("password", DigestUtils.md5DigestAsHex(pwd.getBytes()));
        paramsMap.put("grant_type", "password");
//        String tokenResult = HttpClient.doPost(tokenHost + PlatformConstant.OAUTH_TOKEN_URL, paramsMap, headerMap,true);
        
        String oauthServerIp = commonProperties.getOauthServerIp();
        String oauthServerPort = commonProperties.getOauthServerPort();
        String oauthServerContextPath = commonProperties.getOauthServerContextPath();

        String tokenUrl = "http://" + oauthServerIp + ":" + oauthServerPort + oauthServerContextPath + "/oauth/token";
        String tokenResult = HttpClient.doPost(tokenUrl, paramsMap, headerMap,true);
        if (!isJson(tokenResult)) {
            result = "errorMessage:Get user's auth token failed!";
            return result;
        }
        JSONObject tokenJson = JSON.parseObject(tokenResult);
        if (tokenResult.contains("\"status\":\"200\"") && tokenResult.contains("access_token")) {
            result = "info:Get user's auth token success!";
        } else {
            result = "errorMessage:Get user's auth token failed!!";
            return result;
        }
        try {
            String tokenEncode = URLEncoder.encode(tokenJson.toJSONString(), "GBK");
            String userEncode = URLEncoder.encode(JSON.toJSONString(tokenJson), "GBK");

            Cookie tokenInfoCookie = setCookie(PlatformConstant.COOKIE_TOKEN_INFO, tokenEncode, remoteHost, "/detection");
            Cookie userCookie = setCookie(PlatformConstant.COOKIE_USER, userEncode, remoteHost, "/");
            response.addCookie(tokenInfoCookie);
            response.addCookie(userCookie);
        } catch (Exception e) {
            e.getMessage();
        }
        String redirectUrl = redirectHost + PlatformConstant.REDIRECT_URL+"?access_token="+tokenJson.getString("access_token");
        LOG.info("重定向地址：" + redirectUrl);
        response.sendRedirect(redirectUrl);
        return result;
    }

    private Cookie setCookie(String name, String value, String domain, String path) {
        Cookie cookie = new Cookie(name, value);
        cookie.setDomain(domain);
        cookie.setPath(path);
        return cookie;
    }

    private boolean isJson(String content) {
        boolean b = false;
        if (null != content && content.length() > 0) {
            try {
                JSON.parseObject(content);
                return true;
            } catch (Exception e) {
            }
        }
        return b;
    }

    public static void main(String[] args) {
        Map<String, String> headerMap = Maps.newTreeMap();
        headerMap.put("Authorization", "Basic " + CLIENT_ID);
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");
        Map<String, Object> paramsMap = Maps.newTreeMap();
        paramsMap.put("username", "weibingtie");
        paramsMap.put("password", DigestUtils.md5DigestAsHex("weibingtie0326".getBytes()));
        paramsMap.put("grant_type", "password");
        String tokenResult =
                HttpClient.doPost("https://demo1.privacy.ijiami.cn/detection/oauth/token", paramsMap, headerMap);
        System.out.println(tokenResult);
    }

}
