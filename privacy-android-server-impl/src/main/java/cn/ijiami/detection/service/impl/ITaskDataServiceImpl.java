package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.service.api.ITaskDataService;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDataServiceImpl.java
 * @Description 任务数据
 * @createTime 2024年10月14日 15:52:00
 */
@Slf4j
@Service
public class ITaskDataServiceImpl implements ITaskDataService {


    /**
     * app的快速检测任务数据阶段集，新增值的时候，需要按照值的大小顺序添加
     */
    private static final List<IdbStagedDataEnum> APP_STAGE = Arrays.asList(
            IdbStagedDataEnum.BEHAVIOR_GRANT,
            IdbStagedDataEnum.REFUSE,
            IdbStagedDataEnum.AGREE,
            IdbStagedDataEnum.BEHAVIOR_FRONT,
            IdbStagedDataEnum.SHAKING,
            IdbStagedDataEnum.BEHAVIOR_GROUND,
            IdbStagedDataEnum.BEHAVIOR_EXIT);

    /**
     * app的ai智能检测任务数据阶段集
     */
    private static final List<IdbStagedDataEnum> AI_STAGE = Arrays.asList(
            IdbStagedDataEnum.BEHAVIOR_FRONT,
            IdbStagedDataEnum.SHAKING,
            IdbStagedDataEnum.BEHAVIOR_GROUND,
            IdbStagedDataEnum.BEHAVIOR_EXIT,
            IdbStagedDataEnum.BEHAVIOR_GRANT,
            IdbStagedDataEnum.REFUSE,
            IdbStagedDataEnum.AGREE);

    /**
     * app的ai智能检测任务时需要用户手动登录的阶段集
     */
    private static final List<IdbStagedDataEnum> AI_LOGIN_STAGE = Arrays.asList(
            IdbStagedDataEnum.NONE,
            IdbStagedDataEnum.BEHAVIOR_FRONT,
            IdbStagedDataEnum.SHAKING,
            IdbStagedDataEnum.BEHAVIOR_GROUND,
            IdbStagedDataEnum.BEHAVIOR_EXIT);

    /**
     * 鸿蒙或小程序的任务数据阶段集，新增值的时候，需要按照值的大小顺序添加
     */
    private static final List<IdbStagedDataEnum> APPLET_STAGE = Arrays.asList(
            IdbStagedDataEnum.BEHAVIOR_GRANT,
            IdbStagedDataEnum.REFUSE,
            IdbStagedDataEnum.AGREE,
            IdbStagedDataEnum.BEHAVIOR_FRONT,
            IdbStagedDataEnum.BEHAVIOR_GROUND);

    @Autowired
    private TTaskDataMapper taskDataMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TPrivacyActionNougatMapper privacyActionNougatMapper;

    @Autowired
    private TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;

    @Autowired
    private TPrivacySensitiveWordMapper privacySensitiveWordMapper;

    @Autowired
    private TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

    @Autowired
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;


    /**
     * 需要进行检测的阶段
     * @param task
     * @return
     */
    @Override
    public IdbStagedDataEnum detectStage(TTask task) {
        // 非自动化检测
        if (!TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            return taskInitialStage(task);
        }
        // 任务状态错误
        if (task.getDynamicSubStatus() == null || task.getDynamicSubStatus() == DynamicAutoSubStatusEnum.NONE) {
            return taskInitialStage(task);
        }
        // 任务状态错误
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            return taskInitialStage(task);
        }
        if (task.getDynamicSubStatus().getValue() > DynamicAutoSubStatusEnum.BEHAVIOR_EXIT.getValue()) {
            log.info("子阶段数据错误，重新开始检测 {}", task.getDynamicSubStatus());
            cleanTaskData(task.getTaskId());
            return taskInitialStage(task);
        }
        List<TTaskData> dataList = findTaskData(task.getTaskId());
        if (!validateTaskStagedData(task, task.getDynamicSubStatus(), dataList)) {
            List<IdbStagedDataEnum> allStageList = getAllStageList(task);
            // 找出需要重新开始检测的阶段
            for (IdbStagedDataEnum idbStage:allStageList) {
                // 第一个没在数据集中的阶段就是任务重新要执行的阶段
                if (dataList.stream().noneMatch(data -> data.getDynamicSubStatus().getValue() == idbStage.getValue())) {
                    log.info("子阶段数据不完整，重新开始检测 当前阶段={} 需要重新检测的阶段={}", task.getDynamicSubStatus(), idbStage);
                    return idbStage;
                }
            }
            // 找不到则重新开始检测
            log.info("子阶段数据不完整，重新开始检测 当前阶段={} 找不到需要重新执行的阶段，任务重新开始", task.getDynamicSubStatus());
            cleanTaskData(task.getTaskId());
            return taskInitialStage(task);
        } else {
            // 小程序没有摇一摇和退出阶段阶段，如果前台阶段已经检测完，直接进入后台阶段
            IdbStagedDataEnum next = nextStage(task, IdbStagedDataEnum.getItem(task.getDynamicSubStatus().getValue()));
            if (next == IdbStagedDataEnum.NONE) {
                log.info("阶段数据错误，重新开始检测 {}", task.getDynamicSubStatus());
                cleanTaskData(task.getTaskId());
                return taskInitialStage(task);
            } else {
                return next;
            }
        }
    }

    private IdbStagedDataEnum taskInitialStage(TTask task) {
        if (task.getDetectionType() == TaskDetectionTypeEnum.AI.getValue()) {
            return IdbStagedDataEnum.BEHAVIOR_FRONT;
        } else {
            return IdbStagedDataEnum.BEHAVIOR_GRANT;
        }
    }

    private List<IdbStagedDataEnum> getAllStageList(TTask task) {
        if (task.getTerminalType().isApplet() || task.getTerminalType() == TerminalTypeEnum.HARMONY) {
            return APPLET_STAGE;
        } else {
            if (task.getDetectionType() == TaskDetectionTypeEnum.AI.getValue()) {
                return AI_STAGE;
            } else {
                return APP_STAGE;
            }
        }
    }

    private void cleanTaskData(Long taskId) {
        log.info("TaskId:{} 删除任务数据", taskId);
        privacyActionNougatExtendMapper.deleteByTaskId(taskId);
        privacyActionNougatMapper.deleteByTaskId(taskId);
        privacySensitiveWordMapper.deleteByTaskId(taskId);
        privacyOutsideAddressMapper.deleteByTaskId(taskId);
        privacySharedPrefsMapper.deleteByTaskId(taskId);
        deleteTaskData(taskId);
    }

    /**
     * 根据任务状态寻找上一个阶段
     * @param task
     * @param stagedData
     * @return
     */
    @Override
    public IdbStagedDataEnum previousStage(TTask task, IdbStagedDataEnum stagedData) {
        List<IdbStagedDataEnum> allStageList = getAllStageList(task);
        int index = allStageList.indexOf(stagedData) - 1;
        if (index >= 0) {
            return allStageList.get(index);
        } else {
            return taskInitialStage(task);
        }
    }

    public IdbStagedDataEnum nextStage(TTask task, IdbStagedDataEnum stagedData) {
        List<IdbStagedDataEnum> allStageList = getAllStageList(task);
        int index = allStageList.indexOf(stagedData) + 1;
        if (index < allStageList.size()) {
            return allStageList.get(index);
        } else {
            return taskInitialStage(task);
        }
    }

    public boolean validateTaskStagedData(TTask task, DynamicAutoSubStatusEnum dynamicAutoSubStatus, List<TTaskData> taskDataList) {
        List<Integer> taskDataStageList = taskDataList.stream()
                .map(TTaskData::getDynamicSubStatus)
                .map(DynamicAutoSubStatusEnum::getValue)
                .collect(Collectors.toList());
        List<IdbStagedDataEnum> allStageList = getAllStageList(task);
        for (IdbStagedDataEnum stagedData: allStageList) {
            if (!taskDataStageList.contains(stagedData.getValue())) {
                return false;
            }
            if (stagedData.getValue() == dynamicAutoSubStatus.getValue()) {
                break;
            }
        }
        return true;
    }

    @Override
    public boolean allTaskDataAnalysisSuccess(TerminalTypeEnum terminalType, List<TTaskData> taskDataList) {
        List<Integer> taskDataStageList = taskDataList.stream()
                .filter(data -> data.getStatus() == TaskDataStatusEnum.SUCCESS)
                .map(TTaskData::getDynamicSubStatus)
                .map(DynamicAutoSubStatusEnum::getValue)
                .collect(Collectors.toList());
        List<IdbStagedDataEnum> allStageList = (terminalType.isApplet() || terminalType == TerminalTypeEnum.HARMONY) ? APPLET_STAGE : APP_STAGE;
        for (IdbStagedDataEnum stagedData: allStageList) {
            if (!taskDataStageList.contains(stagedData.getValue())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<TTaskData> findTaskData(Long taskId) {
        Example example = new Example(TTaskData.class);
        example.createCriteria()
                .andEqualTo("taskId", taskId)
                .andEqualTo("isDelete", BooleanEnum.FALSE.value);
        return taskDataMapper.selectByExample(example);
    }

    @Override
    public void deleteTaskData(Long taskId) {
        Example example = new Example(TTaskData.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        criteria.andEqualTo("isDelete", BooleanEnum.FALSE.value);
        TTaskData update = new TTaskData();
        update.setIsDelete(BooleanEnum.TRUE.value);
        update.setUpdateTime(new Date());
        taskDataMapper.updateByExampleSelective(update, example);
    }

    @Transactional
    @Override
    public TTaskData saveTaskData(TTask task, DynamicAutoSubStatusEnum subStatus, String dataPath)  {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        if (Objects.isNull(subStatus)) {
            throw new IjiamiRuntimeException("阶段数据错误");
        }
        if (task.getDynamicSubStatus().getValue() < subStatus.getValue()) {
            updateTask.setDynamicSubStatus(subStatus);
        }
        updateTask.setUpdateTime(new Date());
        taskMapper.updateByPrimaryKeySelective(updateTask);

        Example example = new Example(TTaskData.class);
        example.createCriteria()
                .andEqualTo("taskId", task.getTaskId())
                .andEqualTo("isDelete", BooleanEnum.FALSE.value)
                .andEqualTo("dynamicSubStatus", subStatus);
        TTaskData taskData = taskDataMapper.selectOneByExample(example);
        if (taskData != null) {
            taskData.setDynamicSubStatus(subStatus);
            taskData.setDynamicStartTime(task.getDynamicStarttime());
            taskData.setCreateTime(new Date());
            taskData.setUpdateTime(new Date());
            taskData.setDataPath(dataPath);
            taskData.setStatus(TaskDataStatusEnum.INIT);
            taskDataMapper.updateByPrimaryKeySelective(taskData);
        } else {
            taskData = new TTaskData();
            taskData.setTaskId(task.getTaskId());
            taskData.setDynamicSubStatus(subStatus);
            taskData.setDynamicStartTime(task.getDynamicStarttime());
            taskData.setIsDelete(BooleanEnum.FALSE.value);
            taskData.setCreateTime(new Date());
            taskData.setUpdateTime(new Date());
            taskData.setDataPath(dataPath);
            taskData.setStatus(TaskDataStatusEnum.INIT);
            taskDataMapper.insert(taskData);
        }
        return taskData;
    }

    @Override
    public void resetTaskDataStatus(Long taskDataId) {
        TTaskData update = new TTaskData();
        update.setId(taskDataId);
        update.setStatus(TaskDataStatusEnum.INIT);
        taskDataMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void analyzingTaskData(Long taskDataId) {
        TTaskData update = new TTaskData();
        update.setId(taskDataId);
        update.setStatus(TaskDataStatusEnum.ANALYZEING);
        update.setUpdateTime(new Date());
        taskDataMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void analysisTaskDataSuccess(Long taskDataId) {
        TTaskData update = new TTaskData();
        update.setId(taskDataId);
        update.setStatus(TaskDataStatusEnum.SUCCESS);
        update.setUpdateTime(new Date());
        taskDataMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void analysisTaskDataFailure(Long taskDataId) {
        TTaskData update = new TTaskData();
        update.setId(taskDataId);
        update.setStatus(TaskDataStatusEnum.FAILURE);
        taskDataMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public boolean isLoginRequiredForAIDetection(IdbStagedDataEnum stage) {
        if (Objects.isNull(stage)) {
            return false;
        }
        return AI_LOGIN_STAGE.contains(stage);
    }
}
