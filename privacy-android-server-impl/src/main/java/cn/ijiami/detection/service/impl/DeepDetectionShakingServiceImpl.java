package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.TShakeBasicParametesVO;
import cn.ijiami.detection.entity.TShakeBasicParametes;
import cn.ijiami.detection.mapper.TShakeBasicParametesMapper;
import cn.ijiami.detection.service.api.DeepDetectionShakingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DeepDetectionShakingServiceImpl implements DeepDetectionShakingService {
    @Autowired
    private TShakeBasicParametesMapper shakeBasicParametesMapper;
    @Override
    public TShakeBasicParametesVO findShakingParametes(Integer type) {
        TShakeBasicParametesVO shakeBasicParametesVO = new TShakeBasicParametesVO();
        List<TShakeBasicParametes> shakeBasicParametesList = shakeBasicParametesMapper.selectShakingParametes(type);
        List<TShakeBasicParametes> acceleratParametes = shakeBasicParametesList.stream().filter(v->v.getX() !=null && v.getY() !=null && v.getZ() !=null).collect(Collectors.toList());
        List<TShakeBasicParametes> gyroscopeParametes = shakeBasicParametesList.stream().filter(v->v.getAx() !=null && v.getAy() !=null && v.getAz() !=null).collect(Collectors.toList());
        shakeBasicParametesVO.setAcceleratParametes(acceleratParametes);
        shakeBasicParametesVO.setGyroscopeParametes(gyroscopeParametes);

        List<Map<String,String>> resourceIds = new ArrayList<>();

        List<Map<String,String>> gyroscopeIds = new ArrayList<>();
        shakeBasicParametesList.stream().forEach(parametes ->{
            //加速度
            Map<String,String> resourceMap = new HashMap<>();
            if(StringUtils.isNotBlank(parametes.getX()) && StringUtils.isNotBlank(parametes.getY())
                    && StringUtils.isNotBlank(parametes.getZ()) && StringUtils.isNotBlank(parametes.getS())){
                resourceMap.put("acceleratX",parametes.getX());
                resourceMap.put("acceleratY",parametes.getY());
                resourceMap.put("acceleratZ",parametes.getZ());
                resourceMap.put("acceleratS",parametes.getS());
                resourceIds.add(resourceMap);
            }
            //角速度
            Map<String,String> gyroscopeMap = new HashMap<>();
            //最大角度
            float maxAngle = 0.00f;
            if(StringUtils.isNotBlank(parametes.getAx()) && StringUtils.isNotBlank(parametes.getAy()) && StringUtils.isNotBlank(parametes.getAz())){
                float a1 = Float.parseFloat(parametes.getAx());
                float a2 = Float.parseFloat(parametes.getAy());
                float a3 = Float.parseFloat(parametes.getAz());
                maxAngle = Math.max(Math.max(a1,a2),a3);
                gyroscopeMap.put("gyroscopeX",parametes.getRx());
                gyroscopeMap.put("gyroscopeY",parametes.getRy());
                gyroscopeMap.put("gyroscopeZ",parametes.getRz());
                gyroscopeMap.put("maxAngle",String.valueOf(maxAngle));
                gyroscopeIds.add(gyroscopeMap);
            }
        });
        shakeBasicParametesVO.setResourceIds(resourceIds);
        shakeBasicParametesVO.setGyroscopeIds(gyroscopeIds);
        return shakeBasicParametesVO;
    }
}
