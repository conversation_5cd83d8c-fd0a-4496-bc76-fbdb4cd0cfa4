package cn.ijiami.detection.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.service.api.ISensitiveWordService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

@Service
@Transactional
@CacheConfig(cacheNames = {"privacy-detection:sensitiveWord"})
public class SensitiveWordServiceImpl implements ISensitiveWordService {

    private final TSensitiveWordMapper sensitiveWordMapper;

    public SensitiveWordServiceImpl(TSensitiveWordMapper sensitiveWordMapper) {
        this.sensitiveWordMapper = sensitiveWordMapper;
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveWord", allEntries = true)
    public void addSensitiveWord(TSensitiveWord sensitiveWord) throws IjiamiApplicationException {
        TSensitiveWord sensitiveWordTwo = new TSensitiveWord();
        sensitiveWordTwo.setName(sensitiveWord.getName());
        sensitiveWordTwo.setTerminalType(sensitiveWord.getTerminalType());
        List<TSensitiveWord> oldSensitiveWordList = sensitiveWordMapper.findSensitiveWord(sensitiveWordTwo)
                .stream()
                .filter(word -> !word.getId().equals(sensitiveWord.getId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oldSensitiveWordList)) {
            throw new IjiamiApplicationException("已存在敏感词汇：" + sensitiveWord.getName());
        }
        if (sensitiveWord.getId() == null) {
            sensitiveWordMapper.insertSelective(sensitiveWord);
        } else {
            sensitiveWordMapper.updateByPrimaryKeySelective(sensitiveWord);
        }
    }

    @Override
    public TSensitiveWord findById(Long id) {
        return sensitiveWordMapper.selectByPrimaryKey(id);
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveWord", allEntries = true)
    public int deleteSensitiveWordById(Long sensitiveWordId) {
        return sensitiveWordMapper.deleteByPrimaryKey(sensitiveWordId);
    }

    @Override
    public PageInfo<TSensitiveWord> findSensitiveWordByWord(TSensitiveWord sensitiveWord)
            throws IjiamiApplicationException {
        if (sensitiveWord.getTypeId() == null) {
            throw new IjiamiApplicationException("敏感词分类ID不能为空");
        }
        if (sensitiveWord.getPage() != null && sensitiveWord.getRows() != null) {
            PageHelper.startPage(sensitiveWord.getPage(), sensitiveWord.getRows());
        }
        List<TSensitiveWord> sensitiveWordList = sensitiveWordMapper.selectSensitiveWordByPage(sensitiveWord);

        return new PageInfo<>(sensitiveWordList);
    }
}
