package cn.ijiami.detection.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.DTO.LawDetectionDTO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyCategory;
import cn.ijiami.detection.entity.TPrivacyPolicy;
import cn.ijiami.detection.entity.TPrivacyPolicyImg;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TPrivacyCategoryMapper;
import cn.ijiami.detection.mapper.TPrivacyCheckMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyImgMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper;
import cn.ijiami.detection.service.api.IDynamicAndroidDetectionService;
import cn.ijiami.detection.service.api.ILawDetectionService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 法规检测 服务实现
 *
 * <AUTHOR>
 * @date 2020-08-27 15:17
 */
@Service
@Transactional
public class LawDetectionServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements ILawDetectionService {

    private static Logger logger = LoggerFactory.getLogger(LawDetectionServiceImpl.class);

    private final IBaseFileService fileService;
    private final TaskDAO taskDAO;
    private final TPrivacyCategoryMapper privacyCategoryMapper;
    private final TPrivacyCheckMapper privacyCheckMapper;
    private final TPrivacyPolicyMapper privacyPolicyMapper;
    private final TPrivacyPolicyImgMapper privacyPolicyImgMapper;
    private final ISendMessageService sendMessageService;
    private final IDynamicAndroidDetectionService dynamicDetectionService;
    private final TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper;
    private final TAssetsMapper assetsMapper;
    private final UserUseDeviceDAO userUseDeviceDAO;

    public LawDetectionServiceImpl(IBaseFileService fileService, TaskDAO taskDAO, TPrivacyCategoryMapper privacyCategoryMapper,
                                   TPrivacyCheckMapper privacyCheckMapper, TPrivacyPolicyMapper privacyPolicyMapper,
                                   TPrivacyPolicyImgMapper privacyPolicyImgMapper, ISendMessageService sendMessageService,
                                   IDynamicAndroidDetectionService dynamicDetectionService, TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper,
                                   TAssetsMapper assetsMapper, UserUseDeviceDAO userUseDeviceDAO) {
        this.fileService = fileService;
        this.taskDAO = taskDAO;
        this.privacyCategoryMapper = privacyCategoryMapper;
        this.privacyCheckMapper = privacyCheckMapper;
        this.privacyPolicyMapper = privacyPolicyMapper;
        this.privacyPolicyImgMapper = privacyPolicyImgMapper;
        this.sendMessageService = sendMessageService;
        this.dynamicDetectionService = dynamicDetectionService;
        this.tPrivacyPolicyTypeMapper = tPrivacyPolicyTypeMapper;
        this.assetsMapper = assetsMapper;
        this.userUseDeviceDAO = userUseDeviceDAO;
    }

    @Override
    public String imgUploadToServer(MultipartFile multipartFile) throws IjiamiApplicationException {
        InputStream inputStream;
        try {
            inputStream = multipartFile.getInputStream();
        } catch (IOException e) {
            throw new IjiamiApplicationException("图片资源读取失败");
        }
        FileVO fileVO = new FileVO();
        fileVO.setInputStream(inputStream);
        fileVO.setFileSize(multipartFile.getSize());
        String fileName = multipartFile.getOriginalFilename().replaceAll(" ", "").trim();
        fileVO.setFileName(UuidUtil.uuid() + ConstantsUtils.FILE_NAME_SEPARATOR + fileName);
        Map<String, Object> params = new HashMap<>(4);
        params.put("size", multipartFile.getSize());
        fileVO.setParams(params);
        // 上传图片至本地服务器
        fileVO = fileService.uploadFile(fileVO);
        return fileVO.getFileKey();
    }

    public String imageReplaceToServer(MultipartFile multipartFile, String oldFileKey) throws IjiamiApplicationException {
        fileService.findFileByFileKey(oldFileKey);
        return imgUploadToServer(multipartFile);
    }

    @Override
    public void analysisLawDetectData(LawDetectionDTO lawDetectionDTO) throws IjiamiApplicationException {
        Long taskId = lawDetectionDTO.getTaskId();
        TTask task = taskDAO.findTaskById(taskId);
        if (Objects.isNull(task)) {
            throw new IjiamiApplicationException("检测任务不存在");
        }

        if (task.getDynamicLawStatus().getValue() == DynamicLawStatusEnum.DETECTION_LAW_FAILED.getValue()) {
            throw new IjiamiApplicationException("法规检测已经中断,无法提交");
        }

        // 处理法规检测点数据
        lawDetectPolicyPointHandle(taskId, lawDetectionDTO.getPrivacyPolicyTypes());
        // 处理功能类型数据
        lawDetectAppCategoryHandle(taskId, lawDetectionDTO.getCategoryIds());
        // 更新任务法规状态
        updateTaskLawStatus(task);
    }


    private void updateTaskLawStatus(TTask task) {
        taskDAO.updateLawSuccess(task.getTaskId());
        sendMessage(task, BroadcastMessageTypeEnum.DETECTION_LAW_FINISH, "法规检测完成");
    }

    private void sendMessage(TTask task, BroadcastMessageTypeEnum messageType, String message) {
        sendMessageService.sendTaskStatusBroadcast(messageType, message, task);
    }

    /**
     * 法规检测应用功能类型
     *
     * @param taskId      任务ID
     * @param categoryIds 功能类型ID
     */
    private void lawDetectAppCategoryHandle(Long taskId, List<Long> categoryIds) {
        //v2.5版本逻辑修改
        //点击确认就执行数据删除操作
        privacyCategoryMapper.deleteByTaskId(taskId);
        String assetsFunctionType="";
        //获取上传资产时存储的功能类型数据
        TAssets assets = assetsMapper.getAssetByTaskId(taskId);
        if(assets!=null){
            //只要点了确认，则同步修改资产表里面的功能类型数据避免查询时又复现功能类型
            assetsMapper.updateFunctionTypeById(assets.getId(),assetsFunctionType);
        }

        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }
        List<TPrivacyCategory> privacyCategories = new ArrayList<>(categoryIds.size());

        for (Long categoryId : categoryIds) {
            TPrivacyCategory tPrivacyCategory = new TPrivacyCategory();
            tPrivacyCategory.setCategoryId(categoryId);
            tPrivacyCategory.setTaskId(taskId);
            privacyCategories.add(tPrivacyCategory);
            assetsFunctionType +=categoryId.toString()+",";
        }
        if(!StringUtils.isEmpty(assetsFunctionType)){
            assetsFunctionType=assetsFunctionType.substring(0,assetsFunctionType.length()-1);
        }

        if(assets!=null){
            //同步修改资产表里面的功能类型数据避免查询时又复现功能类型
            assetsMapper.updateFunctionTypeById(assets.getId(),assetsFunctionType);
        }

        if (privacyCategories.size() > 0) {
            privacyCategoryMapper.insertList(privacyCategories);
        }
    }

    /**
     * 法规检测检测点结果存储处理
     *
     * @param taskId             任务ID
     * @param privacyPolicyTypes 法规检测项
     */
    private void lawDetectPolicyPointHandle(Long taskId, List<PrivacyPolicyTypeVO> privacyPolicyTypes) {
        if (CollectionUtils.isEmpty(privacyPolicyTypes)) {
            return;
        }
        // 避免重复提交，一个任务对应的法规数据只有一组
        Integer lawTypeId = privacyPolicyTypes.get(0).getType();
        privacyPolicyMapper.deleteByTaskIdAndType(taskId, lawTypeId);
        // 合并所有检测点
        List<PrivacyCheckVO> privacyChecks = privacyPolicyTypes.stream().flatMap((l) -> l.getPrivacyCheckList().stream()).collect(Collectors.toList());
        List<TPrivacyPolicy> privacyPolicies = new ArrayList<>(privacyChecks.size());
        // 每个法规检测点最多可截图5张
        List<TPrivacyPolicyImg> imgs = new ArrayList<>(privacyChecks.size() * 5);
        for (PrivacyCheckVO privacyCheckVO : privacyChecks) {
            TPrivacyPolicy tPrivacyPolicy = new TPrivacyPolicy();
            tPrivacyPolicy.setPrivacyId(privacyCheckVO.getId());
            tPrivacyPolicy.setResult(privacyCheckVO.getResult());
            tPrivacyPolicy.setSuggestion(privacyCheckVO.getSuggestion());
            tPrivacyPolicy.setTaskId(taskId);
            tPrivacyPolicy.setType(privacyCheckVO.getType());
            // 添加到存储列表
            privacyPolicies.add(tPrivacyPolicy);

            List<String> images = privacyCheckVO.getImages();
            if (CollectionUtils.isEmpty(images)) {
                continue;
            }
            // 添加图片关联记录
            for (String imageKey : images) {
                TPrivacyPolicyImg img = new TPrivacyPolicyImg();
                img.setTaskId(taskId);
                img.setPolicyId(privacyCheckVO.getId());
                img.setFileKey(imageKey);
                // 添加到图片存储表
                imgs.add(img);
            }
        }
        logger.info("lawTypeId={} privacyChecks={} privacyPolicies={} imgs={}", lawTypeId, privacyChecks.size(), privacyPolicies.size(), imgs.size());
        // 记录法规检测点
        if (privacyPolicies.size() > 0) {
            privacyPolicyMapper.insertList(privacyPolicies);
        }
        // 记录法规检测点对应的图片
        if (imgs.size() > 0) {
            privacyPolicyImgMapper.insertList(imgs);
        }
    }
}

