package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.analyzer.helper.BehaviorActionConvertHelper.ConvertActionId.SDCARD_WRITE;
import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_ANALYSIS_TASK_DATA_PREFIX;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.DETECTION_AUTO_FAILED;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.DETECTION_AUTO_WAITING;
import static cn.ijiami.detection.utils.CommonUtil.analysisErrorMsg;
import static cn.ijiami.detection.utils.SuspiciousSdkUtils.addPackageNames;
import static cn.ijiami.detection.utils.SuspiciousSdkUtils.addTwoLevelPackageNames;
import static cn.ijiami.detection.utils.SuspiciousSdkUtils.setSuspiciousSdkExecutorAndPackageName;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.ijiami.detection.utils.SensitiveUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.DynamicDetectFailedVO;
import cn.ijiami.detection.VO.PrivacyPolicyCheck;
import cn.ijiami.detection.VO.PushProgressVO;
import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyVO;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.CaptureHostIpAction;
import cn.ijiami.detection.analyzer.CaptureInfoAction;
import cn.ijiami.detection.analyzer.HostIpaAction;
import cn.ijiami.detection.analyzer.SharedPrefAction;
import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TOdpCompany;
import cn.ijiami.detection.entity.TOdpCompanyProduct;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.detection.entity.TPrivacyLawsBasis;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicy;
import cn.ijiami.detection.entity.TPrivacyPolicyImg;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.entity.TXiaomiAppStoreDetectItem;
import cn.ijiami.detection.entity.TXiaomiAppStoreDetectResult;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.CookieMarkEnum;
import cn.ijiami.detection.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.DynamicDetectFailedStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyPolicyItemNoEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultCategoryEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.PushProgressEnum;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.TaskDataErrorException;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.mapper.TApplyPermissionMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TManualScreenshotImageMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyImgMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectItemMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectResultMapper;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.parser.AndroidResultDataLogParser;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.IDynamicAndroidDetectionService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.SDKWhitelistRuleService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.detection.utils.PdfToTextFileUtil;
import cn.ijiami.detection.utils.SuspiciousSdkUtils;
import cn.ijiami.detection.utils.URLTokenizer;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 检测数据处理
 *
 * <AUTHOR>
 * @date 2020/9/15 10:35
 **/
@Service
public class DynamicAndroidDetectionServiceImpl extends AbstractDynamicDetectionService<TaskDetailVO> implements IDynamicAndroidDetectionService {

    private static Logger log = LoggerFactory.getLogger(DynamicAndroidDetectionServiceImpl.class);
    /**
     * 动态检测需要解析的文件组
     */
    private static final Map<BehaviorStageEnum, String> ANALYZE_FILE_MAP = new HashMap<>();

    /**
     * 华为安全检测项目
     */
    private static final String HUAWEI_SECURITY_DETECTION_ITEM = "1205";

    static {
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_FRONT, "behavior_info.json,hostip.txt,capture_info.txt,shared_prefs,capture_info");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GRANT, "behavior_info_02.json,hostip_02.txt,capture_info_02.txt,shared_prefs_02,capture_info_02");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GROUND, "behavior_info_03.json,hostip_03.txt,capture_info_03.txt,shared_prefs_03,capture_info_03");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_EXIT, "behavior_info_04.json,hostip_04.txt,capture_info_04.txt,shared_prefs_04,capture_info_04");
    }

    @Autowired
    BehaviorInfoAction behaviorInfoAction;
    @Autowired
    CaptureInfoAction captureInfoAction;
    @Autowired
    CaptureHostIpAction captureHostIpAction;
    @Autowired
    HostIpaAction hostIpaAction;
    @Autowired
    SharedPrefAction sharedPrefAction;
    @Autowired
    IjiamiCommonProperties commonProperties;
    @Autowired
    TSensitiveWordMapper sensitiveWordMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    TPrivacyPolicyImgMapper privacyPolicyImgMapper;
    @Autowired
    TPrivacyPolicyMapper privacyPolicyMapper;
    @Autowired
    TPrivacyLawsDetailMapper privacyLawsDetailMapper;
    @Resource
    AndroidResultDataLogParser androidResultDataLogParser;
    @Autowired
    SDKWhitelistRuleService sdkWhitelistRuleService;
    @Autowired
    TPermissionMapper permissionMapper;
    @Autowired
    TManualScreenshotImageMapper manualScreenshotImageMapper;

    @Autowired
    TApplyPermissionMapper applyPermissionMapper;

    @Autowired
    IDynamicTaskContextService dynamicTaskDataService;

    @Autowired
    UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    TXiaomiAppStoreDetectItemMapper xiaomiAppStoreDetectItemMapper;

    @Autowired
    TXiaomiAppStoreDetectResultMapper xiaomiAppStoreDetectResultMapper;

    @Autowired
    ICommonMongodbService commonMongodbService;
    
    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;
    @Autowired
    private TTaskExtendMapper taskExtendMapper;
    
    @Resource
    TAssetsMapper assetsMapper;

    @Value("#{${ijiami.cloud.phone.addressBook}}")
    private Map<String, String> cloudPhoneAddressBook;

    @Value("${ijiami.tensorflow.checkboxModelPath:}")
    private String checkboxModelPath;

    @Value("${ijiami.tensorflow.checkedModelPath:}")
    private String checkedModelPath;

    @Override
    public void analysisAutoFromUpload(TTask task, IdbStagedDataEnum stage, MultipartFile data) {
        // 校验任务存在
        if (!checkDocumentValid(task)) {
            log.info("TaskId:{} 任务不存在", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测任务阶段数据完成", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_FAILED) {
            log.info("TaskId:{} 动态检测已经中断", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_WAITING) {
            log.info("TaskId:{} 动态检测等待状态，修改任务状态", task.getTaskId());
            taskDAO.updateDynamicAutoWaiting(task, "", false);
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "AUTO_" + stage.getValue(), data);
            String dfsPath = uploadDataFileToFastDfs(file.getAbsolutePath());
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), dfsPath);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath()));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }


    @Override
    public void analysisAutoFromXXLData(Long taskId, IdbStagedDataEnum stage, String fastDfsDataPath) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            log.info("TaskId:{} 任务不存在", taskId);
            return;
        }
        try {
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), fastDfsDataPath);
            File file = downloadFile(fastDfsDataPath);
            // xxl过来的数据已经上传到文件服务器的，不需要再上传
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath()));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {
        String uncompress = null;
        try {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            uncompress = CommonUtil.uncompressTaskData(dataPath, dynamicPath + UuidUtil.uuid());
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            List<TSdkLibrary> firstPartyLibraries = sdkLibraryMapper.findFirstPartyByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisIdbDataStageUncompress(taskDetailVo, uncompress, sdkLibraries, stageEnum);
            // 提取和保存疑似SDK数据
            List<SuspiciousSdkBO> suspiciousSdkBOs = findSuspiciousSdks(detectDataMap, taskDetailVo, sdkLibraries, firstPartyLibraries);
            Map<Long, String> screenshotMap = getScreenShotMap(stageEnum, uncompress);
            saveStageData(task.getTaskId(), taskData.getId(), detectDataMap, screenshotMap, suspiciousSdkBOs);
        } finally {
            CommonUtil.deleteFile(uncompress);
        }
    }

    protected void saveStageData(Long taskId, Long taskDataId, Map<BehaviorStageEnum, DetectDataBO> detectDataMap,
                        Map<Long, String> screenshotMap, List<SuspiciousSdkBO> suspiciousSdkBOs) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 事物隔离级别
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        // 事务传播行为
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transaction = dataSourceTransactionManager.getTransaction(def);
        try {
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, screenshotMap, taskId);
            // 更新sdk库权限和写入疑似SDK记录
            updateSdkPermissionCodeAndInsertRecord(taskId, suspiciousSdkBOs);
            // 更新SDK权限编码
            List<TPrivacyActionNougat> nougatList = detectDataMap.values().stream()
                    .flatMap(it -> it.getPrivacyActionNougats().stream())
                    .collect(Collectors.toList());
            updateSdkPermission(nougatList);
            taskDataService.analysisTaskDataSuccess(taskDataId);
            dataSourceTransactionManager.commit(transaction);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(transaction);
            throw e;
        }
    }

    @Override
    protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception {
        long startTime = System.currentTimeMillis();
        List<File> stagedZipList = new ArrayList<>();
        String uncompress = null;
        try {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            uncompress = dynamicPath + UuidUtil.uuid();
            // 解压阶段数据
            log.info("解压分阶段的数据 开始");
            for (TTaskData taskData : taskDataList) {
                File stagedZip = new File(saveFilePath(task.getMd5(), task.getTaskId(), "AUTO_" + taskData.getDynamicSubStatus().getValue()));
                if (!stagedZip.exists()) {
                   try {
                       FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                       stagedZipList.add(stagedZip);
                   } catch (Exception e) {
                       taskDataRollback(task, taskData.getDynamicSubStatus());
                       throw new TaskDataErrorException(e);
                   }
                }
                try {
                    CommonUtil.uncompressOverwrite(stagedZip.getAbsolutePath(), uncompress);
                } catch (UncompressFailException e) {
                    log.info("压缩包损坏 删除={}", stagedZip.delete());
                    throw new TaskDataErrorException(e);
                }
            }
            log.info("解压分阶段的数据 完成");
            // 爬取链接-提前解析文件，python方式解析到完成需要点时间
            getUrlPrivacyContent(uncompress);
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = findDetectData(task.getTaskId());
            List<SuspiciousSdkBehaviorVO> suspiciousSdkBOs = findSuspiciousSdk(task.getTaskId());
            // 164号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap, sdkLibraries, suspiciousSdkBOs);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            long analysisTakesTime = System.currentTimeMillis() - startTime;
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.DYNAMIC, analysisTakesTime);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "动态检测完成");
            log.info("android 动态检测完成 taskId={} 时间={}s", task.getTaskId(), analysisTakesTime / 1000);
            pushProgress(task);
        } finally {
            CommonUtil.deleteFile(uncompress);
            for (File stagedZip:stagedZipList) {
                CommonUtil.deleteFile(stagedZip.getAbsolutePath());
            }
        }
    }

    private Map<BehaviorStageEnum, DetectDataBO> findDetectData(Long taskId) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (BehaviorStageEnum behaviorStageEnum:ANALYZE_FILE_MAP.keySet()) {
            DetectDataBO detectDataBO = new DetectDataBO();
            detectDataBO.setPrivacyActionNougats(privacyActionNougatMapper.findByTaskId(taskId, Collections.emptyList(),
                    Collections.emptyList(), Collections.singletonList(behaviorStageEnum)));
            detectDataBO.setPrivacySensitiveWords(privacySensitiveWordMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacyOutsideAddresses(privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskId,
                    StringUtils.EMPTY, StringUtils.EMPTY, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacySharedPrefs(privacySharedPrefsMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataMap.put(behaviorStageEnum, detectDataBO);
        }
        return detectDataMap;
    }

    private List<SuspiciousSdkBehaviorVO> findSuspiciousSdk(Long taskId) {
        return suspiciousSdkMapper.findBehaviorByTaskId(taskId);
    }

    //检测完成回调
    private void pushProgress(TTask task){
    	 try {
			TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
			 if(Objects.isNull(taskExtend)) {
			 	return;
			 }
			 String callbackUrl = taskExtend.getCallbackUrl();
			 if (StringUtils.isBlank(callbackUrl)) {
			     return;
			 }
			//通知检测完成给第三方接口
			PushProgressVO push = new PushProgressVO();
			push.setDetectionType(DetectionTypeEnum.DYNAMIC);
			push.setPushProgress(PushProgressEnum.PUSH_FINISH);
			// 进行第三方进度信息消息推送
			apiPushProgressServer.pushProgressByUrl(task.getTaskId(), taskExtend.getBussinessId(), callbackUrl, push);
		} catch (Exception e) {
			e.getMessage();
		}
    }

    private void updateSdkPermission(List<TPrivacyActionNougat> nougatList) {
        taskService.updateSdkPermission(nougatList);
    }


    // 异步线程去处理，避免卡住上传流程，导致idb那边上传超时
    @Override
    public void analysisManual(Long taskId, MultipartFile data) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "MANUAL", data);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisManualInternal(task, file.getAbsolutePath(), StringUtils.EMPTY));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    @Override
    protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        String uncompress = null;
        try {
            uncompress = CommonUtil.uncompress(dataPath, dynamicPath + UuidUtil.uuid());
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            updateTask.setDataPath(StringUtils.isEmpty(fastDfsDataPath) ? uploadDataFileToFastDfs(dataPath) : fastDfsDataPath);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisManual(taskDetailVo, uncompress, false, null);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, null);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.MANUAL);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "深度检测完成");
            dynamicTaskDataService.removeTaskContext(task);
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "深度检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    protected void analysisAutoOldFile(TTask task, String uncompress) {
        try {
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            //爬取链接-提前解析文件，python方式解析到完成需要点时间
            getUrlPrivacyContent(uncompress);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            List<TSdkLibrary> firstPartyLibraries = sdkLibraryMapper.findFirstPartyByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisIdbDataStageUncompress(taskDetailVo, uncompress, sdkLibraries);
            // 提取和保存疑似SDK数据
            List<SuspiciousSdkBO> suspiciousSdkBOs = findSuspiciousSdks(detectDataMap, taskDetailVo, sdkLibraries, firstPartyLibraries);
            deleteOldData(task.getTaskId());
            Map<Long, String> screenshotMap = getScreenShotMap(IdbStagedDataEnum.NONE, uncompress);
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, screenshotMap, task.getTaskId());
            // 更新sdk库权限和写入疑似SDK记录
            updateSdkPermissionCodeAndInsertRecord(task.getTaskId(), suspiciousSdkBOs);
            List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS = suspiciousSdkBOs.stream()
                    .map(SuspiciousSdkBO::getSuspiciousSdk)
                    .collect(Collectors.toList());
            // 146号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap, sdkLibraries, suspiciousSdkBehaviorVOS);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeRetry(task);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, DetectionTypeEnum.DYNAMIC.getName() + "完成");
        } catch (Exception e) {
        	e.getMessage();
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, DetectionTypeEnum.DYNAMIC.getName() + "失败");
            log.error("检测数据处理 - {}，数据解析异常:{}", DetectionTypeEnum.DYNAMIC.getName(), e.getMessage());
        } finally {
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    public void analysisLaw(Long taskId, Integer type, MultipartFile data) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + taskId;
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", taskId, lockKey);
            return;
        }
        String uncompress = null;
        try {
            uncompress = saveAndUncompress(task.getMd5(), taskId, "LAW" + type, data);
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicLawDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            File policyFile = new File(uncompress + File.separator + "privacy_policy.json");
            if (policyFile.exists()) {
                savePolicy(taskId, policyFile);
            }
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.LAW);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_LAW_FINISH, "法规检测完成");
        } catch (Exception e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.LAW, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "法规检测失败");
            log.error("检测数据处理 - {}，数据解析异常:{}", DetectionTypeEnum.LAW.getName(), e.getMessage());
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    public void dynamicDetectFailed(DynamicDetectFailedVO dynamicDetectFailedVO) {
        TTask task = taskMapper.selectByPrimaryKey(dynamicDetectFailedVO.getTaskId());
        if (DynamicDetectFailedStageEnum.DYNAMIC.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateDynamicFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        } else if (DynamicDetectFailedStageEnum.MANUAL.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateManualFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        } else if (DynamicDetectFailedStageEnum.LAW.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateLawFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "法规检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    @Caching(evict = {@CacheEvict(value = "privacy-detection:task", allEntries = true, beforeInvocation = true),
            @CacheEvict(value = "privacy-detection:action", allEntries = true, beforeInvocation = true)})
    public void removeTaskCache() {
        log.info("检测数据更新，清除缓存");
    }

    private CommonDetectInfo analysisMiitDetectResult(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, List<TSdkLibrary> sdkLibraries,
                                                      List<SuspiciousSdkBehaviorVO> suspiciousSdkBehaviorVOS) {
        // 深度检测直接返回
        if (!TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            return null;
        }
        privacyLawsDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsResultMapper.deleteByTaskId(task.getTaskId());
        appLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        appLawsRiskCollectMapper.deleteByTaskId(task.getTaskId());
        sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
        sdkLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsConclusionActionMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        CommonDetectInfo commonDetectInfo = buildAndroidDetectInfo(task, uncompress, detectDataMap);
        
        //查询检测项
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        for (PrivacyLawId lawId: getDetectionLawIds(extend, task.getTerminalType())) {
            analysisLaw(task, sdkVOList, commonDetectInfo, suspiciousSdkBehaviorVOS, lawId);
        }
        saveSdkDectDetail(task, sdkVOList);
        return commonDetectInfo;
    }

    /**
     * 重新录入sdk统计数据
     * @param task
     */
    @Transactional
    public void restoreSdkDectDetail(TTask task) {
        log.info("重新录入sdk统计数据");
        sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        saveSdkDectDetail(task, sdkVOList);
    }

    @Override
    protected CustomDetectInfo makeCustomDetectInfo(String itemNo, Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys) {
        CustomDetectInfo customDetectInfo = super.makeCustomDetectInfo(itemNo, itemNoWithActionAndKeys);
        customDetectInfo.setCloudPhoneAddressBook(cloudPhoneAddressBook);
        return customDetectInfo;
    }

    private void analysisPrivacyCheck(TTask task, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, CommonDetectInfo commonDetectInfo) {
        PrivacyPolicyCheck isTransfer = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isSharedPrefs = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isOutSide = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isSdk = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isApp = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isCookie = new PrivacyPolicyCheck();
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            List<TPrivacySensitiveWord> sensitiveWords = detectData.getPrivacySensitiveWords();
            if (SensitiveUtils.havePlaintextTransmission(sensitiveWords)) {
                isTransfer.setNonCompliance(true);
                isTransfer.getBehaviorStageEnums().add(entry.getKey());
            }
            List<TPrivacySharedPrefs> sharedPrefs = detectData.getPrivacySharedPrefs();
            if (CollectionUtils.isNotEmpty(sharedPrefs)) {
                isSharedPrefs.setNonCompliance(true);
                isSharedPrefs.getBehaviorStageEnums().add(entry.getKey());
            }
            List<TPrivacyOutsideAddress> outsideAddresses = detectData.getPrivacyOutsideAddresses();
            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                for (TPrivacyOutsideAddress outsideAddress : outsideAddresses) {
                    if (outsideAddress.getOutside() == PrivacyStatusEnum.YES.getValue()) {
                        isOutSide.setNonCompliance(true);
                        isOutSide.getBehaviorStageEnums().add(entry.getKey());
                        break;
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                for (TPrivacyOutsideAddress outsideAddress : outsideAddresses) {
                    if (CookieMarkEnum.HAVE.getValue().equals(outsideAddress.getCookieMark())) {
                        isCookie.setNonCompliance(true);
                        isCookie.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }

            if (entry.getKey() == BehaviorStageEnum.BEHAVIOR_GRANT) {
                List<TPrivacyActionNougat> actionNougats = detectData.getPrivacyActionNougats();
                for (TPrivacyActionNougat actionNougat : actionNougats) {
                    if (ExecutorTypeEnum.APP.getValue().equals(actionNougat.getExecutorType())
                            && StringUtils.equals(actionNougat.getIsPersonal(), String.valueOf(PrivacyStatusEnum.YES.getValue()))) {
                        isApp.setNonCompliance(true);
                        isApp.getBehaviorStageEnums().add(entry.getKey());
                    }
                    if (ExecutorTypeEnum.SDK.getValue().equals(actionNougat.getExecutorType())
                            && StringUtils.equals(actionNougat.getIsPersonal(), String.valueOf(PrivacyStatusEnum.YES.getValue()))) {
                        isSdk.setNonCompliance(true);
                        isSdk.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }
        }

        try {
			TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());
			// 数据更新，需要taskId
			taskDetail.setTaskId(task.getTaskId());
			// 清除历史记录
			privacyPolicyResultMapper.deleteByTaskId(task.getTaskId());
			Map<String, TPrivacyPolicyItem> itemMap = new HashMap<>();
			for (TPrivacyPolicyItem privacyPolicyItem : privacyPolicyItemMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue())) {
			    itemMap.put(privacyPolicyItem.getItem_no(), privacyPolicyItem);
			}
			List<TPrivacyPolicyResult> policyResultList = new ArrayList<>();
			// 一揽子授权
			if (task.getTaskTatus().getValue() == DetectionStatusEnum.DETECTION_OVER.getValue()) {
			    TPrivacyPolicyResult permissionResult = new TPrivacyPolicyResult();
			    permissionResult.setTaskId(task.getTaskId());
			    permissionResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.PERMISSION.itemNo).getId());
                permissionResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			    int targetSdkVersion = CommonUtil.findTargetSdkVersion(taskDetail);
			    if (targetSdkVersion >= 23) {
			        permissionResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
			    } else {
			        permissionResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
			    }
			    permissionResult.setDetailResult("该应用targetSdkVersion=" + targetSdkVersion);
			    policyResultList.add(permissionResult);
			}

			// 通信明文传输-传输个人信息
			TPrivacyPolicyResult transferResult = new TPrivacyPolicyResult();
			transferResult.setTaskId(task.getTaskId());
			transferResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.CLEAR_TEXT_DATA.itemNo).getId());
            transferResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			if (isTransfer.isNonCompliance()) {
			    transferResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
			    transferResult.setBehaviorStage(isTransfer.getNonComplianceBehaviorStage());
			} else {
			    transferResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
			}
			policyResultList.add(transferResult);

			// 境外通信访问
			TPrivacyPolicyResult outSideResult = new TPrivacyPolicyResult();
			outSideResult.setTaskId(task.getTaskId());
			outSideResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.OUTSIDE_ADDRESS_DATA.itemNo).getId());
            outSideResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			if (isOutSide.isNonCompliance()) {
			    outSideResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
			    outSideResult.setBehaviorStage(isOutSide.getNonComplianceBehaviorStage());
			} else {
			    outSideResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
			}
			policyResultList.add(outSideResult);

			// 快速检测才检测
			if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
			    //个人信息保护政策检测
			    policyResultList.add(buildPrivacyPolicyDetailResult(taskDetail, commonDetectInfo, itemMap));
			    //私自收集使用个人信息
			    TPrivacyPolicyResult appPersonalInfoResult = new TPrivacyPolicyResult();
			    appPersonalInfoResult.setTaskId(task.getTaskId());
			    appPersonalInfoResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.APP_COLLECT_PRIVACY.itemNo).getId());
                appPersonalInfoResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			    if (isApp.isNonCompliance()) {
			        appPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
			        appPersonalInfoResult.setBehaviorStage(isApp.getNonComplianceBehaviorStage());
			    } else {
			        appPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
			    }
			    policyResultList.add(appPersonalInfoResult);

			    //第三方私自收集个人信息
			    TPrivacyPolicyResult sdkPersonalInfoResult = new TPrivacyPolicyResult();
			    sdkPersonalInfoResult.setTaskId(task.getTaskId());
			    sdkPersonalInfoResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.SDK_COLLECT_PRIVACY.itemNo).getId());
                sdkPersonalInfoResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			    if (isSdk.isNonCompliance()) {
			        sdkPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
			        sdkPersonalInfoResult.setBehaviorStage(isSdk.getNonComplianceBehaviorStage());
			    } else {
			        sdkPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
			    }
			    policyResultList.add(sdkPersonalInfoResult);
			}

			// Androidmanifest.xml文件修改
			TPrivacyPolicyResult androidManifestResult = new TPrivacyPolicyResult();
			androidManifestResult.setTaskId(task.getTaskId());
			androidManifestResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.ANDROID_MANIFEST.itemNo).getId());
			androidManifestResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
            androidManifestResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
			policyResultList.add(androidManifestResult);
            TPrivacyPolicyItem cookieItem = itemMap.get(PrivacyPolicyItemNoEnum.COOKIE.itemNo);
			if (cookieItem != null) {
                // 使用cookie及其同类技术
                TPrivacyPolicyResult cookieResult = new TPrivacyPolicyResult();
                cookieResult.setTaskId(task.getTaskId());
                cookieResult.setPolicyItemId(cookieItem.getId());
                cookieResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
                if (isCookie.isNonCompliance()) {
                    cookieResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                    cookieResult.setBehaviorStage(isCookie.getNonComplianceBehaviorStage());
                } else {
                    cookieResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                }
                policyResultList.add(cookieResult);
            }

            TPrivacyPolicyItem xiaomiAppStoreSpecialized = itemMap.get(PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_SPECIALIZED.itemNo);
            // 小米应用商店检测专项数据保存
            if (xiaomiAppStoreSpecialized != null) {
                TPrivacyPolicyResult xiaomiAppStoreInfoResult = new TPrivacyPolicyResult();
                xiaomiAppStoreInfoResult.setTaskId(task.getTaskId());
                xiaomiAppStoreInfoResult.setPolicyItemId(xiaomiAppStoreSpecialized.getId());
                xiaomiAppStoreInfoResult.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
                if (saveXiaomiAppstoreDetect(task.getTaskId(), detectDataMap)) {
                    xiaomiAppStoreInfoResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                } else {
                    xiaomiAppStoreInfoResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                }
                policyResultList.add(xiaomiAppStoreInfoResult);
            }

            TPrivacyPolicyItem xiaomiAppStoreStickBroadcast = itemMap.get(PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_STICKY_BROADCAST.itemNo);
            if (xiaomiAppStoreStickBroadcast != null) {
                // 小米应用商店粘性广播检测
                TPrivacyPolicyResult xiaomiAppStoreStickyBroadcast = new TPrivacyPolicyResult();
                xiaomiAppStoreStickyBroadcast.setTaskId(task.getTaskId());
                xiaomiAppStoreStickyBroadcast.setPolicyItemId(xiaomiAppStoreStickBroadcast.getId());
                xiaomiAppStoreStickyBroadcast.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
                List<String> permissionList = privacyDetectionService.getDetectionPermissions(task.getApkDetectionDetailId());
                if (permissionList.contains("android.permission.BROADCAST_STICKY")) {
                    xiaomiAppStoreStickyBroadcast.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                } else {
                    xiaomiAppStoreStickyBroadcast.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                }
                policyResultList.add(xiaomiAppStoreStickyBroadcast);
            }

            addAppStoreSelfAssessment(policyResultList, task.getTaskId(), itemMap.get(PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_APP_NO.itemNo));
            addAppStoreSelfAssessment(policyResultList, task.getTaskId(), itemMap.get(PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_LOCATION.itemNo));
            addAppStoreSelfAssessment(policyResultList, task.getTaskId(), itemMap.get(PrivacyPolicyItemNoEnum.XIAOMI_APP_STORE_SOURCE_DIR.itemNo));
            addAppStoreSelfAssessment(policyResultList, task.getTaskId(), itemMap.get(PrivacyPolicyItemNoEnum.HUAWEI_APP_STORE_ACTION.itemNo));

            TPrivacyPolicyItem huaweiAppStorePersonalRisk = itemMap.get(PrivacyPolicyItemNoEnum.HUAWEI_APP_STORE_PERSONAL_RISK.itemNo);
            if (huaweiAppStorePersonalRisk != null) {
                // 华为应用商店检测专项-个人信息风险数据保存
                TPrivacyPolicyResult huaweiAppStoreInfoResult = new TPrivacyPolicyResult();
                huaweiAppStoreInfoResult.setTaskId(task.getTaskId());
                huaweiAppStoreInfoResult.setPolicyItemId(huaweiAppStorePersonalRisk.getId());
                huaweiAppStoreInfoResult.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
                if (isTransfer.isNonCompliance() || isSharedPrefs.isNonCompliance()) {
                    huaweiAppStoreInfoResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                    if (isTransfer.isNonCompliance()) {
                        huaweiAppStoreInfoResult.setBehaviorStage(isTransfer.getNonComplianceBehaviorStage());
                    } else if (isSharedPrefs.isNonCompliance()) {
                        huaweiAppStoreInfoResult.setBehaviorStage(isSharedPrefs.getNonComplianceBehaviorStage());
                    }
                } else {
                    huaweiAppStoreInfoResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                }
                policyResultList.add(huaweiAppStoreInfoResult);
            }

            TPrivacyPolicyItem huaweiAppStoreSecurityDetection = itemMap.get(PrivacyPolicyItemNoEnum.HUAWEI_APP_STORE_SECURITY_DETECTION.itemNo);

            if (huaweiAppStoreSecurityDetection != null) {
                //华为应用商店检测专项-安全检测
                TPrivacyPolicyResult huaweiAppStoreSecureDetection = new TPrivacyPolicyResult();
                huaweiAppStoreSecureDetection.setTaskId(task.getTaskId());
                huaweiAppStoreSecureDetection.setPolicyItemId(huaweiAppStoreSecurityDetection.getId());
                huaweiAppStoreSecureDetection.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
                huaweiAppStoreSecureDetection.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                net.sf.json.JSONObject itemResultJson = CommonUtil.findTaskDetailItemResult(taskDetail, HUAWEI_SECURITY_DETECTION_ITEM);
                if(itemResultJson != null && itemResultJson.get("unsafe_num") != null && itemResultJson.getInt("unsafe_num")>0) {
                    huaweiAppStoreSecureDetection.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                }
                policyResultList.add(huaweiAppStoreSecureDetection);
            }

            TPrivacyPolicyItem sdkContrast = itemMap.get(PrivacyPolicyItemNoEnum.SDK_CONTRAST.itemNo);
            if (sdkContrast != null) {
                //全国SDK服务管理平台-对比评估
                TPrivacyPolicyResult huaweiAppStoreSecureDetection = new TPrivacyPolicyResult();
                huaweiAppStoreSecureDetection.setTaskId(task.getTaskId());
                huaweiAppStoreSecureDetection.setPolicyItemId(sdkContrast.getId());
                huaweiAppStoreSecureDetection.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
                huaweiAppStoreSecureDetection.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
                // TODO
                policyResultList.add(huaweiAppStoreSecureDetection);
            }

			privacyPolicyResultMapper.insertList(policyResultList);
		} catch (Exception e) {
			e.getMessage();
		}
    }

    /**
     *  添加自主评估检测项
     */
    private void addAppStoreSelfAssessment(List<TPrivacyPolicyResult> policyResultList, Long taskId, TPrivacyPolicyItem policyItem) {
        if (policyItem != null) {
            policyResultList.add(buildAppStoreSelfAssessment(taskId, policyItem.getId()));
        }
    }

    /**
     *  构建自主评估检测项
     */
    private TPrivacyPolicyResult buildAppStoreSelfAssessment(Long taskId, Long policyItemId) {
        TPrivacyPolicyResult result = new TPrivacyPolicyResult();
        result.setTaskId(taskId);
        result.setPolicyItemId(policyItemId);
        result.setCategory(PrivacyPolicyResultCategoryEnum.APP_STORE.value);
        result.setStatus(PrivacyPolicyResultStatusEnum.SELF_ASSESSMENT.status);
        return result;
    }

    protected boolean saveXiaomiAppstoreDetect(Long taskId, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        TXiaomiAppStoreDetectItem query = new TXiaomiAppStoreDetectItem();
        query.setStatus(StatusEnum.NORMAL.itemValue());
        List<TXiaomiAppStoreDetectItem> detectItems = xiaomiAppStoreDetectItemMapper.select(query);
        boolean havePersonalAction = false;
        // 删除旧数据
        TXiaomiAppStoreDetectResult detectResult = new TXiaomiAppStoreDetectResult();
        detectResult.setTaskId(taskId);
        xiaomiAppStoreDetectResultMapper.delete(detectResult);
        for (TXiaomiAppStoreDetectItem item:detectItems) {
            TXiaomiAppStoreDetectResult result = new TXiaomiAppStoreDetectResult();
            result.setTaskId(taskId);
            result.setItemId(item.getId());
            result.setUpdateTime(new Date());
            result.setCreateTime(new Date());
            if (StringUtils.isNotBlank(item.getActionIds())) {
                List<Long> actions = Arrays.stream(item.getActionIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (StringUtils.isNotBlank(item.getDetectionResultItemNo())) {
                    result.setHavePersonalAction(haveItemAction(taskId, item.getDetectionResultItemNo(), actions, result));
                } else {
                    result.setHavePersonalAction(haveAction(actions, detectDataMap));
                }
            }
            xiaomiAppStoreDetectResultMapper.insert(result);
        }
        return havePersonalAction;
    }

    private boolean haveItemAction(Long taskId, String itemNo, List<Long> actionIds, TXiaomiAppStoreDetectResult result) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVO = commonMongodbService.findDetectionResult(task.getApkDetectionDetailId(),
                Collections.singletonList(itemNo));
        if (taskDetailVO == null) {
            return false;
        }
        String detection_result = taskDetailVO.getDetection_result().toString();
        JSONArray jsonArray = JSON.parseArray(detection_result);
        JSONArray resultContent = jsonArray.getJSONObject(0).getJSONArray("result_content");
        if (resultContent == null) {
            return false;
        }
        for (int i=0; i<resultContent.size(); i++) {
            JSONObject item = resultContent.getJSONObject(i);
            if (actionIds.contains(item.getLong("actionId"))) {
            	// v5.1 2025-3-20添加
            	StringBuffer buff = new StringBuffer();
            	buff.append("文件路径：");
            	buff.append(item.getString("file_path")+"\r\n");
            	buff.append("方法名称：");
            	buff.append(item.getString("methodName")+"\r\n");
            	buff.append("关键代码位置：");
            	buff.append(item.getString("key_code")+"\r\n");
            	result.setStackInfo(buff.toString());
                return true;
            }
        }
        return false;
    }

    private boolean haveAction(List<Long> actionIds, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        if (Objects.nonNull(detectDataMap)) {
            return detectDataMap.values().stream().anyMatch(detectDataBO -> haveAction(actionIds, detectDataBO.getPrivacyActionNougats()));
        }
        return false;
    }

    private boolean haveAction(List<Long> actionIds, List<TPrivacyActionNougat> actionNougats) {
        if (CollectionUtils.isNotEmpty(actionNougats)) {
            return actionNougats.stream().anyMatch(action -> actionIds.contains(action.getActionId()));
        }
        return false;
    }

    /**
     * 提取疑似SDK数据
     *
     * @param detectDataMap
     * @param taskDetailVo
     * @param sdkLibraries
     * @return
     */
    protected List<SuspiciousSdkBO> findSuspiciousSdks(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, TaskDetailVO taskDetailVo,
                                                       List<TSdkLibrary> sdkLibraries, List<TSdkLibrary> firstPartyLibraries) {
        log.info("findSuspiciousSdks");
        try {
            Set<String> sdkNameSet = new ConcurrentHashSet<>();
            Set<String> firstPartyNameSet = new ConcurrentHashSet<>();
            Set<String> twoLevelSdkNameSet = new ConcurrentHashSet<>();
            addPackageNames(sdkLibraries, sdkNameSet);
            addPackageNames(firstPartyLibraries, firstPartyNameSet);
            addTwoLevelPackageNames(sdkLibraries, twoLevelSdkNameSet);
            Map<String, List<TOdpCompanyProduct>> productMap = makeProductMap();
            Map<String, List<TOdpCompanyProduct>> companyMap = makecompanyMap();
			log.info("findSuspiciousSdks sdkLibraries finish");
			List<SuspiciousSdkBO> suspiciousSdkBOList = detectDataMap
                    .values()
			        .parallelStream()
			        .map(detectDataBO -> {
			            // 行为数据
			            return extractPackageNameByDetectData(detectDataBO, taskDetailVo, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap);
			        })
			        .flatMap(Collection::stream)
			        .collect(Collectors.toList());
			log.info("findSuspiciousSdks filter packageName");
			Set<String> allSuspiciousSdkNames = suspiciousSdkBOList.stream().map(sdk -> sdk.getSuspiciousSdk().getPackageName()).collect(Collectors.toSet());
            List<SuspiciousSdkBO> filterList = suspiciousSdkBOList.parallelStream().filter(
			        // 有com.abc.xxx时，要把com.abc的记录排除掉
			        sdk -> allSuspiciousSdkNames.stream().noneMatch(name -> {
			            String packageName = sdk.getSuspiciousSdk().getPackageName();
			            return name.length() > packageName.length() && name.startsWith(packageName);
			        }))
			        .collect(Collectors.toList());
            log.info("updateDetectDataExecutorBySuspiciousSdk end");
            return filterList;
		} catch (Exception e) {
			log.error("疑似SDK匹配出错", e);
		}
        return null;
    }

    private List<SuspiciousSdkBO> extractPackageNameByDetectData(DetectDataBO detectDataBO, TaskDetailVO taskDetailVO, Set<String> sdkNameSet,
                                                                 Set<String> firstPartyNameSet, Set<String> twoLevelSdkNameSet,
                                                                 Map<String, List<TOdpCompanyProduct>> productMap, Map<String, List<TOdpCompanyProduct>> companyMap) {
        List<SuspiciousSdkBO> sdkBOList = new ArrayList<>();
        sdkBOList.addAll(extractPackageNameActionExecutor(detectDataBO.getPrivacyActionNougats(), taskDetailVO, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap));
        sdkBOList.addAll(extractPackageNameActionExecutor(detectDataBO.getPrivacyOutsideAddresses(), taskDetailVO, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap));
        sdkBOList.addAll(extractPackageNameActionExecutor(detectDataBO.getPrivacySensitiveWords(), taskDetailVO, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap));
        sdkBOList.addAll(extractPackageNameActionExecutor(detectDataBO.getPrivacySharedPrefs(), taskDetailVO, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap));
        log.info("extractPackageNameByDetectData extractPackageName {} 条", sdkBOList.size());
        return sdkBOList;
    }

    private List<SuspiciousSdkBO> extractPackageNameActionExecutor(List<? extends ActionExecutor> actionExecutors, TaskDetailVO taskDetailVO,
                                                                   Set<String> sdkNameSet, Set<String> firstPartyNameSet, Set<String> twoLevelSdkNameSet,
                                                                   Map<String, List<TOdpCompanyProduct>> productMap, Map<String, List<TOdpCompanyProduct>> companyMap) {
        return actionExecutors
                .stream()
                .map(nougat -> {
                    List<SuspiciousSdkBO> sdkBOList = SuspiciousSdkUtils.extractPackageName(taskDetailVO, nougat,
                            sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, sdkWhitelistRuleService);
                    // 匹配行为信息的数据，把疑似SDK包名更新到调用主体名中
                    setSuspiciousSdkExecutorAndPackageName(nougat, sdkBOList, productMap, companyMap);
                    return sdkBOList;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public Map<String, List<TOdpCompanyProduct>> makeProductMap() {
        // 创建就公司产品数据
        List<TOdpCompanyProduct> productList = odpCompanyProductMapper.selectAll();
        return productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
    }

    public Map<String, List<TOdpCompanyProduct>> makecompanyMap() {
        // 构造一份公司名数据
        List<TOdpCompany> companyList = odpCompanyMapper.selectAll();
        return SuspiciousSdkUtils.buildCompanyMainProduct(companyList)
                .stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
    }

    /**
     * 更新sdk库和疑似sdk库的权限
     *
     * @param suspiciousSdkBOs
     */
    protected void updateSdkPermissionCodeAndInsertRecord(Long taskId, List<SuspiciousSdkBO> suspiciousSdkBOs) {
        log.info("updateSdkPermissionCodeAndInsertRecord");
        // 这两张表的数据量不大且基本固定，全读出来加快匹配速度，不用反复查数据库
        Map<String, TPermission> permissionMap = permissionMapper.findByTerminalType(null, TerminalTypeEnum.ANDROID.getValue())
                .stream().collect(Collectors.toMap(TPermission::getName, Function.identity()));
        Map<Long, TActionNougat> actionNougats = actionNougatMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue())
                .stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (key1, key2) -> key2));
        updateSuspiciousSdkLibraryCodesAndInsertRecord(taskId, suspiciousSdkBOs, actionNougats, permissionMap, TerminalTypeEnum.ANDROID);
    }

    /**
     * SDK库的新权限
     *
     * @param detectDataMap
     * @param sdkLibraries
     * @param permissionMap
     * @return
     */
    protected void updateSdkLibraryCodes(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, List<TSdkLibrary> sdkLibraries,
                                         Map<Long, TActionNougat> actionNougats, Map<String, TPermission> permissionMap) {
        Map<Long, String> updatePermissionCodeMap = findUpdatePermissionCode(detectDataMap, sdkLibraries, actionNougats, permissionMap);
        updatePermissionCodeMap.forEach((libraryId, codes) -> {
            log.info("执行更新权限 id={} permissionCodes={}", libraryId, codes);
            sdkLibraryMapper.updatePermissionCodesById(codes, libraryId);
        });
    }

    protected Map<Long, String> findUpdatePermissionCode(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, List<TSdkLibrary> sdkLibraries,
                                                         Map<Long, TActionNougat> actionNougats, Map<String, TPermission> permissionMap) {
        Map<String, List<TSdkLibrary>> sdkPackageNameMap = makeSdkPackageNameMap(sdkLibraries);
        Map<Long, String> updatePermissionCodeMap = new HashMap<>();
        detectDataMap.values().forEach(detectDataBO -> {
            // 分析行为数据
            detectDataBO.getPrivacyActionNougats()
                    .stream()
                    .filter(nougat -> ExecutorTypeEnum.SDK.getValue().equals(nougat.getExecutorType()))
                    .forEach(nougat -> {
                        // 找出跟调用主体包名相同的SDK列表，因为有可能SDK有多个版本
                        List<TSdkLibrary> sdkLibraryList = findSdkByPackageName(sdkPackageNameMap, nougat.getPackageName());
                        if (CollectionUtils.isEmpty(sdkLibraryList)) {
                            return;
                        }
                        TActionNougat actionNougat = actionNougats.get(nougat.getActionId());
                        if (Objects.isNull(actionNougat)) {
                            return;
                        }
                        // 找出这条行为的权限编码
                        TPermission permission = permissionMap.get(actionNougat.getActionPermission());
                        if (Objects.isNull(permission)) {
                            return;
                        }
                        // SDK更新权限
                        sdkLibraryList.forEach(sdkLibrary -> {
                            // 这条行为触发的权限不在SDK已有的权限信息中，添加到更新列表
                            if (!Objects.isNull(sdkLibrary) && StringUtils.isNoneBlank(sdkLibrary.getPermissionCodes())
                                    && !sdkLibrary.getPermissionCodes().contains(permission.getPermissionCode())) {
                                String updateCodes = updatePermissionCodeMap.get(sdkLibrary.getId());
                                if (updateCodes == null) {
                                    updateCodes = sdkLibrary.getPermissionCodes();
                                    log.info("需要新增权限 actionId={} packageName={} oldPermissionCodes={}",
                                            nougat.getActionId(), nougat.getPackageName(), updateCodes);
                                }
                                // 待更新权限列表里是否已经有了这个权限，有可能多个行为里都有这个新增权限，前面的已经添加进去了
                                if (!updateCodes.contains(permission.getPermissionCode())) {
                                    updateCodes = updateCodes + "," + permission.getPermissionCode();
                                }
                                updatePermissionCodeMap.put(sdkLibrary.getId(), updateCodes);
                            }
                        });
                    });
        });
        return updatePermissionCodeMap;
    }

    private List<TSdkLibrary> findSdkByPackageName(Map<String, List<TSdkLibrary>> sdkPackageNameMap, String packageNames) {
        return Arrays.stream(packageNames.split(","))
                .map(sdkPackageNameMap::get)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 把sdk数据转成包名直接查询的结构
     *
     * @param sdkLibraries
     * @return
     */
    protected Map<String, List<TSdkLibrary>> makeSdkPackageNameMap(List<TSdkLibrary> sdkLibraries) {
        Map<String, List<TSdkLibrary>> sdkPackageNameMap = new HashMap<>();
        sdkLibraries.forEach(sdkLibrary -> {
            if (CollectionUtils.isEmpty(sdkLibrary.getPackageList())) {
                return;
            }
            sdkLibrary.getPackageList()
                    .stream()
                    .filter(tPackage -> StringUtils.isNotBlank(tPackage.getPackageName()))
                    .forEach(tPackage -> {
                List<TSdkLibrary> libraryList = sdkPackageNameMap.computeIfAbsent(tPackage.getPackageName(), k -> new ArrayList<>());
                libraryList.add(sdkLibrary);
            });
        });
        return sdkPackageNameMap;
    }

    private CommonDetectInfo buildAndroidDetectInfo(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        log.info("TaskId:{} buildAndroidDetectInfo uncompress={}", task.getTaskId(), uncompress);

        // 组装公共参数
        CommonDetectInfo commonDetectInfo = buildCommonDetectInfo(task, uncompress, detectDataMap);

        // 从uncomperss中解析出的数据
        List<ResultDataLogBO> resultDataLogBOList = new ArrayList<>();
        File[] files = new File(uncompress).listFiles();
        if (files == null) {
            throw new UncompressFailException("数据文件不存在");
        }
        for (File file:files) {
            if (StringUtils.startsWith(file.getName(), "resultDataLog")) {
                resultDataLogBOList.addAll(androidResultDataLogParser.parser(file.getAbsolutePath(), commonDetectInfo.getApkName()));
            }
        }
        commonDetectInfo.setResultDataLogs(resultDataLogBOList);
        List<ResultDataLogBO> privacyResults = new ArrayList<>();
        for (ResultDataLogBO resultDataLogBO : resultDataLogBOList) {
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                privacyResults.add(resultDataLogBO);
            }
        }
        List<PrivacyPolicyTextInfo> subPagePrivacyDetailList = getSubPagePrivacyDetailByHtmlDir(uncompress + File.separator + "privacyhtml");
        //解析是否存在PDF隐私政策链接
        try {
            pdfPrivacyContent(uncompress, subPagePrivacyDetailList);
        } catch (IOException e1) {
            e1.getMessage();
        }
        TAssets assets = assetsMapper.getAssetIncludeDeletedByTaskId(task.getTaskId());
        List<ResultDataLogBO> privacyDetailResults = getPrivacyDetailList(privacyResults);
        // 先解析清单
        if (StringUtils.isNotBlank(assets.getThirdPartyShareListPath())) {
            analysisCheckListByUpload(assets, commonDetectInfo);
        } else {
            analysisCheckListByUi(commonDetectInfo, privacyDetailResults, subPagePrivacyDetailList);
        }
        // 解析隐私政策
        if (StringUtils.isNotBlank(assets.getPrivacyPolicyPath())) {
            // 用户有上传隐私文件
            analysisUserUploadPrivacyPolicy(assets, commonDetectInfo);
        } else if (privacyResults.isEmpty() && subPagePrivacyDetailList.isEmpty()) {
            commonDetectInfo.setHasPrivacyPolicy(false);
            commonDetectInfo.setPrivacyPolicyImg(null);
            commonDetectInfo.setPrivacyPolicyContent(null);
        } else {
            analysisDetectPrivacyPolicy(task, commonDetectInfo, privacyResults, subPagePrivacyDetailList);
        }
        // 没有单独的第三方SDK清单，尝试从隐私政策中解析
        analysisCheckListByPolicyContent(commonDetectInfo);
        commonDetectInfo.setTensorflowCheckBoxModelPath(checkboxModelPath);
        commonDetectInfo.setTensorflowCheckedModelPath(checkedModelPath);
        return commonDetectInfo;
    }

    private void analysisDetectPrivacyPolicy(TTask task,
                                             CommonDetectInfo commonDetectInfo,
                                             List<ResultDataLogBO> privacyResults,
                                             List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        commonDetectInfo.setHasPrivacyPolicy(true);
        // 隐私文本拼接 取最长文本图片
        String privacyImagePath = null;
        String privacyContent = null;
        int maxLength = 0;
        StringBuilder privacyContentBuilder = new StringBuilder();
        List<ResultDataLogBO> privacyBOList = new ArrayList<>();
        String appName = null;
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        if(assets != null) {
        	appName = assets.getName();
        }
        
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && StringUtils.isNotBlank(privacyResult.getUiDumpResult().getFullText())) {
                String[] uiList = privacyResult.getUiDumpResult().getFullText().split("\n");
                for (String string : uiList) {
                    if (StringUtils.isNoneBlank(string) && string.startsWith("。")) {
                        string = string.replaceFirst("。", "");
                    }
                    if (!privacyContentBuilder.toString().contains(string)) {
                        privacyContentBuilder.append(string).append("\n");
                    }
                }
                int length = privacyResult.getUiDumpResult().getFullText().length();
                //定义大于1500长度的页面内容
                if(length> ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH) {
                	privacyBOList.add(privacyResult);
                }
                if (length > maxLength) {
                    maxLength = length;
                    privacyImagePath = privacyResult.getImgPath();
                    privacyContent = privacyContentBuilder.toString();
                }
            }
        }
        
        if(privacyContent != null && privacyContent.length()<privacyContentBuilder.toString().length()) {
       	 	privacyContent = privacyContentBuilder.toString();
        }
        
        //增加精准判断隐私页面v5.1-20241226
        if(privacyBOList != null && privacyBOList.size()>0){
        	StringBuilder privacyContentBuilderNew = new StringBuilder();
        	//1、循环 截取文本内容前50个字获取包含隐私政策关键词的页面 
        	maxLength = 0;
        	//定义第一段隐私政策内容
        	String firstPrivacyContent = null;
        	//根据App名称定义精准隐私政策截图
//        	String imagePath = null;
        	for(ResultDataLogBO bo : privacyBOList){
        		String privacyContentNew = bo.getUiDumpResult().getFullText();
        		//提取前50个字段来判断是否为隐私政策
//        		String fullTextStart = privacyContentNew != null ? privacyContentNew.substring(0, Math.min(privacyContentNew.length(), 100)) : "";
        		String fullTextStart = getChinaWord(privacyContentNew);
        		System.out.println("隐私政策前50个字为："+fullTextStart);
        		if(MiitWordKit.checkIsContainsPrivacy(fullTextStart) && !fullTextStart.contains("儿童隐私") && !fullTextStart.contains("用户协议") && !fullTextStart.contains("用户服务协议")) {
        			 int length = privacyContentNew.length();
        			 if (length > maxLength && StringUtils.isNotBlank(appName) && fullTextStart.contains(appName)) {
                         maxLength = length;
                         privacyImagePath = bo.getImgPath();
                         firstPrivacyContent = privacyContentNew;
                     }
        		}
        		if (!privacyContentBuilderNew.toString().contains(privacyContentNew)) {
        			privacyContentBuilderNew.append(privacyContentNew).append("\n");
        		}
        	}
        	
        	privacyContent = privacyContentBuilderNew.toString();
        	if(privacyContentBuilderNew.length()> ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH && StringUtils.isNotBlank(firstPrivacyContent)) {
        		//把截图的隐私政策内容放到第一段
        		privacyContent = firstPrivacyContent+"\n"+privacyContentBuilderNew.toString().replace(firstPrivacyContent, "");
        	}
        }
        
        //如果取不到截图，随机给一个
        if (StringUtils.isBlank(privacyImagePath) && subPagePrivacyDetailList.size() > 0) {
            privacyContent = subPagePrivacyDetailList.get(0).content;
        }
        commonDetectInfo.setPrivacyPolicyContent(mergePrivacyPolicySubPage(privacyContent, subPagePrivacyDetailList));
        uploadPrivacyPolicyImage(task, commonDetectInfo, privacyImagePath);
        setPrivacyPolicyNlp(commonDetectInfo);
    }
    
    //获取前面50个中文字来判断隐私政策内容
    private String getChinaWord(String privacyContentNew){
    	String fullTextStart = "";
    	if (privacyContentNew != null) {
    	    int chineseCount = 0;
    	    int endIndex = 0;
    	    for (int i = 0; i < privacyContentNew.length(); i++) {
    	        char c = privacyContentNew.charAt(i);
    	        // 判断是否为中文字符（基本Unicode范围）
    	        if (c >= '\u4E00' && c <= '\u9FFF') {
    	            chineseCount++;
    	            if (chineseCount == 50) {
    	                endIndex = i + 1; // 截取到当前字符的下一位
    	                break;
    	            }
    	        }
    	        endIndex = i + 1; // 更新索引以防中文字符不足50个
    	    }
    	    // 确保不超过字符串长度
    	    endIndex = Math.min(endIndex, privacyContentNew.length());
    	    fullTextStart = privacyContentNew.substring(0, endIndex);
    	}
    	return fullTextStart;
    }

    private List<ResultDataLogBO> getPrivacyDetailList(List<ResultDataLogBO> privacyResults) {
        // 隐私政策详情文件
        List<ResultDataLogBO> privacyDetailResults = new ArrayList<>();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && privacyResult.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()) {
                privacyDetailResults.add(privacyResult);
            }
        }
        // 没详情文件 取所有的
        if (privacyDetailResults.size() == 0) {
            privacyDetailResults.addAll(privacyResults);
        }
        return privacyDetailResults;
    }

    //解析是否存在PDF隐私政策链接
    private static void pdfPrivacyContent(String uncompress, List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) throws IOException {
        List<String> filePaths = CommonUtil.listAllFilesInDir(uncompress + File.separator + "capture_info", new String[]{".txt", ".TXT"}, true);
        if (filePaths.size() == 0) {
            return;
        }
        long t1 = System.currentTimeMillis();
        List<String> list = null;
        try {
            //得到PDF链接地址
            list = URLTokenizer.threadSegment(filePaths);
        } catch (InterruptedException | ExecutionException e) {
            e.getMessage();
        }

        if (list == null || list.size() == 0) {
            return;
        }
        log.info("得到PDF链接地址:{}", list.toString());
        for (int i = 0; i < list.size(); i++) {
            //识别PDF内容
            String url = list.get(i);
            String fileName = url.substring(url.lastIndexOf("/") + 1, url.length());
            String pdfLoalPath = uncompress + File.separator + fileName;
//			HttpUtils.download(list.get(i), pdfLoalPath, null);
            FileUtils.copyURLToFile(new URL(list.get(i)), new File(pdfLoalPath));
            if (!new File(pdfLoalPath).exists()) {
                continue;
            }
            String privacyContent = PdfToTextFileUtil.getPdfFileText(pdfLoalPath);
            if (StringUtils.isBlank(privacyContent)) {
                continue;
            }
            boolean isContainsPirvacy = cn.ijiami.detection.miit.kit.MiitWordKit.checkIsContainsPrivacy(privacyContent);
            if (!isContainsPirvacy || privacyContent.length() < 200) {
                continue;
            }
            PrivacyPolicyTextInfo info = new PrivacyPolicyTextInfo(fileName, privacyContent);
            subPagePrivacyDetailList.add(info);
        }
        long t2 = System.currentTimeMillis();
        log.info("识别耗时：{}", (t2 - t1));
    }

//    public String getMD5Str(String str) {
//        try {
//            // 生成一个MD5加密计算摘要
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            // 计算md5函数
//            md.update(str.getBytes());
//            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
//            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
//            return new BigInteger(1, md.digest()).toString(16);
//        } catch (Exception e) {
//        }
//        return null;
//    }
//    
    public static String getMD5Str(String str) {
        if (str == null) {
            return null; // 输入为空时直接返回 null
        }

        try {
            // 获取 MD5 摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 使用 UTF-8 编码将字符串转换为字节数组
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为 32 位十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                // 将每个字节转换为两位的十六进制表示
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补齐前导零
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            log.error("Error calculating MD5 for input: {}", str, e);
            return null;
        }
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @param sdkLibraries
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisIdbDataStageUncompress(TaskDetailVO taskDetailVo, String uncompress, List<TSdkLibrary> sdkLibraries, IdbStagedDataEnum idbUploadDataStage) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        DetectDataBO detectData = analyzeZipAction(idbUploadDataStage.getBehaviorStage(), idbUploadDataStage.getFileNames(), uncompress, taskDetailVo, sdkLibraries, sensitiveWords);
        if (Objects.nonNull(detectData)) {
            detectDataMap.put(idbUploadDataStage.getBehaviorStage(), detectData);
        }
        return detectDataMap;
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @param sdkLibraries
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisIdbDataStageUncompress(TaskDetailVO taskDetailVo, String uncompress, List<TSdkLibrary> sdkLibraries) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                String fileNames = entry.getValue();
                DetectDataBO detectData = analyzeZipAction(behaviorStage, fileNames, uncompress, taskDetailVo, sdkLibraries, sensitiveWords);
                if (Objects.nonNull(detectData)) {
                    detectDataMap.put(behaviorStage, detectData);
                }
            } catch (Exception e) {
                log.error("数据解析失败", e);
            }
        }
        return detectDataMap;
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @param isRetry
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisManual(TaskDetailVO taskDetailVo, String uncompress, boolean isRetry, List<TSdkLibrary> sdks) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                String fileNames = entry.getValue();
                //如果是重试就重新对zip包重新解压，解析
                if(isRetry) {
                	detectDataMap.put(entry.getKey(), analyzeZipAction(behaviorStage, fileNames, uncompress, taskDetailVo, sdks, sensitiveWords));
                }else {
                	detectDataMap.put(entry.getKey(), analyzeAction(behaviorStage, fileNames, uncompress, taskDetailVo, sensitiveWords));
                }
            } catch (Exception e) {
                log.error("数据解析失败", e);
            }
        }
        return detectDataMap;
    }

    /**
     * 检测数据入库
     *
     * @param detectDataMap
     */
    private void insertTaskAnalyzeResult(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, Map<Long, String> screenshotMap, Long taskId) {
        log.info("insertTaskAnalyzeResult");
        // 获取静态检测已经入库的检测项数据(标记为：静态数据)
        List<TPrivacyOutsideAddress> outsideAddresses = privacyOutsideAddressMapper.findByTaskId(taskId, 0);
        // 数据重新入库
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            // 计算行为触发次数（秒/次）
            setNougatsCycleTrigger(detectData.getPrivacyActionNougats(), TerminalTypeEnum.ANDROID);
            // 堆栈数据
            List<List<TPrivacyActionNougat>> actionInserts = DataHandleUtil.split(detectData.getPrivacyActionNougats());
            for (List<TPrivacyActionNougat> actionInsert : actionInserts) {
                privacyActionNougatMapper.insertList(actionInsert);
                // 扩展表数据需要放到主表写入后有id了再写入，需要关联数据
                List<TPrivacyActionNougatExtend> extendList = actionInsert.stream()
                        .filter(nougat -> StringUtils.isNoneBlank(nougat.getJniStackInfo()))
                        .map(TPrivacyActionNougatExtend::make)
                        .collect(Collectors.toList());
                if (!extendList.isEmpty()) {
                    privacyActionNougatExtendMapper.insertList(extendList);
                }
                //动态检测截图入库
                //新增保存应用行为截图的方法 2021/6/11
                screenshotImageService.saveDynamicBehaviorImg(taskId, screenshotMap, actionInsert);
            }

            // 通讯行为数据
            List<TPrivacyOutsideAddress> privacyOutsideAddresses = detectData.getPrivacyOutsideAddresses();
            // 静态数据重新入库
            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                outsideAddresses.forEach(o -> o.setId(null));
                privacyOutsideAddresses.addAll(outsideAddresses);
            }
            List<List<TPrivacyOutsideAddress>> outSideInserts = DataHandleUtil.split(privacyOutsideAddresses);
            for (List<TPrivacyOutsideAddress> outsideInsert : outSideInserts) {
                privacyOutsideAddressMapper.insertList(outsideInsert);
                //动态检测截图入库
                //新增保存应用行为截图的方法 2021/7/8
                screenshotImageService.saveImgOfOutsideData(taskId, screenshotMap, outsideInsert);
            }

            // 传输个人信息
            List<List<TPrivacySensitiveWord>> sensitiveInserts = DataHandleUtil.split(detectData.getPrivacySensitiveWords());
            for (List<TPrivacySensitiveWord> sensitiveInsert : sensitiveInserts) {
                privacySensitiveWordMapper.insertList(sensitiveInsert);
                //动态检测截图入库
                //新增保存应用行为截图的方法 2021/6/16
                screenshotImageService.saveDynamicBehaviorImgSensitiveWord(taskId, screenshotMap, sensitiveInsert);
            }

            // 存储个人信息
            List<List<TPrivacySharedPrefs>> shareInserts = DataHandleUtil.split(detectData.getPrivacySharedPrefs());
            for (List<TPrivacySharedPrefs> shareInsert : shareInserts) {
                privacySharedPrefsMapper.insertList(shareInsert);
                //动态检测截图入库
                //新增保存应用行为截图的方法 2021/6/16
                screenshotImageService.saveImgOfSharedData(taskId, screenshotMap, shareInsert);
            }
        }
    }

    /**
     * 保存压缩包并返回
     *
     * @param md5    文件md5
     * @param taskId 任务ID
     * @param suffix 文件后缀
     * @return
     */
    private String saveAndUncompress(String md5, Long taskId, String suffix, MultipartFile data) throws IOException {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = saveFile(md5, taskId, suffix, data);
        return CommonUtil.uncompress(file.getAbsolutePath(), dynamicPath + UuidUtil.uuid());
    }

    protected Map<Long, TActionNougat> getActionNougatMap(TerminalTypeEnum terminalTypeEnum) {
        return actionNougatMapper.findByTerminalType(terminalTypeEnum.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
    }

    /**
     * 分析检测得到的数据
     *
     * @param behaviorStage
     * @param fileNames
     * @param uncompress
     * @param taskDetailVO
     * @param sdks
     * @param sensitiveWords
     * @return
     */
    private DetectDataBO analyzeZipAction(BehaviorStageEnum behaviorStage, String fileNames, String uncompress, TaskDetailVO taskDetailVO, List<TSdkLibrary> sdks,
                                       List<TSensitiveWord> sensitiveWords) {
        log.info("检测数据解析，任务ID：[{}]，资产名称：[{}]，解析阶段：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name(), behaviorStage.getName());
        String[] fileNamePaths = fileNames.split(",");
        // 分析堆栈数据
        String behaviorFilePath = uncompress + File.separator + fileNamePaths[0];
        List<TPrivacyActionNougat> privacyActionNougats = behaviorInfoAction.analyzeBehaviorInfo(behaviorFilePath, taskDetailVO, behaviorStage, sdks);
        // 是否只保存个人信息行为
        boolean onlySavePersonalBehavior = isOnlySavePersonalBehavior(taskDetailVO.getTaskId());
        if (onlySavePersonalBehavior) {
            Map<Long, TActionNougat> actionNougatMap = getActionNougatMap(TerminalTypeEnum.ANDROID);
            privacyActionNougats = privacyActionNougats.stream()
                    .filter(nougat -> {
                        TActionNougat actionNougat = actionNougatMap.get(nougat.getActionId());
                        return Objects.nonNull(actionNougat) && actionNougat.getPersonal() == PrivacyStatusEnum.YES.getValue();
                    }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(privacyActionNougats)) {
            return null;
        }
        List<TPrivacyActionNougat> item_18002 = privacyActionNougats.stream().filter(a -> a.getActionId() == 18002).collect(Collectors.toList());
        List<TPrivacyActionNougat> item_17006 = privacyActionNougats.stream()
                .filter(a -> a.getActionId() == 17006 || a.getActionId().equals(SDCARD_WRITE.id))
                .collect(Collectors.toList());
        List<TPrivacyOutsideAddress> privacyOutsideAddresses = new ArrayList<>();
        List<TPrivacySensitiveWord> privacySensitiveWords = new ArrayList<>();

        // 抓包文件不包含堆栈
        String captureInfoFilePath = uncompress + File.separator + fileNamePaths[2];
        if (new File(captureInfoFilePath).exists()) {
            // 分析传输个人信息数据
            privacySensitiveWords = captureInfoAction.analyzeCaptureInfo(captureInfoFilePath, item_18002, sensitiveWords);

            // 分析通讯行为数据
            String hostIpFilePath = uncompress + File.separator + fileNamePaths[1];
            privacyOutsideAddresses = hostIpaAction.analyzeHostIp(hostIpFilePath, item_18002);
        }
        
        // 2022.06.08-bing-新增补充解析通讯传输新为数据
        if(item_18002 != null && item_18002.size()>0) {
            Map<String, String> sensitiveWordMap = new HashMap<>();
            List<TPrivacySensitiveWord> groupCapture = captureInfoAction.getPrivacySensitiveWordResult(item_18002, sensitiveWords, sensitiveWordMap);
            privacySensitiveWords.addAll(groupCapture);
            log.info("分析传输个人信息数据sensitiveWordsList={}条记录,", privacySensitiveWords.size());
            sensitiveWordMap.clear();
        }

        // 抓包文件包含堆栈
        String captureInfoFileDirPath = uncompress + File.separator + fileNamePaths[4];
        if (new File(captureInfoFileDirPath).exists()) {
            // 分析传输个人信息数据
            List<CaptureInfoBO> captureInfoBOList = captureHostIpAction.analyzeCaptureInfo(captureInfoFileDirPath, sdks, taskDetailVO.getApk_name(), taskDetailVO.getApk_package());
            List<TPrivacySensitiveWord> privacySensitiveWordsList = captureHostIpAction.getPrivacySensitiveWordResult(captureInfoBOList, sensitiveWords,
                    taskDetailVO.getTaskId(), behaviorStage);
            log.info("分析传输个人信息数据privacySensitiveWordsList={}条记录,", privacySensitiveWordsList.size());
            if(privacySensitiveWordsList != null) {
            	privacySensitiveWords.addAll(privacySensitiveWordsList);
            }
            // 分析通讯行为数据
            privacyOutsideAddresses = captureHostIpAction.getPrivacyOutsideAddressResult(captureInfoBOList, taskDetailVO.getTaskId(), behaviorStage);
        }

        // 分析存储个人信息数据
        String sharedPrefFilePath = uncompress + File.separator + fileNamePaths[3];
        List<TPrivacySharedPrefs> privacySharedPrefs = sharedPrefAction.analyzeSharedPref(sharedPrefFilePath, item_17006, sensitiveWords);
        // 返回数据
        DetectDataBO data = new DetectDataBO();
        data.setPrivacyActionNougats(privacyActionNougats);
        data.setPrivacySensitiveWords(privacySensitiveWords);
        data.setPrivacyOutsideAddresses(privacyOutsideAddresses);
        data.setPrivacySharedPrefs(privacySharedPrefs);
        return data;
    }

    /**
     * 分析检测得到的数据
     *
     * @param behaviorStage
     * @param fileNames
     * @param uncompress
     * @param taskDetailVO
     * @param sensitiveWords
     * @return
     */
    private DetectDataBO analyzeAction(BehaviorStageEnum behaviorStage, String fileNames, String uncompress, TaskDetailVO taskDetailVO,
                                       List<TSensitiveWord> sensitiveWords) {
        log.info("检测数据解析，任务ID：[{}]，资产名称：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name());
        String[] fileNamePaths = fileNames.split(",");
        // 返回数据
        DetectDataBO data = new DetectDataBO();
        // 是否只保存个人信息行为
        boolean onlySavePersonalBehavior = isOnlySavePersonalBehavior(taskDetailVO.getTaskId());
        if (onlySavePersonalBehavior) {
            privacyActionNougatMapper.deleteNotPersonalByTaskId(taskDetailVO.getTaskId());
        }
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskDetailVO.getTaskId(),
                Collections.emptyList(), Collections.emptyList(), Collections.singletonList(behaviorStage));
        data.setPrivacyActionNougats(nougatList);
        data.setPrivacyOutsideAddresses(privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskDetailVO.getTaskId(),
                StringUtils.EMPTY, StringUtils.EMPTY, behaviorStage.getValue()));
        data.setPrivacySensitiveWords(privacySensitiveWordMapper.findByTaskId(taskDetailVO.getTaskId(), behaviorStage.getValue()));
        if (CollectionUtils.isEmpty(nougatList)) {
            data.setPrivacySharedPrefs(new ArrayList<>());
            return data;
        }
        // 按照每秒钟统计数据相同actionId的数据，并更新
        setNougatsCycleTrigger(nougatList, TerminalTypeEnum.ANDROID);
        nougatList.stream()
                .filter(nougat -> Objects.nonNull(nougat.getNumberAction()) || Objects.nonNull(nougat.getTriggerCycleTime()))
                .forEach(nougat -> privacyActionNougatMapper.updateCycleTrigger(nougat.getId(), nougat.getNumberAction(), nougat.getTriggerCycleTime()));

        List<TPrivacyActionNougat> item17006List = nougatList.stream()
                .filter(a -> a.getActionId() == 17006 || a.getActionId().equals(SDCARD_WRITE.id))
                .collect(Collectors.toList());
        // 分析存储个人信息数据
        String sharedPrefFilePath = uncompress + File.separator + fileNamePaths[3];
        data.setPrivacySharedPrefs(sharedPrefAction.analyzeSharedPref(sharedPrefFilePath, item17006List, sensitiveWords));
        log.info("检测数据解析 privacyActionNougats={} privacyOutsideAddresses={} privacySensitiveWordsList={} privacySharedPrefs={} 条记录,",
                data.getPrivacyActionNougats().size(), data.getPrivacyOutsideAddresses().size(), data.getPrivacySensitiveWords().size(), data.getPrivacySharedPrefs().size());
        setScreenshotImage(taskDetailVO.getTaskId(), data, uncompress, TerminalTypeEnum.getAndValid(taskDetailVO.getTerminal_type()));
        if (CollectionUtils.isNotEmpty(data.getPrivacySharedPrefs())) {
            // 单独匹配存储个人信息的疑似sdk数据，其它的行为在实时日志里已经匹配
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            List<TSdkLibrary> firstPartyLibraries = sdkLibraryMapper.findFirstPartyByTerminalType(TerminalTypeEnum.ANDROID.getValue());
            Map<String, List<TOdpCompanyProduct>> productMap = makeProductMap();
            Map<String, List<TOdpCompanyProduct>> companyMap = makecompanyMap();
            Set<String> sdkNameSet = new ConcurrentHashSet<>();
            Set<String> firstPartyNameSet = new ConcurrentHashSet<>();
            Set<String> twoLevelSdkNameSet = new ConcurrentHashSet<>();
            addPackageNames(sdkLibraries, sdkNameSet);
            addPackageNames(firstPartyLibraries, firstPartyNameSet);
            addTwoLevelPackageNames(sdkLibraries, twoLevelSdkNameSet);
            extractPackageNameActionExecutor(data.getPrivacySharedPrefs(), taskDetailVO, sdkNameSet, firstPartyNameSet, twoLevelSdkNameSet, productMap, companyMap);
            InsertListHelper.insertList(data.getPrivacySharedPrefs(), tPrivacySharedPrefs -> privacySharedPrefsMapper.insertList(tPrivacySharedPrefs));
        }
        return data;
    }

    private void updateTaskInfoWhenAnalyzeRetry(TTask task) {
        // 非中断状态不更新
        if (Objects.isNull(task)) {
            return;
        }
        Objects.requireNonNull(DetectionTypeEnum.DYNAMIC);
        taskDAO.updateDynamicSuccess(task.getTaskId());
    }
    /**
     * 保存隐私条款
     *
     * @param taskId   任务id
     * @param jsonFile 隐私条款json文件
     * @throws IOException
     * @throws IjiamiApplicationException
     */
    private void savePolicy(Long taskId, File jsonFile) throws IOException, IjiamiApplicationException {
        JSONArray privacy_policy_info = getJsonFromFile("privacy_policy_info", jsonFile);
        if (privacy_policy_info.size() == 0) {
            return;
        }
        List<TPrivacyPolicy> privacyPolicies = new ArrayList<>();
        for (int i = 0; i < privacy_policy_info.size(); i++) {
            JSONObject policy = privacy_policy_info.getJSONObject(i);
            PrivacyPolicyVO policyVO = privacyPolicyMapper.findByPrivacyIdAndTaskId(policy.getLong("id"), taskId);
            if (policyVO != null) {
                continue;
            }

            TPrivacyPolicy privacyPolicy = new TPrivacyPolicy();
            privacyPolicy.setTaskId(taskId);
            privacyPolicy.setPrivacyId(policy.getLong("id"));
            privacyPolicy.setResult(policy.getBoolean("measure_up"));
            privacyPolicy.setSuggestion(policy.getString("suggestion"));
            if (policy.getInteger("type") != null) {
                privacyPolicy.setType(policy.getInteger("type"));
            }
            privacyPolicies.add(privacyPolicy);

            if (policy.containsKey("pictures")) {
                JSONArray pictures = policy.getJSONArray("pictures");
                List<String> imgs = pictures.toJavaList(String.class);
                for (String img : imgs) {
                    File image = new File(jsonFile.getParent() + File.separator + img);
                    savePolicyImg(image, taskId, policy.getLong("id"));
                }
            }
        }
        privacyPolicyMapper.insertList(privacyPolicies);
    }

    /**
     * 从json文件中读取json字符串
     *
     * @param key      json key
     * @param jsonFile json文件
     * @return json字符串
     * @throws IOException
     */
    private JSONArray getJsonFromFile(String key, File jsonFile) throws IOException {
        String jsonStr = CommonUtil.readFileToString(jsonFile.getAbsolutePath());
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        return jsonObject.getJSONArray(key);
    }

    /**
     * 保存隐私政策截图
     *
     * @param file     图片
     * @param taskId   任务id
     * @param policyId 隐私条款id
     * @throws IOException
     * @throws IjiamiApplicationException
     */
    private void savePolicyImg(File file, Long taskId, Long policyId) throws IOException, IjiamiApplicationException {
        if (file == null || !file.exists()) {
            return;
        }

        if (file.isDirectory() && Objects.requireNonNull(file.list()).length == 0) {
            return;
        }

        TPrivacyPolicyImg img = new TPrivacyPolicyImg();
        img.setTaskId(taskId);
        img.setPolicyId(policyId);
        img.setFileKey(uploadFile(file));
        privacyPolicyImgMapper.insertSelective(img);
    }

    /**
     * 上传图片
     *
     * @param file 文件
     * @return 文件key
     * @throws FileNotFoundException
     * @throws IjiamiApplicationException
     */
    private String uploadFile(File file) throws FileNotFoundException, IjiamiApplicationException {
        if (file.exists()) {
            try {
                FileVO fileVO = new FileVO();
                fileVO.setFileName(file.getName());
                fileVO.setFileKey(UuidUtil.uuid());
                fileVO.setInputStream(new FileInputStream(file));
                fileVO.setFileExtName(file.getName().substring(file.getName().lastIndexOf(".")));
                fileVO.setFilePath(file.getAbsolutePath());
                fileService.uploadFile(fileVO);
                return fileVO.getFileKey();
            } finally {
                FileUtil.deleteFile(file);
            }
        }
        return null;
    }

}
