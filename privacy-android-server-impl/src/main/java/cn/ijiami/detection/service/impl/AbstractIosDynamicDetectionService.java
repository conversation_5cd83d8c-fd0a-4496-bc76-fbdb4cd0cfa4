package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.IosActionExecutorHelper;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractIosDynamicDetectionService<T extends TaskDetailVO> extends AbstractDynamicDetectionService<T> {

    private static final int NO_FOUND_EXECUTABLE_NAME = Integer.MAX_VALUE;

    @Autowired
    private TPermissionMapper permissionMapper;

    @Autowired
    private IosActionExecutorHelper iosActionExecutorHelper;

    @Autowired
    private TActionNougatMapper actionNougatMapper;

    protected void setNougatExecutor(ActionExecutor actionExecutor, String stackInfo, String executableName,
                                     String appName, String packageName, Map<String, TIosPrivacyActionSdk> sdkApiVOMap, Map<String, SuspiciousSdkBO> suspiciousSdkMap,
                                     List<TIosFrameworkLibrary> libraryList, List<TSdkLibraryClass> classList) {
        iosActionExecutorHelper.setNougatExecutor(actionExecutor, stackInfo, executableName, appName, packageName, sdkApiVOMap, suspiciousSdkMap, libraryList, classList);
    }

    /**
     * 更新sdk库和疑似sdk库的权限
     *
     * @param taskId
     * @param suspiciousSdkBOs
     */
    protected void updateSdkPermissionCodeAndInsertRecord(Long taskId, List<SuspiciousSdkBO> suspiciousSdkBOs, TerminalTypeEnum terminalTypeEnum) {
        log.info("updateSdkPermissionCodeAndInsertRecord");
        // 这两张表的数据量不大且基本固定，全读出来加快匹配速度，不用反复查数据库
        Map<String, TPermission> permissionMap = permissionMapper.findByTerminalType(null, TerminalTypeEnum.IOS.getValue())
                .stream().collect(Collectors.toMap(TPermission::getName, Function.identity()));
        Map<Long, TActionNougat> actionNougats = actionNougatMapper.findByTerminalType(TerminalTypeEnum.IOS.getValue())
                .stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (key1, key2) -> key2));
        updateSuspiciousSdkLibraryCodesAndInsertRecord(taskId, suspiciousSdkBOs, actionNougats, permissionMap, terminalTypeEnum);
    }
}
