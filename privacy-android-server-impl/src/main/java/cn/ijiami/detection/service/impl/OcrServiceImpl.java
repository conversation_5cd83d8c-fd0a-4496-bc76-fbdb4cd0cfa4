package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.util.Collections;
import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.DTO.ocr.OcrResponse;
import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.DTO.ocr.RecognizeResult;
import cn.ijiami.detection.VO.OcrDetectParams;
import cn.ijiami.detection.enums.OcrResultCode;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.service.api.OcrService;
import cn.ijiami.detection.service.spi.OcrFeign;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OcrServiceImpl.java
 * @Description ocr服务
 * @createTime 2022年04月14日 15:05:00
 */
@Slf4j
@Service
public class OcrServiceImpl implements OcrService {

    private final static String TAG = OcrServiceImpl.class.getSimpleName();

    @Autowired
    private OcrFeign ocrFeign;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    @Value("${fastDFS.ip}")
    private String fastDfsIp;

    @Override
    public List<RecognizeData> extractText(String imagePath) {
        if (StringUtils.isBlank(imagePath)) {
            return Collections.emptyList();
        }
        long startTime = System.currentTimeMillis();
        OcrDetectParams params = new OcrDetectParams();
        try {
            params.setUrl(uploadToFastDfs(imagePath));
            params.setOcpOcrRecordId(UuidUtil.uuid());
            OcrResponse ocrResponse = ocrFeign.extractPicText(params);
            if (ocrResponse != null && ocrResponse.ocrSuccess()) {
                RecognizeResult result = CommonUtil.jsonToBean(ocrResponse.getResult().getRecognizeText(), new TypeReference<RecognizeResult>() {
                });
                if (result != null && result.getCode() == OcrResultCode.SUCCESS.code) {
                    return CommonUtil.jsonToBean(result.getResults(), new TypeReference<List<RecognizeData>>() {
                    });
                }
            }
        } catch (Exception e) {
            log.error(TAG, "ocr失败", e);
        } finally {
            log.info("ocr duration=" + (System.currentTimeMillis() - startTime));
            if (StringUtils.isNotBlank(params.getUrl())) {
                deleteFastDfsFile(params.getUrl());
            }
        }
        return Collections.emptyList();
    }

    private String uploadToFastDfs(String filePath) throws Exception {
        FileVO fileVO = new FileVO();
        File file = new File(filePath);
        // 文件扩展名
        FileInputStream inputStream = new FileInputStream(file);

        fileVO.setFileExtName(FilenameUtils.getExtension(file.getName()));
        fileVO.setInputStream(inputStream);
        fileVO.setFileSize(file.length());
        fileVO.setFileName(file.getName());
        fileVO.setFilePath(filePath);
        FileVO uploadResult = singleFastDfsFileService.instance().upload(fileVO);
        if (uploadResult == null || StringUtils.isBlank(uploadResult.getFilePath())) {
            throw new IjiamiApplicationException("上传图片失败");
        }
        return getHost() + uploadResult.getFilePath();
    }

    private void deleteFastDfsFile(String filePath) {
        singleFastDfsFileService.instance().deleteFile(filePath.replace(getHost(), ""));
    }

    private String getHost() {
        return fastDfsIp + "/";
    }

}
