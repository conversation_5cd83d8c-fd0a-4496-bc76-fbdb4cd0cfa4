package cn.ijiami.detection.service.impl;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.analyzer.parser.WechatAppletInfoParser;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.query.AddApplet;
import cn.ijiami.detection.query.BatchAddApplet;
import cn.ijiami.detection.service.api.IStorageLogService;
import cn.ijiami.detection.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.AppletExtraInfoVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.bean.WaverifyInfo;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TComplianceAppletPluginsMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.IAppletService;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import lombok.extern.slf4j.Slf4j;

import static cn.ijiami.detection.utils.CommonUtil.isSamePlugins;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletTaskServiceImpl.java
 * @Description 小程序相关
 * @createTime 2023年04月18日 15:49:00
 */
@Slf4j
@Service
public class AppletServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IAppletService {

    private static final int START_TASK_SUCCESS = 0;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TaskDAO taskDAO;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private TComplianceAppletPluginsMapper pluginsMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private IAssetsService assetsService;

    @Autowired
    private IStorageLogService storageLogService;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    @Autowired
    private IStorageLogService iStorageLogService;

    @Transactional
    @Override
    public void addAppletInfo(BatchAddApplet batchAddApplet, IUser current) throws IjiamiApplicationException, IjiamiCommandException {
        for (AddApplet b: batchAddApplet.getAppletList()) {
            if (batchAddApplet.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET.getValue()) {
                addAppletInfo(b, current);
            } else if (batchAddApplet.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
                addAlipayAppletInfo(b, current);
            }
        }
    }


    /**
     * 添加微信小程序
     * @param wechatApplet
     * @return
     */
    @Override
    public void addAppletInfo(AddApplet wechatApplet, IUser current) throws IjiamiApplicationException, IjiamiCommandException {
        if (StringUtils.isBlank(wechatApplet.getName())) {
            throw new IjiamiApplicationException("小程序名字不能为空");
        }
        if (StringUtils.isBlank(wechatApplet.getQrcodeFileId())
                && StringUtils.isBlank(wechatApplet.getShareUrl())
                && StringUtils.isBlank(wechatApplet.getAppId())) {
            throw new IjiamiApplicationException("小程序AppId、二维码、分享链接必须填其中一个");
        }
        TAssets assets = new TAssets();
        assets.setName(wechatApplet.getName());
        assets.setAppId(wechatApplet.getAppId());
        assets.setPakage(wechatApplet.getAppId());
        String md5;
        if (StringUtils.isBlank(wechatApplet.getAppId())) {
            md5 = MD5Util.encode(wechatApplet.getName(), "");
        } else {
            md5 = MD5Util.encode(wechatApplet.getAppId(), "");
        }
        assets.setMd5(md5);
        assets.setTerminalType(TerminalTypeEnum.WECHAT_APPLET);
        assets.setVersion(StringUtils.EMPTY);
        assets.setCreateUserId(current.getUserId());
        assets.setUpdateUserId(current.getUserId());
        assets.setShareUrl(wechatApplet.getShareUrl());
        assetsService.setAssetsFile(assets, wechatApplet.getAppPrivacyPolicyFileId(), wechatApplet.getThirdPartySharedListFileId(), wechatApplet.getQrcodeFileId());
        assetsService.saveOrUpdate(assets);
    }

    /**
     * 添加支付宝小程序
     * @param aliPayApplet
     * @return
     */
    @Override
    public void addAlipayAppletInfo(AddApplet aliPayApplet, IUser current) throws IjiamiApplicationException, IjiamiCommandException {
        if (StringUtils.isBlank(aliPayApplet.getName())) {
            throw new IjiamiApplicationException("小程序名字不能为空");
        }
        TAssets assets = new TAssets();
        assets.setName(aliPayApplet.getName());
        assets.setAppId(aliPayApplet.getAppId());
        assets.setPakage(aliPayApplet.getAppId());
        assets.setMd5(MD5Util.encode(aliPayApplet.getName(), ""));
        assets.setTerminalType(TerminalTypeEnum.ALIPAY_APPLET);
        assets.setVersion(StringUtils.EMPTY);
        assets.setCreateUserId(current.getUserId());
        assets.setUpdateUserId(current.getUserId());
        assets.setShareUrl(aliPayApplet.getShareUrl());
        assetsService.setAssetsFile(assets, aliPayApplet.getAppPrivacyPolicyFileId(), aliPayApplet.getThirdPartySharedListFileId(), aliPayApplet.getQrcodeFileId());
        assetsService.saveOrUpdate(assets);
    }

    public AppletExtraInfoVO buildAppletInfo(File infoFile, List<TComplianceAppletPlugins> newPluginsList) {
        WaverifyInfo info = WechatAppletInfoParser.parserBasicInfo(infoFile.getAbsolutePath());
        AppletExtraInfoVO extraInfoVO = new AppletExtraInfoVO();
        if (Objects.nonNull(info)) {
            extraInfoVO.setAppId(info.getAppId());
            //小程序名称
            extraInfoVO.setNickname(info.getNickname());
            //账号主体
            extraInfoVO.setAccountSubject(info.getName());
            //原始ID
            extraInfoVO.setOriginalId(info.getUsername());
            //服务隐私及数据提示
            extraInfoVO.setPrivacyData(info.getServiceAndData());
            //服务声明
            extraInfoVO.setStatement(info.getServiceStatement());
            //更新时间
            extraInfoVO.setUpdateTime(info.getUpdateTime());
            //服务类目
            extraInfoVO.setServiceCategory(info.getCategoryList());
            //引用插件
            extraInfoVO.setPluginAppIds(newPluginsList.stream().map(TComplianceAppletPlugins::getPluginAppid).collect(Collectors.toList()));
            //引用插件
            extraInfoVO.setPlugins(newPluginsList.stream().map(TComplianceAppletPlugins::getPluginName).collect(Collectors.joining("、")));
            //授权服务商
            extraInfoVO.setServiceProvider(info.getAuth3reList());
        }
        return extraInfoVO;
    }

    /**
     * 保存插件信息入库
     * @param task
     */
    @Override
    public void savePluginsAndBasicInfo(TTask task, List<File> pluginsFiles, File basicInfoFile) throws IjiamiApplicationException, IjiamiCommandException {
        List<TComplianceAppletPlugins> pluginsList;
        if (CollectionUtils.isEmpty(pluginsFiles)) {
            pluginsList = pluginsMapper.selectAllPluginsByTaskId(task.getTaskId());
        } else {
            List<TComplianceAppletPlugins> oldPluginsList = pluginsMapper.selectAllPluginsByTaskId(task.getTaskId());
            pluginsList = pluginsFiles.stream()
                    .map(file -> WechatAppletInfoParser.parserPlugins(file.getAbsolutePath()))
                    .filter(Objects::nonNull)
                    .peek(plugins -> plugins.setTaskId(task.getTaskId())).collect(Collectors.toList());
            pluginsList.stream()
                    .filter(plugins -> oldPluginsList.stream().noneMatch(p -> isSamePlugins(p, plugins)))
                    .forEach(plugins -> {
                        pluginsMapper.insert(plugins);
                        log.info("保存小程序引用插件成功");
                    });
            oldPluginsList.stream()
                    .filter(plugins -> pluginsList.stream().noneMatch(p -> isSamePlugins(p, plugins)))
                    .forEach(plugins -> {
                        pluginsMapper.deleteByPrimaryKey(plugins.getId());
                        log.info("删除小程序引用插件成功");
                    });
        }
        if (basicInfoFile.exists()) {
            Update update = new Update();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("_id", task.getApkDetectionDetailId());
            AppletExtraInfoVO extraInfoVO = buildAppletInfo(basicInfoFile, pluginsList);
            update.set("applet_extra_info", extraInfoVO);
            update(paramMap, update);
            TAssets updateAssets = new TAssets();
            updateAssets.setId(task.getAssetsId());
            updateAssets.setAppId(extraInfoVO.getAppId());
            assetsMapper.updateByPrimaryKeySelective(updateAssets);
            log.info("更新小程序信息成功");
        }
    }


    /**
     * 构建插件信息
     * @param responseBady
     * @return
     */
    private TComplianceAppletPlugins buildComplianceAppletPlugin(String responseBady){
        //将请求的信息进行json化
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(responseBady);
        TComplianceAppletPlugins tComplianceAppletPlugins = new TComplianceAppletPlugins();
        //开发者
        if(jsonObject.containsKey("contractor_name")){
            tComplianceAppletPlugins.setDeveloper(jsonObject.getString("contractor_name"));
        }
        //appid
        if(jsonObject.containsKey("appid")){
            tComplianceAppletPlugins.setPluginAppid(jsonObject.getString("appid"));
        }
        //服务类目
        if(jsonObject.containsKey("categories")){
            com.alibaba.fastjson.JSONObject ctagories = jsonObject.getJSONObject("categories");
            String catagory = "";
            //一级
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(ctagories.getString("first"))){
                catagory = ctagories.getString("first");
            }
            //二级
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(ctagories.getString("second"))){
                catagory += ">" + ctagories.getString("second");
            }
            //三级
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(ctagories.getString("third"))){
                catagory += ">" +ctagories.getString("third");
            }
            tComplianceAppletPlugins.setServiceCategory(catagory);
        }
        //插件名称
        if(jsonObject.containsKey("nickname")){
            tComplianceAppletPlugins.setPluginName(jsonObject.getString("nickname"));
        }
        return tComplianceAppletPlugins;
    }

    /**
     * 查询所有插件信息
     * @param taskId
     * @return
     */
    @Override
    public List<TComplianceAppletPlugins> findAppletPlugins(Long taskId) {
        return pluginsMapper.selectAllPluginsByTaskId(taskId);
    }

    @Override
    public List<String> findAlipayAppletService(Long taskId) {
        return pluginsMapper.selectAllPluginsByTaskId(taskId)
                .stream()
                .map(TComplianceAppletPlugins::getPluginName)
                .collect(Collectors.toList());
    }
}
