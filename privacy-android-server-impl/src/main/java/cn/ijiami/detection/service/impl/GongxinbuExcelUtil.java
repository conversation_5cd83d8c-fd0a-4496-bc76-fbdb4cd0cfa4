package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.gongxinbu.GongxinbuVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/14 17:57
 */
public class GongxinbuExcelUtil {

    /**
     * 导出Excel统计数据
     *
     * @param templatePath 模板绝对路径
     * @param outputPath   输出的excel文件绝对路径
     * @param data         需要导出的数据
     */
    public static void exportReport(String templatePath, String outputPath, List<GongxinbuVO> data) {

        FileInputStream fis = null;
        FileOutputStream fos = null;
        Workbook workbook = null;

        try {
            fis = new FileInputStream(templatePath);
            fos = new FileOutputStream(outputPath);
            workbook = new XSSFWorkbook(fis);

            Sheet sheet = workbook.getSheetAt(0);
            int i = 1;
            for (GongxinbuVO vo : data) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(i);
                row.createCell(1).setCellValue(vo.getAppName());
                row.createCell(2).setCellValue(vo.getPackageName());
                row.createCell(3).setCellValue(vo.getVersionName());
                row.createCell(4).setCellValue(vo.getApkSize());
                row.createCell(5).setCellValue(vo.getTargetSdkVersion());
                row.createCell(6).setCellValue(vo.getApkMD5());
                row.createCell(7).setCellValue(vo.getSignMd5());
                row.createCell(8).setCellValue(vo.getSignDetail());
                row.createCell(9).setCellValue(StringUtils.isEmpty(vo.getEncryptDetail()) ? "" : vo.getEncryptDetail());
                row.createCell(10).setCellValue(vo.isCol1() ? "有" : "无");
                row.createCell(11).setCellValue(vo.isCol2() ? "有" : "无");
                row.createCell(12).setCellValue(vo.isCol3() ? "有" : "无");
                row.createCell(13).setCellValue(vo.isCol4() ? "有" : "无");
                row.createCell(14).setCellValue(vo.isCol5() ? "有" : "无");
                row.createCell(15).setCellValue(vo.isCol6() ? "有" : "无");
                row.createCell(16).setCellValue(vo.isCol7() ? "有" : "无");
                row.createCell(17).setCellValue(vo.isCol8() ? "有" : "无");
                i++;
            }

            workbook.write(fos);

            fis.close();
            fos.close();
        } catch (IOException e) {
            e.getMessage();
        } finally {
            try {
                if (fis != null){
                	 fis.close();
                }
                if (fos != null) {
                	fos.close();
                }
                if (workbook != null){
                	 workbook.close();
                }
            } catch (IOException e) {
                e.getMessage();
            }
        }
    }

    public static void main(String[] args) {
        String from = "/Users/<USER>/Desktop/report_template.xlsx";
        String to = "/Users/<USER>/Desktop/工信部信管涵【2019】337号_1000个应用检测结果_201910102023.xlsx";

        exportReport(from, to, new ArrayList<GongxinbuVO>() {
            private static final long serialVersionUID = -7565272221558233635L;

            {
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
                add(new GongxinbuVO("Wechat", "im.wechat.com", "26.7", "65MB", "29", "mmmmmmm", "nnnnnnnn", "bbbbbbbb", "爱加密加固", false, false, false, false, false, false, false, false));
            }
        });
    }
}
