package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.io.FileUtil;
import cn.ijiami.detection.entity.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.VO.detection.ScreenshotImage;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.mapper.TDynamicBehaviorImgMapper;
import cn.ijiami.detection.mapper.TManualScreenshotImageMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import tk.mybatis.mapper.entity.Example;

/**
 * v2.5版本行为截图入库服务
 */
@Service
public class ScreenshotImageServiceImpl implements IScreenshotImageService{

    private static Logger logger = LoggerFactory.getLogger(ScreenshotImageServiceImpl.class);
    @Autowired
    TManualScreenshotImageMapper manualScreenshotImageMapper;
    @Autowired
    IBaseFileService fileService;
    @Autowired
    TDynamicBehaviorImgMapper dynamicBehaviorImgMapper;
    @Autowired
    TPrivacyActionNougatMapper privacyActionNougatMapper;

    /**
     * 获取检测时的手动截图数据
     * @param taskId
     * @return
     */
    @Override
    public List<ScreenshotImage> getManualScreenshotImage(Long taskId) {
        TManualScreenshotImage imageQuery = new TManualScreenshotImage();
        imageQuery.setTaskId(taskId);
        TManualScreenshotImage imageData = manualScreenshotImageMapper.selectOne(imageQuery);
        if (imageData != null && StringUtils.isNotBlank(imageData.getImageData())) {
            return JSON.parseArray(imageData.getImageData(), ScreenshotImage.class);
        } else {
            return Collections.emptyList();
        }
    }


    /**
     *安卓与ios快速检测检测截图入库，用于应用行为截图
     * @param taskId  任务id
     * @param screenshotMap 截图时间搓与filekey
     * @param tPrivacyActionNougatList 应用行为实体
     * @Date 2021/6/11 20:11
     */
    @Override
    public void saveDynamicBehaviorImg(Long taskId, Map<Long,String> screenshotMap, List<TPrivacyActionNougat> tPrivacyActionNougatList){
        //获取图片的时间搓
        if(MapUtils.isEmpty(screenshotMap)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        List<Long> pictureTime = new ArrayList<>(screenshotMap.keySet());
        //将所有时间搓正序排列
        Collections.sort(pictureTime);
        for (TPrivacyActionNougat tPrivacyActionNougat: tPrivacyActionNougatList) {
            for(int i=0;i<pictureTime.size();i++){
                if(tPrivacyActionNougat.getActionTimeStamp()==null){//判断行为有没有时间搓
                    continue;
                }
                if(pictureTime.size()==1){
                    TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                    detail.setTaskId(taskId);
                    detail.setActionId(tPrivacyActionNougat.getActionId());
                    detail.setBusinessId(tPrivacyActionNougat.getId());
                    detail.setBusinessType(1);
                    detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                    detail.setCreateTime(new Date());
                    detail.setUpdateTime(new Date());
                    detail.setPictureTimeStamp(pictureTime.get(i));
                    imageList.add(detail);
                }else{
                    if(i==0){//截第二张图片时间前的行为都用第一张图片
                        if(tPrivacyActionNougat.getActionTimeStamp() < pictureTime.get(i+1)){
                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(tPrivacyActionNougat.getActionId());
                            detail.setBusinessId(tPrivacyActionNougat.getId());
                            detail.setBusinessType(1);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }else if(i<pictureTime.size()-1){//截第三张图片前的行为都是第二张图片的行为
                        if(pictureTime.get(i) <= tPrivacyActionNougat.getActionTimeStamp() && tPrivacyActionNougat.getActionTimeStamp() < pictureTime.get(i+1)){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(tPrivacyActionNougat.getActionId());
                            detail.setBusinessId(tPrivacyActionNougat.getId());
                            detail.setBusinessType(1);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }else if(i==pictureTime.size()-1){//最后一张截图时间到行为结束后的时间都算是最后一张截图的行为
                        if(pictureTime.get(i) <= tPrivacyActionNougat.getActionTimeStamp()){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(tPrivacyActionNougat.getActionId());
                            detail.setBusinessId(tPrivacyActionNougat.getId());
                            detail.setBusinessType(1);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }


    /**
     * 安卓与ios快速检测截图入库，用于传输个人信息行为截图
     * @param taskId 任务id
     * @param screenshotMap 截图时间搓与filekey
     * @param tPrivacySensitiveWordList  传输个人信息行为实体
     */
    @Override
    public void saveDynamicBehaviorImgSensitiveWord(Long taskId, Map<Long,String> screenshotMap,
                                                    List<TPrivacySensitiveWord> tPrivacySensitiveWordList){
        //获取图片的时间
        if (MapUtils.isEmpty(screenshotMap)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        List<Long> pictureTime = new ArrayList<>(screenshotMap.keySet());
        //将所有时间搓正序排列
        Collections.sort(pictureTime);
        for (TPrivacySensitiveWord tPrivacySensitiveWord:tPrivacySensitiveWordList) {
            for (int i=0;i<pictureTime.size();i++){
                // 判断行为有没有时间
                if (tPrivacySensitiveWord.getActionTime() == null){
                    continue;
                }
                Long time = tPrivacySensitiveWord.getActionTime().getTime();
                if(pictureTime.size()==1){
                    TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                    detail.setTaskId(taskId);
                    detail.setActionId(null);
                    detail.setBusinessId(tPrivacySensitiveWord.getId());
                    detail.setBusinessType(3);
                    detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                    detail.setCreateTime(new Date());
                    detail.setUpdateTime(new Date());
                    detail.setPictureTimeStamp(pictureTime.get(i));
                    imageList.add(detail);
                } else {
                    if(i==0){//截第二张图片时间前的行为都用第一张图片
                        if(time < pictureTime.get(i+1)){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySensitiveWord.getId());
                            detail.setBusinessType(3);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }else if(i<pictureTime.size()-1){//截第三张图片前的行为都是第二张图片的行为
                        if(pictureTime.get(i)<=time && time<pictureTime.get(i+1)){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySensitiveWord.getId());
                            detail.setBusinessType(3);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    } else if(i==pictureTime.size()-1){//最后一张截图时间到行为结束后的时间都算是最后一张截图的行为
                        if(pictureTime.get(i)<=time){
                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySensitiveWord.getId());
                            detail.setBusinessType(3);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }



    /**
     * 安卓与ios深度检测动态截图入库，用于应用行为手动截图
     *
     * @param taskId                   任务id
     * @param tPrivacyActionNougatList 应用行为实体
     * @param screenshotImages         手动截图的数据
     * @date 2021/6/22
     */
    @Override
    public void saveManualBehaviorImg(Long taskId, List<TPrivacyActionNougat> tPrivacyActionNougatList, List<ScreenshotImage> screenshotImages,
                                      Map<Long, TActionNougat> actionNougatMap) {
        //进行数据判断
        if (CollectionUtils.isEmpty(screenshotImages)) {
            return;
        }
        for (TPrivacyActionNougat tPrivacyActionNougat : tPrivacyActionNougatList) {
            if (tPrivacyActionNougat.getId() == null || tPrivacyActionNougat.getId() == 0L) {
                continue;
            }
            TActionNougat actionNougat = actionNougatMap.get(tPrivacyActionNougat.getActionId());
            if (actionNougat == null) {
                continue;
            }
            List<TDynamicBehaviorImg> imgList = new ArrayList<>();
            for(ScreenshotImage screenshotImage:screenshotImages){
                //判断行为名称是否存在
                if (CollectionUtils.isEmpty(screenshotImage.getBehaviorValue())) {
                    continue;
                }
                for (String behavior:screenshotImage.getBehaviorValue()){
                    //行为名称跟要已经入库的行为是否一致
                    if (isEffectScreenshotImage(tPrivacyActionNougat, screenshotImage)) {
                        if(!behavior.equals(actionNougat.getActionName())){
                            continue;
                        }
                        TDynamicBehaviorImg detail = gettDynamicBehaviorImg(taskId, tPrivacyActionNougat.getActionId(), tPrivacyActionNougat.getId(), 1, screenshotImage);
                        imgList.add(detail);
                    }
                }
            }
            InsertListHelper.insertList(imgList, dynamicBehaviorImgMapper::insertList);
        }
    }

    @NotNull
    private static TDynamicBehaviorImg gettDynamicBehaviorImg(Long taskId, Long tPrivacyActionNougat, Long tPrivacyActionNougat1, int businessType, ScreenshotImage screenshotImage) {
        TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
        detail.setTaskId(taskId);
        detail.setActionId(tPrivacyActionNougat);
        detail.setBusinessId(tPrivacyActionNougat1);
        detail.setBusinessType(businessType);
        detail.setFileKey(screenshotImage.getImgMsg());
        detail.setCreateTime(new Date());
        detail.setUpdateTime(new Date());
        detail.setRemarks(screenshotImage.getRemark());
        detail.setPictureTimeStamp(screenshotImage.getEndTime());
        return detail;
    }

    /**
     * 应用行为截图入库判断条件
     * @param tPrivacyActionNougat
     * @param screenshotImage
     * @return
     */
    private boolean isEffectScreenshotImage(TPrivacyActionNougat tPrivacyActionNougat, ScreenshotImage screenshotImage) {

        if(tPrivacyActionNougat.getActionTimeStamp() == null || tPrivacyActionNougat.getActionTimeStamp() == 0){
            return false;
        }
        if(screenshotImage.getStartTime() == 0L || screenshotImage.getEndTime() == 0L){
            return false;
        }
        return screenshotImage.getStartTime()<=tPrivacyActionNougat.getActionTimeStamp()
                && screenshotImage.getEndTime()>=tPrivacyActionNougat.getActionTimeStamp();
    }


    /**
     * 安卓与ios深度检测动态截图入库，用于传输个人信息行为手动截图
     * @param taskId  任务id
     * @param tPrivacySensitiveWordList 传输个人信息行为实体
     * @param screenshotImages 手动截图的数据
     * @date 2021/6/22
     */
    @Override
    public void saveManualBehaviorImgSensitiveWord(Long taskId, List<TPrivacySensitiveWord> tPrivacySensitiveWordList,List<ScreenshotImage> screenshotImages){
        //进行数据判断
        if(CollectionUtils.isEmpty(screenshotImages)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        for (TPrivacySensitiveWord tPrivacySensitiveWord: tPrivacySensitiveWordList) {
            if(tPrivacySensitiveWord.getActionTime() == null){
                return;
            }
            for(ScreenshotImage screenshotImage:screenshotImages){
                //传输个人信息只需要判断在时间搓之间就入库
                if (isBooleanScreenshotImg(tPrivacySensitiveWord, screenshotImage)) {
                    TDynamicBehaviorImg detail = gettDynamicBehaviorImg(taskId, null, tPrivacySensitiveWord.getId(), 3, screenshotImage);
                    imageList.add(detail);
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }


    /**
     * 传输信息截图入库判断条件
     * @param tPrivacySensitiveWord
     * @param screenshotImage
     * @return
     */
    private boolean isBooleanScreenshotImg(TPrivacySensitiveWord tPrivacySensitiveWord, ScreenshotImage screenshotImage) {

        Long timesamp =tPrivacySensitiveWord.getActionTime().getTime();
        if(screenshotImage.getStartTime() == 0L || screenshotImage.getEndTime() == 0L){
            return false;
        }
        return screenshotImage.getStartTime()<=timesamp
                && screenshotImage.getEndTime()>=timesamp;
    }


    /**
     * ios快速检测存储截图数据并返回时间搓与图片key的map集合
     * @param screenshotPath
     * @return
     */
    public Map<Long,String> savePictureAndReturnMap(String screenshotPath){
        Map<Long,String> pictureMap = new HashMap<>();
        File screenshotFile = new File(screenshotPath);
        if(screenshotFile.exists()){
            //获取到文件的全路径
            List<String> pictureFilePaths = CommonUtil.listAllFilesInDir(screenshotFile.getPath(), CommonUtil.IMAGE_EXT_NAMES, true);
            if( pictureFilePaths!=null && pictureFilePaths.size()>0) {
                for (String str : pictureFilePaths) {
                    try{
                        String picName = str.substring((str.lastIndexOf("/") + 1), (str.lastIndexOf(".")));
                        Long timestamp = Long.parseLong(picName.substring(picName.lastIndexOf("_") + 1));
                        File file = new File(str);
                        FileVO fileVO = fileService.uploadFile(new MockMultipartFile(file.getName(), new FileInputStream(file)));
                        pictureMap.put(timestamp,fileVO.getFileKey());
                    }catch (IOException | IjiamiApplicationException e){
                        logger.info("上传图片异常："+e.getStackTrace());
                        e.getStackTrace();
                    }
                }
            }
        }
        return pictureMap;
    }


    /**
     * 安卓快速检测保存截图图片并放回时间搓与图片key的map集合
     * @param screenshotPath
     * @return
     */
	public Map<Long, String> saveScreenPictureReturnMap(String screenshotPath) {
        SimpleDateFormat dateFormat = null;
        Map<Long,String> pictureMap = new HashMap<>();
        File screenshotFile = new File(screenshotPath);
        if(screenshotFile.exists()) {
            //获取到文件的全路径
            List<String> pictureFilePaths = CommonUtil.listAllFilesInDir(screenshotFile.getPath(), CommonUtil.IMAGE_EXT_NAMES, true);
            if(pictureFilePaths.size()>0) {
                for (String str : pictureFilePaths) {
                    try {
                        File file = new File(str);
                        String fileName = FileUtil.mainName(file).replace("snapshot_", "");
                        long timestamp;
                        if (str.contains("-") && str.contains("_")) {
                            if (dateFormat == null) {
                                dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
                            }
                            timestamp = dateFormat.parse(fileName).getTime();
                        } else {
                            timestamp = Long.parseLong(fileName);
                        }
                        FileVO fileVO = fileService.uploadFile(new MockMultipartFile(file.getName(), new FileInputStream(file)));
                        pictureMap.put(timestamp, fileVO.getFileKey());
                    } catch (Exception e) {
                        logger.error("上传图片异常：", e);
                    }
                }
            }
        }
        return pictureMap;
    }

    /**
     * 安卓与ios快速检测截图入库，用于通信行为截图
     * @param taskId 任务id
     * @param screenshotMap 截图时间搓与filekey
     * @param tPrivacyOutsideAddressList  通信行为实体
     */
    @Override
    public void saveImgOfOutsideData(Long taskId, Map<Long,String> screenshotMap, List<TPrivacyOutsideAddress> tPrivacyOutsideAddressList){
        //获取图片的时间搓
        if(MapUtils.isEmpty(screenshotMap)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        List<Long> pictureTime = new ArrayList<>(screenshotMap.keySet());
        //将所有时间搓正序排列
        Collections.sort(pictureTime);
        for (TPrivacyOutsideAddress tPrivacyOutsideAddress: tPrivacyOutsideAddressList) {
            for(int i=0;i<pictureTime.size();i++){
                if(tPrivacyOutsideAddress.getActionTime()!=null){//判断行为有没有时间搓
                    Long time = tPrivacyOutsideAddress.getActionTime().getTime();
                    if(pictureTime.size()==1){
                        TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                        detail.setTaskId(taskId);
                        detail.setActionId(null);
                        detail.setBusinessId(tPrivacyOutsideAddress.getId());
                        detail.setBusinessType(2);
                        detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                        detail.setCreateTime(new Date());
                        detail.setUpdateTime(new Date());
                        detail.setPictureTimeStamp(pictureTime.get(i));
                        imageList.add(detail);
                    }else{
                        if(i==0){//截第二张图片时间前的行为都用第一张图片
                            if(time < pictureTime.get(i+1)){

                                TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                                detail.setTaskId(taskId);
                                detail.setActionId(null);
                                detail.setBusinessId(tPrivacyOutsideAddress.getId());
                                detail.setBusinessType(2);
                                detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                                detail.setCreateTime(new Date());
                                detail.setUpdateTime(new Date());
                                detail.setPictureTimeStamp(pictureTime.get(i));
                                imageList.add(detail);
                            }
                        }else if(i<pictureTime.size()-1){//截第三张图片前的行为都是第二张图片的行为
                            if(pictureTime.get(i)<=time && time<pictureTime.get(i+1)){

                                TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                                detail.setTaskId(taskId);
                                detail.setActionId(null);
                                detail.setBusinessId(tPrivacyOutsideAddress.getId());
                                detail.setBusinessType(2);
                                detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                                detail.setCreateTime(new Date());
                                detail.setUpdateTime(new Date());
                                detail.setPictureTimeStamp(pictureTime.get(i));
                                imageList.add(detail);
                            }
                        }else if(i==pictureTime.size()-1){//最后一张截图时间到行为结束后的时间都算是最后一张截图的行为
                            if(pictureTime.get(i)<=time){

                                TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                                detail.setTaskId(taskId);
                                detail.setActionId(null);
                                detail.setBusinessId(tPrivacyOutsideAddress.getId());
                                detail.setBusinessType(2);
                                detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                                detail.setCreateTime(new Date());
                                detail.setUpdateTime(new Date());
                                detail.setPictureTimeStamp(pictureTime.get(i));
                                imageList.add(detail);
                            }
                        }
                    }
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }


    /**
     * 安卓与ios快速检测截图入库，用于存储个人信息行为截图
     * @param taskId 任务id
     * @param screenshotMap 截图时间搓与filekey
     * @param tPrivacySharedPrefsList  存储个人信息实体
     */
    @Override
    public void saveImgOfSharedData(Long taskId, Map<Long,String> screenshotMap, List<TPrivacySharedPrefs> tPrivacySharedPrefsList){
        //获取图片的时间搓
        if (MapUtils.isEmpty(screenshotMap)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        List<Long> pictureTime = new ArrayList<>(screenshotMap.keySet());
        //将所有时间搓正序排列
        Collections.sort(pictureTime);
        for (TPrivacySharedPrefs tPrivacySharedPrefs: tPrivacySharedPrefsList) {
            for(int i=0;i<pictureTime.size();i++){
                if(tPrivacySharedPrefs.getActionTime() == null){//判断行为有没有时间搓
                    continue;
                }
                Long time = tPrivacySharedPrefs.getActionTime().getTime();
                if(pictureTime.size()==1){
                    TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                    detail.setTaskId(taskId);
                    detail.setActionId(null);
                    detail.setBusinessId(tPrivacySharedPrefs.getId());
                    detail.setBusinessType(4);
                    detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                    detail.setCreateTime(new Date());
                    detail.setUpdateTime(new Date());
                    detail.setPictureTimeStamp(pictureTime.get(i));
                    imageList.add(detail);
                }else{
                    if(i==0){//截第二张图片时间前的行为都用第一张图片
                        if(time < pictureTime.get(i+1)){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySharedPrefs.getId());
                            detail.setBusinessType(4);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }else if(i<pictureTime.size()-1){//截第三张图片前的行为都是第二张图片的行为
                        if(pictureTime.get(i)<=time && time<pictureTime.get(i+1)){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySharedPrefs.getId());
                            detail.setBusinessType(4);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }else if(i==pictureTime.size()-1){//最后一张截图时间到行为结束后的时间都算是最后一张截图的行为
                        if(pictureTime.get(i)<=time){

                            TDynamicBehaviorImg detail = new TDynamicBehaviorImg();
                            detail.setTaskId(taskId);
                            detail.setActionId(null);
                            detail.setBusinessId(tPrivacySharedPrefs.getId());
                            detail.setBusinessType(4);
                            detail.setFileKey(screenshotMap.get(pictureTime.get(i)));
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setPictureTimeStamp(pictureTime.get(i));
                            imageList.add(detail);
                        }
                    }
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }


    /**
     * 安卓与ios深度检测动态截图入库，用于通信行为手动截图
     * @param taskId  任务id
     * @param tPrivacyOutsideAddressList 通信行为实体
     * @param screenshotImages 手动截图的数据
     * @date 2021/7/8
     */
    @Override
    public void saveManualBehaviorImgOutside(Long taskId, List<TPrivacyOutsideAddress> tPrivacyOutsideAddressList,List<ScreenshotImage> screenshotImages){
        //进行数据判断
        if(CollectionUtils.isEmpty(screenshotImages)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        for (TPrivacyOutsideAddress tPrivacyOutsideAddress : tPrivacyOutsideAddressList) {
            if(tPrivacyOutsideAddress.getActionTime() == null){
                return;
            }
            for(ScreenshotImage screenshotImage:screenshotImages){
                //只需要判断在时间搓之间就入库
                if (isBooleanScreenshotImgOfOutside(tPrivacyOutsideAddress, screenshotImage)) {
                    TDynamicBehaviorImg detail = gettDynamicBehaviorImg(taskId, null, tPrivacyOutsideAddress.getId(), 2, screenshotImage);
                    imageList.add(detail);
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }

    /**
     * 通信行为截图入库判断条件
     * @param tPrivacyOutsideAddress
     * @param screenshotImage
     * @return
     */
    private boolean isBooleanScreenshotImgOfOutside(TPrivacyOutsideAddress tPrivacyOutsideAddress, ScreenshotImage screenshotImage) {

        Long timesamp =tPrivacyOutsideAddress.getActionTime().getTime();
        if(screenshotImage.getStartTime() == 0L || screenshotImage.getEndTime() == 0L){
            return false;
        }
        return screenshotImage.getStartTime()<=timesamp
                && screenshotImage.getEndTime()>=timesamp;
    }

    /**
     * 安卓与ios深度检测动态截图入库，用于存储个人信息行为手动截图
     * @param taskId  任务id
     * @param tPrivacySharedPrefsList 存储个人信息行为实体
     * @param screenshotImages 手动截图的数据
     * @date 2021/7/8
     */
    @Override
    public void saveManualBehaviorImgShared(Long taskId, List<TPrivacySharedPrefs> tPrivacySharedPrefsList,List<ScreenshotImage> screenshotImages){
        //进行数据判断
        if(CollectionUtils.isEmpty(screenshotImages)){
            return;
        }
        List<TDynamicBehaviorImg> imageList = new ArrayList<>();
        for (TPrivacySharedPrefs tPrivacySharedPrefs:tPrivacySharedPrefsList) {
            if(tPrivacySharedPrefs.getActionTime() == null){
                return;
            }
            for(ScreenshotImage screenshotImage:screenshotImages){
                //只需要判断在时间搓之间就入库
                if (isBooleanScreenshotImgOfShared(tPrivacySharedPrefs, screenshotImage)) {
                    TDynamicBehaviorImg detail = gettDynamicBehaviorImg(taskId, null, tPrivacySharedPrefs.getId(), 4, screenshotImage);
                    imageList.add(detail);
                }
            }
        }
        InsertListHelper.insertList(imageList, dynamicBehaviorImgMapper::insertList);
    }

    /**
     * 存储个人信息行为截图入库判断条件
     * @param tPrivacySharedPrefs
     * @param screenshotImage
     * @return
     */
    private boolean isBooleanScreenshotImgOfShared(TPrivacySharedPrefs tPrivacySharedPrefs, ScreenshotImage screenshotImage) {

        Long timesamp = tPrivacySharedPrefs.getActionTime().getTime();
        if (screenshotImage.getStartTime() == 0L || screenshotImage.getEndTime() == 0L) {
            return false;
        }
        return screenshotImage.getStartTime() <= timesamp
                && screenshotImage.getEndTime() >= timesamp;
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        Example example = new Example(TDynamicBehaviorImg.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        dynamicBehaviorImgMapper.deleteByExample(example);
    }
}
