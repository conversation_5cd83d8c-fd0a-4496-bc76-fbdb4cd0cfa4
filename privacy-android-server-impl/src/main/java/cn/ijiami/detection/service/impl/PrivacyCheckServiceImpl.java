package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.entity.TPrivacyPolicyImg;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.detection.mapper.TPrivacyCheckMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyImgMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper;
import cn.ijiami.detection.service.api.IPrivacyCheckService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-05-27 10:44
 */
@Service
@CacheConfig(cacheNames = {"privacy-detection:policy"})
public class PrivacyCheckServiceImpl implements IPrivacyCheckService {

    private final TPrivacyCheckMapper      privacyCheckMapper;
    private final TPrivacyPolicyTypeMapper privacyPolicyTypeMapper;
    private final TPrivacyPolicyMapper     privacyPolicyMapper;
    private final TPrivacyPolicyImgMapper  privacyPolicyImgMapper;

    public PrivacyCheckServiceImpl(TPrivacyCheckMapper privacyCheckMapper, TPrivacyPolicyTypeMapper privacyPolicyTypeMapper,
                                   TPrivacyPolicyMapper privacyPolicyMapper, TPrivacyPolicyImgMapper privacyPolicyImgMapper) {
        this.privacyCheckMapper = privacyCheckMapper;
        this.privacyPolicyTypeMapper = privacyPolicyTypeMapper;
        this.privacyPolicyMapper = privacyPolicyMapper;
        this.privacyPolicyImgMapper = privacyPolicyImgMapper;
    }

    @Override
    public List<PrivacyPolicyTypeVO> findByTaskIdAndType(Long taskId, Integer type, Integer terminalType) {
        List<PrivacyPolicyTypeVO> policyTypeVOList = new ArrayList<>();
        // 查询应用截图
        List<TPrivacyPolicyImg> images = privacyPolicyImgMapper.selectImgByTaskId(taskId);
        Map<Long, List<String>> policyIamges = new HashMap<>(32);
        if (!CollectionUtils.isEmpty(images)) {
            policyIamges = images.stream().collect(Collectors.groupingBy(TPrivacyPolicyImg::getPolicyId, Collectors.mapping(TPrivacyPolicyImg::getFileKey, Collectors.toList())));
        }
        // 组装返回数据
        List<PrivacyCheckVO> privacyCheckVOList = privacyPolicyTypeMapper.findByTaskIdAndType(taskId, type, terminalType);
        for(PrivacyCheckVO vo: privacyCheckVOList){
            vo.setImages(policyIamges.get(vo.getId()));
        }
        //根据类型名称分组
        Map<String,List<PrivacyCheckVO>> privacyCheckMap = privacyCheckVOList.stream().collect(Collectors.groupingBy(vo -> vo.getTypeSort() + "-" + vo.getTypeName()));
        //组装数据
        for(String key:privacyCheckMap.keySet()){
            PrivacyPolicyTypeVO policyTypeVO = new PrivacyPolicyTypeVO();
            policyTypeVO.setType(type);
            policyTypeVO.setPrivacyCheckList(privacyCheckMap.get(key));
            policyTypeVO.setId(privacyCheckMap.get(key).get(0).getId());
//            policyTypeVO.setId(privacyCheckMap.get(key).get(0).getTypeId());
            policyTypeVO.setPolicyPointNum(privacyCheckMap.get(key).size());
            policyTypeVO.setTypeName(privacyCheckMap.get(key).get(0).getTypeName());
            policyTypeVO.setTypeSort(privacyCheckMap.get(key).get(0).getTypeSort());
            policyTypeVOList.add(policyTypeVO);
        }
        return policyTypeVOList.stream().sorted(Comparator.comparing(PrivacyPolicyTypeVO::getTypeSort)).collect(Collectors.toList());
    }

    @Override
    public List<PrivacyCheckVO> selectPrivacyPolicy(Long taskId, Integer type, Integer terminalType) {
        return privacyPolicyMapper.selectPrivacyPolicy(taskId, type, terminalType);
    }

    @Override
    public List<TPrivacyPolicyType> countLaw(Long taskId,Integer terminalType) {
        return privacyPolicyMapper.countLawByTaskId(taskId,terminalType);
    }

    @Override
    public List<TPrivacyPolicyType> countLawByDocumentId(String documentId) {
        return privacyPolicyMapper.countLawByDocumentId(documentId);
    }

    @Override
    public List<TPrivacyCheck> findByTypeVo(Integer type,Integer terminalType) {
       return privacyPolicyMapper.findByTypeVo(type,terminalType);

    }

    @Override
    public int deleteByTaskIdAndType(Long taskId, Integer type) {
        return privacyPolicyMapper.deleteByTaskIdAndType(taskId, type);
    }

    @Override
    public List<PrivacyPolicyTypeVO> findCheckPointByTerminalAndType(Integer terminalType, Integer lawType) {
        List<PrivacyPolicyTypeVO> policyTypeVOS = new ArrayList<>();
        List<PrivacyCheckVO> privacyChecks = privacyPolicyMapper.selectPrivacyCheckByTerminalAndLawType(terminalType, lawType);
        Map<String, List<PrivacyCheckVO>> groups = privacyChecks.stream().collect(Collectors.groupingBy(PrivacyCheckVO::getTypeName));
        for(String key:groups.keySet()){
            PrivacyPolicyTypeVO policyTypeVO = new PrivacyPolicyTypeVO();
            policyTypeVO.setType(lawType);
            policyTypeVO.setPrivacyCheckList(groups.get(key));
            policyTypeVO.setId(groups.get(key).get(0).getId());
            policyTypeVO.setPolicyPointNum(groups.get(key).size());
            policyTypeVO.setTypeName(groups.get(key).get(0).getTypeName());
            policyTypeVO.setTypeSort(groups.get(key).get(0).getTypeSort());
            policyTypeVOS.add(policyTypeVO);
        }
        return policyTypeVOS.stream().sorted(Comparator.comparing(PrivacyPolicyTypeVO::getTypeSort)).collect(Collectors.toList());
    }

    @Override
    public TPrivacyPolicyType findLawByType(Long taskId, Integer type,Integer terminalType) {
        return privacyPolicyMapper.countLawByTaskIdAndType(taskId, type,terminalType);
    }


    @Override
    public List<Integer> countType(Long taskId) {
        return privacyPolicyMapper.distinctTypeByTaskId(taskId);
    }

    @Override
    public List<TPrivacyCheck> findCcrcPrivacyPolicy() {
        return privacyCheckMapper.findByIds(Arrays.asList(35L, 36L, 52L));
    }

    @Override
    public List<TPrivacyPolicyType> findLaw(Integer terminalType) {
        return privacyPolicyTypeMapper.findLaw(terminalType);
    }
}
