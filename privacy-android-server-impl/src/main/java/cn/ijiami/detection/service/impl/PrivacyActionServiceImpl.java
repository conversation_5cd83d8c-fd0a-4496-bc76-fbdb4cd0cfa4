package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.base.common.file.entity.File;
import cn.ijiami.detection.VO.PrivacyActionVO;
import cn.ijiami.detection.VO.RiskImageVo;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.mapper.TPrivacyActionMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.IPrivacyActionService;
import cn.ijiami.detection.utils.ImageUtils;
import cn.ijiami.report.service.api.IReportChartService;

/**
 * <AUTHOR>
 * @date 2019-06-03 14:45
 */
@Service
public class PrivacyActionServiceImpl implements IPrivacyActionService {

    private final TPrivacyActionMapper privacyActionMapper;
    private final ICommonMongodbService commonMongodbService;
    private final TTaskMapper taskMapper;
    private final IjiamiCommonProperties commonProperties;
    private final IReportChartService reportChartService;
    private final IBaseFileService fileService;

    public PrivacyActionServiceImpl(TPrivacyActionMapper privacyActionMapper, ICommonMongodbService commonMongodbService,
                                    TTaskMapper tTaskMapper, IjiamiCommonProperties commonProperties,
                                    IReportChartService reportChartService, IBaseFileService fileService) {
        this.privacyActionMapper = privacyActionMapper;
        this.commonMongodbService = commonMongodbService;
        this.taskMapper = tTaskMapper;
        this.commonProperties = commonProperties;
        this.reportChartService = reportChartService;
        this.fileService = fileService;
    }

    @Override
    public List<PrivacyActionVO> findActionsByTaskId(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            return null;
        }
        List<PrivacyActionVO> privacyActions = privacyActionMapper.findByTaskId(taskId);

        TaskDetailVO detectionResult = commonMongodbService.findDetectionResult(task.getApkDetectionDetailId(), Collections.singletonList("0103"));
        if (detectionResult == null) {
            return privacyActions;
        }

        String detection_result = detectionResult.getDetection_result().toString();
        JSONArray jsonArray = JSON.parseArray(detection_result);
        JSONObject result_content = jsonArray.getJSONObject(0).getJSONObject("result_content");
        if (result_content == null) {
            return privacyActions;
        }
        JSONArray behavior = result_content.getJSONArray("behavior");
        if (behavior == null) {
            return privacyActions;
        }
        for (PrivacyActionVO privacyAction : privacyActions) {
            for (int i = 0; i < behavior.size(); i++) {
                JSONObject jsonObject = behavior.getJSONObject(i);
                if (jsonObject.getString("code").equals(privacyAction.getActionFunction())) {
                    JSONObject indepth = jsonObject.getJSONObject("indepth");
                    if (indepth != null) {
                        privacyAction.setFunctionLocation(indepth.getString("file_path"));
                    }
                }
            }

            if (StringUtils.hasText(privacyAction.getImgs())) {
                List<RiskImageVo> base64Images = new ArrayList<>();
                String[] split = privacyAction.getImgs().split(",");
                for (String s : split) {
                    File img = fileService.findFileByFileKey(s);
                    RiskImageVo riskImage = ImageUtils.getRiskImage(commonProperties.getFilePath() + img.getFilePath());
                    if (riskImage != null) {
                        riskImage.setImageBase64(reportChartService.getImageBASE64(commonProperties.getFilePath() + img.getFilePath()));
                        base64Images.add(riskImage);
                    }
                }
                privacyAction.setBase64Images(base64Images);
            }
        }
        return privacyActions;
    }
}
