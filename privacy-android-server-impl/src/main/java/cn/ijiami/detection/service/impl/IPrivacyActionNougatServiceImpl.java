package cn.ijiami.detection.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.utils.ActionNougatUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.service.api.IPrivacyActionNougatService;

/**
 * <AUTHOR>
 * @date 2019/11/12 16:37
 */
@Service
@CacheConfig(cacheNames = {"privacy-detection:action"})
public class IPrivacyActionNougatServiceImpl implements IPrivacyActionNougatService {

    private final TPrivacyActionNougatMapper privacyActionNougatMapper;
    private final TPrivacyActionMapper       privacyActionMapper;
    private final TActionNougatMapper        actionNougatMapper;
    private final TAssetsMapper tAssetsMapper;
    private final TTaskMapper tTaskMapper;

    public IPrivacyActionNougatServiceImpl(TPrivacyActionNougatMapper privacyActionNougatMapper, TPrivacyActionMapper privacyActionMapper,
                                           TActionNougatMapper actionNougatMapper, TAssetsMapper tAssetsMapper, TTaskMapper tTaskMapper) {
        this.privacyActionNougatMapper = privacyActionNougatMapper;
        this.privacyActionMapper = privacyActionMapper;
        this.actionNougatMapper = actionNougatMapper;
        this.tAssetsMapper = tAssetsMapper;
        this.tTaskMapper = tTaskMapper;
    }

    @Override
    //@Cacheable(key = "'findByTaskId:'+#p0")
    public List<TPrivacyActionNougat> findByTaskId(Long taskId) {
        return privacyActionNougatMapper.countByTaskId(taskId);
    }

    @Override
    public List<TPrivacyActionNougat> findAllByTaskId(Long taskId) {
        List<TPrivacyActionNougat> privacyActionNougatList = privacyActionNougatMapper.countByTaskId(taskId);
        for (TPrivacyActionNougat privacyActionNougat : privacyActionNougatList) {
            privacyActionNougat.setPrivacyActionNougats(privacyActionNougatMapper.findByTaskIdAndActionId(taskId, privacyActionNougat.getActionId()));
        }
        return privacyActionNougatList;
    }

    @Override
    @Cacheable(key = "'findByTaskIdAndActionId:'+#p0+','+#p1")
    public List<TPrivacyActionNougat> findByTaskIdAndActionId(Long taskId, Long actionId) {
        return privacyActionNougatMapper.findByTaskIdAndActionId(taskId, actionId);
    }

    @Override
    public Boolean isNougat(Long taskId) {
        return CollectionUtils.isEmpty(privacyActionMapper.findByTaskId(taskId));
    }

    @Override
    public List<PermissionVO> getActionPermissions(Long taskId, Integer behaviorStage) {
        return privacyActionNougatMapper.countActionPermission(taskId, behaviorStage);
    }

    @Override
    public List<PermissionVO> countActionPermissions(Long taskId) {
        return privacyActionNougatMapper.countActionPermissionByTaskId(taskId);
    }

    @Override
    public List<TPrivacyActionNougat> countActionByTaskId(Long taskId, Integer behaviorStage) {
        return privacyActionNougatMapper.countActionByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<PrivacyActionNougatVO> countActionNougatByTaskId(Long taskId, Integer behaviorStage) {
        return privacyActionNougatMapper.countActionNougatByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<TPrivacyActionNougat> findByTaskIdExcel(Long taskId) {
        return privacyActionNougatMapper.findByTaskIdExcel(taskId);
    }

    @Override
    public Integer countBehaviorsCategoryByTaskId(Long taskId) {
        return privacyActionNougatMapper.countBehaviorsCategoryByTaskId(taskId);
    }

    @Override
    public List<TPrivacyActionNougat> countBehaviorsByTaskIdAndStage(Long taskId, int countByTaskIdAndStage, Boolean merge) {
        List<TPrivacyActionNougat> result = privacyActionNougatMapper.countBehaviorsByTaskIdAndStage(taskId, countByTaskIdAndStage);
        if (merge) {
            return mergeTPrivacyActionNougatList(result);
        }
        return result;
    }

    @Override
    public List<TPrivacyActionNougat> countBehaviorsByTaskId(Long taskId, Boolean merge) {
        List<TPrivacyActionNougat> result = privacyActionNougatMapper.countBehaviorsByTaskId(taskId);
        if (merge) {
            return mergeTPrivacyActionNougatList(result);
        }
        return result;
    }

    private List<TPrivacyActionNougat> mergeTPrivacyActionNougatList(List<TPrivacyActionNougat> list) {
        List<TPrivacyActionNougat> result = new ArrayList<>();
        if (list == null) {
            return list;
        }
        // 其他分类
        TPrivacyActionNougat otherItem = new TPrivacyActionNougat();
        otherItem.setActionName("其他");
        otherItem.setCounter(0);
        int limitSize = 10;
        for (int index = 0; index < list.size(); index++) {
            TPrivacyActionNougat item = list.get(index);
            if (index > (limitSize - 2)) {
                if (list.size() > limitSize) {
                    otherItem.setBehaviorStage(item.getBehaviorStage());
                    otherItem.setCounter(item.getCounter() + otherItem.getCounter());
                } else {
                    result.add(item);
                }
            } else {
                result.add(item);
            }
        }
        if (otherItem.getCounter().intValue() > 0) {
            result.add(otherItem);
        }
        return result;
    }

    @Override
    public List<TPrivacyActionNougat> findByTaskIdAndExecutors(Long taskId, String executors, Integer behaviorStage) {

    	TAssets assets = tAssetsMapper.selectAssetByTaskId(taskId);
        List<TPrivacyActionNougat> roots = privacyActionNougatMapper.findByTaskIdAndBehaviorStageNew(taskId, behaviorStage, assets.getPakage());
//      List<TPrivacyActionNougat> roots = privacyActionNougatMapper.findByTaskIdAndBehaviorStage(taskId, behaviorStage);
        if (CollectionUtils.isEmpty(roots)) {
            return roots;
        }
//        System.out.println(JSON.toJSONString(roots));
        // 有筛选条件，先过滤一边（此方案可优化）
        if (StringUtils.isNotBlank(executors)) {
            roots = roots.stream().filter(executorFilter(executors)).collect(Collectors.toList());
        }
        // 合并统计数据
        List<TPrivacyActionNougat> targets = new ArrayList<>();
        roots.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getActionId)).forEach((actionId, actionNougats) -> {
            actionNougats.stream().reduce((a, b) -> {
                int aNum = Optional.ofNullable(a.getCounter()).orElse(0);
                int bNum = Optional.ofNullable(b.getCounter()).orElse(0);
                a.setCounter(aNum + bNum);
                return a;
            }).ifPresent(targets::add);
        });
        return targets.stream().sorted(Comparator
                .comparing((Function<TPrivacyActionNougat, Boolean>) n -> Optional.ofNullable(n.getSensitive()).orElse(false))
                .thenComparing(n -> Optional.ofNullable(n.getCounter()).orElse(0))
                .reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<TPrivacyActionNougat> findByTaskIdAndActionIdAndExecutors(Long taskId, Long actionId, String executors, Integer behaviorStage) {
        List<TPrivacyActionNougat> roots = null;
		if(actionId==121005 || actionId==21004 ||actionId==21005){
			TAssets assets = tAssetsMapper.selectAssetByTaskId(taskId);
			roots = privacyActionNougatMapper.findByTaskIdAndActionIdAndExecutorsNew(taskId, actionId, null, behaviorStage,assets.getPakage());
		}else {
			roots = privacyActionNougatMapper.getByTaskIdAndActionId(taskId, actionId, null, behaviorStage);
		}
        if (CollectionUtils.isEmpty(roots) || StringUtils.isEmpty(executors)) {
            return roots;
        }
        return roots.stream().filter(executorFilter(executors)).collect(Collectors.toList());
    }

    /**
     * 筛选出符合要求的记录
     *
     * @param targetExecutor eg:"全民吃瓜,第三方SDK,腾讯Bugly崩溃分析"
     * @return
     */
    private Predicate<TPrivacyActionNougat> executorFilter(String targetExecutor) {
        Predicate<TPrivacyActionNougat> filter = (actionNougat) -> {
            String executor = actionNougat.getExecutor();
            if (StringUtils.isEmpty(executor)) {
                return false;
            }
            String[] executors = executor.split(",");
            List<String> executorList = new ArrayList<>(executors.length);
            Collections.addAll(executorList, executors);
            boolean isValid = false;
            for (String str : executorList) {
                if (targetExecutor.contains(str)) {
                    isValid = true;
                    break;
                }
            }
            return isValid;
        };
        return filter;
    }

    @Override
    public List<TActionNougat> findTActionNougatByTerminalType(Integer terminalType) {
        return actionNougatMapper.findByTerminalType(terminalType);
    }

    @Override
    public List<String> getBehaviorSdk(Long taskId, Integer behaviorStage) {
        List<String> list = privacyActionNougatMapper.getBehaviorSdk(taskId, behaviorStage);
        TAssets tAssets = tAssetsMapper.selectAssetByTaskId(taskId);
        List<String> result = new ArrayList<>();
        for (String lineInfo : list) {
            for (String sdkInfo : lineInfo.split(",")) {
                if (result.contains(sdkInfo)) {
                    continue;
                }
                result.add(sdkInfo.replace("\n",""));
            }
        }
        //过滤掉与app名称相同的sdk主体
        result = result.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<String> getLawDetailBehaviorSdk(Long taskId, Integer dataType, String itemNo) {
        List<String> list = privacyActionNougatMapper.getLawDetailBehaviorSdk(taskId, dataType, itemNo);
        TAssets tAssets = tAssetsMapper.selectAssetByTaskId(taskId);
        List<String> result = new ArrayList<>();
        for (String lineInfo : list) {
            for (String sdkInfo : lineInfo.split(",")) {
                if (result.contains(sdkInfo)) {
                    continue;
                }
                result.add(sdkInfo.replace("\n",""));
            }
        }
        //过滤掉与app名称相同的sdk主体
        result = result.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        return result;
    }

    /**
     * 根据新增加的筛选条件查出所有数据平铺展示
     * @param behaviorQuery
     * @return
     */
    @Override
    public TPrivacyActionNougatVO findByTaskIdAndActionIdAndQuerys(BehaviorQuery behaviorQuery) {
        TPrivacyActionNougatVO tPrivacyActionNougatVo=new TPrivacyActionNougatVO();

        if(behaviorQuery != null && behaviorQuery.getTaskId() != null && behaviorQuery.getTaskId() != null){

            Long taskId=behaviorQuery.getTaskId();
            TTask task = tTaskMapper.selectByPrimaryKey(taskId);
            String executors=behaviorQuery.getExecutors();
            String actionPermissionAliases=behaviorQuery.getActionPermissionAliases();
            // 2.7.3新增行为名查询。行为表数据量大，为了避免新建行为名查询的索引，充分利用原有的行为id索引，把行为名转为id
            String actionIds;
            if (StringUtils.isNotBlank(behaviorQuery.getActionNames())) {
                List<Long> ids = actionNougatMapper.findActionIdByNames(Arrays.asList(behaviorQuery.getActionNames().split(",")), task.getTerminalType());
                actionIds = ids.stream().map(Object::toString).collect(Collectors.joining(","));
            } else {
                actionIds = behaviorQuery.getActionIds();
            }
            String executorTypeString=behaviorQuery.getExecutorType();
            String isPersonal=behaviorQuery.getIsPersonal();
            Integer behaviorStage=behaviorQuery.getBehaviorStage();
            Integer sortType=behaviorQuery.getSortType();
            Integer sortOrder=behaviorQuery.getSortOrder();

            TAssets assets = tAssetsMapper.selectAssetByTaskId(taskId);

            if (behaviorQuery.getPage() != null && behaviorQuery.getRows() != null) {
                PageHelper.startPage(behaviorQuery.getPage(), behaviorQuery.getRows());
            }
            //存放查询结果集
            List<TPrivacyActionNougat> roots = privacyActionNougatMapper.findByTaskIdAndActionIdAndQuerys(taskId, behaviorStage, assets.getPakage(),
                    executors, actionPermissionAliases, actionIds, executorTypeString, isPersonal, sortType, sortOrder, task.getTerminalType().getValue());

            //存放最终分页结果
            PageInfo<TPrivacyActionNougat> pageInfo = new PageInfo<>(roots);
            pageInfo.setList(roots
                    .stream()
                    .peek(ActionNougatUtils::setTriggerCycle)
                    .collect(Collectors.toList()));
            tPrivacyActionNougatVo.setPageInfo(pageInfo);
        }
        return tPrivacyActionNougatVo;
    }

    /**
     * 获取应用行为和行为权限互为关联的数据集合返回
     * @param taskId
     * @param behaviorStage
     * @return
     */
    @Override
    public BehaviorPermissionVo getBehaviorApplyNameAndPermission(Long taskId, Integer behaviorStage ,String packageName) {
        //要返回的数据类型
        BehaviorPermissionVo behaviorPermissionVo = new BehaviorPermissionVo();
        //数据库查询获取应用行为名称数据集
        List<ActionNameAndPermission> actionNameList;
        //数据库查询获取行为权限数据集
        List<ActionNameAndPermission> actionPermissionAliasList;
        //应用行为名称基础数据
        List<ActionNameAndPermission> actionNames;
        //行为权限基础数据
        List<String> actionNameAndPermissions;


        if(taskId==null || taskId==0L){
            return behaviorPermissionVo;
        }
        actionNames=privacyActionNougatMapper.getBehaviorApplyName(taskId, behaviorStage,packageName);
        actionNameAndPermissions=privacyActionNougatMapper.getBehaviorPermission(taskId, behaviorStage);
        actionNameList=privacyActionNougatMapper.getBehaviorActionNameList(taskId,behaviorStage,packageName);
        actionPermissionAliasList=privacyActionNougatMapper.getBehaviorPermissionList(taskId,behaviorStage,packageName);

        behaviorPermissionVo.setActionNames(actionNames);
        behaviorPermissionVo.setActionPermissions(actionNameAndPermissions);
        setActionNameAndPermission(behaviorPermissionVo, actionNameList, actionPermissionAliasList);
        return behaviorPermissionVo;
    }

    /**
     * 获取应用行为和行为权限互为关联的数据集合返回
     * @param taskId
     * @return
     */
    @Override
    public BehaviorPermissionVo getLawDetailBehaviorApplyNameAndPermission(Long taskId, Integer dataType, String itemNo) {
        //要返回的数据类型
        BehaviorPermissionVo behaviorPermissionVo =new BehaviorPermissionVo();
        if(taskId==null || taskId==0L){
            return behaviorPermissionVo;
        }
        //数据库查询获取应用行为名称数据集
        List<ActionNameAndPermission> actionNameList = privacyActionNougatMapper.getLawDetailBehaviorActionNameList(taskId, dataType, itemNo);
        //数据库查询获取行为权限数据集
        List<ActionNameAndPermission> actionPermissionAliasList = privacyActionNougatMapper.getLawDetailBehaviorPermissionList(taskId, dataType, itemNo);
        //应用行为名称基础数据
        List<ActionNameAndPermission> actionNames = privacyActionNougatMapper.getLawDetailBehaviorApplyName(taskId, dataType, itemNo);
        //行为权限基础数据
        List<String> actionNameAndPermissions = privacyActionNougatMapper.getLawDetailBehaviorPermission(taskId, dataType, itemNo);
        behaviorPermissionVo.setActionNames(actionNames);
        behaviorPermissionVo.setActionPermissions(actionNameAndPermissions);
        behaviorPermissionVo.setTaskId(taskId);
        setActionNameAndPermission(behaviorPermissionVo, actionNameList, actionPermissionAliasList);
        return behaviorPermissionVo;
    }

    @Override
    public List<AppStorePrivacyApiVO> getAppStorePrivacyApiList(Long taskId) {
        return privacyActionNougatMapper.getAppStorePrivacyApiList(taskId);
    }

    private void setActionNameAndPermission(BehaviorPermissionVo behaviorPermissionVo,
                                            List<ActionNameAndPermission> actionNameList,
                                            List<ActionNameAndPermission> actionPermissionAliasList) {
        //存储筛选后的应用行为关联行为权限数据集
        Map<Long,List<ActionNameAndPermission>> actionRelatedPermission = new HashMap<>();
        //存储筛选后的行为权限关联应用行为数据集
        Map<String,List<ActionNameAndPermission>> permissionRelatedAction = new HashMap<>();
        for(ActionNameAndPermission a:actionNameList){
            List<ActionNameAndPermission> list=new ArrayList<>();
            for(ActionNameAndPermission b:actionPermissionAliasList){
                if(a.getActionId().equals(b.getActionId())){
                    list.add(b);
                }
            }
            actionRelatedPermission.put(a.getActionId(),list.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(ActionNameAndPermission::getActionId))
                    ), ArrayList::new)));

        }

        for(ActionNameAndPermission permission:actionPermissionAliasList){
            List<ActionNameAndPermission> list=new ArrayList<>();
            for(ActionNameAndPermission aName:actionNameList){
                if(StringUtils.isEmpty(permission.getActionPermissionAlias()) || StringUtils.isEmpty(aName.getActionPermissionAlias())){
                    continue;
                }
                if(permission.getActionPermissionAlias().equals(aName.getActionPermissionAlias())){
                    list.add(aName);
                }
            }
            permissionRelatedAction.put(permission.getActionPermissionAlias(),list.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(ActionNameAndPermission::getActionId))
                    ), ArrayList::new)));
        }
        behaviorPermissionVo.setActionNameList(actionRelatedPermission);
        behaviorPermissionVo.setActionPermissionAliasList(permissionRelatedAction);
    }
}
