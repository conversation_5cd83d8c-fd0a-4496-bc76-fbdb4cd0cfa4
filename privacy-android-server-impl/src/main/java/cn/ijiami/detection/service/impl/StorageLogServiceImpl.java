package cn.ijiami.detection.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TStorageLog;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.TChunkUploadFileMapper;
import cn.ijiami.detection.mapper.TStorageLogMapper;
import cn.ijiami.detection.service.api.IStorageLogService;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.manager.user.entity.User;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件存储记录服务
 *
 * <AUTHOR>
 * @date 2020/7/11 11:07
 **/
@Slf4j
@Service
public class StorageLogServiceImpl implements IStorageLogService {

    @Autowired
    private TStorageLogMapper storageLogMapper;

    @Autowired
    private TChunkUploadFileMapper chunkUploadFileMapper;

    @Autowired
    private SingleFastDfsFileService fastDfsFileService;

    @Override
    public void saveStorageLogByAsset(TAssets asset, Long userId) {
        if (Objects.isNull(asset)) {
            return;
        }
        String filePath = asset.getAddress();
        // 本地存储文件类型
        if (StringUtils.isNotEmpty(filePath)) {
            TStorageLog diskFileLog = generateWaitingForUse(FileUtil.decodeData(filePath), getStorageFileTypeByTerminalType(asset.getTerminalType()), StorageTypeEnum.LOCAL, userId);
            if (CollectionUtils.isEmpty(storageLogMapper.findNullAssetsIdByAddress(diskFileLog.getStorageAddress(), diskFileLog.getCreateUserId()))) {
                storageLogMapper.insert(diskFileLog);
            }
        }
        // 文件服务器存储
        String fastDfsPath = asset.getShellIpaPath();
        if (StringUtils.isNotEmpty(fastDfsPath)) {
            TStorageLog fastDfsFileLog = generateWaitingForUse(fastDfsPath, getStorageFileTypeByTerminalType(asset.getTerminalType()), StorageTypeEnum.FAST_DFS, userId);
            fastDfsFileLog.setAssociationKey(asset.getMd5());
            if (CollectionUtils.isEmpty(storageLogMapper.findNullAssetsIdByAddress(fastDfsFileLog.getStorageAddress(), fastDfsFileLog.getCreateUserId()))) {
                storageLogMapper.insert(fastDfsFileLog);
            }
        }
    }

    private StorageFileType getStorageFileTypeByTerminalType(TerminalTypeEnum terminalTypeEnum) {
        if (TerminalTypeEnum.ANDROID == terminalTypeEnum) {
            return StorageFileType.ANDROID;
        } else if (TerminalTypeEnum.IOS == terminalTypeEnum) {
            return StorageFileType.IOS;
        } else if (TerminalTypeEnum.ALIPAY_APPLET == terminalTypeEnum) {
            return StorageFileType.ALIPAY_APPLET;
        } else if (TerminalTypeEnum.WECHAT_APPLET == terminalTypeEnum) {
            return StorageFileType.WECHAT_APPLET;
        } else if (TerminalTypeEnum.HARMONY == terminalTypeEnum) {
            return StorageFileType.HARMONY;
        } else {
            throw new IllegalArgumentException("storage file type not found!");
        }
    }

    @Override
    public void saveStorageLogByFastDfsPath(String associationKey, String fastDfsPath, StorageFileType storageFileType, Long userId) {
        if (Objects.isNull(fastDfsPath)) {
            return;
        }
        // 文件服务器存储
        if (StringUtils.isNotEmpty(fastDfsPath)) {
            TStorageLog fastDfsFileLog = generateWaitingForUse(fastDfsPath, storageFileType, StorageTypeEnum.FAST_DFS, userId);
            fastDfsFileLog.setAssociationKey(associationKey);
            if (CollectionUtils.isEmpty(storageLogMapper.findNullAssetsIdByAddress(fastDfsFileLog.getStorageAddress(), fastDfsFileLog.getCreateUserId()))) {
                storageLogMapper.insert(fastDfsFileLog);
            }
        }
    }

    @Override
    public void saveStorageLogByTaskFile(Long taskId, String fastDfsPath) {
        if (Objects.isNull(fastDfsPath)) {
            return;
        }
        // 文件服务器存储
        if (StringUtils.isNotEmpty(fastDfsPath)) {
            TStorageLog fastDfsFileLog = generateNormal(fastDfsPath, StorageTypeEnum.FAST_DFS);
            fastDfsFileLog.setAssociationKey(taskId.toString());
            if (CollectionUtils.isEmpty(storageLogMapper.findNullAssetsIdByAddress(fastDfsFileLog.getStorageAddress(), fastDfsFileLog.getCreateUserId()))) {
                storageLogMapper.insert(fastDfsFileLog);
            }
        }
    }

    @Override
    public void saveStorageLogByExpired(String fastDfsPath, Long expireIn) {

    }

    /**
     * 查询所有记录，包括已删除的
     * @param storageAddress
     * @return
     */
    @Override
    public boolean existsAssetsIdNotNullByAddress(String storageAddress) {
        return storageLogMapper.countAssetsIdNotNullByAddress(storageAddress) > 0;
    }

    @Override
    public TStorageLog findOneByAssociationKey(String associationKey, List<StorageFileType> fileTypeList, Long createUserId) {
        return storageLogMapper.findOneByAssociationKey(associationKey, fileTypeList, createUserId);
    }

    @Override
    public boolean hasTaskFile(Long taskId) {
        return storageLogMapper.countTaskFileByTaskId(taskId.toString(), StorageTypeEnum.FAST_DFS.getValue()) > 0;
    }

    @Override
    public void deleteTaskFile(Long taskId) {
        storageLogMapper.markWaitDeleteByAssociationKey(taskId.toString(), StorageTypeEnum.FAST_DFS.getValue(), new Date());
    }

    @Override
    public void deleteFile(String associationKey) {
        storageLogMapper.markWaitDeleteByAssociationKey(associationKey, StorageTypeEnum.FAST_DFS.getValue(), new Date());
    }

    @Override
    public void deleteStorage(TAssets asset) {
        Long assetId = asset.getId();
        if (assetId == null) {
            return;
        }
        // 更新存储记录
        String logoPath = FileUtil.decodeData(asset.getAddress());
        String apkPath = asset.getShellIpaPath();
        String privacyPolicyPath = asset.getPrivacyPolicyPath();
        String thirdPartyShareListPath = asset.getThirdPartyShareListPath();
        String obbPath = asset.getObbDataPath();
        if (StringUtils.isNotEmpty(logoPath)) {
            log.info("标记等待清除文件 assetsId={} filePath={}", asset.getId(), logoPath);
            // 只有一个资产使用了这个文件，标记为移除，等待定时间清除
            storageLogMapper.markWaitDeleteByStorageAddress(logoPath, assetId, new Date());
        }
        if (StringUtils.isNotEmpty(apkPath)) {
            log.info("标记等待清除文件 assetsId={} apkPath={}", asset.getId(), apkPath);
            storageLogMapper.markWaitDeleteByStorageAddress(apkPath, assetId, new Date());
        }
        if (StringUtils.isNotEmpty(privacyPolicyPath)) {
            log.info("标记等待清除文件 assetsId={} privacyPolicyPath={}", asset.getId(), privacyPolicyPath);
            storageLogMapper.markWaitDeleteByStorageAddress(privacyPolicyPath, assetId, new Date());
        }
        if(StringUtils.isNotEmpty(thirdPartyShareListPath)){
            log.info("标记等待清除文件 assetsId={} thirdPartyShareListPath={}",asset.getId(),thirdPartyShareListPath);
            storageLogMapper.markWaitDeleteByStorageAddress(thirdPartyShareListPath,assetId,new Date());
        }
        if(StringUtils.isNotEmpty(obbPath)){
            log.info("标记等待清除文件 assetsId={} obbPath={}",asset.getId(),obbPath);
            storageLogMapper.markWaitDeleteByStorageAddress(obbPath,assetId,new Date());
        }
    }

    @Override
    public void updateStorageLog(TAssets asset) {
        Long assetId = asset.getId();
        if (assetId == null) {
            return;
        }
        // 更新存储记录为已使用
        String logoPath = FileUtil.decodeData(asset.getAddress());
        String appPath = asset.getShellIpaPath();
        String privacyPolicyPath = asset.getPrivacyPolicyPath();
        String thirdPartyShareListPath = asset.getThirdPartyShareListPath();
        String qrcodePath = asset.getQrcodePath();
        if (StringUtils.isNotEmpty(logoPath)) {
            storageLogMapper.updateByStorageAddress(logoPath, assetId, new Date(), asset.getCreateUserId());
        }
        if (StringUtils.isNotEmpty(appPath)) {
            storageLogMapper.updateByStorageAddress(appPath, assetId, new Date(), asset.getCreateUserId());
        }
        if (StringUtils.isNotEmpty(privacyPolicyPath)) {
            storageLogMapper.updateByStorageAddress(privacyPolicyPath, assetId, new Date(), asset.getCreateUserId());
        }
        if(StringUtils.isNotEmpty(thirdPartyShareListPath)){
            storageLogMapper.updateByStorageAddress(thirdPartyShareListPath,assetId,new Date(),asset.getCreateUserId());
        }
        if(StringUtils.isNotEmpty(qrcodePath)){
            storageLogMapper.updateByStorageAddress(qrcodePath,assetId,new Date(),asset.getCreateUserId());
        }
    }

    @Override
    public void updateByAssociationKey(String associationKey, Long createUserId) {
        // 更新存储记录为已使用
        storageLogMapper.updateByAssociationKey(associationKey, new Date(), createUserId);
    }

    @Override
    public void cleanLocalUnused(TStorageLog storageLog) {
        // 同一个地址，TStorageLog可能会有多个资产使用记录，需要是否最后一条记录
        if (storageLogMapper.countAssetsIdNotNullByAddress(storageLog.getStorageAddress()) == 0) {
            boolean success = FileUtil.deleteFile(storageLog.getStorageAddress());
            if (success) {
                log.info("删除文件 filePath={}", storageLog.getStorageAddress());
                storageLog.setDelete(StorageLogDeleteEnum.DELETE.getValue());
                storageLog.setGmtModified(new Date());
                storageLogMapper.updateByPrimaryKey(storageLog);
            } else if (Objects.nonNull(storageLog.getGmtModified())
                    && System.currentTimeMillis() - storageLog.getGmtModified().getTime() > TimeUnit.DAYS.toMillis(7)) {
                log.info("删除文件失败 文件修改超过7天 记录强制改为已删除 filePath={}", storageLog.getStorageAddress());
                storageLog.setDelete(StorageLogDeleteEnum.DELETE.getValue());
                storageLog.setGmtModified(new Date());
                storageLogMapper.updateByPrimaryKey(storageLog);
            }
        } else {
            log.info("不用删除 直接更新 filePath={}", storageLog.getStorageAddress());
            storageLog.setDelete(StorageLogDeleteEnum.DELETE.getValue());
            storageLog.setGmtModified(new Date());
            storageLogMapper.updateByPrimaryKey(storageLog);
        }
    }

    @Override
    public void cleanFastDfsUnused(TStorageLog storageLog) {
        // 同一个地址，TStorageLog可能会有多个资产使用记录，需要是否最后一条记录
        if (storageLogMapper.countAssetsIdNotNullByAddress(storageLog.getStorageAddress()) == 0) {
            boolean success = false;
            try {
                success = fastDfsFileService.instance().deleteFile(storageLog.getStorageAddress());
            } catch (Exception e) {
                log.info("删除文件 fastDfsPath={} 错误={}", storageLog.getStorageAddress(), e.getMessage());
            }
            if (success) {
                log.info("删除文件 fastDfsPath={}", storageLog.getStorageAddress());
                storageLog.setDelete(StorageLogDeleteEnum.DELETE.getValue());
                storageLog.setGmtModified(new Date());
                storageLogMapper.updateByPrimaryKey(storageLog);
            } else {
                log.info("删除文件 fastDfsPath={} 失败", storageLog.getStorageAddress());
            }
        } else {
            log.info("不用删除 直接更新 fastDfsPath={}", storageLog.getStorageAddress());
            storageLog.setDelete(StorageLogDeleteEnum.DELETE.getValue());
            storageLog.setGmtModified(new Date());
            storageLogMapper.updateByPrimaryKey(storageLog);
        }
    }

    @Override
    public void cleanUnusedStorage() {
        log.info("IStorageLogService-清理无效资产上传文件开始");
        List<TStorageLog> logs = storageLogMapper.selectUnusedStorage();
        for (TStorageLog log : logs) {
            StorageTypeEnum storage = StorageTypeEnum.getStorageByValue(log.getStorageType());
            switch (storage) {
                case LOCAL:
                    cleanLocalUnused(log);
                    break;
                case FAST_DFS:
                    cleanFastDfsUnused(log);
                    break;
                default:
                    break;
            }
        }
        log.info("IStorageLogService-清理无效资产上传文件结束，清理个数：{}", logs.size());
    }

    private TStorageLog generateWaitingForUse(String address, StorageFileType storageFileType, StorageTypeEnum storageType, Long userId) {
        TStorageLog tStorageLog = new TStorageLog();
        tStorageLog.setAssetsId(null);
        tStorageLog.setStorageAddress(address);
        tStorageLog.setFileType(storageFileType.getValue());
        tStorageLog.setStorageType(storageType.getValue());
        tStorageLog.setDelete(StorageLogDeleteEnum.WAITING_FOR_USE.getValue());
        tStorageLog.setCreateUserId(userId);
        tStorageLog.setGmtCreate(new Date());
        tStorageLog.setGmtModified(new Date());
        return tStorageLog;
    }

    private TStorageLog generateNormal(String address, StorageTypeEnum storageType) {
        TStorageLog tStorageLog = new TStorageLog();
        tStorageLog.setAssetsId(null);
        tStorageLog.setStorageAddress(address);
        tStorageLog.setFileType(StorageFileType.IMAGE.getValue());
        tStorageLog.setStorageType(storageType.getValue());
        tStorageLog.setDelete(StorageLogDeleteEnum.NORMAL.getValue());
        tStorageLog.setCreateUserId(null);
        tStorageLog.setGmtCreate(new Date());
        tStorageLog.setGmtModified(new Date());
        return tStorageLog;
    }
}
