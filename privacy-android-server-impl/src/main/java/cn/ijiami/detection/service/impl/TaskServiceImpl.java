package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.PinfoConstant.SUSPICIOUS_TYPE_NAME;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;

import cn.ijiami.detection.VO.TDetectionConfigVO;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.base.common.data.DataHelper;
import cn.ijiami.detection.android.client.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.android.client.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.android.client.param.StartTaskParam;
import cn.ijiami.detection.bean.DetectionItem;
import cn.ijiami.detection.bean.HarmonyBaseResultContentDTO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.TIosDeviceConnectionMapper;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.*;
import cn.ijiami.manager.user.mapper.UserMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;

import cn.ijiami.base.common.about.entity.About;
import cn.ijiami.base.common.about.service.api.IAboutService;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.ActionVO;
import cn.ijiami.detection.VO.AppletExtraInfoVO;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.ChongqinVO;
import cn.ijiami.detection.VO.ClientUpdateProgressVO;
import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.SuspiciousSdkMergeVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskConditionCheckResult;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.UserPermissionVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.DetectionResultVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.sdk.IosPrivacySdk;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.helper.PackageNameExtractHelper;
import cn.ijiami.detection.helper.TaskDetailHelper;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.job.TaskSortThread;
import cn.ijiami.detection.job.XxlDetectAnalysisHelper;
import cn.ijiami.detection.job.XxlDetectWebServer;
import cn.ijiami.detection.mapper.TActionFilterGroupMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TCustomLawsGroupMapper;
import cn.ijiami.detection.mapper.TDynamicBehaviorImgMapper;
import cn.ijiami.detection.mapper.TIosPrivacyActionSdkMapper;
import cn.ijiami.detection.mapper.TOdpCompanyMapper;
import cn.ijiami.detection.mapper.TOdpCompanyProductMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsResultMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyResultMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSensitiveTypeMapper;
import cn.ijiami.detection.mapper.TStaticFunctionRecordMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.message.MessageSendKit;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.detection.service.api.vo.ContentDataModel;
import cn.ijiami.detection.service.api.vo.ItemDataModel;
import cn.ijiami.detection.websocket.SampleIdbCallback;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.report.service.api.IReportChartService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import tk.mybatis.mapper.entity.Example;

/**
 * 检测任务模块接口实现
 *
 * <AUTHOR>
 */
@Service
@Transactional
@CacheConfig(cacheNames = {"privacy-detection:task"})
public class TaskServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements ITaskService {
    private static final Logger                       LOG             = LoggerFactory.getLogger(TaskServiceImpl.class);
    private final        TTaskMapper                  taskMapper;
    private final        TPermissionMapper            permissionMapper;
    private final        TAssetsMapper                assetsMapper;
    private final        TSensitiveTypeMapper         sensitiveTypeMapper;
    private final        UserMapper                 userMapper;
    private final        IDetectionTemplateService    detectionTemplateServiceImpl;
    private final        ICommonMongodbService        commonMongodbService;
    private final        TSdkLibraryMapper            sdkMapper;
    private final        IjiamiCommonProperties       commonProperties;
    private final        IPrivacyCheckService         privacyCheckService;
    private final        ISendMessageService          sendMessageServiceImpl;
    private final        TPrivacyActionNougatMapper   privacyActionNougatMapper;
    private final        TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;
    private final        TPrivacySensitiveWordMapper  tPrivacySensitiveWordMapper;
    private final        XxlDetectWebServer           xxlDetectWebServer;
    private final        TPrivacySharedPrefsMapper    tPrivacySharedPrefsMapper;
    private final        TaskSortThread               taskSortThread;
    private final        MessageSendKit               messageSendKit;
    private final        TPrivacyPolicyTypeMapper     tPrivacyPolicyTypeMapper;
    private final        IjiamiCommonProperties       ijiamiCommonProperties;
    private final        ITaskExtendService           taskExtendService;
    private final        TTaskExtendMapper            taskExtendMapper;
    private final        IReportChartService          reportChartService;
    private final        TSuspiciousSdkMapper         suspiciousSdkMapper;
    private final        TSuspiciousSdkLibraryMapper  suspiciousSdkLibraryMapper;
    private final        TStaticFunctionRecordMapper  staticFunctionRecordMapper;
    private final        StaticFunctionAnalysisService staticFunctionAnalysisService;
    private final        TIosDeviceConnectionMapper iosDeviceConnectionMapper;
    private final        IdbManagerService             idbManagerService;
    private final        TOdpCompanyProductMapper      odpCompanyProductMapper;
    private final        TOdpCompanyMapper             odpCompanyMapper;
    private final        TIosPrivacyActionSdkMapper    iosPrivacyActionSdkMapper;
    private final        TDynamicBehaviorImgMapper     dynamicBehaviorImgMapper;
    private final        TaskDAO                       taskDAO;
    private final        TStaticFunctionRecordMapper   tStaticFunctionRecordMapper;
    private final        TPrivacyLawsResultMapper      privacyLawsResultMapper;
    private final        TPrivacyLawsDetailMapper      privacyLawsDetailMapper;
    private final        TPrivacyPolicyResultMapper    privacyPolicyResultMapper;
    private final        StatisticsService             statisticsService;
    private final IDynamicTaskContextService dynamicTaskContextService;
    private final SingleFastDfsFileService singleFastDfsFileService;
    private final        IStorageLogService            storageLogService;
    private final        DetectionConfigService detectionConfigService;
    private final        TaskManagerService taskManagerService;
    private final        TActionFilterGroupMapper tActionFilterGroupMapper;
    private final        TCustomLawsGroupMapper customLawsGroupMapper;
	private final        IAboutService aboutService;

    private final ITaskDataService taskDataService;
	

    @Value("${fastDFS.ip}")
    private String fastDFSIp;
    /**
     * mongodb 查询返回字段
     */
    private static String[] fields =
            new String[]{"_id", "assets_id", "template_id", "apk_name", "apk_version", "apk_package", "apk_logo", "md5", "sign_md5", "apk_size",
                    "apk_file_address", "apk_detection_score", "apk_highrisk_count", "apk_middlerisk_count", "apk_lowrisk_count", "apk_is_reinforce", "isSafe",
                    "apk_detection_starttime", "apk_detection_status", "dynamic_detection_status", "dynamic_manual_detection_status",
                    "dynamic_law_detection_status", "dynamicProgress", "describe", "apk_type", "is_delete", "terminal_type", "create_user_id", "create_time",
                    "progress", "update_time", "apk_detection_endtime", "apk_detection_time", "dynamic_detection_description", "idb_type", "applet_extra_info"};
    private static final String[] HOT_UPDATE_SDKS =
            new String[]{"Dexposed", "Q-Zone", "AndFix", "Tinker", "Robust", "Amigo", "Nuwa", "HotFix", "RootFix"};

    @Value("${ijiami.download.app.prefix}")
    private String downloadAppUrlPrefix;

    @Value("${ijiami.download.dump.prefix}")
    private String downloadDumpUrlPrefix;

    @Value("${ijiami.task.privacy.info:false}")
    private boolean taskPrivacyInfo;

    @Value("${ijiami.ios.remote.tool.uploadUrl}")
    private String uploadUrl;

    public TaskServiceImpl(TPermissionMapper permissionMapper, TTaskMapper taskMapper, TaskSortThread taskSortThread, ITaskExtendService taskExtendService,
                           TAssetsMapper assetsMapper, TSensitiveTypeMapper sensitiveTypeMapper,
                           TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper, TPrivacySensitiveWordMapper tPrivacySensitiveWordMapper,
                           UserMapper userMapper, IDetectionTemplateService detectionTemplateServiceImpl,
                           TPrivacyActionNougatMapper privacyActionNougatMapper, TPrivacyOutsideAddressMapper privacyOutsideAddressMapper,
                           TDynamicBehaviorImgMapper dynamicBehaviorImgMapper, IReportChartService reportChartService, MessageSendKit messageSendKit,
                           ICommonMongodbService commonMongodbService, TSdkLibraryMapper sdkMapper, XxlDetectWebServer xxlDetectWebServer,
                           TPrivacySharedPrefsMapper tPrivacySharedPrefsMapper, ISendMessageService sendMessageServiceImpl,
                           IjiamiCommonProperties commonProperties, IjiamiCommonProperties ijiamiCommonProperties, TTaskExtendMapper taskExtendMapper,
                           IPrivacyCheckService privacyCheckService, TSuspiciousSdkMapper suspiciousSdkMapper, TSuspiciousSdkLibraryMapper suspiciousSdkLibraryMapper,
                           TStaticFunctionRecordMapper staticFunctionRecordMapper, StaticFunctionAnalysisService staticFunctionAnalysisService,
                           TIosDeviceConnectionMapper iosDeviceConnectionMapper, IdbManagerService idbManagerService,
                           TOdpCompanyProductMapper odpCompanyProductMapper, TOdpCompanyMapper odpCompanyMapper,
                           TIosPrivacyActionSdkMapper iosPrivacyActionSdkMapper, SingleFastDfsFileService singleFastDfsFileService,
                           TaskDAO taskDAO, TStaticFunctionRecordMapper tStaticFunctionRecordMapper, TPrivacyLawsResultMapper privacyLawsResultMapper,
                           TPrivacyLawsDetailMapper privacyLawsDetailMapper, TPrivacyPolicyResultMapper privacyPolicyResultMapper,
                           StatisticsService statisticsService,
                           IDynamicTaskContextService dynamicTaskDataService, IStorageLogService storageLogService, ITaskDataService taskDataService,
                           TaskManagerService taskManagerService, DetectionConfigService detectionConfigService,
                           TActionFilterGroupMapper tActionFilterGroupMapper, TCustomLawsGroupMapper customLawsGroupMapper, IAboutService aboutService) {
        this.permissionMapper = permissionMapper;
        this.taskMapper = taskMapper;
        this.taskSortThread = taskSortThread;
        this.taskExtendService = taskExtendService;
        this.assetsMapper = assetsMapper;
        this.sensitiveTypeMapper = sensitiveTypeMapper;
        this.tPrivacyPolicyTypeMapper = tPrivacyPolicyTypeMapper;
        this.tPrivacySensitiveWordMapper = tPrivacySensitiveWordMapper;
        this.userMapper = userMapper;
        this.detectionTemplateServiceImpl = detectionTemplateServiceImpl;
        this.privacyActionNougatMapper = privacyActionNougatMapper;
        this.privacyOutsideAddressMapper = privacyOutsideAddressMapper;
        this.dynamicBehaviorImgMapper = dynamicBehaviorImgMapper;
        this.reportChartService = reportChartService;
        this.messageSendKit = messageSendKit;
        this.commonMongodbService = commonMongodbService;
        this.sdkMapper = sdkMapper;
        this.xxlDetectWebServer = xxlDetectWebServer;
        this.tPrivacySharedPrefsMapper = tPrivacySharedPrefsMapper;
        this.sendMessageServiceImpl = sendMessageServiceImpl;
        this.commonProperties = commonProperties;
        this.ijiamiCommonProperties = ijiamiCommonProperties;
        this.taskExtendMapper = taskExtendMapper;
        this.privacyCheckService = privacyCheckService;
        this.suspiciousSdkMapper = suspiciousSdkMapper;
        this.suspiciousSdkLibraryMapper = suspiciousSdkLibraryMapper;
        this.staticFunctionRecordMapper = staticFunctionRecordMapper;
        this.staticFunctionAnalysisService = staticFunctionAnalysisService;
        this.iosDeviceConnectionMapper = iosDeviceConnectionMapper;
        this.idbManagerService = idbManagerService;
        this.odpCompanyProductMapper = odpCompanyProductMapper;
        this.odpCompanyMapper = odpCompanyMapper;
        this.iosPrivacyActionSdkMapper = iosPrivacyActionSdkMapper;
        this.taskDAO = taskDAO;
        this.tStaticFunctionRecordMapper = tStaticFunctionRecordMapper;
        this.privacyLawsResultMapper = privacyLawsResultMapper;
        this.privacyLawsDetailMapper = privacyLawsDetailMapper;
        this.privacyPolicyResultMapper = privacyPolicyResultMapper;
        this.statisticsService = statisticsService;
        this.dynamicTaskContextService = dynamicTaskDataService;
        this.singleFastDfsFileService = singleFastDfsFileService;
        this.storageLogService = storageLogService;
        this.taskDataService = taskDataService;
        this.taskManagerService = taskManagerService;
        this.detectionConfigService = detectionConfigService;
        this.tActionFilterGroupMapper = tActionFilterGroupMapper;
        this.customLawsGroupMapper = customLawsGroupMapper;
        this.aboutService = aboutService;
    }


    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Throwable.class)
    @Caching(evict = {@CacheEvict(value = "privacy-detection:assets", allEntries = true, beforeInvocation = true),
            @CacheEvict(value = "privacy-detection:task", allEntries = true, beforeInvocation = true)})
    public Long startTask(StartTaskParam param) {

    	LOG.info("启动参数={}", com.alibaba.fastjson.JSONObject.toJSONString(param));
        Long assetsId = param.getAssetsIds().get(0);
        TaskConditionCheckResult result = detectionConfigService.preTaskConditionCheck(param.getUserId(), Collections.singletonList(assetsId));
        if (!result.isAllowed()) {
            throw new IjiamiRuntimeException(result.getTipsText());
        }
        
        // 设置默认模板
        TerminalTypeEnum terminal = TerminalTypeEnum.ANDROID;
        checkActionFilterGroup(param.getActionFilterGroupId(), param.getUserId(), terminal);
        checkCustomLawsGroup(param.getCustomLawsGroupId(), terminal);
        Long templateId = param.getTemplateId();
        if (Objects.isNull(templateId)) {
            templateId = xxlDetectWebServer.getDefaultTemplateId(terminal);
            param.setTemplateId(templateId);
        }
        // 查询资产信息
        TAssets assets = assetsMapper.selectByPrimaryKey(assetsId);

        // 新增 基本信息Mogondb数据入库
        TaskDetailVO taskDetailVO = buildTaskDetailVO(assets);
        taskDetailVO.setCreate_user_id(param.getUserId()==null?null:param.getUserId().intValue());
        taskDetailVO.setTemplate_id(Math.toIntExact(param.getTemplateId()));
        taskDetailVO.setDynamic_detection_status(DynamicAutoStatusEnum.DETECTION_AUTO_WAITING.getValue());
        LOG.info("mongodb 入库 {}", JSON.toJSONString(taskDetailVO));
        
        //当前检测系统版本
		About about = aboutService.findAbout(new About());
		if(about != null) {
		  taskDetailVO.setSystemVersion(about.getVersion());
		}
        taskDetailVO.setPlatformHost(getPlatformHost());
        taskDetailVO = save(taskDetailVO);

        // 新增 任务task入库
        TTask task = new TTask();
        task.setApkDetectionDetailId(taskDetailVO.getId());
        task.setCreateUserId(param.getUserId()==null?null:param.getUserId());
        task.setPlatform("privacy detection");
        task.setTaskStarttime(new Date());
        task.setAssetsId(assetsId);
        task.setDetectionType(param.getDetectionType());
        // 非小程序类型，如果没有应用包数据，不用进行静态检测
        if (isEmptyFileApp(assets)) {
            task.setTaskTatus(DetectionStatusEnum.DETECTION_OVER);
        } else {
            task.setTaskTatus(DetectionStatusEnum.DETECTION_NOSTART);
        }
        task.setDynamicManualStatus(DynamicManualStatusEnum.DETECTION_MANUAL_WAITING);
        task.setDynamicAutoInit();
        task.setDynamicLawInit();
        task.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_NONE);
        task.setVersion(0L);
        if (param.getDynamicDeviceTypeEnum() == null) {
            param.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD);
        }
        task.setDynamicDeviceType(param.getDynamicDeviceTypeEnum());
        task.setTerminalType(assets.getTerminalType());
        task.setIsSafe(false);
        task.setIsDelete(0);
        //API接口请求1，kafka请求2,0页面请求
        if(param.getPageOrApiOrKafkaEnum()!=null && param.getPageOrApiOrKafkaEnum()== PageOrApiOrKafkaEnum.IS_API){
            task.setIsApi(1);
        }else if(param.getPageOrApiOrKafkaEnum()!=null && param.getPageOrApiOrKafkaEnum()==PageOrApiOrKafkaEnum.IS_KAFKA){
            task.setIsApi(2);
        }else{
            task.setIsApi(0);
        }
        task.setUpdateTime(new Date());
        task.setCreateTime(new Date());
        Integer maxStaticSort = taskMapper.getMaxStaticWaitSort(task.getTerminalType().getValue());
        task.setStaticTaskSort(maxStaticSort != null ? maxStaticSort + 1 : 1);
        Integer maxDynamicSort = taskMapper.getMaxDynamicWaitSort(task.getTerminalType().getValue());
        task.setDynamicTaskSort(maxDynamicSort != null ? maxDynamicSort + 1 : 1);

        if (task.getTerminalType() == TerminalTypeEnum.ANDROID
                && !ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status")
                && param.getPageOrApiOrKafkaEnum()!=null && param.getPageOrApiOrKafkaEnum()==PageOrApiOrKafkaEnum.IS_PAGE) {
            task.setDynamicStarttime(new Date());
        } else if (task.getTerminalType() == TerminalTypeEnum.IOS
                && !ijiamiCommonProperties.getBooleanValue("ijiami.ios.remote.tool.status")
                && param.getPageOrApiOrKafkaEnum()!=null && param.getPageOrApiOrKafkaEnum()==PageOrApiOrKafkaEnum.IS_PAGE) {
            task.setDynamicStarttime(new Date());
        }
        taskMapper.insertSelective(task);
        detectionConfigService.addUserTaskConsumption(task);

        String bussinessId = param.getBussinessId();
        if(StringUtils.isBlank(param.getBussinessId())) {
            bussinessId = task.getTaskId()+"";
        }
        String version = param.getVersion();
        
        //判断用户是否存在指定设备，如果存在指定设备就不给默认云手机版本
        Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
        boolean isAppoint = false;
        if(detectionConfigMap.get(param.getUserId()) != null && StringUtils.isNotBlank(detectionConfigMap.get(param.getUserId()).getAndroidDeviceIps())) {
        	isAppoint = true;
        }
        if(task.getTerminalType() == TerminalTypeEnum.ANDROID && param.getDynamicDeviceTypeEnum() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD){
            if(StringUtils.isBlank(version) && StringUtils.isBlank(param.getModel()) && !isAppoint){
                if(assets.getMinSdkVersion() == null || assets.getMinSdkVersion() < 28){
//                    version = "8.1.0";
                }else{
                    version = "10";
                }
            }
        }
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        if(extend == null) {
            TTaskExtendVO dto = new TTaskExtendVO(task.getTaskId(), bussinessId, null, param.getCallbackUrl());
            dto.setVersion(version);
            dto.setModel(param.getModel());
            dto.setDetectionLaws(param.getLaws());
            dto.setActionFilterGroupId(param.getActionFilterGroupId());
            dto.setCustomLawsGroupId(param.getCustomLawsGroupId());
            taskExtendService.save(dto);
        }
        TAssets updateAssets = new TAssets();
        updateAssets.setId(assets.getId());
        // 更新资产检测次数
        updateAssets.setDetectionCount(assets.getDetectionCount() == null ? 1 : (assets.getDetectionCount() + 1));
        updateAssets.setLastDetectionTime(task.getTaskStarttime());
        assetsMapper.updateByPrimaryKeySelective(updateAssets);
        // 资源是否静态检测过了
        checkAlreadyDetection(assets, task, taskDetailVO);
        taskSortThread.checkTaskSortListAsSoonASPossible();
        return task.getTaskId();
    }

    private void checkActionFilterGroup(Long groupId, Long userId, TerminalTypeEnum terminalTypeEnum) {
        if (Objects.nonNull(groupId) && groupId > 0) {
            if (tActionFilterGroupMapper.countGroup(groupId, userId, terminalTypeEnum) == 0) {
                throw new IjiamiRuntimeException("行为函数过滤集合id错误");
            }
        }
    }

    private void checkCustomLawsGroup(Long groupId, TerminalTypeEnum terminalTypeEnum) {
        if (Objects.nonNull(groupId) && groupId > 0) {
            if (customLawsGroupMapper.countGroup(groupId, terminalTypeEnum) == 0) {
                throw new IjiamiRuntimeException("自定义自动化法规集合id错误");
            }
        }
    }

    private void checkTaskStatus(Long userId, Long taskId, TerminalTypeEnum terminalTypeEnum, Integer detectionType) {
        // 任务如果已经占用，不用再往下检查
        if (taskManagerService.isTaskPreempted(terminalTypeEnum, userId, taskId)) {
            return;
        }
        // 小程序的深度检测任务添加时，需要做检查
        if (terminalTypeEnum.isApplet()
                && detectionType == TaskDetectionTypeEnum.DEPTH.getValue()) {
            int fastCount = taskMapper.countActiveTask(userId,
                    TaskDetectionTypeEnum.FAST.getValue(),
                    terminalTypeEnum.getValue());
            if (fastCount > 0) {
                LOG.info("小程序快速检测与深度检测不可同时运行 fastCount={}", fastCount);
                throw new IjiamiRuntimeException("小程序快速检测与深度检测不可同时运行，请将其他检测任务结束");
            }
            int depthCount = taskMapper.countActiveTask(userId,
                    TaskDetectionTypeEnum.DEPTH.getValue(),
                    terminalTypeEnum.getValue());
            if (depthCount > 0 || taskManagerService.taskPreemptedCount(terminalTypeEnum, userId) > 0) {
                LOG.info("小程序深度检测每次只可运行一个任务，请将其他检测任务结束 depthCount={}", depthCount);
                throw new IjiamiRuntimeException("小程序深度检测每次只可运行一个任务，请将其他检测任务结束");
            }
        }
    }

    /**
     * 是否已经检测过
     *
     * @param assets       资产信息
     * @param task         任务信息
     * @param taskDetailVO 任务详情
     */
    private void checkAlreadyDetection(TAssets assets, TTask task, TaskDetailVO taskDetailVO) {
        if (task.getTerminalType() != TerminalTypeEnum.ANDROID
                && task.getTerminalType() != TerminalTypeEnum.IOS
                && task.getTerminalType() != TerminalTypeEnum.HARMONY) {
            return;
        }
    	//校验是否已经检测过开关
    	String staticResultEnabled = commonProperties.getProperty("static.result.check.enabled");
    	if(StringUtils.isBlank(staticResultEnabled) || staticResultEnabled.equals("false")) {
    		return;
    	}
        // 应用包为空的不进行静态检测
        if (StringUtils.isBlank(assets.getShellIpaPath())) {
            return;
        }
        TerminalTypeEnum terminal = assets.getTerminalType();
        // 校验是否已经进行过静态检测
        DetectionResultVO detectionResult = xxlDetectWebServer.checkAlreadyDetection(terminal, assets.getMd5());
        if (detectionResult != null && !CollectionUtils.isEmpty(detectionResult.getResult())) {
            LOG.info("已有静态检测数据");
            if (StringUtils.isNotBlank(detectionResult.getDumpZipUrl())) {
                assetsMapper.updateDumpZipUrlById(
                        UriUtils.getHttpPath(detectionResult.getDumpZipUrl()),
                        detectionResult.getEncrypt() ? PackerStatusEnum.SHELLING.getValue() : PackerStatusEnum.SHELL_LESS.getValue(),
                        assets.getId());
                assets.setDumpZipUrl(detectionResult.getDumpZipUrl());
            }
            // 静态函数分析情况
            Integer analysisStatus = staticFunctionAnalysisService.getAnalysisStatus(task.getTaskId());
            // ios的静态检测后不进行静态函数分析，直接完成检测，因为要等到动态检测进行砸壳后才能进行静态函数分析。如果是android的静态函数分析已经完成，也直接完成检测
            if (assets.getTerminalType() == TerminalTypeEnum.ANDROID && analysisStatus != StaticFunctionAnalysisStatusEnum.SUCCESS.getValue()) {
                // 静态函数分析未执行或者失败的，要重新执行
                if (analysisStatus == StaticFunctionAnalysisStatusEnum.FAILURE.getValue() || analysisStatus == StaticFunctionAnalysisStatusEnum.NONE.getValue()) {
                	//先判断这个应用是否已经有完成过静态函数分析，如果没有就先不下发任务，避免进行中的检测过多
                	TStaticFunctionRecord history = tStaticFunctionRecordMapper.findSuccessByAssetsId(assets.getId());
                	if(history==null) {
                		return;
                	}
                    taskDAO.updateAndroidStaticFunctionAnalysis(task, XxlDetectAnalysisHelper.analysis(terminal, detectionResult.getApkDetectionEndTime(), detectionResult.getResult()));
                    // apk是检测过的，直接使用检测过的静态函数分析历史数据
                    staticFunctionAnalysisService.useAnalysisHistory(task, assets);
                } else if (analysisStatus == StaticFunctionAnalysisStatusEnum.PROCESSING.getValue()
                        || analysisStatus == StaticFunctionAnalysisStatusEnum.WAITING.getValue()) {
                    staticFunctionAnalysisService.refreshAnalysisStatus(task.getTaskId());
                }
            } else {
                taskDAO.updateStaticSuccess(task.getTaskId(), XxlDetectAnalysisHelper.analysis(terminal, detectionResult.getApkDetectionEndTime(), detectionResult.getResult()));
                sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_FINISH, "静态检测完成", task);
            }
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Throwable.class)
    public Long restartTask(Long taskId) throws IjiamiCommandException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new IjiamiCommandException("该任务不存在");
        }
        if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_OVER) {
            throw new IjiamiCommandException("该任务已经检测完成");
        }
        Map<String, Object> paramMap = new HashMap<>();
        TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            throw new IjiamiCommandException("检测详情不存在,重新检测失败！");
        }
        // 更新任务详情中的状态
        Update update = new Update();
        paramMap.put("_id", task.getApkDetectionDetailId());
        update.set("apk_detection_status", DetectionStatusEnum.DETECTION_NOSTART.getValue());
        update.set("progress", 0);
        update.set("apk_detection_starttime", new Date());
        update(paramMap, update);

        // 查询资产信息
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(taskId);
        updateTask.setTaskTatus(DetectionStatusEnum.DETECTION_NOSTART);
        Integer maxStaticSort = taskMapper.getMaxStaticWaitSort(task.getTerminalType().getValue());
        updateTask.setStaticTaskSort(maxStaticSort != null ? maxStaticSort + 1 : 1);
        updateTask.setTaskStarttime(new Date());
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 检查是否检测过
        checkAlreadyDetection(assets, task, taskDetailVO);
        // 下发任务
        taskSortThread.checkTaskSortListAsSoonASPossible();
        return task.getTaskId();
    }

    @Override
    public Integer restartDynamicTaskStage(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new IjiamiRuntimeException("该任务不存在");
        }
        return taskDataService.detectStage(task).getValue();
    }

    @Override
    public Long restartDynamicTask(Long taskId) throws IjiamiCommandException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new IjiamiCommandException("该任务不存在");
        }
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            throw new IjiamiCommandException("该任务已经检测完成");
        }
        checkTaskStatus(task.getCreateUserId(), task.getTaskId(), task.getTerminalType(), task.getDetectionType());

        //获取配置信息，以防止上次中断的任务因为配置不一致点击重新检测一直排队的情况,=true 云手机，=false 沙箱手机
        DynamicDeviceTypeEnum dynamicDeviceType;
        if(ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status")){
            dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
        }else{
            dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX;
        }
        
        if(task.getTerminalType() == TerminalTypeEnum.ANDROID) {
        	if(ijiamiCommonProperties.getBooleanValue("ijiami.remote.tool.status")){
                dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
            }else{
                dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX;
            }
        }
        
        if(task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET || task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
        	if(ijiamiCommonProperties.getBooleanValue("ijiami.applet.remote.tool.status")){
                dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
            }else{
                dynamicDeviceType = DynamicDeviceTypeEnum.DYNAMIC_DEVICE_SANDBOX;
            }
        }
        // 小程序和android，失败的任务重启是从上次失败的地方开始，重启任务时需要保留上一次的阶段数据和检测进度。
        boolean resetSubStatusAndProgress = task.getTerminalType() != TerminalTypeEnum.ANDROID
                && !task.getTerminalType().isApplet();
        taskDAO.updateDynamicAutoWaiting(task, StringUtils.EMPTY, dynamicDeviceType, resetSubStatusAndProgress);
        taskSortThread.checkTaskSortListAsSoonASPossible();
        return task.getTaskId();
    }

    @Override
    public boolean stopTask(Long taskId) throws IjiamiApplicationException {
        return stopTask(taskId, false);
    }

    @Override
    public boolean stopTask(Long taskId, boolean forceStop) throws IjiamiApplicationException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new IjiamiApplicationException("任务不存在");
        }
        // 更新静态任务状态为中断。除了支付宝小程序（需要释放手机）之外的静态任务都只是修改任务状态，不用消息到xxl-detection进行中断
        updateStaticTaskStopStatus(task);
        messageSendKit.stopXxlStaticDetectionTask(task);
        // 强制中断，直接更新任务状态
        if (forceStop) {
            updateDynamicTaskStopStatus(task);
        } else {
            // 只有快速检测中断动态任务，需要发消息去中断
            if (task.getDetectionType() == null || TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
                if (task.getTerminalType() == TerminalTypeEnum.IOS) {
                    // ios快速检测中的动态检测要发到idb终止
                    if (!stopIosDynamicTask(task)) {
                        return false;
                    }
                } else {
                    // 停止xxl-detection检测
                    messageSendKit.stopXxlDynamicDetectionTask(task);
                    // 强制终止操作，直接更新任务状态信息。非强制终止操作会等xxl-detection或idb中断消息传回再修改任务状态
                }
                updateDynamicTaskStopStatus(task);
            } else {
            	taskDAO.updateDynamicFailure(task, "手动中断");
            }
        }
        LOG.info("stopTask startCheckTaskSortList");
        taskSortThread.checkTaskSortListAsSoonASPossible();
        LOG.info("stopTask finish");
        return true;
    }

    /**
     * 终止ios动态任务的处理，如果是远程模式的快速检测，需要发消息给idb
     * @param task
     */
    private boolean stopIosDynamicTask(TTask task) {
        TAssets assets = assetsMapper.selectAssetByTaskId(task.getTaskId());
        if (assets == null) {
            return false;
        }
        boolean stop = task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA;
        if (stop) {
            LOG.info("发送idb终止 dynamicStatus={} detectionType={}", task.getDynamicStatus(), task.getDetectionType());
            CountDownLatch countDownLatch = new CountDownLatch(1);
            StopIdbDynamicDetectionCallback callback = new StopIdbDynamicDetectionCallback(countDownLatch);
            idbManagerService.stopIosDynamicDetection(
                    task.getTaskId(),
                    assets.getPakage(),
                    task.getDeviceSerial(),
                    IosDynamicDetectionSubCmdEnum.FAST,
                    callback);
            try {
                // 等待终止的任务返回
                if (countDownLatch.await(15, TimeUnit.SECONDS)) {
                    return callback.isSuccess;
                } else {
                    return false;
                }
            } catch (InterruptedException e) {
                e.getMessage();
                return false;
            }
        } else {
            LOG.info("任务状态 dynamicStatus={} detectionType={}", task.getDynamicStatus(), task.getDetectionType());
            return true;
        }
    }

    static class StopIdbDynamicDetectionCallback extends SampleIdbCallback<BaseIdbResponse> {

        private boolean isSuccess = false;

        private CountDownLatch countDownLatch;

        StopIdbDynamicDetectionCallback(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public void onSendFailure() {
            super.onSendFailure();
            // 发送失败，则是idb出现问题了。也认为是中断成功
            isSuccess = true;
            countDownLatch.countDown();
        }

        @Override
        public void onResponse(BaseIdbResponse baseIdbResponse) {
            isSuccess = baseIdbResponse.getIsSuccess();
            if (baseIdbResponse.getIsSuccess()) {
                LOG.info("终止任务成功");
            } else {
                LOG.info("终止任务失败 {}", baseIdbResponse.getErrorMsg());
            }
            countDownLatch.countDown();
        }

        public boolean isSuccess() {
            return isSuccess;
        }
    }

    private void updateStaticTaskStopStatus(TTask task) {
        if (task.getTaskTatus().getValue() == DetectionStatusEnum.DETECTION_NOSTART.getValue()
                || task.getTaskTatus().getValue() == DetectionStatusEnum.DETECTION_IN.getValue()) {
            taskDAO.updateStaticFailure(task, "手动中断");
            sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, "静态检测中断", task);
        }
    }

    /**
     * 更新mongodb文档状态、任务状态、资产分数
     *
     * @param task 资产信息
     */
    private void updateDynamicTaskStopStatus(TTask task) {
        if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_MOBILE_CONNECT
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_HIT
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA) {
            TStaticFunctionRecord record = staticFunctionRecordMapper.findByTaskId(task.getTaskId());
            if (record != null && (record.getStatus() == StaticFunctionAnalysisStatusEnum.WAITING.getValue()
                    || record.getStatus() == StaticFunctionAnalysisStatusEnum.PROCESSING.getValue())) {
                LOG.info("修改静态函数分析状态");
                staticFunctionRecordMapper.updateStatus(record.getId(), StaticFunctionAnalysisStatusEnum.FAILURE.getValue());
            }
            taskDAO.updateDynamicFailure(task, "手动中断");
            sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "快速检测中断", task);
        }
    }

    @Override
    public TaskVO findTaskByPage(TaskQuery taskQuery) {
        TaskVO taskVO = new TaskVO();
        List<TaskDetailVO> taskDetailVOList = new ArrayList<>();
        PageInfo<TaskDetailVO> pageInfoTask = new PageInfo<>();
        if (taskQuery.getPage() != null && taskQuery.getRows() != null) {
            PageHelper.startPage(taskQuery.getPage(), taskQuery.getRows());
        }
        List<TTask> taskList = taskMapper.findTaskByPage(taskQuery);
        PageInfo<TTask> pageInfo = new PageInfo<>(taskList);
        List<TTaskTypeCount> countList = taskMapper.taskTypeCount(taskQuery);
        taskVO.setDeepDetectionCount(countList.stream().filter(Objects::nonNull).mapToInt(TTaskTypeCount::getDeepTaskCount).filter(Objects::nonNull).sum());
        taskVO.setFastDetectionCount(countList.stream().filter(Objects::nonNull).mapToInt(TTaskTypeCount::getFastTaskCount).filter(Objects::nonNull).sum());
        taskVO.setAiDetectionCount(countList.stream().filter(Objects::nonNull).mapToInt(TTaskTypeCount::getAiTaskCount).filter(Objects::nonNull).sum());
        // 移除掉数据范围的数据，后面的sql查询不再让数据范围工具进行重组，避免报错
        DataHelper.remove();
        if (pageInfo.getList().isEmpty()) {
            return taskVO;
        }
        Example queryAssets = new Example(TAssets.class);
        queryAssets.createCriteria().andIn("id", taskList.stream().map(TTask::getAssetsId).collect(Collectors.toList()));
        List<TAssets> assetsList = assetsMapper.selectByExample(queryAssets);
        List<TaskDetailVO> queryTaskDetailVOList = commonMongodbService.findDetectionResultListExcludeField(
                taskList.stream().map(TTask::getApkDetectionDetailId).collect(Collectors.toList()), fields);
        Set<Long> taskIdList = taskList.stream().map(TTask::getTaskId).collect(Collectors.toSet());

        Example queryUser = new Example(User.class);
        queryUser.createCriteria().andIn("userId", taskList.stream().map(TTask::getCreateUserId).collect(Collectors.toSet()));
        List<User> userList = userMapper.selectByExample(queryUser);
        List<TTaskExtendVO> extendVOList = taskExtendMapper.findTaskByTaskIds(taskIdList);
        List<TIosDeviceConnection> connectionList = iosDeviceConnectionMapper.findByTaskIds(taskIdList);
        // 获取法律法规数量
        int lawNumber = tPrivacyPolicyTypeMapper.countLawNumber(taskQuery.getTerminalTypeEnum().getValue());
        LOG.info("findTaskByPage start");
        for (TTask tTask : taskList) {
            Optional<TaskDetailVO> taskDetailVOOpt = queryTaskDetailVOList.stream()
                    .filter(detailVO -> detailVO.getId().equals(tTask.getApkDetectionDetailId())).findFirst();
            if (!taskDetailVOOpt.isPresent()) {
                continue;
            }
            TaskDetailVO taskDetailVO = taskDetailVOOpt.get();
            if (Objects.isNull(taskDetailVO.getAssets_id())) {
                continue;
            }
            Optional<TAssets> assetsOpt = assetsList.stream()
                    .filter(assets1 -> assets1.getId().equals(taskDetailVO.getAssets_id())).findFirst();
            if (!assetsOpt.isPresent()) {
                continue;
            }
            TAssets assets = assetsOpt.get();
            if ((tTask.getTerminalType() == TerminalTypeEnum.ANDROID
                    || tTask.getTerminalType().isApplet()) &&
                    tTask.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD &&
                    StringUtils.isNotBlank(tTask.getDeviceSerial())) {
                taskDetailVO.setIframe_url(StfUtils.getDeviceIframeUrl(tTask, commonProperties, tTask.getDeviceSerial()));
            }
            taskDetailVO.setTaskId(tTask.getTaskId());
            taskDetailVO.setThreadId(tTask.getThreadId());
            taskDetailVO.setUploadTime(assets.getCreateTime());
            taskDetailVO.setTerminalType(assets.getTerminalType());
            taskDetailVO.setAppId(assets.getAppId());
            taskDetailVO.setDynamic_device_type(tTask.getDynamicDeviceType());
            if (taskPrivacyInfo) {
                taskDetailVO.setHavePrivacyPolicy(Objects.nonNull(privacyPolicyResultMapper.findTaskIdByPolicyResult(tTask.getTaskId())));
                taskDetailVO.setGrantActionNougatCount(privacyActionNougatMapper.countActionNougatByTaskIdAndStage(tTask.getTaskId(), BehaviorStageEnum.BEHAVIOR_GRANT.getValue()));
            }
            Optional<User> userOpt = userList.stream().filter(u -> tTask.getCreateUserId().equals(u.getUserId())).findFirst();
            userOpt.ifPresent(user -> taskDetailVO.setUserName(user.getRealName()));
            //查询法律法规
            List<TPrivacyPolicyType> types = privacyCheckService.countLaw(tTask.getTaskId(),tTask.getTerminalType().getValue());
            if (CollectionUtils.isEmpty(types)) {
                taskDetailVO.setLawType("");
            } else {
                List<String> laws = types.stream().map(t -> {
                    if (StringUtils.isBlank(t.getLawName())) {
                        return "";
                    }
                    return t.getLawName();
                }).collect(Collectors.toList());
                taskDetailVO.setLawType(String.join("，", laws));
            }
            taskDetailVO.setLawDetectComplete(taskDetailVO.getLawType().split("，").length == lawNumber);
            taskDetailVO.setShellIpaPath(assets.getAnalysisApkUrl(fastDFSIp));
            taskDetailVO.setApk_detection_status(tTask.getTaskTatus() == null ? 1 : tTask.getTaskTatus().getValue());
            if (tTask.getDynamicStatus() != null) {
                taskDetailVO.setDynamic_detection_status(tTask.getDynamicStatus().getValue());
            }
            if (tTask.getDynamicSubStatus() != null) {
                taskDetailVO.setDynamic_detection_sub_status(tTask.getDynamicSubStatus().getValue());
            }
            if (tTask.getTaskTatus() != null) {
                taskDetailVO.setApk_detection_status(tTask.getTaskTatus().getValue());
            }
            if (tTask.getDynamicLawStatus() != null) {
                taskDetailVO.setDynamic_law_detection_status(tTask.getDynamicLawStatus().getValue());
            }
            if (tTask.getDynamicLawSubStatus() != null) {
                taskDetailVO.setDynamic_law_detection_sub_status(tTask.getDynamicLawSubStatus().getValue());
            }
            taskDetailVO.setIsHavePacker(assets.getIsHavePacker());
            taskDetailVO.setDynamic_manual_detection_status(tTask.getDynamicManualStatus() == null ? null : tTask.getDynamicManualStatus().getValue());
            if (StringUtils.isBlank(taskDetailVO.getDynamic_detection_description())) {
                taskDetailVO.setDynamic_detection_description(tTask.getDescription());
            }

            if (assets.getLogo() != null && !assets.getLogo().contains("http") && assets.getLogo().contains("group")) {
                taskDetailVO.setApk_logo(commonProperties.getProperty("detection.result.url.prefix") + assets.getLogo());
            }
            Optional<TTaskExtendVO> extendOpt = extendVOList.stream().filter(e -> e.getTaskId().equals(tTask.getTaskId())).findFirst();
            if (extendOpt.isPresent()) {
            	taskDetailVO.setVersion(extendOpt.get().getVersion());
            	taskDetailVO.setModel(extendOpt.get().getModel());
            	taskDetailVO.setDetectionLaws(extendOpt.get().getDetectionLaws());
                taskDetailVO.setCustomLawsGroupId(extendOpt.get().getCustomLawsGroupId());
                taskDetailVO.setActionFilterGroupId(extendOpt.get().getActionFilterGroupId());
            }
            if (StringUtils.isNotEmpty(assets.getObbDataPath())) {
                taskDetailVO.setObbDataPath(fastDFSIp + "/" + assets.getObbDataPath());
                taskDetailVO.setObbDevicePath(assets.getObbDevicePath());
            }
            if (StringUtils.isNotEmpty(assets.getQrcodePath())) {
                taskDetailVO.setQrcodePath(fastDFSIp + "/" + assets.getQrcodePath());
            }
            taskDetailVO.setShareUrl(assets.getShareUrl());
            Optional<TIosDeviceConnection> connectionOpt = connectionList.stream().filter(c -> c.getTaskId().equals(tTask.getTaskId())).findFirst();
            TaskDetailHelper.setTaskDetailVoManualTaskExt(taskDetailVO, tTask, connectionOpt);
            taskDetailVOList.add(taskDetailVO);
        }
        BeanUtils.copyProperties(pageInfo, pageInfoTask);
        pageInfoTask.setList(taskDetailVOList);
        taskVO.setPageInfo(pageInfoTask);
        LOG.info("findTaskByPage end");
        return taskVO;
    }

    private String getPlatformHost() {
        if (StringUtils.isNotBlank(uploadUrl)) {
            try {
                return CommonUtil.getHost(uploadUrl);
            } catch (MalformedURLException e) {
                return "";
            }
        }
        return "";
    }

    private Integer findTaskSort(Map<Long, Integer> taskSortMap, Long taskId) {
        // 空数据返回排在第一位
        if (taskSortMap.isEmpty()) {
            return 1;
        }
        Integer sort = taskSortMap.get(taskId);
        if (sort == null) {
            // 如果找不到排队序列数据，则返回列表里最大的值之后
            return Collections.max(taskSortMap.values()) + 1;
        }
        return sort;
    }

    private Map<Long, Integer> getTaskSortMap(List<TTask> taskList) {
        Map<Long, Integer> taskSerialNumberMap = new HashMap<>();
        List<TTask> sortedList = taskList.stream().sorted(taskSortComparator()).collect(Collectors.toList());
        for (int index=0; index<sortedList.size(); index++) {
            taskSerialNumberMap.put(sortedList.get(index).getTaskId(), index + 1);
        }
        return taskSerialNumberMap;
    }

    private Comparator<TTask> taskSortComparator() {
        return (o1, o2) -> {
            int sort = getSafeSort(o1.getDynamicTaskSort()) - getSafeSort(o2.getDynamicTaskSort());
            return sort == 0 ? (int)(o1.getTaskId() - o2.getTaskId()) : sort;
        };
    }

    private Integer getSafeSort(Integer sort) {
        return sort == null ? Integer.MAX_VALUE : sort;
    }

    @Override
    public List<TTask> findDetectedTasks(Long userId) {
        Example example = new Example(TTask.class);
        Example.Criteria criteria = example.createCriteria();
        Optional.of(userId).ifPresent(userId1 -> criteria.andEqualTo("createUserId", userId1));
        criteria.andEqualTo("taskTatus", DetectionStatusEnum.DETECTION_OVER);
        List<TTask> tasks = taskMapper.selectByExample(example);
        for (TTask task : tasks) {
            TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId());
            Optional.of(taskDetailVO).ifPresent(t -> task.setMd5(t.getMd5()));
            task.setMd5(taskDetailVO.getMd5());
        }
        return tasks;
    }

    @Override
    public List<String> findDetectedTasksByTime(String beginTime, String endTime) {
        Date begin = null, end = null;
        if (StringUtils.isNotEmpty(beginTime)) {
            begin = new Date(Long.valueOf(beginTime));
        }
        if (StringUtils.isNotEmpty(endTime)) {
            end = new Date(Long.valueOf(endTime));
        }
        return taskMapper.selectDocumentIdByTime(begin, end);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void startIosDynamicDetect(TaskProgressQuery taskProgressQuery) throws IjiamiCommandException {
        if (taskProgressQuery.getTaskId() == null) {
            throw new IjiamiRuntimeException("任务id不能为空");
        }
        TTask tTask = taskMapper.selectByPrimaryKey(taskProgressQuery.getTaskId());
        if (tTask == null) {
            throw new IjiamiRuntimeException("任务不存在");
        }
        //查询是否有正在动态检测或者法规检测的任务
        if (taskMapper.selectDynamicTaskByUserId(taskProgressQuery.getUserId(), tTask.getTerminalType().getValue(), TaskDetectionTypeEnum.DEPTH.getValue()) > 0) {
            throw new IjiamiRuntimeException("启动失败,已经有一个动态检测正在进行中");
        }
        TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            throw new IjiamiCommandException("检测详情不存在,更新操作失败！");
        }
        dynamicTaskContextService.createTaskContext(tTask, StringUtils.EMPTY);
        if (taskProgressQuery.getType() == 1) {
            if (tTask.getDynamicStatus().getValue() != DynamicAutoStatusEnum.DETECTION_AUTO_WAITING.getValue() || tTask.getDynamicStatus().getValue() != DynamicAutoStatusEnum.DETECTION_AUTO_FAILED.getValue()) {
                throw new IjiamiCommandException("该任务动态检测已经检测完成或者正在检测中");
            }
            //开始动态检测
            taskDAO.updateDynamicAutoIn(tTask.getTaskId(), StringUtils.EMPTY, tTask.getDynamicDeviceType());
        } else {
            if (tTask.getDynamicLawStatus().getValue() != DynamicLawStatusEnum.DETECTION_LAW_WAITING.getValue() || tTask.getDynamicLawStatus().getValue() != DynamicLawStatusEnum.DETECTION_LAW_FAILED.getValue()) {
                throw new IjiamiCommandException("该任务法规检测已经检测完成或者正在检测中");
            }
            taskDAO.updateLawDownloadIPA(tTask);
        }
    }

    @Override
    public void stopDynamicByIdbCallback(Long taskId, String deviceId, String reason, String frpcPort) {
        LOG.info("idb终止任务 taskId={} deviceId={} reason={}", taskId, deviceId, reason);
        TTask tTask = taskMapper.selectByPrimaryKey(taskId);
        if (tTask == null) {
            LOG.info("任务不存在");
            return;
        }
        if (!tTask.isDynamicIn() && !tTask.isLawIn()) {
            LOG.info("任务状态错误 dynamicStatus={} dynamicLawStatus={}", tTask.getDynamicStatus(), tTask.getDynamicLawStatus());
            return;
        }
        TIosDeviceConnection iosDeviceConnection = iosDeviceConnectionMapper.findByTaskId(taskId);
        if (StringUtils.isNotBlank(frpcPort) && iosDeviceConnection != null) {
            if (!frpcPort.equals(iosDeviceConnection.getFrpcPort())) {
                LOG.info("任务端口错误 不进行终止任务 currentFrpcPort={} frpcPort={}", iosDeviceConnection.getFrpcPort(), frpcPort);
                return;
            }
        }
        TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            LOG.info("检测详情不存在,更新操作失败！");
            return;
        }
        if (tTask.isDynamicIn()) {
            taskDAO.updateDynamicFailure(tTask, reason);
        } else {
            taskDAO.updateLawFailure(tTask, reason);
        }
        sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, "idb中断", tTask);
    }

    @Override
    public boolean stopIosDynamicDetect(TaskProgressQuery taskProgressQuery) throws IjiamiCommandException {
        if (taskProgressQuery.getTaskId() == null) {
            throw new IjiamiRuntimeException("任务id不能为空");
        }
        TTask tTask = taskMapper.selectByPrimaryKey(taskProgressQuery.getTaskId());
        if (tTask == null) {
            throw new IjiamiRuntimeException("任务不存在");
        }
        TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            throw new IjiamiCommandException("检测详情不存在,更新操作失败！");
        }
        if (taskProgressQuery.getType() == TaskProgressTypeEnum.DYNAMIC.getValue()) {
            //结束动态检测
            if (taskProgressQuery.getStatus() == TaskProgressStatusEnum.INTERRUPT.getValue()) {
                taskDAO.updateDynamicFailure(tTask, "手动中断");
            } else {
                Update update = new Update();
                TTask updateTask = new TTask();
                updateTask.setTaskId(tTask.getTaskId());
                update.set("dynamic_detection_description", "静态函数分析中");
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE);
                update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE.getValue());
                taskDAO.updateTaskStatus(tTask, updateTask, update);
                TAssets assets = assetsMapper.selectByPrimaryKey(tTask.getAssetsId());
                staticFunctionAnalysisService.startAnalysis(tTask, assets);
            }
        } else {
            if (taskProgressQuery.getStatus() == TaskProgressStatusEnum.INTERRUPT.getValue()) {
                taskDAO.updateLawFailure(tTask, "手动中断");
            } else {
                taskDAO.updateLawSuccess(tTask.getTaskId());
            }
        }
        return true;
    }

    private String uploadDataFileToFastDfs(String dataPath) {
        try {
            FileVO uploadFile = FileVOUtils.convertFileVOByFile(new File(dataPath));
            FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
            String shellIpaPath = fastDfsFile.getFilePath();
            if (StringUtils.isBlank(shellIpaPath)) {
                LOG.error("检测数据上传文件失败 shellIpaPath为空");
            }
            return shellIpaPath;
        } catch (IOException | IjiamiApplicationException e) {
            LOG.error("检测数据上传文件失败", e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delPersonalWord(Integer type, Long id) throws IjiamiCommandException {
        if (type == null) {
            throw new IjiamiCommandException("type类型不能为空!");
        }
        if (id == null) {
            throw new IjiamiCommandException("id不能为空!");
        }
        if (type == 1) {
            tPrivacySensitiveWordMapper.deleteByPrimaryKey(id);
        } else {
            tPrivacySharedPrefsMapper.deleteByPrimaryKey(id);
        }
    }

    @Override
    public void refreshTaskSort(TTask task) {
        // 快速检测删除修改排序字段
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            // 静态检测
            if (task.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART &&
                    task.getStaticTaskSort() != null) {
                taskMapper.updateStaticTaskSort(task.getStaticTaskSort());
            }
            // 动态检测
            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING &&
                    task.getDynamicTaskSort() != null) {
                taskMapper.updateDynamicTaskSort(task.getDynamicTaskSort());
            }
        }
    }

    @Override
    public void changeTaskStatus(TaskChangeStatusDTO taskChangeStatusDTO) {
        // 校验任务是否存在
        TTask tTask = taskMapper.selectByPrimaryKey(taskChangeStatusDTO.getTaskId());
        if (Objects.isNull(tTask)) {
            return;
        }
        Optional<TaskChangeStatusDTO> taskOptional = Optional.ofNullable(taskChangeStatusDTO);
        // 获取任务状态
        Boolean status = taskOptional.map(TaskChangeStatusDTO::getStatus).orElse(false);
        // 获取任务类型：快速、深度
        Integer taskType = taskOptional.map(TaskChangeStatusDTO::getType).orElse(0);
        // 任务任务状态转为启动的时候，需要校验任务是否已经执行成功，再更新为检测中
        if (status && taskType == 2) {
        	
        	if(taskChangeStatusDTO.getUserId() != null) {
        		// 5.1-3深度检测-> 判断指定设备 存储用户的taskId
            	ConstantsUtils.deepDetectionStartTemp.put(taskChangeStatusDTO.getUserId(), taskChangeStatusDTO.getTaskId());
        	}
        	
            // 法规检测完成，直接放弃更新
            if (tTask.getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_SUCCEED) {
                return;
            }
        }
        // 动态检测
        if (taskType.equals(TaskChangeType.FAST_OR_DEEP.itemValue())) {
            if (taskChangeStatusDTO.getStatus()) {
                //重新启动深度检测任务就去清理下android的摇一摇数据
                if (tTask.getTerminalType() == TerminalTypeEnum.ANDROID && tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                    dynamicTaskContextService.deleteShakeValues(tTask.getTaskId());
                }
                if (tTask.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()) {
                    dynamicTaskContextService.removeTaskContext(tTask);
                    dynamicTaskContextService.cleanDynamicAction(tTask.getTaskId());
                }
                // 小程序和android，失败的任务重启是从上次失败的地方开始，重启任务时需要保留上一次的阶段数据和检测进度。
                boolean resetSubStatusAndProgress = tTask.getTerminalType() != TerminalTypeEnum.ANDROID
                        && !tTask.getTerminalType().isApplet() && tTask.getTerminalType() != TerminalTypeEnum.HARMONY;
                taskDAO.updateDynamicAutoWaiting(tTask, taskChangeStatusDTO.getDeviceSerial(), resetSubStatusAndProgress);
            } else {
                taskDAO.updateDynamicFailure(tTask, "手动中断");
                sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "手动中断", tTask);
            }
        }
        else if (taskType.equals(TaskChangeType.REVIEW.itemValue())) {
            if (taskChangeStatusDTO.getStatus()) {
                taskDAO.updateReviewWaiting(tTask, taskChangeStatusDTO.getDeviceSerial());
            } else {
                taskDAO.updateReviewFailure(tTask, "手动中断");
            }
        }
        // 法规检测
        else {
            if (taskChangeStatusDTO.getStatus()) {
                taskDAO.updateDynamicLawWaiting(tTask, taskChangeStatusDTO.getDeviceSerial());
            } else {
                taskDAO.updateLawFailure(tTask, "手动中断");
                sendMessageServiceImpl.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, "手动中断", tTask);
            }
        }
    }

    @Override
    public TTask findByDocumentId(String documentId) {
        Example example = new Example(TTask.class);
        example.createCriteria().andEqualTo("apkDetectionDetailId", documentId);
        return taskMapper.selectOneByExample(example);
    }

    @Override
    public TTask findById(Long taskId) {
        return taskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public TaskDetailVO getTaskDetail(String id) {
        TaskDetailVO taskDetailVO = findById(id, "taskDetailVO");
        if (taskDetailVO != null) {
            TTask task = taskMapper.findByDocumentId(id);
            taskDetailVO.setTaskId(task.getTaskId());
            // 查询模板信息
            TDetectionTemplate template = detectionTemplateServiceImpl.findById(Long.parseLong(String.valueOf(taskDetailVO.getTemplate_id())));
            if (Objects.nonNull(template)) {
                taskDetailVO.setTemplateName(template.getTemplateName());
            } else {
                taskDetailVO.setTemplateName(StringUtils.EMPTY);
            }
            if (taskDetailVO.getLawType() == null) {
                //查询法律法规
                List<TPrivacyPolicyType> types = privacyCheckService.countLaw(task.getTaskId(),task.getTerminalType().getValue());
                if (CollectionUtils.isEmpty(types)) {
                    taskDetailVO.setLawType("");
                } else {
                    List<String> laws = types.stream().map(t -> {
                        if (StringUtils.isBlank(t.getLawName())) {
                            return "";
                        }
                        return t.getLawName();
                    }).collect(Collectors.toList());
                    taskDetailVO.setLawType(String.join("，", laws));
                }
            }
            // 获取法律法规数量
            int lawNumber = tPrivacyPolicyTypeMapper.countLawNumber(task.getTerminalType().getValue());
            taskDetailVO.setLawDetectComplete(taskDetailVO.getLawType().split("，").length == lawNumber);
            TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
            if (assets != null) {
//                taskDetailVO.setDumpFilePath(StringUtils.isNotBlank(assets.getFastDFSDumpZipUrl(fastDFSIp)) ? downloadDumpUrlPrefix + assets.getId() : null);
//                taskDetailVO.setAppFilePath(StringUtils.isNotBlank(assets.getShellIpaPath()) ? downloadAppUrlPrefix + assets.getId() : null);
                taskDetailVO.setDumpFilePath(assets.getFastDFSDumpZipUrl(fastDFSIp));
                taskDetailVO.setAppFilePath(assets.getAppUrl(fastDFSIp));
                taskDetailVO.setApk_name(assets.getName());
            }
            taskDetailVO.setApk_detection_status(task.getTaskTatus() == null ? 1 : task.getTaskTatus().getValue());
            taskDetailVO.setDynamic_detection_status(task.getDynamicStatus() == null ? null : task.getDynamicStatus().getValue());
            taskDetailVO.setDynamic_law_detection_status(task.getDynamicLawStatus() == null ? null : task.getDynamicLawStatus().getValue());
            taskDetailVO.setDynamic_manual_detection_status(task.getDynamicManualStatus() == null ? null : task.getDynamicManualStatus().getValue());
            taskDetailVO.setDynamic_detection_sub_status(task.getDynamicSubStatus() == null ? null : task.getDynamicSubStatus().getValue());
            taskDetailVO.setDynamic_law_detection_sub_status(task.getDynamicLawSubStatus() == null ? null : task.getDynamicLawSubStatus().getValue());
        }
        return taskDetailVO;
    }
    
    @Override
    public TaskDetailVO getTaskDetailByTaskId(Long taskId) {
    	TTask tTask = taskMapper.selectByPrimaryKey(taskId);
        TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
        if(taskDetailVO != null) {
        	TAssets assets = assetsMapper.selectByPrimaryKey(tTask.getAssetsId());
            if ((tTask.getTerminalType() == TerminalTypeEnum.ANDROID
                    || tTask.getTerminalType().isApplet()) &&
                    tTask.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD &&
                    StringUtils.isNotBlank(tTask.getDeviceSerial())) {
                taskDetailVO.setIframe_url(StfUtils.getDeviceIframeUrl(tTask, commonProperties, tTask.getDeviceSerial()));
            }
            taskDetailVO.setTaskId(tTask.getTaskId());
            taskDetailVO.setThreadId(tTask.getThreadId());
            taskDetailVO.setUploadTime(assets.getCreateTime());
            taskDetailVO.setTerminalType(assets.getTerminalType());
            taskDetailVO.setAppId(assets.getAppId());
            taskDetailVO.setDynamic_device_type(tTask.getDynamicDeviceType());
            if (taskPrivacyInfo) {
                taskDetailVO.setHavePrivacyPolicy(Objects.nonNull(privacyPolicyResultMapper.findTaskIdByPolicyResult(tTask.getTaskId())));
                taskDetailVO.setGrantActionNougatCount(privacyActionNougatMapper.countActionNougatByTaskIdAndStage(tTask.getTaskId(), BehaviorStageEnum.BEHAVIOR_GRANT.getValue()));
            }
            taskDetailVO.setShellIpaPath(assets.getAnalysisApkUrl(fastDFSIp));
            taskDetailVO.setApk_detection_status(tTask.getTaskTatus() == null ? 1 : tTask.getTaskTatus().getValue());
            if (tTask.getDynamicStatus() != null) {
                taskDetailVO.setDynamic_detection_status(tTask.getDynamicStatus().getValue());
            }
            if (tTask.getDynamicSubStatus() != null) {
                taskDetailVO.setDynamic_detection_sub_status(tTask.getDynamicSubStatus().getValue());
            }
            if (tTask.getTaskTatus() != null) {
                taskDetailVO.setApk_detection_status(tTask.getTaskTatus().getValue());
            }
            if (tTask.getDynamicLawStatus() != null) {
                taskDetailVO.setDynamic_law_detection_status(tTask.getDynamicLawStatus().getValue());
            }
            if (tTask.getDynamicLawSubStatus() != null) {
                taskDetailVO.setDynamic_law_detection_sub_status(tTask.getDynamicLawSubStatus().getValue());
            }
            taskDetailVO.setIsHavePacker(assets.getIsHavePacker());
            taskDetailVO.setDynamic_manual_detection_status(tTask.getDynamicManualStatus() == null ? null : tTask.getDynamicManualStatus().getValue());
            if (StringUtils.isBlank(taskDetailVO.getDynamic_detection_description())) {
                taskDetailVO.setDynamic_detection_description(tTask.getDescription());
            }

            if (assets.getLogo() != null && !assets.getLogo().contains("http") && assets.getLogo().contains("group")) {
                taskDetailVO.setApk_logo(commonProperties.getProperty("detection.result.url.prefix") + assets.getLogo());
            }
            TTaskExtendVO extendOpt = taskExtendMapper.findTaskByTaskId(taskId);
            if (extendOpt != null) {
            	taskDetailVO.setVersion(extendOpt.getVersion());
            	taskDetailVO.setModel(extendOpt.getModel());
            	taskDetailVO.setDetectionLaws(extendOpt.getDetectionLaws());
                taskDetailVO.setCustomLawsGroupId(extendOpt.getCustomLawsGroupId());
                taskDetailVO.setActionFilterGroupId(extendOpt.getActionFilterGroupId());
            }
            if (StringUtils.isNotEmpty(assets.getObbDataPath())) {
                taskDetailVO.setObbDataPath(fastDFSIp + "/" + assets.getObbDataPath());
                taskDetailVO.setObbDevicePath(assets.getObbDevicePath());
            }
            if (StringUtils.isNotEmpty(assets.getQrcodePath())) {
                taskDetailVO.setQrcodePath(fastDFSIp + "/" + assets.getQrcodePath());
            }
            taskDetailVO.setShareUrl(assets.getShareUrl());
        }
        
        return taskDetailVO;
    }

    /**
     * 拼接检测项数据
     *
     * @param detectionItemList
     * @param sensitiveWordList
     * @param userPermissionVOList
     * @param actionVOList
     * @param sdkList
     * @return
     */
    public List<ItemDataModel> buildDataModelList(List<DetectionItemVO> detectionItemList,
                                                  List<TSensitiveWord> sensitiveWordList, List<UserPermissionVO> userPermissionVOList,
                                                  List<ActionVO> actionVOList, List<UserPermissionVO> allPermissionVOlist, List<TSdkLibrary> sdkList) {
        List<ItemDataModel> detectionItemVOList = new ArrayList<ItemDataModel>();
        // 已经循环过的检测项id集合
//        List<Long> ids = new ArrayList<Long>();
        for (DetectionItemVO detectionItemVO : detectionItemList) {
            ItemDataModel detectionItemDataModel = new ItemDataModel();
            List<ContentDataModel> keyWords = new ArrayList<ContentDataModel>();
            // 检测项拼接

            detectionItemDataModel
                    .setGrade(detectionItemVO.getGrade() == null ? 0 : detectionItemVO.getGrade().getValue());
            detectionItemDataModel.setId(detectionItemVO.getItemNo());
            detectionItemDataModel.setName(detectionItemVO.getName());
            detectionItemDataModel.setTypeId(String.valueOf(detectionItemVO.getTypeId()));
            // 敏感权限项
            if (detectionItemVO.getItemNo().equals("0202")) {
                // 拼接敏感权限
                for (TSensitiveWord sensitiveWord : sensitiveWordList) {
                    ContentDataModel detectionDataModel = new ContentDataModel();
                    detectionDataModel.setKey(sensitiveWord.getName());
                    // 敏感词分类名称
                    TSensitiveType sensitiveType = sensitiveTypeMapper.selectByPrimaryKey(sensitiveWord.getTypeId());
                    detectionDataModel.setDescription(sensitiveType.getTypeName());
                    keyWords.add(detectionDataModel);
                }
            }

            // 权限滥用
            if (detectionItemVO.getItemNo().equals("0204")) {
                // 拼接权限字典
                for (UserPermissionVO permissionVO : allPermissionVOlist) {
                    ContentDataModel detectionDataModel = new ContentDataModel();
                    detectionDataModel.setKey(permissionVO.getSignaTure());
                    detectionDataModel.setDescription(permissionVO.getName());
                    keyWords.add(detectionDataModel);
                }
            }

            // 越权行为
            if (detectionItemVO.getItemNo().equals("0203")) {
                // 拼接用户权限
                for (UserPermissionVO userPermissionVO : userPermissionVOList) {
                    ContentDataModel detectionDataModel = new ContentDataModel();
                    detectionDataModel.setKey(userPermissionVO.getSignaTure());
                    detectionDataModel.setDescription(userPermissionVO.getName());
                    keyWords.add(detectionDataModel);
                }
            }
            // 第三方sdk检测
            if (detectionItemVO.getItemNo().equals("0104")) {
                for (TSdkLibrary sdk : sdkList) {
                    if (CollectionUtils.isEmpty(sdk.getPackageList())) {
                        continue;
                    }
                    for (TSdkLibraryPackage p:sdk.getPackageList()) {
                        if (StringUtils.isNotBlank(p.getPackageName())) {
                            ContentDataModel detectionDataModel = new ContentDataModel();
                            detectionDataModel.setKey(p.getPackageName());
                            String descript = p.getPackageName();
                            detectionDataModel.setDescription(descript);
                            keyWords.add(detectionDataModel);
                        }
                    }
                }
            }
            detectionItemDataModel.setKeyWords(keyWords);
            detectionItemVOList.add(detectionItemDataModel);
        }
        return detectionItemVOList;
    }

    /**
     * 检测详情VO数据拼接
     *
     * @param assets
     * @return
     */
    public TaskDetailVO buildTaskDetailVO(TAssets assets) {
        TaskDetailVO taskDetailVO = new TaskDetailVO();
        taskDetailVO.setApk_name(assets.getName());
        taskDetailVO.setApk_package(assets.getPakage());
        taskDetailVO.setApk_logo(assets.getLogo());
        taskDetailVO.setMd5(assets.getMd5());
        taskDetailVO.setSign_md5(assets.getSignMd5());
        taskDetailVO.setApk_size(assets.getSize());
        taskDetailVO.setApk_version(assets.getVersion());
        taskDetailVO.setTerminal_type(assets.getTerminalType().getValue());
        taskDetailVO.setApk_file_address(assets.getAddress());
        taskDetailVO.setCreate_time(new Date());
        taskDetailVO.setApk_type(Integer.parseInt(assets.getCategory().toString()));
        // 检测引擎变更，去掉终端判断
        taskDetailVO.setApk_detection_status(DetectionStatusEnum.DETECTION_NOSTART.getValue());
        taskDetailVO.setDescribe("待静态检测");

        taskDetailVO.setIsSafe(false);
        taskDetailVO.setAssets_id(assets.getId());
        taskDetailVO.setApk_detection_starttime(new Date());
        if (isEmptyFileApp(assets)) {
            setEmptyStaticDetectionResult(taskDetailVO, assets);
        }
        return taskDetailVO;
    }

    /**
     * 是否空文件的app，无法进行静态解析需要特殊处理静态解析内容
     * @param assets
     * @return
     */
    private boolean isEmptyFileApp(TAssets assets) {
        return !assets.getTerminalType().isApplet() && StringUtils.isBlank(assets.getShellIpaPath());
    }

    private void setEmptyStaticDetectionResult(TaskDetailVO taskDetailVO, TAssets assets) {
        if (assets.getTerminalType() == TerminalTypeEnum.HARMONY) {
            DetectionItem<HarmonyBaseResultContentDTO> baseInfoItem = new DetectionItem<>();
            baseInfoItem.setOverTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()));
            baseInfoItem.setDetectionItemId(DetectionItemIdEnum.BASE.getValue());
            baseInfoItem.setDetectionItemName(DetectionItemIdEnum.BASE.getName());
            baseInfoItem.setStatus(DetectionItemStatusEnum.SAFE.getValue());
            HarmonyBaseResultContentDTO resultContent = new HarmonyBaseResultContentDTO();
            resultContent.setBundleName(assets.getPakage());
            baseInfoItem.setResultContent(resultContent);
            List<String> detectionResult = new ArrayList<>();
            detectionResult.add(CommonUtil.beanToJson(baseInfoItem));
            taskDetailVO.setDetection_result(detectionResult);
        }
    }

    @Override
    public BaseMessageVO getBaseMessage(String documentId) {
        LOG.info("getBaseMessage start");
        BaseMessageVO baseMessageVO = new BaseMessageVO();
        TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(documentId);
        if (taskDetailVO == null) {
            return null;
        }
        if (taskDetailVO.getDetection_result() == null) {
            return null;
        }
        LOG.info("getBaseMessage 2");
        String str = taskDetailVO.getDetection_result().toString();
        JSONArray array = JSONArray.fromObject(str);

        LOG.info("getBaseMessage 3");
        for (int i = 0; i < array.size(); i++) {
            JSONObject json = array.getJSONObject(i);
            if (json.get("detection_item_id") != null &&
                    json.get("detection_item_id").equals(DetectionItemIdEnum.BASE.getValue())) {
                baseMessageVO.setTerminalType(taskDetailVO.getTerminal_type());
                
                String result_content = json.getString("result_content");
            	result_content = result_content.replaceAll("NumberLong", "");
            	result_content = result_content.replaceAll("\\(", "");
            	result_content = result_content.replaceAll("\\)", "");
			    JSONObject childJson = JSONObject.fromObject(result_content);
                if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue()) {
                    baseMessageVO.setApkMd5(childJson.getString("md5"));
                    baseMessageVO.setApkSize(childJson.getString("size"));
                    baseMessageVO.setAppName(childJson.getString("name"));
                    baseMessageVO.setPackageName(childJson.getString("pakage"));
                    baseMessageVO.setVersionName(childJson.getString("version"));
                    baseMessageVO.setTargetSdkVersion(childJson.getString("platformVersion"));
                    baseMessageVO.setMinSdkVersion(childJson.getString("miniVersion"));
                    baseMessageVO.setSha1(childJson.optString("sha1"));
                    baseMessageVO.setSha_256(childJson.optString("sha256"));
                } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
                    baseMessageVO.setApkMd5(childJson.getString("apkMD5"));
                    baseMessageVO.setApkSize(childJson.getString("apkSize"));
                    baseMessageVO.setAppName(childJson.getString("appName"));
                    baseMessageVO.setPackageName(childJson.getString("packageName"));
                    baseMessageVO.setVersionName(childJson.getString("versionName"));
                    baseMessageVO.setTargetSdkVersion(childJson.getString("targetSdkVersion"));
                    baseMessageVO.setMinSdkVersion(childJson.getString("sdkVersion"));
                    baseMessageVO.setEncryptDetail(childJson.getString("encryptCompany"));
                } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.WECHAT_APPLET.getValue()
                        || taskDetailVO.getTerminal_type() == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
                    baseMessageVO.setAppName(childJson.getString("appName"));
                    baseMessageVO.setPackageName(childJson.getString("packageName"));
                    String version = childJson.optString("versionName", StringUtils.EMPTY);
                    baseMessageVO.setVersionName(optDetails(version));
                    AppletExtraInfoVO appletExtraInfoVO = taskDetailVO.getAppletExtraInfo();
                    if (Objects.nonNull(appletExtraInfoVO)) {
                        baseMessageVO.setAppId(optDetails(appletExtraInfoVO.getAppId()));
                        baseMessageVO.setAccountSubject(optDetails(appletExtraInfoVO.getAccountSubject()));
                        baseMessageVO.setOriginalId(optDetails(appletExtraInfoVO.getOriginalId()));
                        baseMessageVO.setPrivacyData(optDetails(appletExtraInfoVO.getPrivacyData()));
                        baseMessageVO.setStatement(optDetails(appletExtraInfoVO.getStatement()));
                        baseMessageVO.setServiceProvider(optDetails(appletExtraInfoVO.getServiceProvider()));
                        baseMessageVO.setServiceCategory(optDetails(appletExtraInfoVO.getServiceCategory()));
                        baseMessageVO.setUpdateTime(appletExtraInfoVO.getUpdateTime());
                        baseMessageVO.setPlugins(optDetails(appletExtraInfoVO.getPlugins()));
                    }
                } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.HARMONY.getValue()) {
                    TAssets assets = assetsMapper.selectAssetByDocumentId(documentId);
                    baseMessageVO.setAppName(assets.getName());
                    String version = childJson.optString("versionName", StringUtils.EMPTY);
                    baseMessageVO.setVersionName(optDetails(version));
                    baseMessageVO.setApkMd5(childJson.optString("fileMD5", PinfoConstant.DETAILS_EMPTY));
                    baseMessageVO.setApkSize(childJson.optString("fileSize", PinfoConstant.DETAILS_EMPTY));
                    baseMessageVO.setPackageName(childJson.optString("bundleName", PinfoConstant.DETAILS_EMPTY));
                    baseMessageVO.setTargetSdkVersion(childJson.optString("targetAPIVersion", PinfoConstant.DETAILS_EMPTY));
                    baseMessageVO.setMinSdkVersion(childJson.optString("minAPIVersion", PinfoConstant.DETAILS_EMPTY));
                    baseMessageVO.setEncryptDetail(childJson.optString("encryptCompany", PinfoConstant.DETAILS_EMPTY));
                }
                if (childJson.get("signature") != null) {
                    if (childJson.get("signature").equals("未签名")) {
                        baseMessageVO.setSignMd5("未签名");
                        baseMessageVO.setSignDetail("未签名");
                    } else {
                        List<String> signDetailList = new ArrayList<>();
                        JSONObject signJson = childJson.getJSONObject("signature");

                        if (signJson.get("证书指纹:") != null) {
                            baseMessageVO.setSignMd5(signJson.get("证书指纹:").toString().split("\n\t")[0].split("MD5:")[1].trim());
                        }

                        if (signJson.get("所有者:") != null && signJson.get("发布者:") != null) {
                            baseMessageVO.setSignDetail("所有者:" + signJson.getString("所有者:") + "\n\t" + "发布者:" + signJson.getString("发布者:"));
                            signDetailList.add("发布者:" + WordStringUtil.converCharacters(signJson.getString("发布者:")));
                            signDetailList.add("所有者:" + WordStringUtil.converCharacters(signJson.getString("所有者:")));
                        }
                        if (signJson.get("issuerDN") != null && signJson.get("subjectDn") != null && signJson.get("md5") != null) {
                            baseMessageVO.setSignMd5(signJson.get("md5").toString());
                            baseMessageVO.setSignDetail("所有者:" + signJson.getString("issuerDN") + "\n\t" + "发布者:" + signJson.getString("subjectDn"));
                            signDetailList.add("发布者:" + WordStringUtil.converCharacters(signJson.getString("issuerDN")));
                            signDetailList.add("所有者:" + WordStringUtil.converCharacters(signJson.getString("subjectDn")));
                        }
                        if (signJson.get("Issuer") != null && signJson.get("Subject") != null) {
                            baseMessageVO.setSignDetail("所有者:" + signJson.getString("Issuer") + "\n\t" + "发布者:" + signJson.getString("Subject"));
                            signDetailList.add("发布者:" + WordStringUtil.converCharacters(signJson.getString("Issuer")));
                            signDetailList.add("所有者:" + WordStringUtil.converCharacters(signJson.getString("Subject")));
                        }
                        if (signJson.get("sha1") !=null){
                            baseMessageVO.setSha1(signJson.get("sha1").toString());
                        }
                        if (signJson.get("sha256") !=null){
                            baseMessageVO.setSha_256(signJson.get("sha256").toString());
                        } else if (signJson.get("SHA256") !=null){
                            baseMessageVO.setSha_256(signJson.get("SHA256").toString());
                        }
                        if(StringUtils.isNoneBlank(baseMessageVO.getSignDetail())) {
                        	baseMessageVO.setSignDetail(baseMessageVO.getSignDetail().replaceAll("�", "").replaceAll("", ""));
                        }
                        baseMessageVO.setSignDetailList(signDetailList);
                    }
                }
                break;
            }
        }
        initializeEmptyFieldsWithDefaults(baseMessageVO);
        LOG.info("getBaseMessage 4");
        try {
			Long assets_id =  taskDetailVO.getAssets_id();
			TAssets assets = assetsMapper.selectByPrimaryKey(assets_id);
			if(assets != null && StringUtils.isNoneBlank(assets.getLogo())) {
				String app_logo = FileUtil.decodeData(assets.getLogo());
				String path = commonProperties.getProperty("ijiami.framework.file.path")+app_logo;
				String base64 = null;
				if(new File(path).exists()) {
					base64 = reportChartService.getImageBASE64(commonProperties.getProperty("ijiami.framework.file.path")+app_logo);
				}
				baseMessageVO.setApkLogo(base64==null?"":base64);
			}
			//设置展示源文件名称 2021/5/27  2.5需求
			if(assets != null && StringUtils.isNoneBlank(assets.getSourceFileName())){
			    baseMessageVO.setSourceFileName(assets.getSourceFileName());
			    baseMessageVO.setAppName(assets.getName());  //同步app名称
            }
		} catch (Exception e) {
			e.getMessage();
		}
        //根据documentId去获取t_task表中的检测开始时间跟检测结束时间，跟文档的时间保持一致 2021/5/27
        TTask taskEntity = new TTask();
        taskEntity.setDetectComplete(null);
        taskEntity.setApkDetectionDetailId(documentId);
        TTask task = taskMapper.selectOne(taskEntity);
        baseMessageVO.setTaskId(task.getTaskId());
        // 设置热更新sdk信息
        List<String> appHostSdkList = getHotfixSdkInternal(taskDetailVO, array, task.getTaskId());
        if (!appHostSdkList.isEmpty()) {
            baseMessageVO.setHotUpdateSdks(StringUtils.join(appHostSdkList, ","));
        } else {
            baseMessageVO.setHotUpdateSdks("无");
        }
        
        if(StringUtils.isNoneBlank(taskDetailVO.getVersion())) {
        	baseMessageVO.setVersion("Android "+taskDetailVO.getVersion());
        }
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        if(extend != null) {
        	baseMessageVO.setModel(StringUtils.isBlank(taskDetailVO.getModel()) ? extend.getModel() : taskDetailVO.getModel());
        	if(task.getTerminalType() == TerminalTypeEnum.ANDROID
                    || task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET
                    || task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
        		if(StringUtils.isBlank(taskDetailVO.getVersion())) {
        			baseMessageVO.setVersion(extend.getVersion()==null ? "":("Android "+extend.getVersion()));
        		}else {
        			baseMessageVO.setVersion("Android "+taskDetailVO.getVersion());
        		}
        	} else {
        		baseMessageVO.setVersion(extend.getVersion()==null?"":extend.getVersion());
        	}
        	baseMessageVO.setDetectionLaws(extend.getDetectionLaws());
        }
        baseMessageVO.setApkDetectionStarttime(taskDetailVO.getApk_detection_starttime()==null?"":DateUtils.getDateFormat(taskDetailVO.getApk_detection_starttime(), DateUtils.DATETIME_DEFAULT_FORMAT));
        long taskDuration = countTaskDuration(task);
        if (taskDuration > 0) {
            baseMessageVO.setApkDetectionTime(DateUtils.getDurationTime(taskDuration));
        } else {
            baseMessageVO.setApkDetectionTime(task.getTaskStarttime()==null || task.getTaskEndtime()==null ? "" :
                    DateUtils.getDistanceTime(task.getTaskEndtime(), task.getTaskStarttime()));
        }
        baseMessageVO.setSystemVersion(taskDetailVO.getSystemVersion());
        baseMessageVO.setPlatformHost(taskDetailVO.getPlatformHost());
        return baseMessageVO;
    }

    /**
     * 设置 BaseMessageVO 对象中空字段的默认值。
     * @param baseMessageVO 需要处理的 BaseMessageVO 对象
     */
    private void initializeEmptyFieldsWithDefaults(BaseMessageVO baseMessageVO) {
        if (StringUtils.isBlank(baseMessageVO.getSha1())) {
            baseMessageVO.setSha1(PinfoConstant.DETAILS_EMPTY);
        }
        if (StringUtils.isBlank(baseMessageVO.getSha256())) {
            baseMessageVO.setSha_256(PinfoConstant.DETAILS_EMPTY);
        }
        if (Objects.isNull(baseMessageVO.getSignDetailList())) {
            baseMessageVO.setSignDetailList(Collections.emptyList());
        }
        if (StringUtils.isBlank(baseMessageVO.getSignMd5())) {
            baseMessageVO.setSignMd5(PinfoConstant.DETAILS_EMPTY);
        }
        if (StringUtils.isBlank(baseMessageVO.getSignDetail())) {
            baseMessageVO.setSignDetail(PinfoConstant.DETAILS_EMPTY);
        }
    }

    private long countTaskDuration(TTask task) {
        long duration = 0;
        if (Objects.nonNull(task.getStaticDetectDuration())) {
            duration += task.getStaticDetectDuration();
        }
        if (Objects.nonNull(task.getDynamicDetectDuration())) {
            duration += task.getDynamicDetectDuration();
        }
        if (Objects.nonNull(task.getLawDetectDuration())) {
            duration += task.getLawDetectDuration();
        }
        return duration;
    }

    protected List<SdkVO> getSdkList(String documentId) {
        Long taskId = taskMapper.findTaskIdByDocumentId(documentId);
        return getSdkList(documentId, taskId);
    }

    @Override
    public List<String> getHotfixSdk(Long taskId) {
        try {
			TTask task = taskMapper.selectByPrimaryKey(taskId);
			if (task == null) {
			    return Collections.emptyList();
			}
			TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(task.getApkDetectionDetailId());
			if (taskDetailVO == null || taskDetailVO.getDetection_result()== null) {
			    return Collections.emptyList();
			}
			String str = taskDetailVO.getDetection_result().toString();
			JSONArray array = JSONArray.fromObject(str);
			return getHotfixSdkInternal(taskDetailVO, array, taskId);
		} catch (Exception e) {
			e.getMessage();
		}
        return Collections.emptyList();
    }

    private List<String> getHotfixSdkInternal(TaskDetailVO taskDetailVO, JSONArray array, Long taskId) {
    	List<String> appHotFixSdkList = new ArrayList<>();
        try {
			List<SdkVO> sdkVOList = this.getOriginalSdkVo(taskDetailVO, array, taskId);
			for (SdkVO sdkVO : sdkVOList) {
			    if (sdkVO.getHotfix() != null && sdkVO.getHotfix() && !appHotFixSdkList.contains(sdkVO.getName())) {
			        appHotFixSdkList.add(sdkVO.getName());
			    }
			}
		} catch (Exception e) {
			e.getMessage();
		}
        return appHotFixSdkList;
    }

    @Override
    public List<SdkVO> getSdkList(String documentId, Long taskId) {
        TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(documentId);
        if (Objects.isNull(taskDetailVO)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(taskDetailVO.getDetection_result())) {
            return getSdkListInternal(taskDetailVO, null, taskId);
        } else {
            JSONArray array = JSONArray.fromObject(taskDetailVO.getDetection_result());
            return getSdkListInternal(taskDetailVO, array, taskId);
        }
    }
    
    @Override
    public SdkResponseVO getIosSDKList(String documentId, Long taskId) {
        TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(documentId);
        if (Objects.isNull(taskDetailVO)) {
            return null;
        }
        if (Objects.isNull(taskDetailVO.getDetection_result())) {
            return getIosSdkListInternal(taskDetailVO, null, taskId);
        } else {
            JSONArray array = JSONArray.fromObject(taskDetailVO.getDetection_result());
            return getIosSdkListInternal(taskDetailVO, array, taskId);
        }
    }
    
    
    @Override
    public List<SdkVO> getAPISdkList(String documentId, Long taskId) {
        TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(documentId);
        if (Objects.isNull(taskDetailVO)) {
            return null;
        }
        if (Objects.isNull(taskDetailVO.getDetection_result())) {
            return null;
        }
        JSONArray array = JSONArray.fromObject(taskDetailVO.getDetection_result());
        return getAPISdkListInternal(taskDetailVO, array, taskId);
    }
    
    private List<SdkVO> getAPISdkListInternal(TaskDetailVO taskDetailVO, JSONArray detectionResult, Long taskId) {
        List<SdkVO> resultList = new ArrayList<>();
        List<String> sdks = new ArrayList<>();
        for (int i = 0; i < detectionResult.size(); i++) {
            JSONObject json = detectionResult.getJSONObject(i);
            if (json.get("detection_item_id") != null && (json.get("detection_item_id").equals("0104") || json.get("detection_item_id").equals("0107"))) {
                if (json.containsKey("result_content") && !json.get("result_content").equals("")) {
                    try {
                        JSONObject childJson = null;
                        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue() && json.get("detection_item_id").equals("0104")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList") && childJson.getJSONArray("sdkList") != null) {
                                List<String> finalSdks = sdks;
                                childJson.getJSONArray("sdkList").stream().forEach(sdkInfo -> {
                                    String[] sdkArray = sdkInfo.toString().split("\\s+");
                                    if (sdkArray.length > 0) {
                                        finalSdks.add(sdkArray[0]);
                                    }
                                });
                            }
                            break;
                        } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && json.get("detection_item_id").equals("0107")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList")) {
                                sdks = new Gson().fromJson(childJson.getString("sdkList"), new TypeToken<List<String>>() {}.getType());
                                break;

                            }
                        }
                    } catch (Exception ex) {
                        ex.getMessage();
                    }
                }
            }
        }
        List<SdkVO> sdkVOS = getSdkVOById(sdks, taskId, taskDetailVO.getTerminal_type());
        for (SdkVO sdkVO : sdkVOS) {
            if (CollectionUtils.isEmpty(sdkVO.getPackageList())) {
                sdkVO.setPackageName(buildPackageName(sdkVO.getPackageList()));
            }
            if (StringUtils.isNotBlank(sdkVO.getPermissionCodes())) {
                sdkVO.setPermissions(getSdkPermission(sdkVO));
            }
            if (Objects.nonNull(sdkVO.getId())) {
//                sdkVO.setOutsideAddresses(getOutsideAddress(taskId, sdkVO));
            }
            resultList.add(sdkVO);
        }
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
            resultList.addAll(getSuspiciousSdkList(taskId,true));
        }
        return resultList;
    }
    
    
    private List<PermissionVO> getSdkPermission(SdkVO sdkVO) {
        List<PermissionVO> permissionVOS = permissionMapper.findPermissionByPermissionCodes(sdkVO.getPermissionCodes());
        if (CollectionUtils.isEmpty(sdkVO.getPermissions())) {
            return permissionVOS;
        } else {
            List<PermissionVO> newPermissions = new ArrayList<>(sdkVO.getPermissions());
            for (PermissionVO permissionVO : permissionVOS) {
                boolean anyMatch = sdkVO.getPermissions().stream().anyMatch(p -> p.getName().equals(permissionVO.getName()));
                if (!anyMatch) {
                    newPermissions.add(permissionVO);
                }
            }
            Collections.sort(newPermissions);
            return newPermissions;
        }
    }
    
    
    /**
     * 获取疑似sdk数据
     * @param taskId
     * @return
     */
    private List<SdkVO> getSuspiciousSdkList(Long taskId,boolean is_api) {
        // 创建就公司产品数据
        List<TOdpCompanyProduct> productList = odpCompanyProductMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> productMap = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
        // 构造一份公司名数据
        List<TOdpCompany> companyList = odpCompanyMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> companyMap = SuspiciousSdkUtils.buildCompanyMainProduct(companyList)
                .stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));

        List<SuspiciousSdkMergeVO> mergeList = SuspiciousSdkUtils.mergePackageName(suspiciousSdkMapper.findAndroidBehaviorByTaskDistinctId(taskId));
        List<SdkVO> sdkVOList = new ArrayList<>();
        if (!mergeList.isEmpty()) {
            List<TSuspiciousSdkLibrary> libraryList = suspiciousSdkLibraryMapper.findInId(mergeList.stream()
                    .map(SuspiciousSdkMergeVO::getSuspiciousSdkIds)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
            for (SuspiciousSdkMergeVO tSuspiciousSdk : mergeList) {
                Optional<TOdpCompanyProduct> companyProduct = SuspiciousSdkUtils.findCompanyProduct(tSuspiciousSdk.getPackageName(), productMap, companyMap);
                SdkVO sdkVO = new SdkVO();
                sdkVO.setPackageName(tSuspiciousSdk.getPackageName());
                sdkVO.setTypeName(SUSPICIOUS_TYPE_NAME);
                // 是否在产品库里找到数据
                if (companyProduct.isPresent()) {
                    sdkVO.setName(companyProduct.get().getProductName());
                    // 找到对应的公司信息
                    Optional<TOdpCompany> company = companyList
                            .stream()
                            .filter(c -> c.getId().equals(companyProduct.get().getCompanyId()))
                            .findFirst();
                    company.ifPresent(tOdpCompany -> sdkVO.setManufacturer(tOdpCompany.getCompanyName()));
                } else {
                    sdkVO.setName(tSuspiciousSdk.getPackageName());
                }
                sdkVO.setId(tSuspiciousSdk.getSuspiciousSdkIds().get(0));
                libraryList.stream()
                        .filter(lib -> tSuspiciousSdk.getSuspiciousSdkIds().contains(lib.getId()))
                        .findFirst()
                        .ifPresent(tSuspiciousSdkLibrary -> {
                        	if(!is_api) {
                        		sdkVO.setPermissions(permissionMapper.findPermissionByPermissionCodes(tSuspiciousSdkLibrary.getPermissionCodes()));
                        	}
                        });
                sdkVOList.add(sdkVO);
            }
        }
        return sdkVOList;
    }

    /**
     * 获取原始的静态和动态检测sdk列表，sdk没有权限信息，没有境外访问信息，不包含疑似sdk
     * @param taskDetailVO
     * @param detectionResult
     * @param taskId
     * @return
     */
    private List<SdkVO> getOriginalSdkVo(TaskDetailVO taskDetailVO, JSONArray detectionResult, Long taskId) {
        LOG.info("getOriginalSdkVo start");
        List<String> sdks = getStaticDataSdkList(taskDetailVO, detectionResult);
        List<SdkVO> sdkVOS = getSdkVOById(sdks, taskId, taskDetailVO.getTerminal_type());
        LOG.info("getOriginalSdkVo end");
        return sdkVOS;
    }

    private List<SdkVO> getSdkListInternal(TaskDetailVO taskDetailVO, JSONArray detectionResult, Long taskId) {
        List<SdkVO> resultList = new ArrayList<>();
        try {
			List<SdkVO> sdkVOS = getOriginalSdkVo(taskDetailVO, detectionResult, taskId);
			for (SdkVO sdkVO : sdkVOS) {
			    if (CollectionUtils.isEmpty(sdkVO.getPackageList())) {
			        sdkVO.setPackageName(buildPackageName(sdkVO.getPackageList()));
			    }
			    if (StringUtils.isNotBlank(sdkVO.getPermissionCodes())) {
			        sdkVO.setPermissions(getSdkPermission(sdkVO, taskId));
			    }
			    if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue() && Objects.nonNull(sdkVO.getId())) {
			        sdkVO.setOutsideAddresses(getOutsideAddress(taskId, sdkVO, taskDetailVO.getTerminal_type()));
			    }

			    if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && Objects.nonNull(sdkVO.getName())) {
			        sdkVO.setOutsideAddresses(getOutsideAddress(taskId, sdkVO, taskDetailVO.getTerminal_type()));
			    }
			    resultList.add(sdkVO);
			}
			LOG.info("getSdkListInternal setPermission");
			// 把工具和框架的sdk移到最后面
			List<String> rearSdkTypeNames = Arrays.asList("工具", "框架", "网络");
			Iterator<SdkVO> sdkVOIterator = resultList.iterator();
			List<SdkVO> rearList = new ArrayList<>();
			while (sdkVOIterator.hasNext()) {
			    SdkVO sdkVO = sdkVOIterator.next();
			    if (rearSdkTypeNames.contains(sdkVO.getTypeName())) {
			        sdkVOIterator.remove();
			        rearList.add(sdkVO);
			    }
			}
			resultList.addAll(rearList);
			LOG.info("getSdkListInternal addSuspiciousSdk");
			if(taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && !resultList.isEmpty()) {
				for (SdkVO sdkVO : resultList) {
					if(StringUtils.isNoneBlank(sdkVO.getName()) && 
							StringUtils.isBlank(sdkVO.getPackageName()) && 
							sdkVO.getName().contains("-")) {
						String packageName = sdkVO.getName().split("-")[0];
						sdkVO.setPackageName(packageName);
					}
					if(StringUtils.isNoneBlank(sdkVO.getName()) && 
							StringUtils.isBlank(sdkVO.getPackageName()) && 
							sdkVO.getName().contains("_")) {
						String packageName = sdkVO.getName().split("_")[0];
						sdkVO.setPackageName(packageName);
					}
					if(StringUtils.isBlank(sdkVO.getPackageName())) {
						sdkVO.setPackageName(sdkVO.getName());
					}
				}
			}
		} catch (Exception e) {
			e.getMessage();
		}
        
        return resultList;
    }

    @Override
    public List<SdkVO> getSuspiciousSdkList(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            throw new IllegalArgumentException("任务不存在");
        }
        List<SdkVO> resultList = new ArrayList<>();
        if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
            addAndroidSuspiciousSdk(task.getTaskId(), resultList);
        } else if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            addIosSuspiciousSdk(task.getTaskId(), resultList);
        }
        return resultList;
    }

    @Override
    public Long startAiDetection(Long taskId) throws IjiamiCommandException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            throw new IllegalArgumentException("任务不存在");
        }
        if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            throw new IllegalArgumentException("任务状态错误");
        }
        TTaskExtend taskExtendUpdate = new TTaskExtend();
        taskExtendUpdate.setAiDetectLoginStatus(AiDetectLoginStatusEnum.LOGGED_IN);
        Example example = new Example(TTaskExtend.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        taskExtendMapper.updateByExampleSelective(taskExtendUpdate, example);
        messageSendKit.sendStartAIDetectionMsgToXxlDetection(task);
        return task.getTaskId();
    }

    public List<SdkVO> getSuspiciousSdk(Long taskId, TaskDetailVO taskDetailVO) {
        List<SdkVO> resultList = new ArrayList<>();
        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue()) {
            addAndroidSuspiciousSdk(taskId, resultList);
        } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue()) {
            addIosSuspiciousSdk(taskId, resultList);
        }
        return resultList;
    }
    
    /**
     *
     * @param taskDetailVO
     * @param detectionResult
     * @param taskId
     * @return
     */
    private SdkResponseVO getIosSdkListInternal(TaskDetailVO taskDetailVO, JSONArray detectionResult, Long taskId) {
    	SdkResponseVO response = new SdkResponseVO();
        List<SdkVO> resultList = new ArrayList<>();
        List<String> sdks = getStaticDataSdkList(taskDetailVO, detectionResult);
        List<SdkVO> sdkVOS = getSdkVOById(sdks, taskId, taskDetailVO.getTerminal_type());
        for (SdkVO sdkVO : sdkVOS) {
            if (CollectionUtils.isEmpty(sdkVO.getPackageList())) {
                sdkVO.setPackageName(buildPackageName(sdkVO.getPackageList()));
            }
            if (StringUtils.isNotBlank(sdkVO.getPermissionCodes())) {
                sdkVO.setPermissions(getSdkPermission(sdkVO, taskId));
            }
            if (taskDetailVO.getTerminal_type() == 1 && Objects.nonNull(sdkVO.getId())) {
                sdkVO.setOutsideAddresses(getOutsideAddress(taskId, sdkVO, taskDetailVO.getTerminal_type()));
            }

            if (taskDetailVO.getTerminal_type() == 2 && Objects.nonNull(sdkVO.getName())) {
                sdkVO.setOutsideAddresses(getOutsideAddress(taskId, sdkVO, taskDetailVO.getTerminal_type()));
            }
            resultList.add(sdkVO);
        }
        
        // 把工具和框架的sdk移到最后面
        List<String> rearSdkTypeNames = Arrays.asList("工具", "框架", "网络");
        Iterator<SdkVO> sdkVOIterator = resultList.iterator();
        List<SdkVO> rearList = new ArrayList<>();
        while (sdkVOIterator.hasNext()) {
            SdkVO sdkVO = sdkVOIterator.next();
            if (rearSdkTypeNames.contains(sdkVO.getTypeName())) {
                sdkVOIterator.remove();
                rearList.add(sdkVO);
            }
        }
        resultList.addAll(rearList);
        //处理ios没包名问题
        if(taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && resultList != null && resultList.size()>0) {
        	for (SdkVO sdkVO : resultList) {
        		if(StringUtils.isNoneBlank(sdkVO.getName()) && 
        				StringUtils.isBlank(sdkVO.getPackageName()) && 
        				sdkVO.getName().contains("-")) {
        			String packageName = sdkVO.getName().split("-")[0];
        			sdkVO.setPackageName(packageName);
        		}
        		if(StringUtils.isNoneBlank(sdkVO.getName()) && 
        				StringUtils.isBlank(sdkVO.getPackageName()) && 
        				sdkVO.getName().contains("_")) {
        			String packageName = sdkVO.getName().split("_")[0];
        			sdkVO.setPackageName(packageName);
        		}
        		if(StringUtils.isBlank(sdkVO.getPackageName())) {
        			sdkVO.setPackageName(sdkVO.getName());
        		}
			}
        }
        
        response.setSdkList(resultList);
        
        
        //IOS隐私清单文件解析到数据
        List<String> iosPrivacyInfoXCPrivacySdk = getStaticDataPrivacyInfoXCPrivacySdkList(taskDetailVO, detectionResult);
        if(iosPrivacyInfoXCPrivacySdk !=null && iosPrivacyInfoXCPrivacySdk.size()>0) {
        	
        	//查询官方定义的SDK数据
       	 	List<IosPrivacySdk> privacySdkList = sdkMapper.getIosPrivacySdkList();
       	 	
       	 	//提取IOS隐私文件iosPrivacyInfoXCPrivacySdk中的 sdkName与官方定义的内容SDK，如果没有就不走下一步判断了
            List<String> privacySdkNames = privacySdkList.stream()
                    .filter(privacySdk -> containsAny(privacySdk.getSdkName(), iosPrivacyInfoXCPrivacySdk))
                    .map(IosPrivacySdk::getSdkName)
                    .collect(Collectors.toList());
            if(privacySdkNames== null || privacySdkNames.size()==0) {
           	 	return response;
            }
            
//            resultList.forEach(sdk->{
//            	System.out.println(sdk.getName());
//            });
        	
       	 	// 提取三方 SDK 列表中包含在官方定义 SDK列表
		   	List<SdkVO> privacySdk = resultList.stream()
		            .filter(sdk -> containsSdk(sdk.getName(), privacySdkList))
		            .collect(Collectors.toList());
		   	
		   	//提取到的三方sdk数据与隐私文件提取出官方sdk对比，如果不存在，说明是未声明的sdk
		   	List<SdkVO> undeclaredList = privacySdk.stream()
	                .filter(sdk -> !containsSdkString(sdk.getName(), privacySdkNames))
	                .collect(Collectors.toList());
            
		   	response.setUndeclaredList(undeclaredList);
//        	try {
//				List<SdkVO> iosSdkVO = getIosSdkVOById(iosPrivacyInfoXCPrivacySdk, taskDetailVO.getTerminal_type());
//				sdkVOS.addAll(iosSdkVO);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
        }
       
        
        return response;
    }

    static boolean containsSdk(String sdkName, List<IosPrivacySdk> iosPrivacyInfoXCPrivacySdk) {
        for (IosPrivacySdk sdk : iosPrivacyInfoXCPrivacySdk) {
            if (sdkName.contains(sdk.getSdkName())) {
                return true;
            }
        }
        return false;
    }
    
    static boolean containsSdkString(String sdkName, List<String> privacySdkNames) {
        for (String name : privacySdkNames) {
            if (sdkName.contains(name)) {
                return true;
            }
        }
        return false;
    }
    
    
    private static boolean containsAny(String str, List<String> list) {
        for (String s : list) {
            if (str.contains(s)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取静态检测数据中的sdk
     * @param taskDetailVO
     * @param detectionResult
     * @return
     */
    public List<String> getStaticDataSdkList(TaskDetailVO taskDetailVO, JSONArray detectionResult) {
        if (TerminalTypeEnum.getAndValid(taskDetailVO.getTerminal_type()).isApplet()) {
            if (Objects.isNull(taskDetailVO.getAppletExtraInfo())) {
                return Collections.emptyList();
            }
            List<String> sdkList = taskDetailVO.getAppletExtraInfo().getSdkList();
            return Objects.isNull(sdkList) ? Collections.emptyList() : sdkList;
        } else {
            return getIosOrAndroidStaticSdkList(taskDetailVO, detectionResult);
        }
    }

    private List<String> getIosOrAndroidStaticSdkList(TaskDetailVO taskDetailVO, JSONArray detectionResult) {
        List<String> sdks = new ArrayList<>();
        if (Objects.isNull(detectionResult)) {
            return sdks;
        }
        for (int i = 0; i < detectionResult.size(); i++) {
            JSONObject json = detectionResult.getJSONObject(i);
            if (json.get("detection_item_id") != null && (json.get("detection_item_id").equals("0104") || json.get("detection_item_id").equals("0107"))) {
                if (json.containsKey("result_content") && !json.get("result_content").equals("")) {
                    try {
                        JSONObject childJson = null;
                        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue() && json.get("detection_item_id").equals("0104")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList") && childJson.getJSONArray("sdkList") != null) {
                                childJson.getJSONArray("sdkList").forEach(sdkInfo -> {
                                    String[] sdkArray = sdkInfo.toString().split("\\s+");
                                    if (sdkArray.length > 0) {
                                        sdks.add(sdkArray[0]);
                                    }
                                });
                            }
                            break;
                        } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && json.get("detection_item_id").equals("0107")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList")) {
                                sdks.addAll(CommonUtil.jsonToList(childJson.getString("sdkList"), String.class));
                                break;
                            }
                        }
                    } catch (Exception ex) {
                        ex.getMessage();
                    }
                }
            }
        }
        return sdks;
    }

    /**
     * 解析IOS SDK声明文件
     * @param taskDetailVO
     * @param detectionResult
     * @return
     */
    public List<String> getStaticDataPrivacyInfoXCPrivacySdkList(TaskDetailVO taskDetailVO, JSONArray detectionResult) {
        List<String> sdks = new ArrayList<>();
        if (Objects.isNull(detectionResult)) {
            return sdks;
        }
        if (taskDetailVO.getTerminal_type() != TerminalTypeEnum.IOS.getValue()) {
        	return sdks;
        }
        
        for (int i = 0; i < detectionResult.size(); i++) {
            JSONObject json = detectionResult.getJSONObject(i);
            if (json.get("detection_item_id") == null || (!json.get("detection_item_id").equals("0101"))) {
            	continue;
            }
            if (json.containsKey("result_content") && !json.get("result_content").equals("")) {
                try {
                    com.alibaba.fastjson.JSONObject childJson = null;
                    childJson = com.alibaba.fastjson.JSONObject.parseObject(json.get("result_content").toString());
                    if (!childJson.containsKey("xcprivacy")) {
                    	if(childJson.get("xcprivacy") instanceof com.alibaba.fastjson.JSONArray) {
                    		com.alibaba.fastjson.JSONArray xcprivacyJson = childJson.getJSONArray("xcprivacy");
                    		List<String> stringList = new ArrayList<>();
                            for (Object obj : xcprivacyJson) {
                                stringList.add(obj.toString());
                            }
                            if(stringList != null && stringList.size()>0) {
                            	sdks.addAll(stringList);
                            }
                    	}else {
                    		com.alibaba.fastjson.JSONObject xcprivacyJson = childJson.getJSONObject("xcprivacy");
                        	List<String> list = extractMatches(xcprivacyJson);
                        	if(list != null) {
                        		sdks.addAll(list);
                        	}
                    	}
                        break;
                    }
                } catch (Exception ex) {
                    ex.getMessage();
                }
            }
        }
        return sdks;
    }
    
    private static List<String> extractMatches(com.alibaba.fastjson.JSONObject xcprivacy) {
        List<String> matches = new ArrayList<>();
        for (String key : xcprivacy.keySet()) {
           String pattern = "/(\\w+)_Privacy\\.bundle/";
           Pattern regex = Pattern.compile(pattern);
           Matcher matcher = regex.matcher(key);
           if (matcher.find()) {
               System.out.println("Match: " + matcher.group(1)); // 获取第一个捕获组的内容
               matches.add(matcher.group(1));
           }
        }
        return matches;
    }

    private List<SdkVO> getPrivacyActionNougatSdk(List<String> sdks, Long taskId) {
        // 获取行为信息中的sdk信息
        List<String> behaviorSdk = privacyActionNougatMapper.getBehaviorSdkPkg(taskId);
        // 通讯行为中的sdk信息
        behaviorSdk.addAll(getOutSideSdk(taskId));
        for (String sdkLine : behaviorSdk) {
            for (String sdkInfo : sdkLine.split(",")) {
                if (!sdks.contains(sdkInfo)) {
                    sdks.add(sdkInfo);
                }
            }
        }
        if (!sdks.isEmpty()) {
            List<TSdkLibrary> sdkList = sdkMapper.findInPackageName(sdks.stream().distinct().collect(Collectors.toList()));
            return convertNewestSdkVOList(sdkList);
        }
        return Collections.emptyList();
    }

    private List<SdkVO> getSdkVOById(List<String> sdks, Long taskId, Integer terminalType) {
        List<SdkVO> sdkVOList = new ArrayList<>();
        if (terminalType == TerminalTypeEnum.ANDROID.getValue()) {
            // 获取行为信息中的sdk信息
            sdkVOList.addAll(getPrivacyActionNougatSdk(sdks, taskId));
        } else if (terminalType == TerminalTypeEnum.IOS.getValue()) {
            // 获取ios的第三方sdk库匹配信息
            Set<String> sdkPackageNameSet = new HashSet<>();
            Set<String> sdkNameSet = new HashSet<>();
            TIosPrivacyActionSdk query = new TIosPrivacyActionSdk();
            query.setTaskId(taskId);
            iosPrivacyActionSdkMapper.select(query).forEach(sdk -> {
                sdkNameSet.add(sdk.getSdkName());
                sdkPackageNameSet.add(sdk.getSdkPackageName());
                SdkVO sdkVO = new SdkVO();
                sdkVO.setName(sdk.getSdkName());
                sdkVO.setManufacturer(convertIosSdkManufacturer(sdk.getManufacturer()));
                sdkVO.setPackageName(sdk.getSdkPackageName());
                sdkVO.setTypeName(sdk.getTypeName());
                sdkVOList.add(sdkVO);
            });
            for (String sdk: sdks) {
                String[] s = sdk.split(" ");
                if (s.length > 2) {
                    String name = s[0];
                    // 判断SDK是否已经添加过了
                    if (sdkNameSet.contains(name)) {
                        continue;
                    }
                    sdkNameSet.add(name);
                } else {
                    String packageName = s[0];
                    // 判断SDK是否已经添加过了
                    if (sdkPackageNameSet.contains(packageName)) {
                        continue;
                    }
                    sdkPackageNameSet.add(packageName);
                }
                Optional<SdkVO> sdkVOOptional = buildIosSdk(sdk);
                if (sdkVOOptional.isPresent()) {
                    //过滤掉没有厂商信息的SDK
                    if(sdkVOOptional.get().getManufacturer().equals(PinfoConstant.DETAILS_EMPTY) || sdkVOOptional.get().getManufacturer().equals("unKnow")){
                        continue;
                    }
                    sdkVOList.add(sdkVOOptional.get());
                }
            }
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET.getValue()) {
            for (String sdk:sdks) {
                SdkVO sdkVO = new SdkVO();
                sdkVO.setName(sdk);
                sdkVO.setManufacturer(PinfoConstant.DETAILS_EMPTY);
                sdkVO.setPackageName(PinfoConstant.DETAILS_EMPTY);
                sdkVO.setTypeName(PinfoConstant.DETAILS_EMPTY);
                sdkVOList.add(sdkVO);
            }
        }
        return sdkVOList;
    }
    
    private List<SdkVO> getIosSdkVOById(List<String> sdks, Integer terminalType) {
        List<SdkVO> sdkVOList = new ArrayList<>();
        if (terminalType == TerminalTypeEnum.IOS.getValue()) {
            if (!sdks.isEmpty()) {
                sdkMapper.findInPackageName(sdks.stream().distinct().collect(Collectors.toList())).stream()
                        .collect(Collectors.groupingBy(TSdkLibrary::getName))
                        .forEach((id, sdkVOs) -> {
                        	SdkVO sdkVO = getNewestSdkVO(sdkVOs);
                            sdkVOList.add(sdkVO);
                        });
            }
        }
        return sdkVOList;
    }

    private List<SdkVO> convertNewestSdkVOList(List<TSdkLibrary> sdkList) {
        List<TSdkLibrary> distinctSdkList = new ArrayList<>();
        for (TSdkLibrary sdk:sdkList) {
            // 是否有同包名的sdk已经添加
            Optional<TSdkLibrary> sameSdkOpt = distinctSdkList.stream()
                    .filter(s -> intersection(s.getPackageList(), sdk.getPackageList()))
                    .findFirst();
            if (sameSdkOpt.isPresent()) {
                // 当前sdk版本新，替换原来的同包名sdk
                if (SdkUtils.compareSdkVersion(sdk, sameSdkOpt.get())) {
                    distinctSdkList.remove(sameSdkOpt.get());
                    distinctSdkList.add(sdk);
                }
            } else {
                distinctSdkList.add(sdk);
            }
        }
        List<SdkVO> sdkVoList = new ArrayList<>(distinctSdkList.size());
        for (TSdkLibrary sdk:distinctSdkList) {
            SdkVO sdkVO = new SdkVO();
            BeanUtils.copyProperties(sdk, sdkVO);
            // 多包名的情况下，需要拼接
            if (!sdk.getPackageList().isEmpty()) {
                sdkVO.setPackageName(sdk.getPackageList()
                        .stream()
                        .map(TSdkLibraryPackage::getPackageName)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(",")));
            }
            sdkVoList.add(sdkVO);
        }
        return sdkVoList;
    }

    private boolean intersection(List<TSdkLibraryPackage> packageList, List<TSdkLibraryPackage> packageList2) {
        for (TSdkLibraryPackage p:packageList) {
            for (TSdkLibraryPackage p2:packageList2) {
                if (StringUtils.equals(p.getPackageName(), p2.getPackageName())) {
                    return true;
                }
            }
        }
        return false;
    }

    private SdkVO getNewestSdkVO(List<TSdkLibrary> sdkVOS) {
        SdkVO sdkVO = new SdkVO();
        TSdkLibrary tSdkLibrary;
        if (sdkVOS.size() == 1) {
            tSdkLibrary = sdkVOS.get(0);
        } else {
            tSdkLibrary = sdkVOS.stream().max(Comparator.comparing(TSdkLibrary::getId)).orElseThrow(() -> new IjiamiRuntimeException("数据错误，找不到sdk"));
        }
        BeanUtils.copyProperties(tSdkLibrary, sdkVO);
        // 多包名的情况下，需要拼接
        if (!tSdkLibrary.getPackageList().isEmpty()) {
            sdkVO.setPackageName(tSdkLibrary.getPackageList()
                    .stream()
                    .map(TSdkLibraryPackage::getPackageName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(",")));
        }
        return sdkVO;
    }

    private List<String> getOutSideSdk(Long taskId) {
        List<String> executorList = privacyOutsideAddressMapper.getOutSideAddressExecutor(taskId);
        // 主体名是“SDK名1,SDK名2”这种拼接形式，需要拆开来
        List<String> executorNameList = executorList
                .stream()
                .filter(Objects::nonNull)
                .map(executor -> executor.split(","))
                .flatMap(Arrays::stream)
                .distinct().collect(Collectors.toList());
        if (!executorNameList.isEmpty()) {
            // 去sdk库查询
            return sdkMapper.findInSdkName(executorNameList)
                    .stream()
                    // 获取每个sdk的包名列表
                    .flatMap(sdkVO -> Optional.ofNullable(sdkVO.getPackageList())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(TSdkLibraryPackage::getPackageName))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private List<TPrivacyOutsideAddress> getOutsideAddress(Long taskId, SdkVO sdkVO, Integer terminalType) {
        List<TPrivacyOutsideAddress> newOutsideList = new ArrayList<>();
        List<TPrivacyOutsideAddress> outsideList = null;
        if(terminalType == 2) {
        	outsideList = privacyOutsideAddressMapper.findByTaskIdAndOutsideAndSdkName(taskId, null, sdkVO.getName());
        } else {
        	outsideList = privacyOutsideAddressMapper.findByTaskIdAndOutsideAndSdkId(taskId, null, String.valueOf(sdkVO.getId()));
        }
        if(outsideList != null && outsideList.size()>0) {
            for (TPrivacyOutsideAddress tPrivacyOutsideAddress : outsideList) {
            	if(terminalType == 1) {
            		String sdkIds = tPrivacyOutsideAddress.getSdkIds();
                    if(StringUtils.isBlank(sdkIds)) {
                        continue;
                    }
                    List<Long> ids = JSON.parseArray(sdkIds, Long.class);
                    if(!ids.contains(sdkVO.getId())){
                        continue;
                    }
            	}
                newOutsideList.add(tPrivacyOutsideAddress);
            }
        }
        return newOutsideList;
    }

    private List<PermissionVO> findPermissionByPermissionCodesSdk(String permissionCodes, Long taskId, Long sdkId) {
        List<PermissionVO> permissionVOS = permissionMapper.findPermissionByPermissionCodesSdk(permissionCodes, taskId, sdkId);
        for (PermissionVO permissionVO:permissionVOS) {
            if (Objects.isNull(permissionVO.getSdkCount())) {
                permissionVO.setSdkCount(0);
            }
            if (Objects.isNull(permissionVO.getAppCount())) {
                permissionVO.setAppCount(0);
            }
            permissionVO.setUseCount(permissionVO.getSdkCount() + permissionVO.getAppCount());
        }
        return permissionVOS;
    }

    private List<PermissionVO> getSdkPermission(SdkVO sdkVO, Long taskId) {
    	if(StringUtils.isBlank(sdkVO.getPermissionCodes()) || sdkVO.getId()==null) {
    		return Collections.emptyList();
    	}
    	List<PermissionVO> permissionVOS = findPermissionByPermissionCodesSdk(sdkVO.getPermissionCodes(), taskId, sdkVO.getId());
        if (Objects.isNull(permissionVOS)) {
            return Collections.emptyList();
        } else {
            List<PermissionVO> newPermissions = new ArrayList<>(sdkVO.getPermissions());
            for (PermissionVO permissionVO : permissionVOS) {
                if (permissionVO.getSdkCount() == 0) {
                    continue;
                }
                boolean anyMatch = sdkVO.getPermissions().stream().anyMatch(p -> p.getName().equals(permissionVO.getName()));
                if (!anyMatch) {
                    newPermissions.add(permissionVO);
                }
            }
            Collections.sort(newPermissions);
            return newPermissions;
        }
    }

    private Optional<SdkVO> buildIosSdk(String sdk) {
        String[] s = sdk.split(" ");
        if (s.length > 3) {
        	SdkVO sdkVO = new SdkVO();
            sdkVO.setName(s[0]);
            sdkVO.setManufacturer(convertIosSdkManufacturer(s[1]));
            sdkVO.setTypeName(s[3]);
            sdkVO.setPackageName(PinfoConstant.DETAILS_EMPTY);
            sdkVO.setDescribe(convertIosSdkManufacturer(s[2]));
            return Optional.of(sdkVO);
        }
        return Optional.empty();
    }

    private String convertIosSdkManufacturer(String name) {
        if (StringUtils.isNotBlank(name)) {
            return name.replace("未知", PinfoConstant.DETAILS_EMPTY).replaceAll("@", "");
        } else {
            return PinfoConstant.DETAILS_EMPTY;
        }


    }

    /**
     * 获取疑似sdk数据
     * @param taskId
     * @return
     */
    private void addAndroidSuspiciousSdk(Long taskId, List<SdkVO> resultList) {
        // 创建就公司产品数据
        List<TOdpCompanyProduct> productList = odpCompanyProductMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> productMap = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
        // 构造一份公司名数据
        List<TOdpCompany> companyList = odpCompanyMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> companyMap = SuspiciousSdkUtils.buildCompanyMainProduct(companyList)
                .stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));

        List<SuspiciousSdkMergeVO> mergeList = SuspiciousSdkUtils.mergePackageName(suspiciousSdkMapper.findAndroidBehaviorByTaskDistinctId(taskId));
        if (!mergeList.isEmpty()) {
            List<TSuspiciousSdkLibrary> libraryList = suspiciousSdkLibraryMapper.findInId(mergeList.stream()
                    .map(SuspiciousSdkMergeVO::getSuspiciousSdkIds)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
            for (SuspiciousSdkMergeVO tSuspiciousSdk : mergeList) {
                // 疑似sdk已经存在于sdk数据中，检测完成后有新sdk信息添加，这个疑似sdk包名不再是疑似sdk
                if (resultList.stream().anyMatch(sdkVO -> StringUtils.containsIgnoreCase(sdkVO.getPackageName(), tSuspiciousSdk.getPackageName()))) {
                    continue;
                }
                Optional<TOdpCompanyProduct> companyProduct = SuspiciousSdkUtils.findCompanyProduct(tSuspiciousSdk.getPackageName(), productMap, companyMap);
                SdkVO sdkVO = new SdkVO();
                sdkVO.setPackageName(tSuspiciousSdk.getPackageName());
                sdkVO.setTypeName(SUSPICIOUS_TYPE_NAME);
                // 是否在产品库里找到数据
                if (companyProduct.isPresent()) {
                    sdkVO.setName(companyProduct.get().getProductName());
                    // 找到对应的公司信息
                    Optional<TOdpCompany> company = companyList
                            .stream()
                            .filter(c -> c.getId().equals(companyProduct.get().getCompanyId()))
                            .findFirst();
                    company.ifPresent(tOdpCompany -> sdkVO.setManufacturer(tOdpCompany.getCompanyName()));
                } else {
                    sdkVO.setName(tSuspiciousSdk.getPackageName());
                }
                sdkVO.setId(tSuspiciousSdk.getSuspiciousSdkIds().get(0));
                libraryList.stream()
                        .filter(lib -> tSuspiciousSdk.getSuspiciousSdkIds().contains(lib.getId()))
                        .findFirst()
                        .ifPresent(tSuspiciousSdkLibrary -> {
                            sdkVO.setPermissions(findPermissionByPermissionCodesSdk(tSuspiciousSdkLibrary.getPermissionCodes(),taskId, sdkVO.getId())
                                    .stream()
                                    .filter(permissionVO -> permissionVO.getSdkCount() > 0)
                                    .collect(Collectors.toList())
                            );
                        });
                resultList.add(sdkVO);
            }
        }
    }

    private void addIosSuspiciousSdk(Long taskId, List<SdkVO> resultList) {
        for (SuspiciousSdkBehaviorVO behavior : suspiciousSdkMapper.findIosBehaviorByTaskDistinctId(taskId)) {
            // 疑似sdk已经存在于sdk数据中，检测完成后有新sdk信息添加，这个疑似sdk包名不再是疑似sdk
            if (resultList.stream().anyMatch(sdkVO -> StringUtils.containsIgnoreCase(sdkVO.getPackageName(), behavior.getPackageName()))) {
                continue;
            }
            SdkVO sdkVO = new SdkVO();
            sdkVO.setPackageName(behavior.getPackageName());
            sdkVO.setTypeName(SUSPICIOUS_TYPE_NAME);
            sdkVO.setName(behavior.getExecutor());
            sdkVO.setId(behavior.getSuspiciousSdkId());
            resultList.add(sdkVO);
        }
    }

    private List<TOdpCompanyProduct> buildCompanyMainProduct(List<TOdpCompany> companyList) {
        return companyList.stream().map(company -> {
            TOdpCompanyProduct product = new TOdpCompanyProduct();
            product.setCompanyId(company.getId());
            product.setProductName(company.getCompanyAbbrName());
            List<String> keywordList = buildKeywordList(company.getWebsiteUrl());
            product.setProductEnglishName(StringUtils.join(keywordList, "."));
            return product;
        }).collect(Collectors.toList());
    }

    protected List<SuspiciousSdkMergeVO> mergeSamePackageName(List<SuspiciousSdkBehaviorVO> behaviorVOList) {
        List<SuspiciousSdkMergeVO> mergeList = new ArrayList<>();
        behaviorVOList.forEach(b -> {
            String name = PackageNameExtractHelper.extractMergePackageName(b.getPackageName());
            if (StringUtils.isNotBlank(name)) {
                // 如果不包含 . 说明是abc这种单层结构的无意义包名，直接过滤掉，不参与合并。只有abc.abc, abc.abc.abc及以上层级的才有意义
                if (name.contains(".")) {
                    Optional<SuspiciousSdkMergeVO> mergeVO = mergeList.stream().filter(m -> m.getPackageName().equals(name)).findFirst();
                    if (mergeVO.isPresent()) {
                        mergeVO.get().getSuspiciousSdkIds().add(b.getSuspiciousSdkId());
                    } else {
                        mergeList.add(SuspiciousSdkMergeVO.makeSuspiciousSdkMergeVO(name, b.getSuspiciousSdkId()));
                    }
                }
            } else {
                mergeList.add(SuspiciousSdkMergeVO.makeSuspiciousSdkMergeVO(b.getPackageName(), b.getSuspiciousSdkId()));
            }
        });
        return mergeList;
    }

    protected Optional<TOdpCompanyProduct> findCompanyProduct(String packageName, List<TOdpCompanyProduct> productList) {
        List<String> keywordList = getMatchKeywordList(packageName);
        Optional<TOdpCompanyProduct> productOptional = keywordList
                .stream()
                .map(keyword -> findSdkProduct(keyword, productList))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
        return productOptional;
    }

    protected Optional<TOdpCompanyProduct> findSdkProduct(String target, List<TOdpCompanyProduct> productList) {
        List<TOdpCompanyProduct> targetProductList = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .filter(company -> company.getProductEnglishName().equals(target))
                .collect(Collectors.toList());
        return targetProductList.stream().findFirst();
    }

    private Set<String> domainNameSuffix = buildDomainNameSuffix();

    private List<String> getMatchKeywordList(String packageName) {
        List<String> keywordList = buildKeywordList(packageName);
        StringJoiner joiner = new StringJoiner(".");
        for (String keyword:keywordList) {
            joiner.add(keyword);
        }
        keywordList.add(joiner.toString());
        keywordList.add(packageName);
        return keywordList;
    }

    private List<String> buildKeywordList(String packageName) {
        String[] packageNamePartArray = packageName.split("\\.");
        List<String> keywordList = new ArrayList<>(packageNamePartArray.length);
        for (String part : packageNamePartArray) {
            if (domainNameSuffix.contains(part)) {
                continue;
            }
            keywordList.add(part);
        }
        return keywordList;
    }

    private Set<String> buildDomainNameSuffix() {
        Set<String> domainNameSuffix = new HashSet<>();
        domainNameSuffix.add("com");
        domainNameSuffix.add("net");
        domainNameSuffix.add("cn");
        domainNameSuffix.add("org");
        domainNameSuffix.add("gov");
        domainNameSuffix.add("mil");
        domainNameSuffix.add("edu");
        domainNameSuffix.add("www");
        return domainNameSuffix;
    }

    private String reversalWebsite(String website) {
        String[] websitePartArray = website.split("\\.");
        reverseStringArray(websitePartArray);
        StringJoiner joiner = new StringJoiner(".");
        for (int i=0; i<websitePartArray.length && i<2; i++) {
            joiner.add(websitePartArray[i]);
        }
        return joiner.toString();
    }

    private static void reverseStringArray(String[] array) {
        String temp;
        for (int i = 0; i < array.length / 2; i++) {
            temp = array[i];
            array[i] = array[array.length - i - 1];
            array[array.length - i - 1] = temp;
        }
    }

    private String buildPackageName(List<TSdkLibraryPackage> packageList) {
        if (CollectionUtils.isEmpty(packageList)) {
            return "";
        }
        return packageList.stream().map(TSdkLibraryPackage::getPackageName).collect(Collectors.joining("\n"));
    }

    private List<String> getPackageNameList(List<TSdkLibraryPackage> packageList) {
        if (CollectionUtils.isEmpty(packageList)) {
            return Collections.emptyList();
        }
        return packageList.stream().map(TSdkLibraryPackage::getPackageName).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteByTaskId(Long taskId, Long userId, boolean isAdmin) throws IjiamiApplicationException {
        TTask tTask = findById(taskId);
        if (tTask == null) {
            throw new IjiamiApplicationException("检测任务不存在");
        }
        if (tTask.getTaskTatus().equals(DetectionStatusEnum.DETECTION_IN) || tTask.getDynamicStatus().equals(DynamicAutoStatusEnum.DETECTION_AUTO_IN)) {
            throw new IjiamiApplicationException("检测任务进行中，无法删除");
        }
        deleteTaskRecord(tTask, isAdmin);
        cleanStorageTaskData(tTask);
    }

    private void deleteTaskRecord(TTask tTask, boolean isAdmin) {
        taskDAO.delete(tTask, isAdmin); //删除任务

        privacyActionNougatMapper.deleteByTaskId(tTask.getTaskId());
        tPrivacySensitiveWordMapper.deleteByTaskId(tTask.getTaskId());
        privacyOutsideAddressMapper.deleteByTaskId(tTask.getTaskId());
        tPrivacySharedPrefsMapper.deleteByTaskId(tTask.getTaskId());
        privacyLawsDetailMapper.deleteByTaskId(tTask.getTaskId());
        privacyLawsResultMapper.deleteByTaskId(tTask.getTaskId());
        suspiciousSdkMapper.deleteByTaskId(tTask.getTaskId());
        privacyPolicyResultMapper.deleteByTaskId(tTask.getTaskId());
        statisticsService.deleteByTaskId(tTask.getTaskId());
    }

    private void cleanStorageTaskData(TTask task) {
        if (storageLogService.hasTaskFile(task.getTaskId())) {
            // 清除任务文件
            storageLogService.deleteTaskFile(task.getTaskId());
            // 删除数据包，数据包是动态检测引擎传回的，不在文件记录里面
            if (StringUtils.isNotBlank(task.getDataPath())) {
                LOG.info("delete task data taskId={} url={} status={}",
                        task.getTaskId(), task.getDataPath(), singleFastDfsFileService.instance().deleteFile(task.getDataPath()));
            }
        } else {
            // 旧版没有把任务文件记录起来，只能查表删除
            String dfsFlag = "group";
            Set<String> waitingForClean = new HashSet<>();
            waitingForClean.add(task.getDataPath());
            TPrivacyLawsDetail queryDetail = new TPrivacyLawsDetail();
            queryDetail.setTaskId(task.getTaskId());
            privacyPolicyResultMapper.findByTaskId(task.getTaskId())
                    .forEach((detail -> {
                        waitingForClean.add(detail.getFileKey());
                    }));
            privacyLawsDetailMapper.select(queryDetail)
                    .forEach((detail -> {
                        waitingForClean.add(detail.getFileKey());
                    }));
            TDynamicBehaviorImg queryImg = new TDynamicBehaviorImg();
            queryImg.setTaskId(task.getTaskId());
            dynamicBehaviorImgMapper.select(queryImg)
                    .forEach((detail -> {
                        waitingForClean.add(detail.getFileKey());
                    }));
            // 删除调用
            waitingForClean
                    .stream()
                    .filter(StringUtils::isNotBlank)
                    .filter(result -> StringUtils.contains(result, dfsFlag))
                    .forEach(url -> {
                LOG.info("delete task file taskId={} url={} status={}",
                        task.getTaskId(), url, singleFastDfsFileService.instance().deleteFile(url));
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteInTaskId(List<Long> taskIds, Long userId, boolean isAdmin) throws IjiamiApplicationException {
        Example example = new Example(TTask.class);
        example.createCriteria().andIn("taskId", taskIds);
        List<TTask> taskList = taskMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new IjiamiApplicationException("检测任务不存在");
        }
        if (taskList.stream().anyMatch(task -> task.getTaskTatus().equals(DetectionStatusEnum.DETECTION_IN)
                || task.getDynamicStatus().equals(DynamicAutoStatusEnum.DETECTION_AUTO_IN))) {
            throw new IjiamiApplicationException("批量删除中有检测任务进行中，无法删除");
        }
        if (!isAdmin && taskList.stream().anyMatch(task -> !userId.equals(task.getCreateUserId()))) {
            throw new IjiamiApplicationException("批量删除中有您无权删除的检测任务" );
        }
        taskList.forEach(task -> deleteTaskRecord(task, isAdmin));
    }

    @Override
    public List<BigDataVO> findMd5() {
        return taskMapper.findDetectedMd5();
    }

    @Override
    public TTask findDocumentId(String md5) {
        return taskMapper.findByMd5(md5);
    }


    @Override
    public void bigDataCallback(BigDataVO bigDataVO) {
        OkHttpClient client = new OkHttpClient.Builder().build();

        String jsonString = JSON.toJSONString(bigDataVO);

        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonString);
        Request request = new Request.Builder().post(requestBody).url(commonProperties.getProperty("ijiami.bigdata.callback.url")).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                LOG.info("重保云回调失败");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                LOG.info("重保云回调成功");
            }
        });
    }

    @Override
    public void chongqinCallback(ChongqinVO chongqinVO) {
        OkHttpClient client = new OkHttpClient.Builder().build();
        String jsonString = JSON.toJSONString(chongqinVO);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonString);
        Request request = new Request.Builder().post(requestBody).url(commonProperties.getProperty("ijiami.chongqin.callback.url")).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                LOG.error("重庆移动回调失败");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                LOG.info("重庆移动回调成功");
            }
        });
    }


    @Override
    public void updateProgress(ClientUpdateProgressVO clientUpdateProgressVO) {
        TTask task = findById(clientUpdateProgressVO.getTaskId());

        Map<String, Object> paramMap = new HashMap<>();
        // 2.跟新文档
        paramMap.put("_id", task.getApkDetectionDetailId());
        Update update = new Update();
        update.set("dynamicProgress", clientUpdateProgressVO.getProgress());
        update(paramMap, update);
        int progress = Objects.nonNull(clientUpdateProgressVO.getProgress()) ? clientUpdateProgressVO.getProgress().intValue() : 0;
        sendMessageServiceImpl.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_PROGRESS, progress, task);
    }


    @Override
    public Long startDynamicTask(Long taskId, Integer type) {
        return startDynamicTask(taskId, type, DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD.getValue());
    }

    @Override
    public Long startDynamicTask(Long taskId, Integer type, int deviceType) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        Update update = new Update();
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDetectionType(task.getDetectionType());
        // 安卓检测特殊处理
        if (task.getTerminalType().getValue() == TerminalTypeEnum.ANDROID.getValue() &&
                (type == StartDynamicTaskTypeEnum.MANUAL.value || type == StartDynamicTaskTypeEnum.LAW.value || type == StartDynamicTaskTypeEnum.RESTART.value)) {
            updateTask.setDynamicDeviceType(DynamicDeviceTypeEnum.getItem(deviceType));
        }
        if (type == StartDynamicTaskTypeEnum.MANUAL.value) {
            Integer maxDynamicSort = taskMapper.getMaxDynamicWaitSort(TerminalTypeEnum.ANDROID.getValue());
            updateTask.setDynamicTaskSort(maxDynamicSort != null ? maxDynamicSort + 1 : 1);
            updateTask.setDynamicManualStatus(DynamicManualStatusEnum.DETECTION_MANUAL_IN);
            update.set("dynamic_manual_detection_status", DynamicManualStatusEnum.DETECTION_MANUAL_IN.getValue());
            updateDynamicStatus(task, updateTask, update);
        }

        if (type == StartDynamicTaskTypeEnum.LAW.value) {
            if (task.getTerminalType().getValue() == TerminalTypeEnum.ANDROID.getValue()) {
                taskDAO.updateDynamicLawWaiting(task, task.getDeviceSerial());
            } else {
                updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
                update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
                updateDynamicStatus(task, updateTask, update);
            }
        }

        if (type == StartDynamicTaskTypeEnum.RESTART.value) {
            if (task.getTaskTatus().equals(DetectionStatusEnum.DETECTION_STOP)) {
                //删除中断的任务。重新创建改资产任务
                taskDAO.delete(task, true);

                StartTaskParam query = new StartTaskParam();
                query.setUserId(task.getCreateUserId());
                query.setAssetsIds(Collections.singletonList(task.getAssetsId()));
                query.setDynamicDeviceTypeEnum(task.getDynamicDeviceType());
                return startTask(query);
            }
            // 小程序和android，失败的任务重启是从上次失败的地方开始，重启任务时需要保留上一次的阶段数据和检测进度。
            boolean resetSubStatusAndProgress = task.getTerminalType() != TerminalTypeEnum.ANDROID
                    && !task.getTerminalType().isApplet();
            taskDAO.updateDynamicAutoWaiting(task, task.getDeviceSerial(), resetSubStatusAndProgress);
        }

        if (type == StartDynamicTaskTypeEnum.AUTO.value) {
            taskDAO.updateDynamicAutoIn(taskId, StringUtils.EMPTY, task.getDynamicDeviceType());
        }

        if (type == StartDynamicTaskTypeEnum.SANDBOX_START.value) {
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_MOBILE_CONNECT);
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_MOBILE_CONNECT.getValue());
            updateDynamicStatus(task, updateTask, update);
        }

        if (type == StartDynamicTaskTypeEnum.SANDBOX_FAIL.value) {
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_MOBILE_CONNECT_FAILED);
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_MOBILE_CONNECT_FAILED.getValue());
            updateDynamicStatus(task, updateTask, update);
        }

        if (type == StartDynamicTaskTypeEnum.STOP.value) {  //检测中断
            // 终止检测引擎中的检测任务
            xxlDetectWebServer.stop(taskId);
            // 停止快速检测
            messageSendKit.stopXxlDetectionTask(task);
            taskDAO.updateManualFailure(task, "检测超时中断");
        }
        taskSortThread.checkTaskSortListAsSoonASPossible();
        return task.getTaskId();
    }

    private void updateDynamicStatus(TTask task, TTask updateTask, Update update) {
        Map<String, Object> param = new HashMap<>();
        param.put("_id", task.getApkDetectionDetailId());
        // 释放设备
        taskDAO.updateTaskStatus(task, updateTask, param, update);
    }

    @Override
    public TTask findDetectionCompleteByMd5(String md5) {
        return taskMapper.findDetectionCompleteByMd5(md5);
    }


	@Override
	public void updateSdkPermission(List<TPrivacyActionNougat> nougatList) {
		if (CollectionUtils.isEmpty(nougatList)) {
			return;
		}
        TTask task = taskMapper.selectByPrimaryKey(nougatList.get(0).getTaskId());
        if (task == null || task.getTerminalType()!=TerminalTypeEnum.ANDROID) {
            return;
        }
        TaskDetailVO taskDetailVO = commonMongodbService.findByDocumentId(task.getApkDetectionDetailId());
        if (taskDetailVO == null) {
            return;
        }
        if (taskDetailVO.getDetection_result() == null) {
            return;
        }
        JSONArray array = JSONArray.fromObject(taskDetailVO.getDetection_result());
        //拿到SDK列表
        List<SdkVO> sdkVOList = this.getBaseSdkList(taskDetailVO, array, task.getTaskId());

        for (SdkVO sdkVO : sdkVOList) {
        	if (StringUtils.isBlank(sdkVO.getPackageName())) {
        		continue;
        	}
            List<String> packageNameList = Lists.newArrayList(sdkVO.getPackageName().split(","));
        	List<Long> actionIdList = nougatList.stream()
                    .filter(actionNougat -> packageNameList.stream().anyMatch(packageName -> StringUtils.contains(actionNougat.getPackageName(), packageName)))
                    .map(TPrivacyActionNougat::getActionId)
                    .distinct()
                    .collect(Collectors.toList());
        	if (actionIdList.isEmpty()) {
        	    continue;
            }
        	//获取行为数据中关联到的权限编码
        	List<String> permissionCodes = permissionMapper.getPermissionCodes(actionIdList);
        	if (CollectionUtils.isEmpty(permissionCodes)) {
        		continue;
        	}
        	//更新SDK库中的权限编码
        	TSdkLibrary sdkLib = sdkMapper.selectByPrimaryKey(sdkVO.getId());
        	if (Objects.isNull(sdkLib)) {
        		continue;
            }
            if (permissionCodes.stream().anyMatch(code -> !StringUtils.contains(sdkLib.getPermissionCodes(), code))) {
                Set<String> sdks = StringUtils.isBlank(sdkLib.getPermissionCodes()) ? new HashSet<>() : Sets.newHashSet(sdkLib.getPermissionCodes().split(","));
                sdks.addAll(permissionCodes);
                sdkLib.setPermissionCodes(StringUtils.join(sdks, ","));
                sdkMapper.updateByPrimaryKeySelective(sdkLib);
            }
        }
    }

    /**
     * 任务名额预占接口，给前端调用，预占任务名额。
     * 小程序深度检测和快速检测任务只能同时一个在执行，预占成功后前端再去调用idb执行任务，idb发送消息给服务端让任务转入执行中或失败时再去掉预占
     *
     * @param taskId
     * @param deviceType
     * @return
     */
    @Override
    public Long taskPreempted(Long taskId, int deviceType) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        checkTaskStatus(task.getCreateUserId(), task.getTaskId(), task.getTerminalType(), task.getDetectionType());
        taskManagerService.addTaskPreempted(task.getTerminalType(), task.getCreateUserId(), task.getTaskId());
        return taskId;
    }

    private List<SdkVO> getBaseSdkList(TaskDetailVO taskDetailVO, JSONArray detectionResult, Long taskId) {
        List<SdkVO> resultList = new ArrayList<>();
        List<String> sdks = new ArrayList<>();
        for (int i = 0; i < detectionResult.size(); i++) {
            JSONObject json = detectionResult.getJSONObject(i);
            if (json.get("detection_item_id") != null && (json.get("detection_item_id").equals("0104") || json.get("detection_item_id").equals("0107"))) {
                if (json.containsKey("result_content") && !json.get("result_content").equals("")) {
                    try {
                        JSONObject childJson = null;
                        if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.ANDROID.getValue() && json.get("detection_item_id").equals("0104")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList") && childJson.getJSONArray("sdkList") != null) {
                                List<String> finalSdks = sdks;
                                childJson.getJSONArray("sdkList").stream().forEach(sdkInfo -> {
                                    String[] sdkArray = sdkInfo.toString().split("\\s+");
                                    if (sdkArray.length > 0) {
                                        finalSdks.add(sdkArray[0]);
                                    }
                                });
                            }
                            break;
                        } else if (taskDetailVO.getTerminal_type() == TerminalTypeEnum.IOS.getValue() && json.get("detection_item_id").equals("0107")) {
                            childJson = JSONObject.fromObject(json.get("result_content"));
                            if (childJson.containsKey("sdkList")) {
                                sdks = new Gson().fromJson(childJson.getString("sdkList"), new TypeToken<List<String>>() {}.getType());
                                break;

                            }
                        }
                    } catch (Exception ex) {
                        ex.getMessage();
                    }
                }
            }
        }
        List<SdkVO> sdkVOS = getSdkVOById(sdks, taskId, taskDetailVO.getTerminal_type());
        for (SdkVO sdkVO : sdkVOS) {
            if (CollectionUtils.isEmpty(sdkVO.getPackageList())) {
                sdkVO.setPackageName(buildPackageName(sdkVO.getPackageList()));
            }
            resultList.add(sdkVO);
        }
        // 把工具和框架的sdk移到最后面
        List<String> rearSdkTypeNames = Arrays.asList("工具", "框架", "网络");
        Iterator<SdkVO> sdkVOIterator = resultList.iterator();
        List<SdkVO> rearList = new ArrayList<>();
        while (sdkVOIterator.hasNext()) {
            SdkVO sdkVO = sdkVOIterator.next();
            if (rearSdkTypeNames.contains(sdkVO.getTypeName())) {
                sdkVOIterator.remove();
                rearList.add(sdkVO);
            }
        }
        resultList.addAll(rearList);
        return resultList;
    }


	@Override
	public Integer findAppletDynamicDetectionCount(Long userId, Integer terminalType) {
		return taskMapper.selectDynamicTaskByUserId(userId, terminalType, null);
	}
}
