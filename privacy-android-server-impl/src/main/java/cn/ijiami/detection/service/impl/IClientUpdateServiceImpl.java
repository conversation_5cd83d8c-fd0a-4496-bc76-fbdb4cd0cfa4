package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.entity.TClientUpdate;
import cn.ijiami.detection.mapper.TClientUpdateMapper;
import cn.ijiami.detection.service.api.IClientUpdateService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-08-27 10:43
 */
@Service
public class IClientUpdateServiceImpl implements IClientUpdateService {

    private final TClientUpdateMapper clientUpdateMapper;

    public IClientUpdateServiceImpl(TClientUpdateMapper clientUpdateMapper) {
        this.clientUpdateMapper = clientUpdateMapper;
    }

    @Override
    public TClientUpdate checkUpdate(Integer currentVersion) {
        List<TClientUpdate> versions = clientUpdateMapper.findNewVersion(currentVersion);
        if (CollectionUtils.isEmpty(versions)) {
            return null;
        }

        Set<String> files = new HashSet<>();
        StringBuilder description = new StringBuilder();
        Double versionCode = 0.0;
        String versionName = "2.0";
        String baseUrl = "";
        Date updateTime = null;
        for (TClientUpdate version : versions) {
            versionCode = version.getVersionCode();
            versionName = version.getVersionName();
            baseUrl = version.getBaseUrl();
            updateTime = version.getUpdateTime();

            String[] fileList = version.getFileList().split(",");
            files.addAll(Arrays.asList(fileList));

            description.append(version.getVersionCode());
            description.append(": ");
            description.append(version.getDescription());
            description.append("\n");
        }

        TClientUpdate clientUpdate = new TClientUpdate();
        clientUpdate.setVersionName(versionName);
        clientUpdate.setVersionCode(versionCode);
        clientUpdate.setBaseUrl(baseUrl);
        clientUpdate.setFileList(String.join(",", files));
        clientUpdate.setDescription(description.toString());
        clientUpdate.setUpdateTime(updateTime);
        return clientUpdate;
    }
}
