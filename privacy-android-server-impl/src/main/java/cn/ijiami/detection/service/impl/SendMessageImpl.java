package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.message.MessageNotificationSendKit;
import cn.ijiami.detection.message.param.MessageReceiveParam;
import cn.ijiami.detection.message.param.SendBroadcastMessageParam;
import cn.ijiami.detection.message.param.SendHiddenMessageToUserParam;
import cn.ijiami.detection.message.param.SendNoticeToUserParam;
import cn.ijiami.detection.message.enums.MessageNotificationEnum;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.AiDetectLoginStatusEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.RealTimeLogCategory;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.IPrivacyOutsideAddressService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.utils.StfUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import net.sf.json.JSONObject;

/**
 * 消息推送实现
 *
 * <AUTHOR>
 */
@Service
public class SendMessageImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements ISendMessageService {

    private static final Logger LOG = LoggerFactory.getLogger(SendMessageImpl.class);

    private static final Integer TASK_STATUS_SENDING = 1;
    /**
     * 任务状态刷新消息合并时间，一定时间内的消息合并，只发送一次
     */
    private static final long TASK_STATUS_MSG_MERGE_PER_TIME = 500L;
    private static final String DEFAULT_TOPIC_NAME = "/default"; // Define inlined or in a constant class

    @Autowired
    private TTaskMapper taskMapper;

    @Lazy
    @Autowired
    private IPrivacyOutsideAddressService iPrivacyOutsideAddressService;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private MessageNotificationSendKit messageNotificationSendKit;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Autowired
    private DetectionConfigService detectionConfigService;

    @Autowired
    private TaskDAO taskDAO;
    
    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    /**
     * 旧版的消息发送方法，会导致任务数据的变更
     * @param message
     * @return
     */
    @Deprecated
    @Override
    public synchronized boolean sendMessage(String message) {
        try {
            JSONObject json = JSONObject.fromObject(message);

            if (!json.containsKey("messageType")) {
                return false;
            }

            long taskId = Long.parseLong(json.get("taskId").toString());
            LOG.info("taskId:" + taskId);

            // 1.查询任务
            TTask task = taskMapper.selectByPrimaryKey(taskId);

            if (task == null) {
                throw new IjiamiCommandException("任务不存在,更新操作失败！");
            }
            // messageType 消息类型(1.检测结果2.异常中断消息3.检测完成消息4.解压、反编译5.解压反编译完成)
            try {
                String message_info = json.getString("message");
                if (message_info != null && message.contains("progress")) {
                    JSONObject message_json = JSONObject.fromObject(message_info);
                    double progress = Double.parseDouble(message_json.get("progress").toString());
                    //临时改动-更新检测时间
                    if (progress > 0 && task.getTaskTatus().getValue() == DetectionStatusEnum.DETECTION_NOSTART.getValue()) {
                        taskDAO.updateStaticIn(task);
                    }
                }
            } catch (Exception e) {
                LOG.error("消息推送服务，获取message失败，参数信息：{},异常信息：{}", message, e.getMessage());
            }

            // 完成
            if (json.get("messageType").equals(BroadcastMessageTypeEnum.DETECTION_FINISH.getValue())) {
                TaskDetailVO taskDetailVO = findById(task.getApkDetectionDetailId(), "taskDetailVO");
                if (taskDetailVO == null) {
                    throw new IjiamiCommandException("检测详情不存在,更新操作失败！");
                }
                try {
                    if (taskDetailVO.getTemplate_id() == 1) {//传统的手动检测
                        // 静态检测完成，更新t_privacy_outside_address表数据信息
                        iPrivacyOutsideAddressService.updateOutSideWhenStaticComplete(task.getTaskId(), task.getApkDetectionDetailId());
                        taskDAO.updateStaticSuccess(task.getTaskId());
                        LOG.info("修改状态成功");
                    } else {
                        Update update = new Update();
                        // 1.查询mongodb文档
                        Map<String, Object> paramMap = new HashMap<>();
                        paramMap.put("_id", task.getApkDetectionDetailId());
                        update.set("detection_version", commonProperties.getProperty("detection_version")); //升级的版本号
                        update(paramMap, update);
                    }
                } catch (Exception e) {
                    LOG.error("状态修改失败", e);
                }
            }
            //发送广播消息
            this.sendBroadcast(message, task); // This now calls the refactored sendBroadcast

            LOG.info("消息发送成功");
            return true;
        } catch (Exception e) {
            LOG.error("消息发送失败", e);
            return false;
        }
    }

    @Override
    public void sendTaskProgressBroadcast(BroadcastMessageTypeEnum typeEnum, int progress, TTask task, long delayMillis) {
        if (delayMillis <= 0) {
            sendTaskProgressBroadcast(typeEnum, progress, task);
        } else {
            executorServiceHelper.schedule(() -> {
                sendTaskProgressBroadcast(typeEnum, progress, task);
            }, delayMillis, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public void sendTaskProgressBroadcast(BroadcastMessageTypeEnum typeEnum, int progress, TTask task) {
        String key = PinfoConstant.CACHE_TASK_STATUS_REFRESH + typeEnum.getValue() + ":" + task.getTerminalType().getValue();
        Integer flag = cacheService.getInt(key);
        // 避免前端接收到过快刷新，做消息限制，1秒只发送一次页面刷新消息
        if (TASK_STATUS_SENDING.equals(flag) && progress != 0) {
            LOG.info("有进度消息在广播中，不进行发送");
            return;
        }
        cacheService.setInt(key, TASK_STATUS_SENDING, TASK_STATUS_MSG_MERGE_PER_TIME, TimeUnit.MILLISECONDS);
        executorServiceHelper.schedule(() -> {
            try {
                sendBroadcast(makeTaskProgressMsg(typeEnum, progress), task);
            } finally {
                cacheService.delete(key);
            }
        }, TASK_STATUS_MSG_MERGE_PER_TIME, TimeUnit.MILLISECONDS);
    }

    @Override
    public void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task, long delayMillis) {
        if (delayMillis <= 0) {
            sendTaskStatusBroadcast(typeEnum, describe, task);
        } else {
            executorServiceHelper.schedule(() -> {
                sendTaskStatusBroadcast(typeEnum, describe, task);
            }, delayMillis, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        if (BroadcastMessageTypeEnum.DETECTION_DYNAMIC_RUNNING == typeEnum || BroadcastMessageTypeEnum.DETECTION_RUNNING == typeEnum) {
            sendTaskProgressBroadcast(typeEnum, 0, task);
        } else {
            sendBroadcast(makeTaskStatusMsg(typeEnum, describe), task);
        }
    }

    @Override
    public void sendTaskStatusMessage(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        try {
            LOG.info("TaskId:{}", task.getTaskId());
            JSONObject json = JSONObject.fromObject(makeTaskStatusMsg(typeEnum, describe));
            SendNoticeToUserParam param = buildUserMessageParamForTaskStatus(json, task, typeEnum);
            if (param != null) {
                sendMessageToUser(param);
            }
            LOG.info("TaskId:{} 发送完成", task.getTaskId());
        } catch (Exception e) {
            LOG.error(String.format("TaskId:%d 发送失败",  task.getTaskId()), e);
        }
    }

    // Renamed and refactored to create a param DTO for MessageServiceApi
    private SendNoticeToUserParam buildUserMessageParamForTaskStatus(JSONObject json, TTask task, BroadcastMessageTypeEnum typeEnum) {
        SendNoticeToUserParam param = new SendNoticeToUserParam();
        param.setTopicName(getTerminalLogTag(task.getTerminalType()));
        param.setNotificationType(MessageNotificationEnum.INFO);
        param.setHiddenEnum(HiddenEnum.HIDDEN.name());
        
        json.put("terminalType", task.getTerminalType().getValue());
        json.put("taskId", task.getTaskId());
        json.put("deviceType", task.getDynamicDeviceType().getValue());
        json.put("userId", task.getCreateUserId());
        if(typeEnum == BroadcastMessageTypeEnum.DETECTION_AI_DETECT_NOT_LOGGED_IN) {
        	json.put("deviceUrl", StfUtils.getDeviceIframeUrl(task, commonProperties, task.getDeviceSerial()));
        }
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        if(extend != null) {
            json.put("model", extend.getModel());
            json.put("version", extend.getVersion());
        }
        
        //如果遍历前登录通知，已经确认启动自动化遍历，就不再发送消息
        if(extend != null && typeEnum == BroadcastMessageTypeEnum.DETECTION_AI_DETECT_NOT_LOGGED_IN && 
        		extend.getAiDetectLoginStatus() != null && extend.getAiDetectLoginStatus() == AiDetectLoginStatusEnum.LOGGED_IN){
        	return null;
        }
        
        param.setMessage(new Gson().toJson(json));
        
        // 日志的发送者是自己
        param.setUserId(task.getCreateUserId());

        // 日志的接受者是自己
        List<MessageReceiveParam> receiveParams = new ArrayList<>();
        MessageReceiveParam receiveParam = new MessageReceiveParam();
        receiveParam.setReceiveUserId(task.getCreateUserId());
        receiveParams.add(receiveParam);
        param.setReceiveParams(receiveParams);

        populateMessageParamDetails(json, param);
        return param;
    }

    private String makeTaskStatusMsg(BroadcastMessageTypeEnum typeEnum, String describe) {
        JSONObject msg = new JSONObject();
        msg.put("messageType", typeEnum.getValue());
        msg.put("describe", describe);
        return msg.toString();
    }

    private String makeTaskProgressMsg(BroadcastMessageTypeEnum typeEnum, int progress) {
        JSONObject msg = new JSONObject();
        msg.put("messageType", typeEnum.getValue());
        msg.put("progress", progress);
        return msg.toString();
    }

    /**
     * 发送ios扫码检测消息给前端
     * @param message
     * @param userId
     * @param notificationId 浏览器标签页通知id
     */
    @Override
    public void sendScanQRCodeMessage(String message, Long userId, String notificationId) {
        sendHiddenMessageToUser("ios_scan_qrcode_connection", message, userId, notificationId);
    }

    /**
     * ip检测消息给前端
     * @param message
     * @param userId
     * @param notificationId 浏览器标签页通知id
     */
    @Override
    public void sendIpAddressCheckMessage(String message, Long userId, String notificationId) {
        sendHiddenMessageToUser("ip_address_check", message, userId, notificationId);
    }

    @Override
    public void sendSystemNoticeMessage(String message, Long userId, String notificationId) {
        sendHiddenMessageToUser("system_notice", message, userId, notificationId);
    }

    /**
     * 不显示提示框的消息给前端，需要指定用户和标签页通知id，如果没有标签页通知id那么会发给所有标签页
     * @param type
     * @param message
     * @param userId
     * @param notificationId 浏览器标签页通知id
     */
    private void sendHiddenMessageToUser(String type, String message, Long userId, String notificationId) {
        SendHiddenMessageToUserParam param = new SendHiddenMessageToUserParam();
        param.setType(type);
        param.setMessage(message);
        param.setUserId(userId);
        param.setNotificationId(notificationId);

        try {
            messageNotificationSendKit.sendHiddenMessageToUser(param);
        } catch (Exception e) {
            LOG.error("发送隐藏消息失败 type={} userId={} notificationId={}: {}", type, userId, notificationId, e.getMessage(), e);
        }
    }

    /**
     * 发送广播消息，所有用户都会接到
     * @param message 消息内容 (JSON string)
     * @param task 任务信息
     */
    @Override
    public void sendBroadcast(String message, TTask task) {
        try {
            JSONObject json = JSONObject.fromObject(message);
            SendBroadcastMessageParam param = buildBroadcastMessageParam(json, task);
            messageNotificationSendKit.sendBroadcastMessage(param);
        } catch (Exception e) {
            LOG.error("发送广播消息失败 taskId={}: {}", task != null ? task.getTaskId() : "null", e.getMessage(), e);
        }
    }

    /**
     * 发送检测任务日志给前端
     *
     * @param message (JSON String)
     * @param data    任务数据
     */
    @Override
    public void sendTaskDynamicLogMessage(String message, DynamicTaskContext data) {
        sendTaskDynamicLogMessage(JSONObject.fromObject(message), data);
    }

    /**
     * 发送检测任务日志给前端
     *
     * @param data    任务数据
     */
    @Override
    public void sendTaskDynamicLogMessage(JSONObject json, DynamicTaskContext data) {
        try {
            LOG.info("TaskId:{} 发送notificationId={}", data.getTaskId(), data.getNotificationId());
            sendMessageToUser(buildDetectionLogMessageParam(json, RealTimeLogCategory.DYNAMIC, data));
            LOG.info("TaskId:{} 发送完成", data.getTaskId());
        } catch (Exception e) {
            LOG.error(String.format("TaskId:%d 发送失败",  data.getTaskId()), e);
        }
    }

    /**
     * 发送检测任务网络日志给前端
     * @param json
     * @param data 任务数据
     */
    @Override
    public void sendTaskNetLogMessage(JSONObject json, DynamicTaskContext data) {
        try {
            LOG.info("TaskId:{} 发送notificationId={}", data.getTaskId(), data.getNotificationId());
            SendNoticeToUserParam param = buildDetectionLogMessageParam(json, RealTimeLogCategory.NET, data);
            sendMessageToUser(param);
        } catch (Exception e) {
            LOG.error("发送网络日志失败 TaskId:{}: {}", data.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 发送检测任务传感器日志给前端
     * @param json
     * @param data 任务数据
     */
    @Override
    public void sendTaskSensorLogMessage(JSONObject json, DynamicTaskContext data) {
        try {
            LOG.info("TaskId:{} 发送notificationId={}", data.getTaskId(), data.getNotificationId());
            SendNoticeToUserParam param = buildDetectionLogMessageParam(json, RealTimeLogCategory.SENSOR, data);
            sendMessageToUser(param);
            LOG.info("TaskId:{} 发送完成", data.getTaskId());
        } catch (Exception e) {
            LOG.error(String.format("TaskId:%d 发送失败",  data.getTaskId()), e);
        }
    }

    private SendBroadcastMessageParam buildBroadcastMessageParam(JSONObject json, TTask task) {
        SendBroadcastMessageParam param = new SendBroadcastMessageParam();
        // param.setClassifyEnum(MessageClassifyEnum.BROADCAST); // Handled by API endpoint
        param.setTopicName(getTerminalLogTag(task.getTerminalType()));
        param.setNotificationType(MessageNotificationEnum.INFO); // Default, or derive from json as before
        param.setHiddenEnum(HiddenEnum.HIDDEN.name());
        
        json.put("terminalType", task.getTerminalType().getValue());
        json.put("taskId", task.getTaskId());
        json.put("deviceType", task.getDynamicDeviceType().getValue()); 
    	TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
    	if(extend != null) {
    		json.put("model", extend.getModel());
            json.put("version", extend.getVersion());
    	}
        
        // Adapt buildMessageSendVO logic here for param
        // This method used to set messageCode, messageTitle etc.
        // These are now part of the 'type' and 'title' in SendBroadcastMessageParam
        // or within the messageContent (json) itself.
        // For now, we assume messageContent contains all necessary details or the microservice handles it.
        String messageContent = new Gson().toJson(json);
        param.setMessageContent(messageContent);
        // param.setSaveData(false); // Handled by microservice if needed

        // Populate title and type for the broadcast param based on json's messageType
        populateBroadcastMessageParamDetails(json, param);

        return param;
    }

    private SendNoticeToUserParam buildDetectionLogMessageParam(JSONObject json, RealTimeLogCategory category, DynamicTaskContext data) {
        SendNoticeToUserParam param = new SendNoticeToUserParam();
        String logTag = getLogTag(category, data);
        String topicName = StringUtils.isBlank(data.getNotificationId()) ? logTag : "/" + data.getNotificationId() + logTag;
        param.setTopicName(topicName);
        param.setNotificationType(MessageNotificationEnum.INFO);
        param.setHiddenEnum(HiddenEnum.HIDDEN.name());
        json.put("terminalType", data.getTerminalType().getValue());
        json.put("taskId", data.getTaskId());
        param.setMessage(json.toString());
        
        // 日志的发送者是自己
        param.setUserId(data.getCreateUserId());
        // 日志的接受者是自己
        List<MessageReceiveParam> receiveParams = new ArrayList<>();
        MessageReceiveParam receiveParam = new MessageReceiveParam();
        receiveParam.setReceiveUserId(data.getCreateUserId());
        receiveParams.add(receiveParam);
        param.setReceiveParams(receiveParams);
        populateMessageParamDetails(json, param);
        return param;
    }

    @NotNull
    private static String getLogTag(RealTimeLogCategory category, DynamicTaskContext data) {
        String logTag;
        if (category == RealTimeLogCategory.NET) {
            logTag = "/net_log";
        } else if (category == RealTimeLogCategory.SENSOR) {
            logTag = "/sensor_log";
        } else {
            return getTerminalLogTag(data.getTerminalType());
        }
        return logTag;
    }

    private static String getTerminalLogTag(TerminalTypeEnum type) {
        String logTag;
        if (type.getValue() == TerminalTypeEnum.IOS.getValue()) {
            logTag = "/ios_log";
        } else if (type.getValue() == TerminalTypeEnum.ANDROID.getValue()) {
            logTag = "/android_log";
        } else if (type.getValue() == TerminalTypeEnum.WECHAT_APPLET.getValue()) {
            logTag = "/applet_log";
        } else if (type.getValue() == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
            logTag = "/alipay_applet_log";
        } else if (type.getValue() == TerminalTypeEnum.HARMONY.getValue()) {
            logTag = "/harmony_log";
        } else {
            throw new IjiamiRuntimeException("不支持的消息类型 terminalType=" + type.getValue());
        }
        return logTag;
    }

    private void populateMessageParamDetails(JSONObject json, SendNoticeToUserParam param) {
        LOG.info("构建用户通知参数，原始JSON消息：{}", json);
        if (json.containsKey("messageType")) {
            int messageType = json.getInt("messageType");
            switch (messageType) {
                case 1: // 检测中
                    param.setCode("result");
                    param.setTitle("检测结果");
                    break;
                case 3: // 检测完成
                    param.setCode("complete");
                    param.setTitle("检测完成");
                    break;
                case 4: // 反编译中
                    param.setCode("dex");
                    param.setTitle("解压、反编译结果");
                    break;
                case 5: // 反编译结束
                    param.setCode("dex_finish");
                    param.setTitle("解压、反编译结束");
                    break;
                case 6: // 工具初始化
                    param.setCode("init");
                    param.setTitle("工具初始化");
                    break;
                case 7: // 图片检测
                    param.setCode("image");
                    param.setTitle("图片检测");
                    break;
                case 8: // 文字检测
                    param.setCode("text");
                    param.setTitle("文字检测");
                    break;
                case 9: // 基本检测项
                    param.setCode("base");
                    param.setTitle("基本检测项");
                    break;
                default:
                    // Potentially set a default type/title or log a warning
                    param.setCode("unknown");
                    param.setTitle("未知消息");
                    break;
            }
        }
    }

    private void populateBroadcastMessageParamDetails(JSONObject json, SendBroadcastMessageParam param) {
        LOG.info("构建广播参数，原始JSON消息：{}", json);
        if (json.containsKey("messageType")) {
            int messageType = json.getInt("messageType");
            switch (messageType) {
                 case 1: // 检测中
                    param.setType("result");
                    param.setTitle("检测结果");
                    break;
                case 3: // 检测完成
                    param.setType("complete");
                    param.setTitle("检测完成");
                    break;
                case 4: // 反编译中
                    param.setType("dex");
                    param.setTitle("解压、反编译结果");
                    break;
                case 5: // 反编译结束
                    param.setType("dex_finish");
                    param.setTitle("解压、反编译结束");
                    break;
                case 6: // 工具初始化
                    param.setType("init");
                    param.setTitle("工具初始化");
                    break;
                case 7: // 图片检测
                    param.setType("image");
                    param.setTitle("图片检测");
                    break;
                case 8: // 文字检测
                    param.setType("text");
                    param.setTitle("文字检测");
                    break;
                case 9: // 基本检测项
                    param.setType("base");
                    param.setTitle("基本检测项");
                    break;       
                default:
                    param.setType("unknown");
                    param.setTitle("未知消息");
                    break;
            }
        }
    }


    /**
     * 给/user/default的订阅者发送通知消息
     * @param notificationType 通知类型，不同类型前端会弹出不同样式的通知框
     * @param type 消息类型
     * @param message 消息文本
     * @param userId 要发给消息的用户ID
     * @return
     */
    @Override
    public boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, Long userId) {
        try {
            SendNoticeToUserParam param = new SendNoticeToUserParam();
            // param.setClassifyEnum(MessageClassifyEnum.USER); // Handled by API endpoint
            param.setCode(type);
            param.setMessage(message); 
            param.setTitle(message);   
            param.setNotificationType(notificationType);
            param.setUserId(userId);         
            
            List<MessageReceiveParam> receiveParams = new ArrayList<>();
            MessageReceiveParam receiveParam = new MessageReceiveParam();
            receiveParam.setReceiveUserId(userId);
            receiveParams.add(receiveParam);
            param.setReceiveParams(receiveParams);

            return sendMessageToUser(param); // Calls the refactored internal method
        } catch (Exception e) {
            LOG.error("发送通知失败 userId={}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, String title, Long userId, HiddenEnum hiddenEnum) {
        return sendNotice(notificationType, type, message, title, userId, hiddenEnum, StringUtils.EMPTY);
    }

    /**
     *
     * @param notificationType 通知类型，不同类型前端会弹出不同样式的通知框
     * @param type 消息类型
     * @param message 消息文本
     * @param title 标题
     * @param userId 要发给消息的用户ID
     * @param hiddenEnum 是否弹出通知框
     * @param topicName 要发送的话题，前端订阅这个话题就能接到消息
     * @return
     */
    @Override
    public boolean sendNotice(MessageNotificationEnum notificationType, String type, String message, String title, Long userId, HiddenEnum hiddenEnum, String topicName) {
        try {
            SendNoticeToUserParam param = new SendNoticeToUserParam();
            param.setCode("2");
            param.setMessage(message);
            param.setTitle(title);
            param.setHiddenEnum(hiddenEnum.name());
            param.setNotificationType(HiddenEnum.HIDDEN == hiddenEnum ? null : notificationType);
            param.setUserId(userId);
            param.setTopicName(topicName);
            
            List<MessageReceiveParam> receiveParams = new ArrayList<>();
            MessageReceiveParam receiveParam = new MessageReceiveParam();
            receiveParam.setReceiveUserId(userId);
            receiveParams.add(receiveParam);
            param.setReceiveParams(receiveParams);
            
            boolean success = sendMessageToUser(param);
            if (success) {
                 LOG.info("通知发送成功 message={} topicName={} userId={}", message, topicName, userId); // Changed error to info for success
            } else {
                 LOG.error("通知发送失败 message={} topicName={} userId={}", message, topicName, userId);
            }
            return success;
        } catch (Throwable e) {
            LOG.error("通知发送失败 message={} topicName={} userId={} e={} ", message, topicName, userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送只有标题的通知到指定浏览器标签页，因为标题再前端的显示比较明显，所以有些消息把内容放在标题中
     * @param notificationType 通知类型，不同类型前端会弹出不同样式的通知框
     * @param type 消息类型
     * @param title 标题
     * @param userId 要发给消息的用户ID
     * @param notificationId 浏览器标签页通知id
     * @return
     */
    @Override
    public boolean sendNoticeToBrowserTab(MessageNotificationEnum notificationType, String type, String title, Long userId, String notificationId) {
        return sendNoticeToBrowserTab(notificationType, type, StringUtils.EMPTY, title, userId, HiddenEnum.SHOW, notificationId);
    }

    /**
     * 发送通知到指定浏览器标签页
     * @param notificationType 通知类型，不同类型前端会弹出不同样式的通知框
     * @param type 消息类型
     * @param message 消息文本
     * @param title 标题
     * @param userId 要发给消息的用户ID
     * @param hiddenEnum 是否弹出通知框
     * @param notificationId 浏览器标签页通知id
     * @return
     */
    @Override
    public boolean sendNoticeToBrowserTab(MessageNotificationEnum notificationType, String type, String message, String title, Long userId, HiddenEnum hiddenEnum, String notificationId) {
        if (StringUtils.isEmpty(notificationId)) {
            LOG.info("notificationId 为空，无法发送到指定标签页，发送到指定用户");
            return sendNotice(notificationType, type, message, title, userId, hiddenEnum, DEFAULT_TOPIC_NAME); 
        } else {
            return sendNotice(notificationType, type, message, title, userId, hiddenEnum, notificationId + DEFAULT_TOPIC_NAME);
        }
    }

    private boolean sendMessageToUser(SendNoticeToUserParam param) {
		if (param == null) {
			LOG.error("sendMessageToUser: SendNoticeToUserParam为空!");
            return false;
		}

        try {
            messageNotificationSendKit.sendNoticeToUser(param);
            return true;
        } catch (Exception e) {
            LOG.error("发送用户消息失败 param={}: {}", param, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送ios砸壳进度更新通知
     * @param message
     * @param userId
     * @param notificationId 浏览器标签页通知id
     */
    @Override
    public void sendShellSmashingProgressMessage(String message, Long userId, String notificationId) {
        sendHiddenMessageToUser("ios_shell_smashing_progress", message, userId, notificationId);
    }

}
