package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.DetectionDeviceVO;
import cn.ijiami.detection.VO.DeviceUdid;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.VO.StfDeviceTypeVO;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.UseDeviceVO;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.stf.StfDeviceStatusEnum;
import cn.ijiami.detection.idb.IdbDevice;
import cn.ijiami.detection.idb.IdbDeviceList;
import cn.ijiami.detection.service.api.AppletIdbDetectionService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.DeviceManagerService;
import cn.ijiami.detection.service.api.IHitShellService;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.IpaShellInfoUtils;
import cn.ijiami.detection.utils.PlatformConstant;
import cn.ijiami.detection.utils.StfUtils;

@Service
public class DeviceManagerServiceImpl implements DeviceManagerService{
	
	private static Logger logger = LoggerFactory.getLogger(DeviceManagerServiceImpl.class);
	
	@Autowired
    IjiamiCommonProperties commonProperties;
	@Autowired
	IHitShellService hitShellService;

	@Autowired
	private	DetectionConfigService detectionConfigService;

	@Lazy
	@Autowired
	AppletIdbDetectionService appletIdbDetectionService;
	
	private static final String GET_All_DEVICE = "%s/api/device/getAllDevice";
	private static final String ADD_DEVICE = "%s/api/device/addDevice/%s";
	private static final String DEL_DEVICE = "%s/api/device/delDevice/%s";
	private static final String REMOVE_All_DEVICE = "%s/api/device/removeAll";

	@Override
	public List<StfDeviceInfo> getFreeDeviceInfoList() {
		List<StfDeviceInfo> freeList = new ArrayList<>();
		List<StfDeviceInfo> list = StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
    	if(list == null || list.size() == 0) {
    		return null;
    	}
		list.forEach(device->{
    		if(StfDeviceStatusEnum.STF_DEVICE_FREE == device.getStatus()){
    			freeList.add(device);
    		}
    	});
		return freeList;
	}
	
	@Override
	public StfDeviceTypeVO getOSTypeList(Integer terminalType, String model, String versoin, IUser currentUser) {
		StfDeviceTypeVO vo = new StfDeviceTypeVO();
		//Android
		if(terminalType == null
				|| terminalType == TerminalTypeEnum.ANDROID.getValue()
				|| terminalType == TerminalTypeEnum.WECHAT_APPLET.getValue()
				|| terminalType == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
			List<StfDeviceInfo> freeList = StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
			if(freeList== null || freeList.size() == 0) {
				return vo;
			}
			
			List<StfDeviceInfo> freeListFree = freeList.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() != StfDeviceStatusEnum.STF_DEVICE_DEAD).collect(Collectors.toList());
			
			// 过滤已经指定用户的设备
			checkDeviceInfo(freeListFree, currentUser);
			
			if(StringUtils.isBlank(model) && StringUtils.isBlank(versoin)) {
				Set<String>  setModelList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
				Set<String>  setVersionList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getOsVersion()!=null).map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				vo.setVersion(new ArrayList<>(setVersionList));
				return  vo;
			}
			
			if(StringUtils.isBlank(model) && StringUtils.isNotBlank(versoin)) {
				Set<String>  setModelList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null && stfDeviceInfo.getOsVersion().equals(versoin)).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				
				Set<String>  setVersionList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getOsVersion()!=null).map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
				vo.setVersion(new ArrayList<>(setVersionList));
			}
			
			if(StringUtils.isNotBlank(model) && StringUtils.isBlank(versoin)) {
				Set<String>  setModelList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				
				Set<String>  setVersionList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getOsVersion()!=null && stfDeviceInfo.getModel().equals(model)).map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
				vo.setVersion(new ArrayList<>(setVersionList));
			}
			
			if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(versoin)) {
				Set<String>  setModelList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null && stfDeviceInfo.getModel().equals(model) && stfDeviceInfo.getOsVersion().equals(versoin)).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				
				Set<String>  setVersionList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null && stfDeviceInfo.getModel().equals(model) && stfDeviceInfo.getOsVersion().equals(versoin)).map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
				vo.setVersion(new ArrayList<>(setVersionList));
			}
		}else if(terminalType == TerminalTypeEnum.HARMONY.getValue()){
			return vo;
		} else {
			
			IdbDeviceList iosDeviceList = hitShellService.getDeviceList();
			logger.info("获取设备："+JSON.toJSONString(iosDeviceList));
			List<IdbDevice> deviceList = iosDeviceList.getDeviceList();
			if(deviceList == null || deviceList.size() == 0) {
				return vo;
			}
			
			if(StringUtils.isBlank(model) && StringUtils.isBlank(versoin)) {
				Set<String>  setModelList = deviceList.stream().filter(device -> device.getProductType()!=null).map(IdbDevice::getProductType).collect(Collectors.toSet());
				Set<String>  setVersionList = deviceList.stream().filter(device -> device.getProVersion()!=null).map(IdbDevice::getProVersion).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				vo.setVersion(new ArrayList<>(setVersionList));
			}
			
			if(StringUtils.isBlank(model) && StringUtils.isNotBlank(versoin)) {
				Set<String>  setModelList = deviceList.stream().filter(device -> device.getProductType()!=null && device.getProVersion().equals(versoin)).map(IdbDevice::getProductType).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				
				Set<String>  setVersionList = deviceList.stream().filter(device -> device.getProVersion()!=null).map(IdbDevice::getProVersion).collect(Collectors.toSet());
				vo.setVersion(new ArrayList<>(setVersionList));
			}
			
			if(StringUtils.isNotBlank(model) && StringUtils.isBlank(versoin)) {
				Set<String>  setModelList = deviceList.stream().filter(device -> device.getProductType()!=null).map(IdbDevice::getProductType).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				List<String> versions = IpaShellInfoUtils.iphoneGenerations(model);
				if(versions != null && versions.size()>0) {
					Set<String>  setVersionList = deviceList.stream().filter(device -> device.getProVersion()!=null && versions.contains(device.getProVersion())).map(IdbDevice::getProVersion).collect(Collectors.toSet());
					vo.setVersion(new ArrayList<>(setVersionList));
				}
			}
			
			if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(versoin)) {
				Set<String>  setModelList = deviceList.stream().filter(device -> device.getProductType()!=null && device.getProVersion().equals(versoin)).map(IdbDevice::getProductType).collect(Collectors.toSet());
				vo.setModel(new ArrayList<>(setModelList));
				
				Set<String>  setVersionList = deviceList.stream().filter(device -> device.getProductType()!=null &&  device.getProVersion().equals(versoin)).map(IdbDevice::getProVersion).collect(Collectors.toSet());
				vo.setVersion(new ArrayList<>(setVersionList));
			}
			
			if(vo.getModel()!= null && vo.getModel().size()>0) {
				List<String> list = vo.getModel();
				List<String> models = new ArrayList<>();
				for (String string : list) {
					String m = IpaShellInfoUtils.iphoneModels(string);
					if(StringUtils.isNoneBlank(m)) {
						models.add(m);
					} 
				}
				vo.setModel(models);
			}
		}
		return vo;
	}
	
	/**
	 * 校验设备是否指定用户，如果制定了设备就过滤
	 * @param freeListFree
	 */
	private void checkDeviceInfo(List<StfDeviceInfo> freeListFree, IUser currentUser) {
	    try {
			Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
			if (detectionConfigMap == null){
				return;
			}
			
			TDetectionConfigVO userConfig = detectionConfigMap.get(currentUser.getUserId());
			//如果是指定设备，就只返回用户指定的设备
			if (userConfig != null && StringUtils.isNotBlank(userConfig.getAndroidDeviceIps())) {
				Set<String> userDevices = Arrays.stream(userConfig.getAndroidDeviceIps().split(","))
                        .collect(Collectors.toSet());
				freeListFree.removeIf(device -> !userDevices.contains(device.getDeviceSerial()));
			}else {
				// 构建被占用的设备IP集合
				Set<String> inUseDeviceIps = detectionConfigMap.values().stream()
				    .map(TDetectionConfigVO::getAndroidDeviceIps)
				    .filter(StringUtils::isNotBlank)
				    .flatMap(ips -> Arrays.stream(ips.split(",")))
				    .collect(Collectors.toCollection(HashSet::new));

				// 从占用集合中移除用户专用设备
				if (userConfig != null && StringUtils.isNotBlank(userConfig.getAndroidDeviceIps())) {
				    Set<String> userDevices = Arrays.stream(userConfig.getAndroidDeviceIps().split(","))
				                                  .collect(Collectors.toSet());
				    inUseDeviceIps.removeAll(userDevices);
				}
				// 过滤已经指定设备列表
				freeListFree.removeIf(device -> inUseDeviceIps.contains(device.getDeviceSerial()));
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
	}
	
	
	@Override
	public String addDevice(Long taskId,String device, TerminalTypeEnum terminalType){
		String enabled = commonProperties.getProperty("ijiami.stf.console.enabled");
		if(StringUtils.isNotBlank(enabled) && StringUtils.equals(enabled, "false")) {
			return "";
		}
		if(StringUtils.isBlank(device) || device.contains("null")) {
			return "";
		}
		logger.info("设备加上占用标识 taskId={}", taskId);
		PlatformConstant.device_map.put(String.valueOf(taskId), device);
		String platformmark = commonProperties.getProperty("ijiami.platformmark");
		device = platformmark+"_"+taskId+"_"+terminalType.getName()+"_"+device;
		return HttpUtils.httpGetString(String.format(ADD_DEVICE, commonProperties.getProperty("ijiami.stf.console"),device), null, 3);
	}
	
	@Override
	public String delDevice(Long taskId, String device, TerminalTypeEnum terminalType){
		String enabled = commonProperties.getProperty("ijiami.stf.console.enabled");
		if(StringUtils.isNotBlank(enabled) || StringUtils.equals(enabled, "false")) {
			return "";
		}
		if(StringUtils.isBlank(device)) {
			device =  PlatformConstant.device_map.get(String.valueOf(taskId));
		}
		PlatformConstant.device_map.remove(String.valueOf(taskId));
		if(StringUtils.isBlank(device)) {
			return "";
		}
		logger.info("过期的设备删除占用标识 taskId={}", taskId);
		String platformmark = commonProperties.getProperty("ijiami.platformmark");
		device = platformmark+"_"+taskId+"_"+terminalType.getName()+"_"+device;
		return HttpUtils.httpGetString(String.format(DEL_DEVICE, commonProperties.getProperty("ijiami.stf.console"),device), null, 3);
	}
	
	@Override
	public List<String> getAllUseDevice(){
		String enabled = commonProperties.getProperty("ijiami.stf.console.enabled");
		if(StringUtils.isNotBlank(enabled) || StringUtils.equals(enabled, "false")) {
			return null;
		}
		String result = HttpUtils.httpGetString(String.format(GET_All_DEVICE, commonProperties.getProperty("ijiami.stf.console")), null, 3);
		List<String> deviceList = com.alibaba.fastjson.JSONObject.parseArray(result, String.class);
		if(deviceList == null || deviceList.size()==0) {
			return deviceList;
		}
		List<String> newDevices = new ArrayList<String>();
		for (String string : deviceList) {
			if(string.contains("_")) {
				string = string.split("_")[3];
			}
			newDevices.add(string);
		}
		return newDevices;
	}
	
	@Override
	public List<UseDeviceVO> getAllUseDeviceInfo(){
		String enabled = commonProperties.getProperty("ijiami.stf.console.enabled");
		if(StringUtils.isNotBlank(enabled) || StringUtils.equals(enabled, "false")) {
			return null;
		}
		
		String stfConsole = commonProperties.getProperty("ijiami.stf.console");
		if(StringUtils.isBlank(stfConsole)) {
			return null;
		}
		
		String result = HttpUtils.httpGetString(String.format(GET_All_DEVICE, commonProperties.getProperty("ijiami.stf.console")), null, 3);
		List<String> deviceList = com.alibaba.fastjson.JSONObject.parseArray(result, String.class);
		if(deviceList == null || deviceList.size()==0) {
			return null;
		}
		List<UseDeviceVO> newDevices = new ArrayList<UseDeviceVO>();
		for (String string : deviceList) {
			UseDeviceVO vo = new UseDeviceVO();
			if(string.contains("_")) {
				String str[] = string.split("_");
				vo.setDeviceSerial(str[3]);
				vo.setPlatform(str[0]);
				vo.setTerminalType(TerminalTypeEnum.getItem(str[2]));
				vo.setTaskId(str[1]==null?null:Long.parseLong(str[1]));
				newDevices.add(vo);
			}
		}
		return newDevices;
	}
	
	@Override
	public UseDeviceVO getUseDeviceInfo(Long taskId, String deviceSerial, TerminalTypeEnum terminalType) {
		String platformmark = commonProperties.getProperty("ijiami.platformmark");
//		String device = platformmark+"_"+taskId+"_"+terminalType.getName()+"_"+deviceSerial;
		List<UseDeviceVO> list = getAllUseDeviceInfo();
		for (UseDeviceVO useDeviceVO : list) {
			if(useDeviceVO.getPlatform().equals(platformmark) && 
					taskId.equals(useDeviceVO.getTaskId())) {
				return useDeviceVO;
			}
		}
		return null;
	}

	@Override
	public String removeAll() {
		String enabled = commonProperties.getProperty("ijiami.stf.console.enabled");
		if(StringUtils.isNotBlank(enabled) || StringUtils.equals(enabled, "false")) {
			return null;
		}
		return HttpUtils.httpGetString(String.format(REMOVE_All_DEVICE, commonProperties.getProperty("ijiami.stf.console")), null, 3);
	}

	@Override
	public int getOnlineDeviceNum() {
		List<StfDeviceInfo> list = StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
		return (int) list.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() != StfDeviceStatusEnum.STF_DEVICE_DEAD).count();
	}

	@Override
	public int getUsedDeviceNum() {
		List<StfDeviceInfo> list = StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
		return (int) list.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() == StfDeviceStatusEnum.STF_DEVICE_BUSY).count();
	}

	@Override
	public int findFreeDeviceByModelAndVersion(Integer terminalType, String model, String version, Long userId) {
		int freeNum = 0;
		if(terminalType == null
				|| terminalType == TerminalTypeEnum.ANDROID.getValue()
				|| terminalType == TerminalTypeEnum.WECHAT_APPLET.getValue()
				|| terminalType == TerminalTypeEnum.ALIPAY_APPLET.getValue()) {
			//查询出所有云手机
			List<StfDeviceInfo> allDevices = StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
			if(allDevices == null || allDevices.size() == 0) {
				return freeNum;
			}
			//筛选出所有空闲手机
			List<StfDeviceInfo> allFreeDevices = allDevices.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() == StfDeviceStatusEnum.STF_DEVICE_FREE).collect(Collectors.toList());
			if(allFreeDevices == null || allFreeDevices.size() ==0) {
				return 0;
			}
			
			// 判断用户是否存在指定设备
			Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
			if (detectionConfigMap != null && detectionConfigMap.get(userId) != null && 
					StringUtils.isNotBlank(detectionConfigMap.get(userId).getAndroidDeviceIps())){
				Set<String> userDevices = Arrays.stream(detectionConfigMap.get(userId).getAndroidDeviceIps().split(","))
                        .collect(Collectors.toSet());
				allFreeDevices.removeIf(device -> !userDevices.contains(device.getDeviceSerial()));
				return allFreeDevices.size();
			}
			
			List<String> useDevices = null; 
			DetectionDeviceVO userDeviceUse = appletIdbDetectionService.getUserDevice(userId, terminalType);
			if(userDeviceUse != null && userDeviceUse.getDeviceIds()!= null && !userDeviceUse.getDeviceIds().isEmpty()) {
				useDevices = userDeviceUse.getDeviceIds();
				useDevices.remove(userDeviceUse.getLastTimeDeviceId());
			}
			List<StfDeviceInfo> freeList = new ArrayList<>();
			//过滤掉被微信占用一小时的手机
			if(useDevices != null){
				for(StfDeviceInfo info: allFreeDevices){
					if(useDevices.contains(info.getDeviceSerial())) {
						continue;
					}
                    freeList.add(info);
                }
			}else{
				freeList.addAll(allFreeDevices);
			}

			if(freeList.isEmpty()){
				return freeNum;
			}
			if(StringUtils.isBlank(model) && StringUtils.isBlank(version)) {
				return freeList.size();
			}
			
			
			
			List<StfDeviceInfo> freeDevices = new ArrayList<>();
			if(StringUtils.isBlank(model) && StringUtils.isNotBlank(version)) {
				freeDevices = freeList.stream().filter(stfDeviceInfo -> version.equals(stfDeviceInfo.getOsVersion())).collect(Collectors.toList());
				return freeDevices.size();
			}

			if(StringUtils.isNotBlank(model) && StringUtils.isBlank(version)) {
				freeDevices = freeList.stream().filter(stfDeviceInfo -> model.equals(stfDeviceInfo.getModel())).collect(Collectors.toList());
				return  freeDevices.size();
			}

			if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(version)) {
				freeDevices = freeList.stream().filter(stfDeviceInfo -> model.equals(stfDeviceInfo.getModel()) && version.equals(stfDeviceInfo.getOsVersion())).collect(Collectors.toList());
				return freeDevices.size();
			}
		}else{
			IdbDeviceList iosDeviceList = hitShellService.getDeviceList();
			logger.info("获取设备："+JSON.toJSONString(iosDeviceList));
			List<IdbDevice> deviceList = iosDeviceList.getDeviceList();
			if(deviceList == null || deviceList.isEmpty()) {
				return freeNum;
			}

			List<IdbDevice> freeDevices = new ArrayList<>();
			if(StringUtils.isBlank(model) && StringUtils.isBlank(version)) {
				freeDevices = deviceList.stream().filter(device -> model.equals(device.getProductType())).collect(Collectors.toList());
				freeNum = freeDevices.size();
			}

			if(StringUtils.isBlank(model) && StringUtils.isNotBlank(version)) {
				freeDevices = deviceList.stream().filter(device -> version.equals(device.getProVersion())).collect(Collectors.toList());
				freeNum = freeDevices.size();
			}

			if(StringUtils.isNotBlank(model) && StringUtils.isBlank(version)) {
				freeDevices = deviceList.stream().filter(device -> model.equals(device.getProductType())).collect(Collectors.toList());
				freeNum = freeDevices.size();
			}

			if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(version)) {
				freeDevices = deviceList.stream().filter(device -> model.equals(device.getProductType()) && version.equals(device.getProVersion())).collect(Collectors.toList());
				freeNum = freeDevices.size();
			}
		}
		return freeNum;
	}
	
	
	@Override
	public List<StfDeviceInfo> getAllDeviceInfoList() {
        return StfUtils.findStfDeviceAll(commonProperties.getProperty("ijiami.stf.url"), commonProperties.getProperty("ijiami.stf.token"));
	}

	@Override
	public List<DeviceUdid> getUdidList(TerminalTypeEnum type) {
		return Collections.emptyList();
	}

}
