package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.DEFAULT_RETRY_TIMES;
import static cn.ijiami.detection.constant.DistributedLockConstant.DEFAULT_SLEEP_TIME_MILLIS;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RedisDistributedLockImpl.java
 * @Description redis实现的分布式锁服务，属于可重入锁，非堵塞锁，非公平锁。使用hash结构存储锁的key，每次成功获取到锁的唯一标识，锁的重入次数。
 * 会有以下几个问题，需要谨慎使用
 * 1. 如果设置的超时时间不合理会有时钟漂移问题
 * 2. 如果使用的是主从redis服务，master宕机，slave升级为master过程中可能会有锁丢失问题
 * @createTime 2021年10月25日 12:25:00
 */
@Slf4j
@Component("redisDistributedLock")
public class RedisDistributedLockImpl implements DistributedLockService {

    public final static Long SUCCESS = 1L;
    public final static Long FAILURE = 0L;
    public final static Long REENTRANCY = 2L;


    private final ThreadLocal<Map<String, String>> threadLocal = new ThreadLocal<>();

    @Autowired
    private RedisTemplate<String, String> redisStringTemplate;

    /**
     * 此方法不需要传入uniqueId，因为uniqueId会自动生成保存到线程本地变量中。
     *
     * @param key
     * @param expireTimeMillis 超时时间
     *                         需要预估一个合理的超时时间，避免发生时钟漂移，就是任务还没执行完锁已经超时释放了，锁的互斥性失效
     * @return
     */
    @Override
    public boolean tryLock(String key, long expireTimeMillis) {
        return tryLock(key, expireTimeMillis, DEFAULT_RETRY_TIMES, DEFAULT_SLEEP_TIME_MILLIS);
    }

    /**
     * 此方法不需要传入uniqueId，因为uniqueId会自动生成保存到线程本地变量中。
     * @param key
     * @param expireTimeMillis 超时时间
     *                         需要预估一个合理的超时时间，避免发生时钟漂移，就是任务还没执行完锁已经超时释放了，锁的互斥性失效
     * @param retryTimes 获取锁失败之后的重试次数
     * @param sleepTimeMillis 获取锁失败之后的重试间隔时间
     * @return
     */
    @Override
    public boolean tryLock(String key, long expireTimeMillis, int retryTimes, long sleepTimeMillis) {
        Map<String, String> idMap = threadLocal.get();
        if (Objects.isNull(idMap)) {
            idMap = new HashMap<>();
            threadLocal.set(idMap);
        }
        String uniqueId = idMap.get(key);
        if (StringUtils.isBlank(uniqueId)) {
            uniqueId = UuidUtil.uuid();
            idMap.put(key, uniqueId);
        }
        return tryLock(key, uniqueId, expireTimeMillis, retryTimes, sleepTimeMillis);
    }

    /**
     *
     * @param key
     * @param uniqueId 获取锁的唯一id，在释放锁的时候要拿去验证。
     *          * 为了避免A任务拿到锁后任务时间过长，导致锁超时提前释放了，然后B任务这时成功拿到刚释放的锁去执行任务，过一会A任务结束去执行释放锁操作，把B任务的锁释放掉了
     *          * 所以需要加一个唯一id，在释放锁的时候判断跟获取锁时设置的id是否一样
     * @param expireTimeMillis 超时时间
     *                         需要预估一个合理的超时时间，避免发生时钟漂移，就是任务还没执行完锁已经超时释放了，锁的互斥性失效
     * @param retryTimes 获取锁失败之后的重试次数
     * @param sleepTimeMillis 获取锁失败之后的重试间隔时间
     */
    @Override
    public boolean tryLock(String key, String uniqueId, long expireTimeMillis, int retryTimes, long sleepTimeMillis) {
        boolean lock = tryLockInternal(key, uniqueId, expireTimeMillis);
        if (lock) {
            log.info("tryLock success key={} uniqueId={}", key, uniqueId);
            return true;
        }
        if (retryTimes-- > 0) {
            try {
                Thread.sleep(sleepTimeMillis);
                log.info("tryLock sleepTimeMillis={} retryTimes={}", sleepTimeMillis, retryTimes);
            } catch (InterruptedException e) {
                log.error("tryLock sleep Interrupted", e);
            }
            return tryLock(key, expireTimeMillis, retryTimes, sleepTimeMillis);
        }
        log.info("tryLock failure key={} uniqueId={}", key, uniqueId);
        return false;
    }

    private boolean tryLockInternal(String key, String uniqueId, long expireTimeMillis) {
        if (expireTimeMillis <= 0) {
            throw new IllegalArgumentException("expireTimeMillis <= 0");
        }
        /*
         * 使用hash结构来实现可重入的分布式锁，记录key和重入次数
         * 1 代表 true
         * 0 代表 false
         */
        String luaScript =
                "if (redis.call('exists', KEYS[1]) == 0) then" +
                "    redis.call('hincrby', KEYS[1], ARGV[2], 1);" +
                "    redis.call('pexpire', KEYS[1], ARGV[1]);" +
                "    return " + SUCCESS + ";" +
                "end ;" +
                "if (redis.call('hexists', KEYS[1], ARGV[2]) == 1) then" +
                "    redis.call('hincrby', KEYS[1], ARGV[2], 1);" +
                "    redis.call('pexpire', KEYS[1], ARGV[1]);" +
                "    return " + SUCCESS + ";" +
                "end ;" +
                "return " + FAILURE + ";";
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        Long result = redisStringTemplate.execute(redisScript, Collections.singletonList(key), String.valueOf(expireTimeMillis), uniqueId);
        return SUCCESS.equals(result);
    }

    @Override
    public boolean unlock(String key) {
        Map<String, String> idMap = threadLocal.get();
        if (Objects.isNull(idMap)) {
            log.error("execute unlock key={} idMap is null", key);
            return false;
        }
        String uniqueId = idMap.get(key);
        if (StringUtils.isBlank(uniqueId)) {
            log.error("execute unlock key={} uniqueId is blank", key);
            return false;
        }
        Long result = null;
        try {
            result = unlockInternal(key, uniqueId);
        } finally {
            // 失败的时候也要移除，因为有可能是锁超时，redis释放掉了。避免key一直存在map造成内存泄露
            if (Objects.isNull(result) || SUCCESS.equals(result) || FAILURE.equals(result)) {
                idMap.remove(key);
            }
        }
        log.info("execute unlock result={}", result);
        return !FAILURE.equals(result);
    }

    @Override
    public boolean unlock(String key, String uniqueId) {
        Long result = unlockInternal(key, uniqueId);
        log.info("execute unlock result={}", result);
        return !FAILURE.equals(result);
    }

    private Long unlockInternal(String key, String uniqueId) {
        log.info("execute unlock key={} uniqueId={}", key, uniqueId);
        // 为了保证原子性，使用lua写释放逻辑
        // 判断hash的value-1是否等于0, 如果为0代表重入次数为0，可以释放key
        String luaScript =
                        "if (redis.call('hexists', KEYS[1], ARGV[1]) == 0) then" +
                                "    return " + FAILURE + ";" +
                                "end ;" +
                                "local counter = redis.call('hincrby', KEYS[1], ARGV[1], -1);" +
                                "if (counter > 0) then" +
                                "    return " + REENTRANCY + ";" +
                                "else" +
                                "    redis.call('del', KEYS[1]);" +
                                "    return " + SUCCESS + ";" +
                                "end ;" +
                                "return " + FAILURE + ";";
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        return redisStringTemplate.execute(redisScript, Collections.singletonList(key), uniqueId);
    }
}
