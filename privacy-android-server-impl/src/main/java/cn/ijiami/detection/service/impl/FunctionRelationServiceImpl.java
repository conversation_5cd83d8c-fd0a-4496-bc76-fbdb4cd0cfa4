package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.entity.TFunctionRelation;
import cn.ijiami.detection.mapper.FunctionRelationMapper;
import cn.ijiami.detection.service.api.IFunctionRelationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
public class FunctionRelationServiceImpl implements IFunctionRelationService{

    private final FunctionRelationMapper functionRelationMapper;

    public FunctionRelationServiceImpl(FunctionRelationMapper functionRelationMapper) {
        this.functionRelationMapper=functionRelationMapper;
    }

    @Override
    public List<TFunctionRelation> getFunctionRelationByFunctionId(List<Long> funcitonIds) {
        if (CollectionUtils.isEmpty(funcitonIds)) {
            return Collections.emptyList();
        } else {
            return functionRelationMapper.getFunctionRelationByFunctionId(funcitonIds);
        }
    }
}
