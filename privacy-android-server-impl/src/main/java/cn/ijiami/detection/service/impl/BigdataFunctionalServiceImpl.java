package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.entity.TBigdataFunctional;
import cn.ijiami.detection.mapper.TBigdataFunctionalMapper;
import cn.ijiami.detection.service.api.IBigdataFunctionalService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BigdataFunctionalServiceImpl implements IBigdataFunctionalService{

    private final TBigdataFunctionalMapper tBigdataFunctionalMapper;

    public BigdataFunctionalServiceImpl(TBigdataFunctionalMapper tBigdataFunctionalMapper){
        this.tBigdataFunctionalMapper=tBigdataFunctionalMapper;
    }

    @Override
    public List<TBigdataFunctional> getBigDataFunctionalById(List<Long> ids) {
        return  tBigdataFunctionalMapper.getBigDataFunctionalById(ids);
    }
}
