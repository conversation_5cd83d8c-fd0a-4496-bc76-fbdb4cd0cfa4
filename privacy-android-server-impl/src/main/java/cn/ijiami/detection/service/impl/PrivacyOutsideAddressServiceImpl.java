package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.CountOutsideTypeVO;
import cn.ijiami.detection.VO.PrivacyDetectionResultVO;
import cn.ijiami.detection.VO.TPrivacyOutsideAddressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.statistical.ChildItemCommonCountVO;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IPrivacyDetectionTransferRiskService;
import cn.ijiami.detection.service.api.IPrivacyOutsideAddressService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.utils.BeanUtil;
import cn.ijiami.detection.utils.DataHandleUtil;

/**
 * <AUTHOR>
 * @date 2019/12/3 15:19
 */
@Service
public class PrivacyOutsideAddressServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IPrivacyOutsideAddressService {

    private static final Logger logger = LoggerFactory.getLogger(PrivacyOutsideAddressServiceImpl.class);

    private final TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;
    private final IPrivacyDetectionTransferRiskService transferRiskService;
    private final TTaskMapper tTaskMapper;
    private final TAssetsMapper tAssetsMapper;
    private final ISendMessageService iSendMessageService;
    private final CacheService cacheService;
    private final TAssetsMapper assetsMapper;

    @Value("${ijiami.networkCheck.callback:}")
    private String callbackUrl;

    public PrivacyOutsideAddressServiceImpl(TPrivacyOutsideAddressMapper privacyOutsideAddressMapper,
                                            IPrivacyDetectionTransferRiskService transferRiskService, TTaskMapper tTaskMapper,
                                            TAssetsMapper tAssetsMapper,
                                            ISendMessageService iSendMessageService, CacheService cacheService,
                                            TAssetsMapper assetsMapper) {
        this.privacyOutsideAddressMapper = privacyOutsideAddressMapper;
        this.transferRiskService = transferRiskService;
        this.tTaskMapper = tTaskMapper;
        this.tAssetsMapper = tAssetsMapper;
        this.iSendMessageService = iSendMessageService;
        this.cacheService = cacheService;
        this.assetsMapper = assetsMapper;
    }

    @Override
    public List<TPrivacyOutsideAddress> getOutSideAddress(Long taskId, Integer behaviorStage) {
        return privacyOutsideAddressMapper.findByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<TPrivacyOutsideAddress> findByTaskIdAndOutside(Long taskId, Integer outside, Integer behaviorStage) {
        TTask tTask = tTaskMapper.selectByPrimaryKey(taskId);
        if (outside == null) {
            outside = 0;
        }
        if (tTask.getTerminalType() == TerminalTypeEnum.IOS) {
            return privacyOutsideAddressMapper.findByTaskIdAndOutsideIos(taskId, outside, behaviorStage);
        } else {
            List<TPrivacyOutsideAddress> result = privacyOutsideAddressMapper.findByTaskIdAndOutside(taskId, outside, behaviorStage);
            // 兼容堆栈数据处理方式
            if (CollectionUtils.isNotEmpty(result) && DataHandleUtil.isJSONValid(result.get(0).getStackInfo())) {
                result.stream().forEach(item -> {
                    item.setCounter(0);
                    if (StringUtils.isNotBlank(item.getStackInfo())) {
                        try {
                            item.setCounter(JSON.parseArray(item.getStackInfo()).size());
                            if (item.getExecutorType() == 1) {
                                item.setAppCount(item.getCounter());
                            }
                            if (item.getExecutorType() == 2) {
                                item.setSdkCount(item.getCounter());
                            }
                        } catch (Exception e) {
                        }
                    }
                    item.setCounter(item.getAppCount() + item.getSdkCount());
                });
            }
            return result;
        }
    }

    @Override
    public List<CountOutsideTypeVO> countByOutside(Long taskId, Integer behaviorStage) {
        return privacyOutsideAddressMapper.countByOutside(taskId, behaviorStage);
    }

    @Override
    public void updateOutSideWhenStaticComplete(Long taskId, String documentId) {
        // 查询应用信息
        TAssets asset = assetsMapper.selectAssetByTaskId(taskId);
        if (asset == null) {
            logger.error("安卓静态检测境内外IP更新失败，找不到应用信息，任务ID：{}", taskId);
            return;
        }
        List<TPrivacyOutsideAddress> outsideAddresses = privacyOutsideAddressMapper.findByTaskId(taskId, 0);
        if (outsideAddresses == null) {
            outsideAddresses = new ArrayList<>();
        }
        // 获取mongo中存的静态检测项检测结果
        PrivacyDetectionResultVO resultFor0806 = transferRiskService.getResultFor(documentId, "0806", 1);
        if (resultFor0806 != null && resultFor0806.getDetails() != null) {
            for (ChildItemCommonCountVO detail : resultFor0806.getDetails()) {
                TPrivacyOutsideAddress outSideAddress = new TPrivacyOutsideAddress();
                outSideAddress.setTaskId(taskId);
                outSideAddress.setIp(detail.getColunm1());
                outSideAddress.setAddress(detail.getColunm2());
                outSideAddress.setCounter(0);
                outSideAddress.setBehaviorStage(BehaviorStageEnum.BEHAVIOR_FRONT);
                if (!StringUtils.isBlank(detail.getColunm2()) && !detail.getColunm2().startsWith("中国")) {
                    outSideAddress.setOutside(1);
                } else {
                    outSideAddress.setOutside(0);
                }
                outSideAddress.setExecutorType(1);
                outSideAddress.setExecutor(asset.getName());
                outsideAddresses.add(outSideAddress);
            }
        }
        // 去重处理
        updateOutSide(taskId, outsideAddresses, false);
        logger.debug("静态检测 - 境内外ip数据更新，任务ID：{}，更新内容：{}", taskId, outsideAddresses);
    }

    @Override
    public void updateOutSide(Long taskId, List<TPrivacyOutsideAddress> outsideAddresses, boolean clean) {
        // 重置数据
        if (clean) {
            privacyOutsideAddressMapper.deleteByTaskId(taskId);
        }
        outsideAddresses.forEach(i -> i.setId(null));
        outsideAddresses = outsideAddresses.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>((o1, o2) -> {
            StringBuilder builder1 = new StringBuilder();
            builder1.append(o1.getIp());
            if (!org.apache.commons.lang3.StringUtils.isBlank(builder1)) {
                builder1.append(o1.getHost());
            }
            StringBuilder builder2 = new StringBuilder();
            builder2.append(o2.getIp());
            if (!org.apache.commons.lang3.StringUtils.isBlank(builder2)) {
                builder2.append(o2.getHost());
            }
            return builder1.toString().compareTo(builder2.toString());
        })), ArrayList::new));
        // 批量插入数据
        if (outsideAddresses.size() > 0) {
            privacyOutsideAddressMapper.insertList(outsideAddresses);
        }
    }


    @Override
    public List<TPrivacyOutsideAddress> findByTaskIdAndOutsideStackInfo(Long taskId, String ip, String host, Integer behaviorStage) {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        List<TPrivacyOutsideAddress> result = privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskId, ip, host, behaviorStage);
        // 兼容堆栈数据处理方式
        try {
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID && result.size() > 0 && DataHandleUtil.isJSONValid(result.get(0).getStackInfo())) {
                TPrivacyOutsideAddress root = result.get(0);
                List<TPrivacyOutsideAddress> finalResult = new ArrayList<>();
                for (ActionStackBO stackBO : JSON.parseArray(root.getStackInfo(), ActionStackBO.class)) {
                    TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
                    BeanUtil.copyPropertiesIgnoreNull(root, outsideAddress);
                    outsideAddress.setStackInfo(stackBO.getStackInfo());
                    outsideAddress.setDetailsData(stackBO.getDetailsData());
                    outsideAddress.setStrTime(stackBO.getActionTime());
                    finalResult.add(outsideAddress);
                }
                return finalResult;
            } else {
                return result;
            }
        } catch (Exception e) {
        }
        return result;
    }
    
    
    /**
     * 通信行为分析通用业务，包含各个筛选条件
     * @param behaviorQuery
     * @return
     */
    @Override
    public TPrivacyOutsideAddressVO findOutsideDataByTaskId(BehaviorQuery behaviorQuery) {
        //返回的数据带分页信息
        TPrivacyOutsideAddressVO tPrivacyOutsideAddressVO = new TPrivacyOutsideAddressVO();
        List<TPrivacyOutsideAddress> result=null;
        PageInfo<TPrivacyOutsideAddress> pageInfo = null;

        if(behaviorQuery !=null && (!"".equals(behaviorQuery.getTaskId()) && behaviorQuery.getTaskId()!=null)
                && (behaviorQuery.getTaskId()!=null && !"".equals(behaviorQuery.getTaskId()))){

            Long taskId = behaviorQuery.getTaskId();
            String executors = behaviorQuery.getExecutors();
            String executorTypeString = behaviorQuery.getExecutorType();
            String outsideString = behaviorQuery.getOutside();
            String protocol = behaviorQuery.getProtocol();
            Integer behaviorStage = behaviorQuery.getBehaviorStage();
            Integer sortType = behaviorQuery.getSortType();
            Integer sortOrder = behaviorQuery.getSortOrder();

            TTask task = tTaskMapper.selectByPrimaryKey(taskId);
            if (behaviorQuery.getPage() != null && behaviorQuery.getRows() != null) {
                PageHelper.startPage(behaviorQuery.getPage(), behaviorQuery.getRows());
            }
            result = privacyOutsideAddressMapper.findOutsideDataByTaskId(taskId,
                    behaviorStage, executors, executorTypeString, outsideString, protocol, sortType, sortOrder,
                    behaviorQuery.getCookieMark(), task.getTerminalType().getValue());
            // 移除乱码
            result.forEach(data -> data.setDetailsData(data.getDetailsData().equals("") ? PinfoConstant.DETAILS_EMPTY : data.getDetailsData()));

            // 兼容堆栈数据处理方式
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID && result.size() > 0) {
                for (TPrivacyOutsideAddress root : result) {
                    if (root.getStackInfo() != null && !PinfoConstant.DETAILS_EMPTY.equals(root.getStackInfo()) && DataHandleUtil.isJSONValid(root.getStackInfo())) {
                        ActionStackBO stackBO = JSON.parseArray(root.getStackInfo(), ActionStackBO.class).get(0);
                        root.setStackInfo(StringUtils.isEmpty(root.getStackInfo()) ? stackBO.getStackInfo() : root.getStackInfo());
                        root.setDetailsData(StringUtils.isEmpty(root.getDetailsData()) ? stackBO.getDetailsData() : root.getDetailsData());
                        root.setStrTime(root.getStrTime()==null?stackBO.getActionTime():root.getStrTime());
                        root.setActionTime(root.getActionTime()==null?stackBO.getActionTime():root.getActionTime());
                    }
                }
            }
        }

        if(result!=null){
            pageInfo = new PageInfo<>(result);
            pageInfo.setList(result);
            tPrivacyOutsideAddressVO.setPageInfo(pageInfo);
        }

        return tPrivacyOutsideAddressVO;
    }

    /**
     * 获取通信行为的筛选条件（主体类型，协议类型）
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public Map<String,List<String>> getBehaviorNoun(String documentId, Integer behaviorStage) {
        List<String> executorList = new ArrayList<>();
        List<String> protocolList = new ArrayList<>();
        Map<String,List<String>> map = new HashMap<>();
        TTask task = tTaskMapper.findByDocumentId(documentId);
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            List<String> list = privacyOutsideAddressMapper.getBehaviorNoun(task.getTaskId(), behaviorStage);
            protocolList = privacyOutsideAddressMapper.getBehaviorNounOfProtocol(task.getTaskId(),behaviorStage);
            TAssets tAssets = tAssetsMapper.selectAssetByTaskId(task.getTaskId());
            for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (executorList.contains(sdkInfo)) {
                        continue;
                    }
                    executorList.add(sdkInfo.replace("\n",""));
                }
            }
            //过滤掉与app名称相同的sdk主体
            executorList=executorList.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        }
        map.put("executor",executorList);
        map.put("protocol",protocolList);
        return map;
    }
}
