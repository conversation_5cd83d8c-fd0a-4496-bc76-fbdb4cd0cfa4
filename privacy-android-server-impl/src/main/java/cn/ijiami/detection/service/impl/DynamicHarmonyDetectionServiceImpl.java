package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_ANALYSIS_TASK_DATA_PREFIX;
import static cn.ijiami.detection.enums.DynamicAutoStatusEnum.DETECTION_AUTO_WAITING;
import static cn.ijiami.detection.utils.CommonUtil.analysisErrorMsg;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.ijiami.detection.analyzer.bo.HarmonyCaptureInfoBO;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.utils.SensitiveUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.VO.DynamicDetectFailedVO;
import cn.ijiami.detection.VO.PrivacyPolicyCheck;
import cn.ijiami.detection.VO.PushProgressVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.parser.HarmonyBehaviorInfoAction;
import cn.ijiami.detection.analyzer.parser.HarmonyCaptureHostIpAction;
import cn.ijiami.detection.analyzer.parser.HarmonySharedPrefAction;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.detection.entity.TPrivacyLawsBasis;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.entity.TTaskExtend;
import cn.ijiami.detection.exception.TaskDataErrorException;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.mapper.TApplyPermissionMapper;
import cn.ijiami.detection.mapper.TManualScreenshotImageMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatExtendMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyImgMapper;
import cn.ijiami.detection.mapper.TPrivacyPolicyMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectItemMapper;
import cn.ijiami.detection.mapper.TXiaomiAppStoreDetectResultMapper;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.parser.HarmonyResultDataLogParser;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.IDynamicHarmonyDetectionService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.detection.service.api.SDKWhitelistRuleService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.PdfToTextFileUtil;
import cn.ijiami.detection.utils.URLTokenizer;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicHarmonyDetectionServiceImpl.java
 * @Description 鸿蒙数据解析
 * @createTime 2024年06月24日 16:45:00
 */
@Slf4j
@Service
public class DynamicHarmonyDetectionServiceImpl extends AbstractDynamicDetectionService<TaskDetailVO> implements IDynamicHarmonyDetectionService {

    /**
     * 动态检测需要解析的文件组
     */
    private static final Map<BehaviorStageEnum, String> ANALYZE_FILE_MAP = new HashMap<>();

    static {
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_FRONT, "behavior_info_01.json,hostip.txt,capture_info.xls,shared_prefs,capture_info");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GRANT, "behavior_info_02.json,hostip_02.txt,capture_info_02.xls,shared_prefs_02,capture_info_02");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GROUND, "behavior_info_03.json,hostip_03.txt,capture_info_03.xls,shared_prefs_03,capture_info_03");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_EXIT, "behavior_info_04.json,hostip_04.txt,capture_info_04.xls,shared_prefs_04,capture_info_04");
    }

    @Autowired
    HarmonyBehaviorInfoAction behaviorInfoAction;
    @Autowired
    HarmonyCaptureHostIpAction captureHostIpAction;
    @Autowired
    HarmonySharedPrefAction sharedPrefAction;
    @Autowired
    IjiamiCommonProperties commonProperties;
    @Autowired
    TSensitiveWordMapper sensitiveWordMapper;
    @Autowired
    TPrivacyActionNougatMapper privacyActionNougatMapper;
    @Autowired
    TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;
    @Autowired
    TPrivacySensitiveWordMapper privacySensitiveWordMapper;
    @Autowired
    TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;
    @Autowired
    TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    @Autowired
    TPrivacyPolicyImgMapper privacyPolicyImgMapper;
    @Autowired
    TPrivacyPolicyMapper privacyPolicyMapper;
    @Autowired
    TPrivacyLawsDetailMapper privacyLawsDetailMapper;
    @Resource
    HarmonyResultDataLogParser harmonyResultDataLogParser;
    @Autowired
    SDKWhitelistRuleService sdkWhitelistRuleService;
    @Autowired
    TPermissionMapper permissionMapper;
    @Autowired
    TManualScreenshotImageMapper manualScreenshotImageMapper;
    @Autowired
    IScreenshotImageService screenshotImageService;

    @Autowired
    TApplyPermissionMapper applyPermissionMapper;

    @Autowired
    IDynamicTaskContextService dynamicTaskDataService;

    @Autowired()
    @Qualifier("redisDistributedLock")
    DistributedLockService distributedLockService;

    @Autowired
    UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    TXiaomiAppStoreDetectItemMapper xiaomiAppStoreDetectItemMapper;

    @Autowired
    TXiaomiAppStoreDetectResultMapper xiaomiAppStoreDetectResultMapper;

    @Autowired
    ICommonMongodbService commonMongodbService;

    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;
    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Value("#{${ijiami.cloud.phone.addressBook}}")
    private Map<String, String> cloudPhoneAddressBook;

    @Value("${ijiami.tensorflow.checkboxModelPath:}")
    private String checkboxModelPath;

    @Value("${ijiami.tensorflow.checkedModelPath:}")
    private String checkedModelPath;


    @Override
    public void analysisAutoFromUpload(TTask task, IdbStagedDataEnum stage, MultipartFile data) {
        // 校验任务存在
        if (!checkDocumentValid(task)) {
            log.info("TaskId:{} 任务不存在", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_WAITING) {
            log.info("TaskId:{} 动态检测等待状态 进行状态转变", task.getTaskId());
            taskDAO.updateDynamicAutoWaiting(task, "", false);
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "AUTO", data);
            String dfsPath = uploadDataFileToFastDfs(file.getAbsolutePath());
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), dfsPath);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath()));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    @Override
    public void analysisAutoAgain(Long taskId, MultipartFile data) {
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            log.info("TaskId:{} 任务不存在", taskId);
            return;
        }
        try {
            File file = saveFile(task.getMd5(), taskId, "AUTO", data);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            analysisAutoInternal(task, file.getAbsolutePath(), task.getDataPath());
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    @Override
    public void analysisAutoFromXXLData(Long taskId, IdbStagedDataEnum stage, String fastDfsDataPath) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            log.info("TaskId:{} 任务不存在", taskId);
            return;
        }
        try {
            File file = downloadFile(fastDfsDataPath);
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), fastDfsDataPath);
            // xxl过来的数据已经上传到文件服务器的，不需要再上传
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath()));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    private void analysisAutoInternal(TTask task, String dataPath, String fastDfsDataPath) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        long startTime = System.currentTimeMillis();
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        String uncompress = null;
        try {
            uncompress = CommonUtil.uncompress(dataPath, dynamicPath + UuidUtil.uuid());
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            updateTask.setDataPath(StringUtils.isEmpty(fastDfsDataPath) ? uploadDataFileToFastDfs(dataPath) : fastDfsDataPath);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);

            //爬取链接-提前解析文件，python方式解析到完成需要点时间
            getUrlPrivacyContent(uncompress);
            File policyFile = new File(uncompress + File.separator + "deviceInfo.txt");
            if (policyFile.exists()) {
                updateDeviceInfo(task.getTaskId(), policyFile);
            }
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisUncompress(taskDetailVo, uncompress, sdkLibraries);
            deleteOldData(task.getTaskId());
            Map<Long, String> screenshotMap = getScreenShotMap(IdbStagedDataEnum.NONE, uncompress);
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, screenshotMap, task.getTaskId());
            // 146号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.DYNAMIC);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "动态检测完成");
            // 更新SDK权限编码
            List<TPrivacyActionNougat> nougatList = detectDataMap.values().stream()
                    .flatMap(it -> it.getPrivacyActionNougats().stream())
                    .collect(Collectors.toList());
            updateSdkPermission(nougatList);
            log.info("harmony 动态检测完成 taskId={} 时间={}s", task.getTaskId(), (System.currentTimeMillis() - startTime) / 1000);
            pushProgress(task);
        } catch (IOException e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包不存在");
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        } catch (Throwable e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            CommonUtil.deleteFile(dataPath);
            IpUtil.ipMap.clear();
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    //检测完成回调
    private void pushProgress(TTask task){
        try {
            TTaskExtendVO taskExtend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
            if(Objects.isNull(taskExtend)) {
                return;
            }
            String callbackUrl = taskExtend.getCallbackUrl();
            if (StringUtils.isBlank(callbackUrl)) {
                return;
            }
            //通知检测完成给第三方接口
            PushProgressVO push = new PushProgressVO();
            push.setDetectionType(DetectionTypeEnum.DYNAMIC);
            push.setPushProgress(PushProgressEnum.PUSH_FINISH);
            // 进行第三方进度信息消息推送
            apiPushProgressServer.pushProgressByUrl(task.getTaskId(), taskExtend.getBussinessId(), callbackUrl, push);
        } catch (Exception e) {
            e.getMessage();
        }
    }

    private void updateSdkPermission(List<TPrivacyActionNougat> nougatList) {
        taskService.updateSdkPermission(nougatList);
    }


    // 异步线程去处理，避免卡住上传流程，导致idb那边上传超时
    @Override
    public void analysisManual(Long taskId, MultipartFile data) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "MANUAL", data);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisManualInternal(task, file.getAbsolutePath(), StringUtils.EMPTY));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, "数据包保存失败");
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "动态检测失败");
        }
    }

    @Override
    protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath) {
        TTask updateTask = new TTask();
        updateTask.setTaskId(task.getTaskId());
        updateTask.setDataPath(StringUtils.isEmpty(fastDfsDataPath) ? uploadDataFileToFastDfs(dataPath) : fastDfsDataPath);
        taskMapper.updateByPrimaryKeySelective(updateTask);
    }

    @Override
    public void analysisDataRetry(Long taskId) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        taskDAO.updateDynamicAutoDataProcess(task.getTaskId());
        // 校验任务是否为数据包解析失败
        TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());
        Integer detectionType = task.getDetectionType();
        String zipSuffix = TaskDetectionTypeEnum.isAuto(detectionType) ? "_AUTO" : "_MANUAL";
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        String uncompress = dynamicPath + UuidUtil.uuid();
        if (StringUtils.isNotBlank(task.getDataPath())) {
            // 整包的数据
            File file = new File(dynamicPath + taskDetail.getMd5() + "_" + task.getTaskId() + zipSuffix + ".zip");
            if (!file.exists()) {
                try {
                    if(task.getDataPath()!= null && task.getDataPath().startsWith("http")) {
                        FileUtils.copyURLToFile(new URL(task.getDataPath()), file);
                    }else {
                        FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIntranetIp)), file);
                    }
                } catch (IOException e) {
                    log.error(String.format("TaskId:%d 解析失败", taskId), e);
                }
            }
            CommonUtil.uncompress(file.getAbsolutePath(), uncompress);
            analysisAutoOldFile(task, uncompress);
        } else {
            try {
                // 分段的数据
                deleteOldData(task.getTaskId());
                obtainAndVerifyTaskData(task, taskDataList -> {
                    for (TTaskData taskData:taskDataList) {
                        File stagedZip = new File(saveFilePath(task.getMd5(), task.getTaskId(), "AUTO_" + taskData.getDynamicSubStatus().getValue()));
                        if (!stagedZip.exists()) {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                        }
                        analysisStageData(task, taskData, IdbStagedDataEnum.getItem(taskData.getDynamicSubStatus().getValue()), stagedZip.getAbsolutePath());
                    }
                    analysisFinalStage(task, taskDataList);
                });
            } catch (Exception e) {
                log.error(String.format("TaskId:%d 解析失败", taskId), e);
            }
        }
    }

    @Override
    public void analysisLaw(Long taskId, Integer type, MultipartFile data) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + taskId;
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", taskId, lockKey);
            return;
        }
        String uncompress = null;
        try {
            uncompress = saveAndUncompress(task.getMd5(), taskId, "LAW" + type, data);
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicLawDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.LAW);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_LAW_FINISH, "法规检测完成");
        } catch (Exception e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.LAW, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "法规检测失败");
            log.error("检测数据处理 - {}，数据解析异常:{}", DetectionTypeEnum.LAW.getName(), e.getMessage());
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    public void dynamicDetectFailed(DynamicDetectFailedVO dynamicDetectFailedVO) {
        TTask task = taskMapper.selectByPrimaryKey(dynamicDetectFailedVO.getTaskId());
        if (DynamicDetectFailedStageEnum.DYNAMIC.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateDynamicFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        } else if (DynamicDetectFailedStageEnum.MANUAL.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateManualFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "动态检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        } else if (DynamicDetectFailedStageEnum.LAW.getStage().equals(dynamicDetectFailedVO.getStage())) {
            taskDAO.updateLawFailure(task, dynamicDetectFailedVO.getReason());
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_ERROR, "法规检测失败");
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    @Caching(evict = {@CacheEvict(value = "privacy-detection:task", allEntries = true, beforeInvocation = true),
            @CacheEvict(value = "privacy-detection:action", allEntries = true, beforeInvocation = true)})
    public void removeTaskCache() {
        log.info("检测数据更新，清除缓存");
    }

    private CommonDetectInfo analysisMiitDetectResult(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        // 深度检测直接返回
        if (!TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            return null;
        }
        privacyLawsDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsResultMapper.deleteByTaskId(task.getTaskId());
        appLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        appLawsRiskCollectMapper.deleteByTaskId(task.getTaskId());
        sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
        sdkLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsConclusionActionMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        CommonDetectInfo commonDetectInfo = buildHarmonyDetectInfo(task, uncompress, detectDataMap);

        //查询检测项
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        for (PrivacyLawId lawId: getDetectionLawIds(extend, task.getTerminalType())) {
            analysisLaw(task, sdkVOList, commonDetectInfo, Collections.emptyList(), lawId);
        }
        saveSdkDectDetail(task, sdkVOList);
        return commonDetectInfo;
    }

    /**
     * 重新录入sdk统计数据
     * @param task
     */
    @Transactional
    public void restoreSdkDectDetail(TTask task) {
        log.info("重新录入sdk统计数据");
        sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        saveSdkDectDetail(task, sdkVOList);
    }

    @Override
    protected CustomDetectInfo makeCustomDetectInfo(String itemNo, Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys) {
        CustomDetectInfo customDetectInfo = super.makeCustomDetectInfo(itemNo, itemNoWithActionAndKeys);
        customDetectInfo.setCloudPhoneAddressBook(cloudPhoneAddressBook);
        return customDetectInfo;
    }

    @Override
    protected void analysisAutoOldFile(TTask task, String uncompress) {
        try {
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            //爬取链接-提前解析文件，python方式解析到完成需要点时间
            getUrlPrivacyContent(uncompress);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisIdbDataStageUncompressed(taskDetailVo, uncompress, sdkLibraries);
            deleteOldData(task.getTaskId());
            Map<Long, String> screenshotMap = getScreenShotMap(IdbStagedDataEnum.NONE, uncompress);
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, screenshotMap, task.getTaskId());
            // 146号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeRetry(task);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, DetectionTypeEnum.DYNAMIC.getName() + "完成");
        } catch (Exception e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, DetectionTypeEnum.DYNAMIC.getName() + "失败");
            log.error("检测数据处理 - {}，数据解析异常:{}", DetectionTypeEnum.DYNAMIC.getName(), e.getMessage());
        } finally {
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception {
        long startTime = System.currentTimeMillis();
        List<File> stagedZipList = new ArrayList<>();
        String uncompress = null;
        try {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            uncompress = dynamicPath + UuidUtil.uuid();
            // 解压阶段数据
            log.info("解压分阶段的数据 开始");
            for (TTaskData taskData : taskDataList) {
                File stagedZip = new File(saveFilePath(task.getMd5(), task.getTaskId(), "AUTO_" + taskData.getDynamicSubStatus().getValue()));
                if (!stagedZip.exists()) {
                    try {
                        FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                        stagedZipList.add(stagedZip);
                    } catch (Exception e) {
                        taskDataRollback(task, taskData.getDynamicSubStatus());
                        throw new TaskDataErrorException(e);
                    }
                }
                try {
                    CommonUtil.uncompressOverwrite(stagedZip.getAbsolutePath(), uncompress);
                } catch (UncompressFailException e) {
                    log.info("压缩包损坏 删除={}", stagedZip.delete());
                    throw new TaskDataErrorException(e);
                }
            }
            log.info("解压分阶段的数据 完成");
            // 爬取链接-提前解析文件，python方式解析到完成需要点时间
            getUrlPrivacyContent(uncompress);
            File policyFile = new File(uncompress + File.separator + "deviceInfo.txt");
            if (policyFile.exists()) {
                updateDeviceInfo(task.getTaskId(), policyFile);
            }
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = findDetectData(task.getTaskId());
            // 164号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            long analysisTakesTime = System.currentTimeMillis() - startTime;
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.DYNAMIC, analysisTakesTime);
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "动态检测完成");
            log.info("harmony 动态检测完成 taskId={} 时间={}s", task.getTaskId(), analysisTakesTime / 1000);
            pushProgress(task);
        } finally {
            CommonUtil.deleteFile(uncompress);
            for (File stagedZip:stagedZipList) {
                CommonUtil.deleteFile(stagedZip.getAbsolutePath());
            }
        }
    }

    private Map<BehaviorStageEnum, DetectDataBO> findDetectData(Long taskId) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (BehaviorStageEnum behaviorStageEnum:ANALYZE_FILE_MAP.keySet()) {
            DetectDataBO detectDataBO = new DetectDataBO();
            detectDataBO.setPrivacyActionNougats(privacyActionNougatMapper.findByTaskId(taskId, Collections.emptyList(),
                    Collections.emptyList(), Collections.singletonList(behaviorStageEnum)));
            detectDataBO.setPrivacySensitiveWords(privacySensitiveWordMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacyOutsideAddresses(privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskId,
                    StringUtils.EMPTY, StringUtils.EMPTY, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacySharedPrefs(privacySharedPrefsMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataMap.put(behaviorStageEnum, detectDataBO);
        }
        return detectDataMap;
    }


    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompressed
     * @param sdkLibraries
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisIdbDataStageUncompressed(TaskDetailVO taskDetailVo, String uncompressed, List<TSdkLibrary> sdkLibraries) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                String fileNames = entry.getValue();
                DetectDataBO detectData = analyzeZipAction(behaviorStage, fileNames, uncompressed, taskDetailVo, sdkLibraries, sensitiveWords);
                if (Objects.nonNull(detectData)) {
                    detectDataMap.put(behaviorStage, detectData);
                }
            } catch (Exception e) {
                log.error("数据解析失败", e);
            }
        }
        return detectDataMap;
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @param sdkLibraries
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisIdbDataStageUncompressed(TaskDetailVO taskDetailVo, String uncompress, List<TSdkLibrary> sdkLibraries, IdbStagedDataEnum idbUploadDataStage) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        DetectDataBO detectData = analyzeZipAction(idbUploadDataStage.getBehaviorStage(), idbUploadDataStage.getFileNames(), uncompress, taskDetailVo, sdkLibraries, sensitiveWords);
        if (Objects.nonNull(detectData)) {
            detectDataMap.put(idbUploadDataStage.getBehaviorStage(), detectData);
        }
        return detectDataMap;
    }

    @Override
    protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {
        String uncompress = null;
        try {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            uncompress = CommonUtil.uncompressTaskData(dataPath, dynamicPath + UuidUtil.uuid());
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisIdbDataStageUncompressed(taskDetailVo, uncompress, sdkLibraries, stageEnum);
            Map<Long, String> screenshotMap = getScreenShotMap(stageEnum, uncompress);
            saveStageData(task.getTaskId(), taskData.getId(), stageEnum, detectDataMap, screenshotMap);
        } finally {
            CommonUtil.deleteFile(uncompress);
        }
    }

    protected void saveStageData(Long taskId, Long taskDataId, IdbStagedDataEnum stageEnum, Map<BehaviorStageEnum, DetectDataBO> detectDataMap,
                                 Map<Long, String> screenshotMap) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 事物隔离级别
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        // 事务传播行为
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transaction = dataSourceTransactionManager.getTransaction(def);
        try {
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, screenshotMap, taskId);
            // 更新SDK权限编码
            List<TPrivacyActionNougat> nougatList = detectDataMap.values().stream()
                    .flatMap(it -> it.getPrivacyActionNougats().stream())
                    .collect(Collectors.toList());
            updateSdkPermission(nougatList);
            TTask task = taskMapper.findByTaskIdForUpdate(taskId);
            TTask updateTask = new TTask();
            updateTask.setTaskId(taskId);
            updateTask.setUpdateTime(new Date());
            DynamicAutoSubStatusEnum subStatus = DynamicAutoSubStatusEnum.getItem(stageEnum.getValue());
            if (Objects.isNull(subStatus)) {
                throw new IjiamiRuntimeException("阶段数据错误");
            }
            if (task.getDynamicSubStatus().getValue() < subStatus.getValue()) {
                updateTask.setDynamicSubStatus(subStatus);
            }
            taskMapper.updateByPrimaryKeySelective(updateTask);
            taskDataService.analysisTaskDataSuccess(taskDataId);
            dataSourceTransactionManager.commit(transaction);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(transaction);
            throw e;
        }
    }

    private void analysisPrivacyCheck(TTask task, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, CommonDetectInfo commonDetectInfo) {
        PrivacyPolicyCheck isTransferResult = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isOutSide = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isSdk = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isApp = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isCookie = new PrivacyPolicyCheck();
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            List<TPrivacySensitiveWord> sensitiveWords = detectData.getPrivacySensitiveWords();
            if (SensitiveUtils.havePlaintextTransmission(sensitiveWords)) {
                isTransferResult.setNonCompliance(true);
                isTransferResult.getBehaviorStageEnums().add(entry.getKey());
            }

            List<TPrivacyOutsideAddress> outsideAddresses = detectData.getPrivacyOutsideAddresses();
            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                for (TPrivacyOutsideAddress outsideAddress : outsideAddresses) {
                    if (outsideAddress.getOutside() == PrivacyStatusEnum.YES.getValue()) {
                        isOutSide.setNonCompliance(true);
                        isOutSide.getBehaviorStageEnums().add(entry.getKey());
                        break;
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(sensitiveWords)) {
                for (TPrivacySensitiveWord sensitiveWord : sensitiveWords) {
                    if (CookieMarkEnum.HAVE.getValue().equals(sensitiveWord.getCookieMark())) {
                        isCookie.setNonCompliance(true);
                        isCookie.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }

            if (entry.getKey() == BehaviorStageEnum.BEHAVIOR_GRANT) {
                List<TPrivacyActionNougat> actionNougats = detectData.getPrivacyActionNougats();
                for (TPrivacyActionNougat actionNougat : actionNougats) {
                    if (ExecutorTypeEnum.APP.getValue().equals(actionNougat.getExecutorType())
                            && StringUtils.equals(actionNougat.getIsPersonal(), String.valueOf(PrivacyStatusEnum.YES.getValue()))) {
                        isApp.setNonCompliance(true);
                        isApp.getBehaviorStageEnums().add(entry.getKey());
                    }
                    if (ExecutorTypeEnum.SDK.getValue().equals(actionNougat.getExecutorType())
                            && StringUtils.equals(actionNougat.getIsPersonal(), String.valueOf(PrivacyStatusEnum.YES.getValue()))) {
                        isSdk.setNonCompliance(true);
                        isSdk.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }
        }

        try {
            TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());
            // 数据更新，需要taskId
            taskDetail.setTaskId(task.getTaskId());
            // 清除历史记录
            privacyPolicyResultMapper.deleteByTaskId(task.getTaskId());
            Map<String, TPrivacyPolicyItem> itemMap = new HashMap<>();
            for (TPrivacyPolicyItem privacyPolicyItem : privacyPolicyItemMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue())) {
                itemMap.put(privacyPolicyItem.getItem_no(), privacyPolicyItem);
            }
            List<TPrivacyPolicyResult> policyResultList = new ArrayList<>();
            // 快速检测才检测
            if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
                //个人信息保护政策检测
                policyResultList.add(buildPrivacyPolicyDetailResult(taskDetail, commonDetectInfo, itemMap));
            }
            privacyPolicyResultMapper.insertList(policyResultList);
        } catch (Exception e) {
            e.getMessage();
        }
    }

    private CommonDetectInfo buildHarmonyDetectInfo(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        log.info("TaskId:{} buildHarmonyDetectInfo uncompress={}", task.getTaskId(), uncompress);

        // 组装公共参数
        CommonDetectInfo commonDetectInfo = buildCommonDetectInfo(task, uncompress, detectDataMap);

        // 从uncomperss中解析出的数据
        List<ResultDataLogBO> resultDataLogBOList = new ArrayList<>();
        File[] files = new File(uncompress).listFiles();
        if (files == null) {
            throw new UncompressFailException("数据文件不存在");
        }
        for (File file:files) {
            if (StringUtils.startsWith(file.getName(), "resultFiles")) {
                resultDataLogBOList.addAll(harmonyResultDataLogParser.parser(file.getAbsolutePath(), commonDetectInfo.getApkName()));
            }
        }
        commonDetectInfo.setResultDataLogs(resultDataLogBOList);
        List<ResultDataLogBO> privacyResults = new ArrayList<>();
        for (ResultDataLogBO resultDataLogBO : resultDataLogBOList) {
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                privacyResults.add(resultDataLogBO);
            }
        }
        List<PrivacyPolicyTextInfo> subPagePrivacyDetailList = getSubPagePrivacyDetailByHtmlDir(uncompress + File.separator + "privacyhtml");
        TAssets assets = assetsMapper.getAssetIncludeDeletedByTaskId(task.getTaskId());
        List<ResultDataLogBO> privacyDetailResults = getPrivacyDetailList(privacyResults);
        // 先解析清单
        if (StringUtils.isNotBlank(assets.getThirdPartyShareListPath())) {
            analysisCheckListByUpload(assets, commonDetectInfo);
        } else {
            analysisCheckListByUi(commonDetectInfo, privacyDetailResults, subPagePrivacyDetailList);
        }
        
        //走一遍OCR
        if (privacyResults.isEmpty() && subPagePrivacyDetailList.isEmpty()){
        	for (ResultDataLogBO resultDataLogBO : resultDataLogBOList) {
        		ocrByImagePath(resultDataLogBO.getUiDumpResult(), uncompress+ File.separator +resultDataLogBO.getImgPath());
        		UIDumpResult dumpResult = resultDataLogBO.getUiDumpResult();
        		String ocrText = dumpResult.getOcrText();
        		String fullTextStart = getChinaWord(ocrText);
        		if(MiitWordKit.checkIsContainsPrivacy(fullTextStart) && 
        				!fullTextStart.contains("儿童隐私") && 
        				!fullTextStart.contains("用户协议") && 
        				!fullTextStart.contains("用户服务协议") && 
        				fullTextStart.contains(assets.getName())) {
        				resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue());
                        privacyResults.add(resultDataLogBO);
                        break;
        		}
            }
        }
        
        // 解析隐私政策
        if (StringUtils.isNotBlank(assets.getPrivacyPolicyPath())) {
            // 用户有上传隐私文件
            analysisUserUploadPrivacyPolicy(assets, commonDetectInfo);
        } else if (privacyResults.isEmpty() && subPagePrivacyDetailList.isEmpty()) {
            commonDetectInfo.setHasPrivacyPolicy(false);
            commonDetectInfo.setPrivacyPolicyImg(null);
            commonDetectInfo.setPrivacyPolicyContent(null);
        } else {
            analysisDetectPrivacyPolicy(task, commonDetectInfo, privacyResults, subPagePrivacyDetailList);
        }
        // 没有单独的第三方SDK清单，尝试从隐私政策中解析
        analysisCheckListByPolicyContent(commonDetectInfo);
        commonDetectInfo.setTensorflowCheckBoxModelPath(checkboxModelPath);
        commonDetectInfo.setTensorflowCheckedModelPath(checkedModelPath);
        return commonDetectInfo;
    }
    
    //获取前面50个中文字来判断隐私政策内容
    private String getChinaWord(String privacyContentNew){
    	String fullTextStart = "";
    	if (privacyContentNew != null) {
    	    int chineseCount = 0;
    	    int endIndex = 0;
    	    for (int i = 0; i < privacyContentNew.length(); i++) {
    	        char c = privacyContentNew.charAt(i);
    	        // 判断是否为中文字符（基本Unicode范围）
    	        if (c >= '\u4E00' && c <= '\u9FFF') {
    	            chineseCount++;
    	            if (chineseCount == 50) {
    	                endIndex = i + 1; // 截取到当前字符的下一位
    	                break;
    	            }
    	        }
    	        endIndex = i + 1; // 更新索引以防中文字符不足50个
    	    }
    	    // 确保不超过字符串长度
    	    endIndex = Math.min(endIndex, privacyContentNew.length());
    	    fullTextStart = privacyContentNew.substring(0, endIndex);
    	}
    	return fullTextStart;
    }
    
    private String ocrByImagePath(UIDumpResult result, String imagePath) {
        // 先检测截图是否OCR过了
        if (CollectionUtils.isEmpty(result.getOcrList())) {
            List<RecognizeData> ocrList = ocrService.extractText(imagePath);
            result.setOcrList(ocrList);
            String ocrText = ocrList.stream()
                    .flatMap(data -> data.getData().stream().map(RecognizeData.DataDTO::getText))
                    .collect(Collectors.joining());
            result.setOcrText(ocrText);
            result.setFullText(ocrText);
        }
        return result.getOcrText();
    }
    
    

    private void analysisDetectPrivacyPolicy(TTask task,
                                             CommonDetectInfo commonDetectInfo,
                                             List<ResultDataLogBO> privacyResults,
                                             List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        commonDetectInfo.setHasPrivacyPolicy(true);
        // 隐私文本拼接 取最长文本图片
        String privacyImagePath = null;
        String privacyContent = null;
        int maxLength = 0;
        StringBuilder privacyContentBuilder = new StringBuilder();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && StringUtils.isNotBlank(privacyResult.getUiDumpResult().getFullText())) {
                String[] uiList = privacyResult.getUiDumpResult().getFullText().split("\n");
                for (String string : uiList) {
                    if (StringUtils.isNoneBlank(string) && string.startsWith("。")) {
                        string = string.replaceFirst("。", "");
                    }
                    if (!privacyContentBuilder.toString().contains(string)) {
                        privacyContentBuilder.append(string).append("\n");
                    }
                }
                int length = privacyResult.getUiDumpResult().getFullText().length();
                if (length > maxLength) {
                    maxLength = length;
                    privacyImagePath = privacyResult.getImgPath();
                    privacyContent = privacyContentBuilder.toString().trim();
                }
            }
        }
        if (StringUtils.isBlank(privacyImagePath) && !subPagePrivacyDetailList.isEmpty()) {
            privacyContent = subPagePrivacyDetailList.get(0).content;
        }
        commonDetectInfo.setPrivacyPolicyContent(mergePrivacyPolicySubPage(privacyContent, subPagePrivacyDetailList));
        uploadPrivacyPolicyImage(task, commonDetectInfo, privacyImagePath);
        setPrivacyPolicyNlp(commonDetectInfo);
    }

    private List<ResultDataLogBO> getPrivacyDetailList(List<ResultDataLogBO> privacyResults) {
        // 隐私政策详情文件
        List<ResultDataLogBO> privacyDetailResults = new ArrayList<>();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && privacyResult.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()) {
                privacyDetailResults.add(privacyResult);
            }
        }
        // 没详情文件 取所有的
        if (privacyDetailResults.size() == 0) {
            privacyDetailResults.addAll(privacyResults);
        }
        return privacyDetailResults;
    }

    //解析是否存在PDF隐私政策链接
    private static void pdfPrivacyContent(String uncompress, List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) throws IOException {
        List<String> filePaths = CommonUtil.listAllFilesInDir(uncompress + File.separator + "capture_info", new String[]{".txt", ".TXT"}, true);
        if (filePaths.size() == 0) {
            return;
        }
        long t1 = System.currentTimeMillis();
        List<String> list = null;
        try {
            //得到PDF链接地址
            list = URLTokenizer.threadSegment(filePaths);
        } catch (InterruptedException | ExecutionException e) {
            e.getMessage();
        }

        if (list == null || list.size() == 0) {
            return;
        }
        log.info("得到PDF链接地址:{}", list.toString());
        for (int i = 0; i < list.size(); i++) {
            //识别PDF内容
            String url = list.get(i);
            String fileName = url.substring(url.lastIndexOf("/") + 1, url.length());
            String pdfLoalPath = uncompress + File.separator + fileName;
//			HttpUtils.download(list.get(i), pdfLoalPath, null);
            FileUtils.copyURLToFile(new URL(list.get(i)), new File(pdfLoalPath));
            if (!new File(pdfLoalPath).exists()) {
                continue;
            }
            String privacyContent = PdfToTextFileUtil.getPdfFileText(pdfLoalPath);
            if (StringUtils.isBlank(privacyContent)) {
                continue;
            }
            boolean isContainsPirvacy = cn.ijiami.detection.miit.kit.MiitWordKit.checkIsContainsPrivacy(privacyContent);
            if (!isContainsPirvacy || privacyContent.length() < 200) {
                continue;
            }
            PrivacyPolicyTextInfo info = new PrivacyPolicyTextInfo(fileName, privacyContent);
            subPagePrivacyDetailList.add(info);
        }
        long t2 = System.currentTimeMillis();
        log.info("识别耗时：{}", (t2 - t1));
    }

    public static String getMD5Str(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            log.error("Error calculating MD5 for input: {}", str, e);
            return null;
        }
    }

    /**
     * 取最长的文本
     *
     * @param privacyResults
     * @return
     */
    private String getPrivacyContent(List<ResultDataLogBO> privacyResults) {
        StringBuilder result = new StringBuilder();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && StringUtils.isNotBlank(privacyResult.getUiDumpResult().getFullText())) {
                result.append(privacyResult.getUiDumpResult().getFullText());
                if (!StringUtils.endsWith(privacyResult.getUiDumpResult().getFullText(), "。")) {
                    result.append("。");
                }
            }
        }
        return result.toString();
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @param sdkLibraries
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisUncompress(TaskDetailVO taskDetailVo, String uncompress, List<TSdkLibrary> sdkLibraries) {
        List<TSensitiveWord> sensitiveWords = sensitiveWordMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                String fileNames = entry.getValue();
                DetectDataBO detectData = analyzeZipAction(behaviorStage, fileNames, uncompress, taskDetailVo, sdkLibraries, sensitiveWords);
                if (Objects.nonNull(detectData)) {
                    detectDataMap.put(behaviorStage, detectData);
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return detectDataMap;
    }

    /**
     * 检测数据入库
     *
     * @param detectDataMap
     */
    private void insertTaskAnalyzeResult(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, Map<Long, String> screenshotMap, Long taskId) {
        log.info("insertTaskAnalyzeResult");
        // 获取静态检测已经入库的检测项数据(标记为：静态数据)
        List<TPrivacyOutsideAddress> outsideAddresses = privacyOutsideAddressMapper.findByTaskId(taskId, 0);
        Map<Long, TActionNougat> actionNougatMap = actionNougatMapper.findByTerminalType(TerminalTypeEnum.HARMONY.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        // 数据重新入库
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            // 计算行为触发次数（秒/次）
            setNougatsCycleTrigger(detectData.getPrivacyActionNougats(), TerminalTypeEnum.HARMONY);
            // 堆栈数据
            List<List<TPrivacyActionNougat>> actionInserts = DataHandleUtil.split(detectData.getPrivacyActionNougats());
            for (List<TPrivacyActionNougat> actionInsert : actionInserts) {
                privacyActionNougatMapper.insertList(actionInsert);
                List<TPrivacyActionNougatExtend> extendList = actionInsert.stream()
                        .filter(nougat -> StringUtils.isNoneBlank(nougat.getJniStackInfo()))
                        .map(TPrivacyActionNougatExtend::make)
                        .collect(Collectors.toList());
                if (!extendList.isEmpty()) {
                    privacyActionNougatExtendMapper.insertList(extendList);
                }
                screenshotImageService.saveDynamicBehaviorImg(taskId, screenshotMap, actionInsert);
            }

            // 通讯行为数据
            List<TPrivacyOutsideAddress> privacyOutsideAddresses = detectData.getPrivacyOutsideAddresses();
            // 静态数据重新入库
            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                outsideAddresses.forEach(o -> o.setId(null));
                privacyOutsideAddresses.addAll(outsideAddresses);
            }
            List<List<TPrivacyOutsideAddress>> outSideInserts = DataHandleUtil.split(privacyOutsideAddresses);
            for (List<TPrivacyOutsideAddress> outsideInsert : outSideInserts) {
                privacyOutsideAddressMapper.insertList(outsideInsert);
                screenshotImageService.saveImgOfOutsideData(taskId, screenshotMap, outsideInsert);
            }

            // 传输个人信息
            List<List<TPrivacySensitiveWord>> sensitiveInserts = DataHandleUtil.split(detectData.getPrivacySensitiveWords());
            for (List<TPrivacySensitiveWord> sensitiveInsert : sensitiveInserts) {
                privacySensitiveWordMapper.insertList(sensitiveInsert);
                screenshotImageService.saveDynamicBehaviorImgSensitiveWord(taskId, screenshotMap, sensitiveInsert);
            }

            // 存储个人信息
            List<List<TPrivacySharedPrefs>> shareInserts = DataHandleUtil.split(detectData.getPrivacySharedPrefs());
            for (List<TPrivacySharedPrefs> shareInsert : shareInserts) {
                privacySharedPrefsMapper.insertList(shareInsert);
                screenshotImageService.saveImgOfSharedData(taskId, screenshotMap, shareInsert);
            }
        }
    }

    /**
     * 保存压缩包并返回
     *
     * @param md5    文件md5
     * @param taskId 任务ID
     * @param suffix 文件后缀
     * @return
     */
    private String saveAndUncompress(String md5, Long taskId, String suffix, MultipartFile data) throws IOException {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = saveFile(md5, taskId, suffix, data);
        String uncompress = CommonUtil.uncompress(file.getAbsolutePath(), dynamicPath + UuidUtil.uuid());
        return uncompress;
    }

    protected Map<Long, TActionNougat> getActionNougatMap(TerminalTypeEnum terminalTypeEnum) {
        return actionNougatMapper.findByTerminalType(terminalTypeEnum.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
    }

    /**
     * 分析检测得到的数据
     *
     * @param behaviorStage
     * @param fileNames
     * @param uncompressed
     * @param taskDetailVO
     * @param sdks
     * @param sensitiveWords
     * @return
     */
    private DetectDataBO analyzeZipAction(BehaviorStageEnum behaviorStage, String fileNames, String uncompressed, TaskDetailVO taskDetailVO, List<TSdkLibrary> sdks,
                                          List<TSensitiveWord> sensitiveWords) {
        log.info("检测数据解析，任务ID：[{}]，资产名称：[{}]，解析阶段：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name(), behaviorStage.getName());
        String[] fileNamePaths = fileNames.split(",");
        Map<Long, TActionNougat> actionNougatMap = getActionNougatMap(TerminalTypeEnum.HARMONY);
        // 分析堆栈数据
        String behaviorFilePath = uncompressed + File.separator + fileNamePaths[0];
        List<TPrivacyActionNougat> privacyActionNougats = behaviorInfoAction.analyzeBehaviorInfo(behaviorFilePath, taskDetailVO, behaviorStage);
        // 是否只保存个人信息行为
        boolean onlySavePersonalBehavior = isOnlySavePersonalBehavior(taskDetailVO.getTaskId());
        if (onlySavePersonalBehavior) {
            privacyActionNougats = privacyActionNougats.stream()
                    .filter(nougat -> {
                        TActionNougat actionNougat = actionNougatMap.get(nougat.getActionId());
                        return Objects.nonNull(actionNougat) && actionNougat.getPersonal() == PrivacyStatusEnum.YES.getValue();
                    }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(privacyActionNougats)) {
            return null;
        }
        // 抓包文件不包含堆栈
        String captureInfoFilePath = uncompressed + File.separator + fileNamePaths[5];
        List<HarmonyCaptureInfoBO> captureInfoBOList = captureHostIpAction.analyzeCaptureInfo(captureInfoFilePath,
                Collections.emptyList(), taskDetailVO.getApk_name(), taskDetailVO.getApk_package());
        List<TPrivacyOutsideAddress> privacyOutsideAddresses = captureHostIpAction.getPrivacyOutsideAddressResult(captureInfoBOList,
                taskDetailVO.getTaskId(), behaviorStage);
        List<TPrivacySensitiveWord> privacySensitiveWords = captureHostIpAction.getPrivacySensitiveWordResult(captureInfoBOList, sensitiveWords,
                taskDetailVO.getTaskId(), behaviorStage);
        // 分析存储个人信息数据
        String sharedPrefFilePath = uncompressed + File.separator + fileNamePaths[3];
        List<TPrivacySharedPrefs> privacySharedPrefs = sharedPrefAction.analyzeSharedPref(sharedPrefFilePath, sensitiveWords);
        // 返回数据
        DetectDataBO data = new DetectDataBO();
        data.setPrivacyActionNougats(privacyActionNougats);
        data.setPrivacySensitiveWords(privacySensitiveWords);
        data.setPrivacyOutsideAddresses(privacyOutsideAddresses);
        data.setPrivacySharedPrefs(privacySharedPrefs);
        return data;
    }

    private void updateTaskInfoWhenAnalyzeRetry(TTask task) {
        // 非中断状态不更新
        if (Objects.isNull(task)) {
            return;
        }
        taskDAO.updateDynamicSuccess(task.getTaskId());
    }

    /**
     * 保存隐私条款
     *
     * @param taskId   任务id
     * @throws IOException
     * @throws IjiamiApplicationException
     */
    private void updateDeviceInfo(Long taskId, File txtFile) throws IOException, IjiamiApplicationException {
        String deviceNamePrefix = "设备名称: ";
        Optional<String> deviceNameOpt = findDeviceInfo(txtFile, deviceNamePrefix);
        if (deviceNameOpt.isPresent()) {
            String deviceName = deviceNameOpt.get().replace(deviceNamePrefix, "").trim();
            TTaskExtend tTaskExtendVO = new TTaskExtend();
            tTaskExtendVO.setModel(deviceName);
            tTaskExtendVO.setVersion("Harmony OS next");

            Example example = new Example(TTaskExtend.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("taskId", taskId);

            taskExtendMapper.updateByExampleSelective(tTaskExtendVO, example);
        }
    }

    private Optional<String> findDeviceInfo(File txtFile, String deviceNamePrefix) throws IOException {
        List<String> readLines = FileUtils.readLines(txtFile, "UTF-8");
        if (readLines.isEmpty()) {
            log.info("没有设备信息");
            return Optional.empty();
        }
        Optional<String> deviceNameOpt = readLines.stream().filter(s -> s.startsWith(deviceNamePrefix)).findFirst();
        if (deviceNameOpt.isPresent()) {
            return deviceNameOpt;
        } else {
            readLines = FileUtils.readLines(txtFile, "GBK");
            return readLines.stream().filter(s -> s.startsWith(deviceNamePrefix)).findFirst();
        }
    }

    /**
     * 上传图片
     *
     * @param file 文件
     * @return 文件key
     * @throws IjiamiApplicationException
     */
    private String uploadFile(File file) throws IjiamiApplicationException, IOException {
        if (file.exists()) {
            try {
                FileVO fileVO = new FileVO();
                fileVO.setFileName(file.getName());
                fileVO.setFileKey(UuidUtil.uuid());
                fileVO.setInputStream(Files.newInputStream(file.toPath()));
                fileVO.setFileExtName(file.getName().substring(file.getName().lastIndexOf(".")));
                fileVO.setFilePath(file.getAbsolutePath());
                fileService.uploadFile(fileVO);
                return fileVO.getFileKey();
            } finally {
                FileUtil.deleteFile(file);
            }
        }
        return null;
    }

    @Override
    public void handleManualFinish(TTask task, int dynamicType) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId() + ":" + dynamicType;
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            // 深度/快速检测
            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
                log.info("任务已经完成");
                return;
            }
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            Map<BehaviorStageEnum, DetectDataBO> detectDataBOMap = getManualDetectionData(task.getTaskId());
            // 手动截图数据关联
            insertManualScreenshotImageByTasId(task.getTaskId(), detectDataBOMap, task.getTerminalType());
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataBOMap, null);
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.MANUAL);
            // 更新任务状态后发送完成检测的消息，消息要在上传数据包之前发送，避免网络原因导致迟迟不发完成检测的消息
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "深度检测完成");
        } catch (Throwable e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, analysisErrorMsg(e));
            sendMessage(task, BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, "深度检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            dynamicTaskDataService.removeTaskContext(task);
        }
    }

}
