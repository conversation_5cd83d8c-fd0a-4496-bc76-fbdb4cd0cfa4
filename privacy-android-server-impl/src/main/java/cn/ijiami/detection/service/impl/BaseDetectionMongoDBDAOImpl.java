package cn.ijiami.detection.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Update;

import cn.ijiami.framework.mongodb.dao.impl.BaseMongoDAOImpl;

import java.util.Map;

@Slf4j
public abstract class BaseDetectionMongoDBDAOImpl<T> extends BaseMongoDAOImpl<T> {

    @Override
    public void update(Map<String, Object> paramMap, Update update) {
        if (update.getUpdateObject().keySet().isEmpty()) {
            log.info("update is empty paramMap={}", paramMap);
        } else {
            log.info("update paramMap={} update={}", paramMap, update);
            super.update(paramMap, update);
        }
    }
}
