package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AbstractAppletDynamicDetectionService.java
 * @Description 小程序解析
 * @createTime 2024年10月22日 17:28:00
 */
@Slf4j
public abstract class AbstractAppletDynamicDetectionService<T extends TaskDetailVO> extends AbstractDynamicDetectionService<T> {

    @Autowired
    protected CacheService cacheService;

    protected void moveDetectionFileToUncompress(Long taskId, String uncompress) throws UncompressFailException {
        String cacheFileKey = PinfoConstant.CACHE_MINI_PROGRAM_FILE + taskId;
        List<String> filePathList = cacheService.opsForStringList().range(cacheFileKey, 0, -1);
        if (Objects.isNull(filePathList)) {
            throw new UncompressFailException("文件不存在");
        }
        try {
            File uncompressDir = new File(uncompress);
            for (String filePath : filePathList) {
                File file = new File(filePath);
                if (file.exists()) {
                    FileUtils.moveFileToDirectory(file, uncompressDir, false);
                }
            }
        } catch (Exception e) {
            throw new UncompressFailException(e);
        }
        cacheService.delete(cacheFileKey);
    }


    protected void saveStageData(Long taskId, Long dataId, IdbStagedDataEnum stageEnum, Map<BehaviorStageEnum, DetectDataBO> detectDataMap,
                                 Map<Long, String> screenshotMap) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 事物隔离级别
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        // 事务传播行为
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transaction = dataSourceTransactionManager.getTransaction(def);
        try {
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, taskId, screenshotMap);
            TTask task = taskMapper.findByTaskIdForUpdate(taskId);
            TTask updateTask = new TTask();
            updateTask.setTaskId(taskId);
            updateTask.setUpdateTime(new Date());
            DynamicAutoSubStatusEnum subStatus = DynamicAutoSubStatusEnum.getItem(stageEnum.getValue());
            if (Objects.isNull(subStatus)) {
                throw new IjiamiRuntimeException("阶段数据错误");
            }
            if (task.getDynamicSubStatus().getValue() < subStatus.getValue()) {
                updateTask.setDynamicSubStatus(subStatus);
            }
            taskDataService.analysisTaskDataSuccess(dataId);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            dataSourceTransactionManager.commit(transaction);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(transaction);
            throw e;
        }
    }

    /**
     * 检测数据入库
     *
     * @param detectDataMap
     */
    protected void insertTaskAnalyzeResult(Map<BehaviorStageEnum, DetectDataBO> detectDataMap, Long taskId, Map<Long, String> screenshotMap) {
        log.info("insertTaskAnalyzeResult");
        // 数据重新入库
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            // 计算行为触发次数（秒/次）
            setNougatsCycleTrigger(detectData.getPrivacyActionNougats(), TerminalTypeEnum.ALIPAY_APPLET);
            // 堆栈数据
            List<List<TPrivacyActionNougat>> actionInserts = DataHandleUtil.split(detectData.getPrivacyActionNougats());
            for (List<TPrivacyActionNougat> actionInsert : actionInserts) {
                privacyActionNougatMapper.insertList(actionInsert);
                //新增保存应用行为截图的方法 2021/6/11
                screenshotImageService.saveDynamicBehaviorImg(taskId, screenshotMap, actionInsert);
            }

            // 通讯行为数据
            List<TPrivacyOutsideAddress> privacyOutsideAddresses = detectData.getPrivacyOutsideAddresses();
            List<List<TPrivacyOutsideAddress>> outSideInserts = DataHandleUtil.split(privacyOutsideAddresses);
            for (List<TPrivacyOutsideAddress> outsideInsert : outSideInserts) {
                privacyOutsideAddressMapper.insertList(outsideInsert);
                //新增保存应用行为截图的方法 2021/7/8
                screenshotImageService.saveImgOfOutsideData(taskId, screenshotMap, outsideInsert);
            }

            // 传输个人信息
            List<List<TPrivacySensitiveWord>> sensitiveInserts = DataHandleUtil.split(detectData.getPrivacySensitiveWords());
            for (List<TPrivacySensitiveWord> sensitiveInsert : sensitiveInserts) {
                privacySensitiveWordMapper.insertList(sensitiveInsert);
                //新增保存应用行为截图的方法 2021/6/16
                screenshotImageService.saveDynamicBehaviorImgSensitiveWord(taskId, screenshotMap, sensitiveInsert);
            }

            // 存储个人信息
            List<List<TPrivacySharedPrefs>> shareInserts = DataHandleUtil.split(detectData.getPrivacySharedPrefs());
            for (List<TPrivacySharedPrefs> shareInsert : shareInserts) {
                privacySharedPrefsMapper.insertList(shareInsert);
                //新增保存应用行为截图的方法 2021/6/16
                screenshotImageService.saveImgOfSharedData(taskId, screenshotMap, shareInsert);
            }
        }
    }

}
