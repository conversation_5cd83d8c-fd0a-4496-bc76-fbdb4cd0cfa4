package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.enums.IosDynamicDetectionCmdEnum;
import cn.ijiami.detection.enums.IosDynamicDetectionSubCmdEnum;
import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.IdbDevice;
import cn.ijiami.detection.idb.IdbDeviceList;
import cn.ijiami.detection.idb.request.BaseIdbRequest;
import cn.ijiami.detection.idb.request.StartIosDynamicDetectionRequest;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.service.api.IdbManagerService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.exception.IdbStartException;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.detection.websocket.idb.IdbWebSocketClient;
import cn.ijiami.framework.kit.utils.UuidUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class IdbManagerServiceImpl implements IdbManagerService {

    @Autowired(required = false)
    private IdbWebSocketClient iosIdbSocketClient;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Value("${ijiami.ios.remote.tool.timeout:60}")
    private Integer idbTimeout;

    @Override
    public boolean availableIosIdb() {
        return iosIdbSocketClient != null;
    }

    @Override
    public void iosDeviceList(IdbCallback<List<IdbDevice>> callback) {
        IdbCmd<BaseIdbRequest> getDeviceList = new IdbCmd<>();
        getDeviceList.setCmdType(IosDynamicDetectionCmdEnum.GET_DEVICE_LIST.getValue());
        getDeviceList.setRequestId(UuidUtil.uuid());
        DeviceResponseMessage messageCallback = new DeviceResponseMessage(callback);
        executorServiceHelper.schedule(() -> {
            if (!messageCallback.isResponse) {
                iosIdbSocketClient.removeCallbackByRequestId(getDeviceList.getRequestId());
                if (callback != null) callback.onResponseTimeout();
            }
        }, idbTimeout, TimeUnit.SECONDS);
        doSendToIosIdb(getDeviceList, messageCallback);
    }

    /**
     * 发送消息
     * @param message
     * @param callback
     */
    private void doSendToIosIdb(IdbCmd<? extends BaseIdbRequest> message, IdbWebSocketClient.SendMessageListener callback) {
        iosIdbSocketClient.sendMessage(message, callback);
    }

    @Override
    public void startIosDynamicDetection(Long taskId, String packageName, String appUrl, String uploadUrl, String deviceId, IdbCallback<IdbCmd<BaseIdbResponse>> callback) throws IdbStartException {
        if (StringUtils.isBlank(appUrl) || !appUrl.contains("http") || StringUtils.isBlank(deviceId) || StringUtils.isBlank(uploadUrl)) {
            throw new IdbStartException(String.format("参数错误 appUrl=%s deviceId=%s uploadUrl=%s", appUrl, deviceId, uploadUrl));
        }
        IdbCmd<StartIosDynamicDetectionRequest> startDetection = new IdbCmd<>();
        startDetection.setCmdType(IosDynamicDetectionCmdEnum.AUTO_DETECTION.getValue());
        startDetection.setRequestId(UuidUtil.uuid());
        startDetection.setDeviceId(deviceId);

        StartIosDynamicDetectionRequest request = new StartIosDynamicDetectionRequest();
        request.setBundleId(packageName);
        request.setTaskId(taskId);
        request.setAppUrl(appUrl);
        request.setUploadUrl(uploadUrl);
        startDetection.setCmdData(request);
        doSendToIosIdb(startDetection, new AckMessageListener(taskId, callback));
    }

    @Override
    public void restartIosDynamicDetection(Long taskId, String packageName, String appUrl, String uploadUrl, String deviceId, IdbCallback<IdbCmd<BaseIdbResponse>> callback) {
        if (StringUtils.isBlank(appUrl) || !appUrl.contains("http") || StringUtils.isBlank(deviceId) || StringUtils.isBlank(uploadUrl)) {
            log.error("参数错误 appUrl={} deviceId={} uploadUrl={}", appUrl, deviceId, uploadUrl);
            return;
        }
        IdbCmd<StartIosDynamicDetectionRequest> startDetection = new IdbCmd<>();
        startDetection.setCmdType(IosDynamicDetectionCmdEnum.RESTART_FAST_DETECTION.getValue());
        startDetection.setRequestId(UuidUtil.uuid());
        startDetection.setDeviceId(deviceId);

        StartIosDynamicDetectionRequest request = new StartIosDynamicDetectionRequest();
        request.setBundleId(packageName);
        request.setTaskId(taskId);
        request.setAppUrl(appUrl);
        request.setUploadUrl(uploadUrl);
        startDetection.setCmdData(request);
        doSendToIosIdb(startDetection, new AckMessageListener(taskId, callback));
    }

    @Override
    public void stopIosDynamicDetection(Long taskId, String packageName, String deviceId, IosDynamicDetectionSubCmdEnum taskType, IdbCallback<BaseIdbResponse> callback) {
        IdbCmd<BaseIdbRequest> stopDynamicDetection = new IdbCmd<>();
        stopDynamicDetection.setCmdType(IosDynamicDetectionCmdEnum.STOP_DETECTION.getValue());
        stopDynamicDetection.setRequestId(UuidUtil.uuid());
        stopDynamicDetection.setSubCmdType(taskType.getValue());
        stopDynamicDetection.setDeviceId(deviceId);

        BaseIdbRequest request = new BaseIdbRequest();
        request.setTaskId(taskId);
        request.setBundleId(packageName);
        stopDynamicDetection.setCmdData(request);
        doSendToIosIdb(stopDynamicDetection, new ResponseMessageListener(taskId, IosDynamicDetectionCmdEnum.STOP_DETECTION, callback));
    }

    @Override
    public void queryIosDynamicDetectionTask(Long taskId, String deviceId, IdbCallback<BaseIdbResponse> callback) {
        IdbCmd<BaseIdbRequest> queryTaskRequest = new IdbCmd<>();
        queryTaskRequest.setCmdType(IosDynamicDetectionCmdEnum.QUERY_TASK_REQUEST.getValue());
        queryTaskRequest.setRequestId(UuidUtil.uuid());
        queryTaskRequest.setDeviceId(deviceId);

        BaseIdbRequest request = new BaseIdbRequest();
        request.setTaskId(taskId);
        queryTaskRequest.setCmdData(request);
        doSendToIosIdb(queryTaskRequest, new ResponseMessageListener(taskId, IosDynamicDetectionCmdEnum.QUERY_TASK_RESULT, callback));
    }

    @Data
    static class IdbSendInfo {
        private IdbCmd<? extends BaseIdbRequest> idbCmd;
        private IdbWebSocketClient.SendMessageListener listener;

        public IdbSendInfo(IdbCmd<? extends BaseIdbRequest> idbCmd, IdbWebSocketClient.SendMessageListener listener) {
            this.idbCmd = idbCmd;
            this.listener = listener;
        }
    }

    static class ResponseMessageListener implements IdbWebSocketClient.SendMessageListener {

        private final Long taskId;
        private final IdbCallback<BaseIdbResponse> callback;
        private final IosDynamicDetectionCmdEnum responseCmd;

        ResponseMessageListener(Long taskId, IosDynamicDetectionCmdEnum responseCmd, IdbCallback<BaseIdbResponse> callback) {
            this.taskId = taskId;
            this.responseCmd = responseCmd;
            this.callback = callback;
        }

        @Override
        public void onSendFailure(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onSendSuccess(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendSuccess();
            }
        }

        @Override
        public void onAckTimeout(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onAckSuccess(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd) {
            if (callback != null) {
                callback.onAckSuccess(responseIdbCmd);
            }
        }

        @Override
        public boolean onResponse(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd, String message) {
            if (responseIdbCmd.getCmdData().getTaskId() != null && !taskId.equals(responseIdbCmd.getCmdData().getTaskId())) {
                log.error("任务id错误 ackTaskId={} taskId={}", responseIdbCmd.getCmdData().getTaskId(), taskId);
                return false;
            }
            if (responseCmd.getValue().equals(responseIdbCmd.getCmdType())) {
                if (callback != null) {
                    callback.onResponse(responseIdbCmd.getCmdData());
                }
                return true;
            }
            return false;
        }
    }

    static class AckMessageListener implements IdbWebSocketClient.SendMessageListener {

        private final Long taskId;
        private final IdbCallback<IdbCmd<BaseIdbResponse>> callback;

        AckMessageListener(Long taskId, IdbCallback<IdbCmd<BaseIdbResponse>> callback) {
            this.taskId = taskId;
            this.callback = callback;
        }

        @Override
        public boolean onResponse(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd, String message) {
            return true;
        }

        @Override
        public void onSendFailure(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onSendSuccess(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendSuccess();
            }
        }

        @Override
        public void onAckTimeout(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onAckSuccess(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd) {
            // 只处理ack消息，不在处理其他消息，移除回调
            client.removeCallbackByRequestId(responseIdbCmd.getRequestId());
            if (responseIdbCmd.getCmdData().getTaskId() != null && !taskId.equals(responseIdbCmd.getCmdData().getTaskId())) {
                log.error("任务id错误 ackTaskId={} taskId={}", responseIdbCmd.getCmdData().getTaskId(), taskId);
                return;
            }
            if (callback != null) {
                callback.onAckSuccess(responseIdbCmd);
            }
        }
    }

    static class DeviceResponseMessage implements IdbWebSocketClient.SendMessageListener {

        private final IdbCallback<List<IdbDevice>> callback;
        private Boolean isResponse = false;

        public DeviceResponseMessage(IdbCallback<List<IdbDevice>> callback) {
            this.callback = callback;
        }

        @Override
        public boolean onResponse(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd, String message) {
            // 可能是调用响应或者设备信息
            if (IosDynamicDetectionCmdEnum.GET_DEVICE_LIST.getValue().equals(responseIdbCmd.getCmdType())) {
                isResponse = true;
                log.info("获取设备信息返回");
                IdbDeviceList deviceList = CommonUtil.jsonToBean(message, new TypeReference<IdbDeviceList>() {
                });
                if (callback != null && Objects.nonNull(deviceList)) {
                    callback.onResponse(deviceList.getDeviceList());
                }
                return true;
            }
            return false;
        }

        @Override
        public void onSendFailure(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onSendSuccess(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendSuccess();
            }
        }

        @Override
        public void onAckTimeout(IdbWebSocketClient client) {
            if (callback != null) {
                callback.onSendFailure();
            }
        }

        @Override
        public void onAckSuccess(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd) {
            if (callback != null) {
                callback.onAckSuccess(responseIdbCmd);
            }
        }

        public Boolean getResponse() {
            return isResponse;
        }
    }

}