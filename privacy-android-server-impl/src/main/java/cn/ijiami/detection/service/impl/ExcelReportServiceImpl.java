package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.VO.statistics.*;
import cn.ijiami.detection.android.client.dto.AssetsStatisticsDetailDTO;
import cn.ijiami.detection.android.client.param.AssetsStatisticsParam;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.*;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.manager.user.entity.User;
import com.github.pagehelper.PageHelper;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.WriteExcel;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.manager.syslog.entity.TsysOperateLog;

import static cn.ijiami.detection.utils.CommonUtil.*;

/**
 * <AUTHOR>
 */
@Service
public class ExcelReportServiceImpl implements IExcelReportService {

	private static final Logger LOG = LoggerFactory.getLogger(ExcelReportServiceImpl.class);
		
	@Autowired
	private TExcelReportMapper excelReportMapper;
	@Autowired
	private TPrivacySensitiveWordMapper privacySensitiveWordMapper;
	@Autowired
	private TPrivacySharedPrefsMapper privacySharedPrefsMapper;
	@Autowired
	private TSuspiciousSdkMapper suspiciousSdkMapper;
	@Autowired
	private TPrivacyLawsResultMapper privacyLawsResultMapper;
	@Autowired
	private IjiamiCommonProperties commonProperties;
	@Autowired
	private TPermissionMapper permissionMapper;
	@Value("${fastDFS.ip}")
	private String fastDFSIp;
	@Autowired
	private TAssetsMapper assetsMapper;
	@Autowired
	private TPrivacyActionNougatMapper privacyActionNougatMapper;
	@Autowired
	@Lazy
	private IPrivacyDetectionService privacyDetectionService;
	@Autowired
	private TTaskMapper taskMapper;
	@Autowired
	private TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

	@Autowired
	private ISysLogMapper sysLogMapper;

	@Autowired
	private IMiitDetectService miitDetectService;

	@Autowired
	private TPrivacyLawsDetailMapper privacyLawsDetailMapper;

	@Autowired
	private IBaseFileService fileService;

	@Autowired
	private IAddWatermarkService addWatermarkService;

	@Autowired
	private StatisticsService statisticsService;

	@Override
	public List<TExcelReport> findList() {
		return excelReportMapper.selectAll();
	}

	@Override
	public File downloadExcelData(ExcelReportQuery query, File reportDir, User user) {
		String endTime = query.getEndTime();
		if (StringUtils.isNoneBlank(endTime) && endTime.contains("00:00:00")) {
			endTime = endTime.replace("00:00:00", "23:59:59");
			query.setEndTime(endTime);
		}
		Integer id = query.getId();
		File file = null;
		switch (id) {
		case 1:
			file = misjudgment01(query,reportDir,user);
			break;
		case 2: //164
			file = misjudgment02(query,reportDir, 1,user);
			break;
		case 3:
			file = misjudgment03(query,reportDir,user);
			break;
		case 4:  //191
			file = misjudgment02(query,reportDir, 2,user);
			break;
		case 99:
			file = misjudgment99(query,reportDir,user);
			break;
		default:
			break;
		}
		
		if(file != null && file.exists() && id != 99) {
			TExcelReport up = new TExcelReport();
			up.setUpdateTime(new Date());
			up.setId(Long.parseLong(String.valueOf(id)));
			excelReportMapper.updateByPrimaryKeySelective(up);
		}
		return file;
	}
	
	private File misjudgment01(ExcelReportQuery query,File reportDir,User user){
		
		String buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/传输个人信息误报记录_年月日时分秒.xlsx";
		
		File file = new File(reportDir.getAbsoluteFile()+ File.separator+"传输个人信息误报记录_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}
		
		if(file.exists()) {
			//传输个人信息
			List<SensitiveWordExcelReportVO> wordList = privacySensitiveWordMapper.findMisjudgmentData(query.getUserId(), query.getStartTime(), query.getEndTime());
			for (SensitiveWordExcelReportVO vo : wordList) {
				if(StringUtils.isNoneBlank(vo.getPath()) && vo.getPath().contains("http")){
					continue;
				}
				vo.setPath(fastDFSIp + "/" +vo.getPath());
			}
			WriteExcel.writeExcelMisjudgment01(wordList, file.getAbsolutePath());
			LOG.info("开始添加隐形水印");
			addWatermarkService.watermarkToReport(file,user);
		}
		
		buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/存储个人信息误报记录_年月日时分秒.xlsx";
		file = new File(reportDir.getAbsoluteFile()+ File.separator+"存储个人信息误报记录_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}
		if(file.exists()) {
			//存储个人信息
			List<SharedPrefsExcelReportVO>  prefsList = privacySharedPrefsMapper.findMisjudgmentData(query.getUserId(), query.getStartTime(), query.getEndTime());
			if(prefsList != null && prefsList.size()>0) {
				for (SharedPrefsExcelReportVO sharedPrefsExcelReportVO : prefsList) {
					if(StringUtils.isNoneBlank(sharedPrefsExcelReportVO.getAppPath()) && sharedPrefsExcelReportVO.getAppPath().contains("http")){
						continue;
					}
					sharedPrefsExcelReportVO.setAppPath(fastDFSIp + "/" +sharedPrefsExcelReportVO.getAppPath());
				}
			}
			WriteExcel.writeExcelMisjudgment02(prefsList, file.getAbsolutePath());
			LOG.info("开始添加隐形水印");
			addWatermarkService.watermarkToReport(file,user);
		}
		
		String zipName = "收集使用个人信息误报记录_"+ new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
        String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
        file = new File(compress);
        if(new File(reportDir.getAbsolutePath()).exists()){
        	try {
				FileUtils.deleteDirectory(new File(reportDir.getAbsolutePath()));
			} catch (IOException e) {
				e.getMessage();
			}
        }
		return file;
	}
	
	private File misjudgment02(ExcelReportQuery query ,File reportDir, Integer lawId,User user){
		String buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/工信部164号文误报记录_年月日时分秒.xlsx";
		
		File file = null;
		if(lawId==2) {
			file = new File(reportDir.getAbsoluteFile()+ File.separator+"191号文误报记录_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		}else {
			file = new File(reportDir.getAbsoluteFile()+ File.separator+"工信部164号文误报记录_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		}
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}
		
		if(file.exists()) {
			//164号文误报
			List<PrivacyLawsMisjudgmentVO> lawsList = privacyLawsResultMapper.findMisjudgmentData(query.getUserId(), query.getStartTime(), query.getEndTime(), lawId);
			if(lawsList != null && lawsList.size()>0) {
				for (PrivacyLawsMisjudgmentVO vo : lawsList) {
					vo.setPath(fastDFSIp + "/" +vo.getPath());
				}
			}
			WriteExcel.writeExcelMisjudgment03(lawsList, file.getAbsolutePath());
			LOG.info("开始添加工信部164号文或191号文误报记录隐形水印");
			addWatermarkService.watermarkToReport(file,user);
		}
		return file;
	}
	
	private File misjudgment03(ExcelReportQuery query ,File reportDir,User user){
		String buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/疑似SDK_年月日时分秒.xlsx";
		
		File file = new File(reportDir.getAbsoluteFile()+ File.separator+"疑似SDK_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}
		
		if(file.exists()) {
			//疑似SDK
			List<SuspiciousSdkDataVO> sdkList = suspiciousSdkMapper.findSuspiciousSdkData(query.getUserId(), query.getStartTime(), query.getEndTime());
			if(sdkList != null && sdkList.size()>0) {
				for (SuspiciousSdkDataVO suspiciousSdkDataVO : sdkList) {
					String codes = suspiciousSdkDataVO.getCodes();
					if(StringUtils.isBlank(codes) || codes.split(",").length==0) {
						continue;
					}
					StringBuffer sfCodes = new StringBuffer();
					StringBuffer sfRemark = new StringBuffer();
					List<PermissionVO> permissionVOS = permissionMapper.findPermissionByPermissionCodes(codes);
					if(permissionVOS==null || permissionVOS.size()==0) {
						continue;
					}
					
					permissionVOS.forEach(p->{
						sfCodes.append(p.getPermissionCode()+",");
						sfRemark.append(p.getRemark()+",");
					});
					suspiciousSdkDataVO.setCodes(sfCodes.toString());
					suspiciousSdkDataVO.setPermissionName(sfRemark.toString());
				}
			}
			
			WriteExcel.writeExcelMisjudgment04(sdkList, file.getAbsolutePath());
			LOG.info("开始给疑似SDK添加隐形水印");
			addWatermarkService.watermarkToReport(file,user);
		}
		return file;
	}
	
	//下载行为数据
	private File misjudgment99(ExcelReportQuery query ,File reportDir,User user) {

		TAssets assets = assetsMapper.getAssetIncludeDeletedByTaskId(query.getTaskId());
		if (assets == null) {
			return null;
		}
		TTask task = taskMapper.selectByPrimaryKey(query.getTaskId());
		if (task == null) {
			return null;
		}
		return buildActionExcelFile(task, assets, reportDir,user);
	}

	private File buildActionExcelFile(TTask task, TAssets assets, File reportDir,User user) {
		String fileName;
		if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET || task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
			fileName = "应用名称_版本号_行为数据_年月日时分秒_小程序.xlsx";
		} else {
			fileName = "应用名称_版本号_行为数据_年月日时分秒.xlsx";
		}
		String buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path") + "reportTemplate/" + fileName;

		File file = new File(reportDir.getAbsoluteFile() + File.separator + assets.getName().replaceAll(" ","-") + "_" + assets.getVersion() + "_行为数据_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}
		if (file.exists()) {
			FileOutputStream out = null;
			try {
				//权限使用
				List<PermissionVO> permissionList = getPermissionList(task.getApkDetectionDetailId());
				if (task.getTerminalType().isApplet()) {
					WriteExcel.writeExcelMisjudgmentAppletPermissionData(task.getDetectionType(), permissionList, file);
				} else {
					WriteExcel.writeExcelMisjudgmentpermissionData(task.getDetectionType(), permissionList, file);
				}
				Workbook workBook = WriteExcel.getSXSSFWorkbook(file.getAbsolutePath());
				// 违规项和应用行为数据
				WriteExcel.writeExcelMisjudgmentLawActionData((pageNum, pageSize) ->
								new WriteExcel.ExcelData<>(selectPrivacyItemActionData(task, pageNum, pageSize), pageNum), task,
						workBook, new WriteExcel.ReadImage(commonProperties, fileService));

				//应用行为数据
				WriteExcel.writeExcelMisjudgmentActionData((pageNum, pageSize) ->
						new WriteExcel.ExcelData<>(selectActionData(task, pageNum, pageSize), pageNum), task, workBook);

				// 小程序去掉sdk
				if (task.getTerminalType().isApplet()) {
					//通信行为
					WriteExcel.writeExcelMisjudgmentOutsideData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectOutsideAddress(task, pageNum, pageSize), pageNum), task, workBook);
					//存储个人信息
					WriteExcel.writeExcelMisjudgmentSharedPrefsData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectSharedPrefs(task, pageNum, pageSize), pageNum), task, workBook);
					//传输个人信息
					WriteExcel.writeExcelMisjudgmentSensitiveData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectSensitiveWord(task, pageNum, pageSize), pageNum), task, workBook);
				} else {
					// SDK数据
					try {
						List<SdkVO> list = privacyDetectionService.getSDKList(task.getApkDetectionDetailId(), task.getTaskId());
						WriteExcel.writeExcelMisjudgmentSdkData(list, workBook);
					} catch (IjiamiApplicationException e) {
						e.getMessage();
					}
					//通信行为
					WriteExcel.writeExcelMisjudgmentOutsideData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectOutsideAddress(task, pageNum, pageSize), pageNum), task, workBook);
					//存储个人信息
					WriteExcel.writeExcelMisjudgmentSharedPrefsData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectSharedPrefs(task, pageNum, pageSize), pageNum), task, workBook);
					//传输个人信息
					WriteExcel.writeExcelMisjudgmentSensitiveData((pageNum, pageSize) ->
							new WriteExcel.ExcelData<>(selectSensitiveWord(task, pageNum, pageSize), pageNum), task, workBook);
				}
				// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
				out = new FileOutputStream(file.getAbsolutePath());
				workBook.write(out);
				LOG.info("开始添加行为数据文件隐形水印");
				addWatermarkService.watermarkToReport(file,user);
			} catch (IOException e) {
				e.getMessage();
			} finally {
				if (out != null) {
					try {
						out.flush();
					} catch (IOException e) {
						e.getMessage();
					}
					try {
						out.close();
					} catch (IOException e) {
						e.getMessage();
					}
				}
			}
		}
		return file;
	}

	private List<ExcelReportPrivacyLawsDetailVO> selectPrivacyItemActionData(TTask task, int pageNum, int pageSize) {
		List<ExcelReportPrivacyLawsDetailVO> actionList = new ArrayList<>();
		// 不进行分页，第一页就返回所有数据
		if (pageNum == 1) {
			actionList.addAll(selectLawsAction(task, getLaw164ByTerminalType(task.getTerminalType())));
			actionList.addAll(selectLawsAction(task, getLaw191ByTerminalType(task.getTerminalType())));
			actionList.addAll(selectLawsAction(task, getLaw35273ByTerminalType(task.getTerminalType())));
			actionList.addAll(selectLawsAction(task, getLaw41391ByTerminalType(task.getTerminalType())));
		}
		return actionList;
	}

	private List<ExcelReportPrivacyLawsDetailVO> selectLawsAction(TTask task, Integer lawId) {
		CountLawDetectResult result = miitDetectService.findLawDetectResultByTaskId(lawId.longValue(),
				task.getTaskId(), LawResultStatusEnum.NON_COMPLIANCE.getValue());
		if (Objects.isNull(result)) {
			return Collections.emptyList();
		}
		// 一次性查出当前任务所有的检测数据
		Map<String, List<LawActionDetailVO>> actionDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(task.getTaskId(),
				lawId, LawDetailDataTypeEnum.ACTION.getValue())
				.stream()
				.collect(Collectors.groupingBy(LawActionDetailVO::getItemNo));
		Map<String, List<TPrivacyLawsDetail>> imageDetails = privacyLawsDetailMapper.selectAllItemOtherDetailByDataType(task.getTaskId(),
				lawId)
				.stream()
				.collect(Collectors.groupingBy(TPrivacyLawsDetail::getItemNo));
		Map<String, List<LawActionDetailVO>> transmissionDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(task.getTaskId(),
				lawId, LawDetailDataTypeEnum.TRANSMISSION.getValue())
				.stream()
				.collect(Collectors.groupingBy(LawActionDetailVO::getItemNo));
		Map<String, List<LawActionDetailVO>> sharedPrefsDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(task.getTaskId(),
				lawId, LawDetailDataTypeEnum.SHARED_PREFS.getValue())
				.stream()
				.collect(Collectors.groupingBy(LawActionDetailVO::getItemNo));
		Map<String, List<LawActionDetailVO>> outsideAddressDetails = privacyLawsDetailMapper.selectAllItemDetailByTaskId(task.getTaskId(),
				lawId, LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue())
				.stream()
				.collect(Collectors.groupingBy(LawActionDetailVO::getItemNo));
		return flatItems(result, actionDetails, imageDetails, transmissionDetails, sharedPrefsDetails, outsideAddressDetails);
	}

	private List<ExcelReportPrivacyLawsDetailVO> flatItems(CountLawDetectResult result,
														   Map<String, List<LawActionDetailVO>> actionDetails,
														   Map<String, List<TPrivacyLawsDetail>> imageDetails,
														   Map<String, List<LawActionDetailVO>> transmissionDetails,
														   Map<String, List<LawActionDetailVO>> sharedPrefsDetails,
														   Map<String, List<LawActionDetailVO>> outsideAddressDetails) {
		List<ExcelReportPrivacyLawsDetailVO> lawsDetailVOList = new ArrayList<>();
		LawDetectResultVO root = result.getLawDetectResult();
		for (LawDetectResultVO parent1Item : getSafeNextLaws(root)) {
			for (LawDetectResultVO parent2Item : getSafeNextLaws(parent1Item)) {
				for (LawDetectResultVO policyItem : getSafeNextLaws(parent2Item)) {
					List<ExcelReportPrivacyLawsDetailVO> itemDetails = new ArrayList<>();
					// 行为
					itemDetails.addAll(buildLawsDetailVO(root.getName(), parent2Item.getName(), policyItem,
							actionDetails.getOrDefault(policyItem.getItemNo(), Collections.emptyList())));
					// 图片
					itemDetails.addAll(buildImageDetailVO(root.getName(), parent2Item.getName(), policyItem,
							imageDetails.getOrDefault(policyItem.getItemNo(), Collections.emptyList())));
					// 传输个人信息
					itemDetails.addAll(buildLawsDetailVO(root.getName(), parent2Item.getName(), policyItem,
							transmissionDetails.getOrDefault(policyItem.getItemNo(), Collections.emptyList())));
					// 储存个人信息
					itemDetails.addAll(buildLawsDetailVO(root.getName(), parent2Item.getName(), policyItem,
							sharedPrefsDetails.getOrDefault(policyItem.getItemNo(), Collections.emptyList())));
					// 传输境外信息
					itemDetails.addAll(buildLawsDetailVO(root.getName(), parent2Item.getName(), policyItem,
							outsideAddressDetails.getOrDefault(policyItem.getItemNo(), Collections.emptyList())));
					if (itemDetails.isEmpty()) {
						lawsDetailVOList.add(buildNonActionLaws(root.getName(), parent2Item.getName(), policyItem));
					} else {
						lawsDetailVOList.addAll(itemDetails);
					}
				}
			}
		}
		return lawsDetailVOList;
	}

	/**
	 * 没有行为数据和图片的检测项
	 *
	 * @param rootName
	 * @param parentName
	 * @param policyItem
	 * @return
	 */
	private ExcelReportPrivacyLawsDetailVO buildNonActionLaws(String rootName, String parentName, LawDetectResultVO policyItem) {
		ExcelReportPrivacyLawsDetailVO detailVO = new ExcelReportPrivacyLawsDetailVO();
		detailVO.setParentLawsName(parentName);
		detailVO.setRootLawsName(rootName);
		detailVO.setItem(policyItem);
		return detailVO;
	}

	private List<ExcelReportPrivacyLawsDetailVO> buildLawsDetailVO(String rootName, String parentName, LawDetectResultVO policyItem,
																   List<LawActionDetailVO> detailVOList) {
		List<ExcelReportPrivacyLawsDetailVO> lawsDetailVOList = new ArrayList<>();
		for (LawActionDetailVO detailVO : detailVOList) {
			ExcelReportPrivacyLawsDetailVO lawsDetailVO = new ExcelReportPrivacyLawsDetailVO();
			lawsDetailVO.setParentLawsName(parentName);
			lawsDetailVO.setRootLawsName(rootName);
			lawsDetailVO.setItem(policyItem);
			lawsDetailVO.setDetailVO(detailVO);
			lawsDetailVOList.add(lawsDetailVO);
		}
		return lawsDetailVOList;
	}

	private List<ExcelReportPrivacyLawsDetailVO> buildImageDetailVO(String rootName, String parentName, LawDetectResultVO policyItem,
																	List<TPrivacyLawsDetail> detailVOList) {
		List<ExcelReportPrivacyLawsDetailVO> lawsDetailVOList = new ArrayList<>();
		for (TPrivacyLawsDetail detailVO : detailVOList) {
			if (StringUtils.isNotBlank(detailVO.getFileKey())) {
				ExcelReportPrivacyLawsDetailVO lawsDetailVO = new ExcelReportPrivacyLawsDetailVO();
				lawsDetailVO.setParentLawsName(parentName);
				lawsDetailVO.setRootLawsName(rootName);
				lawsDetailVO.setItem(policyItem);
				lawsDetailVO.setImageVO(detailVO);
				lawsDetailVOList.add(lawsDetailVO);
			}
		}
		return lawsDetailVOList;
	}

	private static List<LawDetectResultVO> getSafeNextLaws(LawDetectResultVO resultVO) {
		return resultVO.getNextLaws() != null ? resultVO.getNextLaws() : Collections.emptyList();
	}

	private List<TPrivacyActionNougat> selectActionData(TTask task, int pageNum, int pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		//应用行为数据
        return privacyActionNougatMapper
				.selectAllActionData(task.getTaskId(), task.getTerminalType().getValue(), null)
				.stream()
				.sorted(Comparator.comparingLong(TPrivacyActionNougat::getActionTimeStamp).reversed())
				.collect(Collectors.toList());
	}

	private List<TPrivacyOutsideAddress> selectOutsideAddress(TTask task, int pageNum, int pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<TPrivacyOutsideAddress> outsideList = privacyOutsideAddressMapper.findOutsideDataByTaskId(task.getTaskId(),
				null, null, null, null, null, null, null, null, task.getTerminalType().getValue());
		return outsideList;
	}

	private List<TPrivacySharedPrefs> selectSharedPrefs(TTask task, int pageNum, int pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<TPrivacySharedPrefs> shareList = privacySharedPrefsMapper.findByTaskIdAndBehaviorStageAndQuerys(task.getTaskId(),
				null, null, null, null, null, null, null, task.getTerminalType().getValue());
		return shareList;
	}

	private List<TPrivacySensitiveWord> selectSensitiveWord(TTask task, int pageNum, int pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		List<TPrivacySensitiveWord> sensitiveList = privacySensitiveWordMapper.findByTaskIdAndAllQuerys(task.getTaskId(),
				null, null, null, null, null, null,
				null, null, task.getTerminalType().getValue(), null);
		return sensitiveList;
	}

	private List<PermissionVO> getPermissionList(String documentId) {
		List<PermissionVO> vo1 = privacyDetectionService.getXMLPermission(documentId, null);
		List<PermissionVO> vo2 = privacyDetectionService.getExcessPermission(documentId, null);
		if (vo1 == null || vo1.size() == 0) {
			return vo2;
		}
		for (PermissionVO v : vo1) {
			v.setAliasName(v.getName().substring(v.getName().lastIndexOf(".") + 1, v.getName().length()));
			for (PermissionVO v2 : vo2) {
				v2.setAliasName(v2.getName().substring(v2.getName().lastIndexOf(".") + 1, v2.getName().length()));
				if (StringUtils.isNoneBlank(v.getAliasName()) && v.getAliasName().equals(v2.getAliasName()) && v.getRemark().equals(v2.getRemark())) {
					v.setExcessPermission(v2.getExcessPermission());
				}
			}
		}
		return vo1;
	}

	@Override
	public File exprotActionToFile(TTask task,User user){
		TAssets assets = null;
		long time1 = System.currentTimeMillis();
		File file = null;
		try {
			task = taskMapper.selectByPrimaryKey(task.getTaskId());
			assets = assetsMapper.selectAssetByTaskId(task.getTaskId());
			if (assets == null) {
				return null;
			}
			task = taskMapper.selectByPrimaryKey(task.getTaskId());
			file = buildActionExcelFile(task, assets, new File(commonProperties.getProperty("ijiami.report.root.path") + "out/"),user);
			long time2 = System.currentTimeMillis();
			LOG.info("生成文件成功name={},耗时={},", file.getName(), (time2 - time1));
		} catch (Exception e) {
			LOG.info("生成文件异常taskId={},name={}",task.getTaskId(),assets.getName());
			e.getMessage();
		}
		return file;
	}


	public File downloadLogs(SysLogQuery query , File reportDir) {

		String buidDataTemplates = commonProperties.getProperty("ijiami.report.root.path")+"reportTemplate/日志数据_年月日时分秒.xlsx";

		File file = new File(reportDir.getAbsoluteFile()+ File.separator+"日志数据_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+".xlsx");
		try {
			FileUtils.copyFile(new File(buidDataTemplates), file);
		} catch (IOException e) {
			e.getMessage();
		}

		if (file.exists()) {
			query.setExport(true);
			List<TsysOperateLog> logList = this.sysLogMapper.selectSysLogByQuery(query);
			WriteExcel.writeExcelSysOperateLog(logList, file.getAbsolutePath());
		}
		return file;
	}

	@Override
	public File exportDetectionReport(User user, DetectionStatisticsQuery query) {
		TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getItem(query.getTerminalType());
		if (terminalTypeEnum == null) {
			throw new IjiamiRuntimeException("缺少平台参数");
		}
		FileOutputStream out = null;
		try {
			String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
			File file = new File(reportRootPath + "out"
					+ File.separator + terminalTypeEnum.getName() + "_检测统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
			if (file.createNewFile()) {
				XSSFWorkbook workBook = new XSSFWorkbook();
				DetectionStatistics detectionStatistics = statisticsService.detectionStatistics(user.getUserId(), query);
				// 违规项和应用行为数据
				writeExcelDetectionStatisticsDetail(user, query, detectionStatistics.getLaw164Statistics(), workBook);
				writeExcelDetectionStatisticsDetail(user, query, detectionStatistics.getLaw191Statistics(), workBook);
				writeExcelDetectionStatisticsDetail(user, query, detectionStatistics.getLaw35273Statistics(), workBook);
				writeExcelDetectionStatisticsDetail(user, query, detectionStatistics.getLaw41391Statistics(), workBook);
				if (!terminalTypeEnum.isApplet()) {
					writeExcelSdkStatisticsDetail(user, query, detectionStatistics.getSdkUsageStatisticsList(), workBook);
				}
				// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
				out = new FileOutputStream(file.getAbsolutePath());
				workBook.write(out);
				LOG.info("开始添加行为数据文件隐形水印");
				addWatermarkService.watermarkToReport(file, user);
				return file;
			}
		} catch (Exception ex) {
			LOG.error("生成报告失败", ex);
		} finally {
			if (out != null) {
				try {
					out.flush();
					out.close();
				} catch (IOException e) {
					LOG.error("关闭报告写入流失败", e);
				}
			}
		}
		return null;
	}

	private void writeExcelSdkStatisticsDetail(User user, DetectionStatisticsQuery query,
											   List<SdkUsageStatistics> sdkUsageStatisticsList,
											   XSSFWorkbook workBook) {
		WriteExcel.writeExcelSdlStatisticsDetail(TerminalTypeEnum.getItem(query.getTerminalType()),
				sdkUsageStatisticsList, (pageNum, pageSize) ->
						new WriteExcel.ExcelData<>(selectSdkDetailData(user, query, pageNum, pageSize), pageNum), workBook);
	}

	private List<SdkStatisticsDetail> selectSdkDetailData(User user, DetectionStatisticsQuery query, int pageNum, int pageSize) {
		SdkDetailsQuery sdkDetailsQuery = new SdkDetailsQuery();
		BeanUtils.copyProperties(query, sdkDetailsQuery);
		sdkDetailsQuery.setPage(pageNum);
		sdkDetailsQuery.setRows(pageSize);
		return statisticsService.sdkDetailsByPage(user.getUserId(), sdkDetailsQuery).getList();
	}

	private Map<Long, DetectionStatisticsLawItem> getDetectionStatisticsLawItemMap(Long userId, LawStatistics lawStatistics, DetectionStatisticsQuery query) {
		return lawStatistics
				.getLawItemList()
				.stream()
				.map(lawItem -> {
					DetectionLawItemQuery itemQuery = new DetectionLawItemQuery();
					itemQuery.setLawItemId(lawItem.getLawItemId());
					itemQuery.setAssetsName(query.getAssetsName());
					itemQuery.setUserName(query.getUserName());
					itemQuery.setStartDate(query.getStartDate());
					itemQuery.setEndDate(query.getEndDate());
					itemQuery.setTerminalType(query.getTerminalType());
					return statisticsService.detectionLawItem(userId, itemQuery, ConstantsUtils.STATISTICS_UNLIMITED);
				})
				.collect(Collectors.toMap(DetectionStatisticsLawItem::getLawItemId, Function.identity(), (key1, key2) -> key2));
	}

	private void writeExcelDetectionStatisticsDetail(User user, DetectionStatisticsQuery query, LawStatistics lawStatistics, XSSFWorkbook workBook) {
		if (lawStatistics != null) {
			DetectionDetailsQuery detailQuery = new DetectionDetailsQuery();
			BeanUtils.copyProperties(query, detailQuery);
			detailQuery.setLawId(lawStatistics.getLawId());
			detailQuery.setRows(Integer.MAX_VALUE);
			List<DetectionStatisticsDetail> detailList = statisticsService.detectionDetailsByPage(user.getUserId(), detailQuery).getList();
			Map<Long, DetectionStatisticsLawItem> lawItemMap = getDetectionStatisticsLawItemMap(user.getUserId(), lawStatistics, query);
			WriteExcel.writeExcelDetectionStatisticsDetail(TerminalTypeEnum.getItem(query.getTerminalType()),
					lawStatistics, detailList, lawItemMap, workBook);
		}
	}

	@Override
	public File exportAssetsReport(User user, AssetsStatisticsParam query) {
		TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getItem(query.getTerminalType());
		if (terminalTypeEnum == null) {
			throw new IjiamiRuntimeException("缺少平台参数");
		}
		FileOutputStream out = null;
		try {
			String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
			File file = new File(reportRootPath + "out" + File.separator
					+ terminalTypeEnum.getName() + "_检测数据应用资产统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
			if (file.createNewFile()) {
				Workbook workBook = new XSSFWorkbook();
				List<AssetsStatisticsDetailDTO> detailList = statisticsService.assetsDetailsAll(user.getUserId(), query);
				// 违规项和应用行为数据
				WriteExcel.writeExcelAssetsStatisticsDetail(detailList, workBook);
				List<AssetsTask> taskList = statisticsService.assetsTaskAll(user.getUserId(), query);
				List<AssetsTask> fastTaskList = taskList.stream()
						.filter(t -> t.getDetectionType() == TaskDetectionTypeEnum.FAST.getValue()).collect(Collectors.toList());
				List<AssetsTask> aiTaskList = taskList.stream()
						.filter(t -> t.getDetectionType() == TaskDetectionTypeEnum.AI.getValue()).collect(Collectors.toList());
				List<AssetsTask> deepTaskList = taskList.stream()
						.filter(t -> t.getDetectionType() == TaskDetectionTypeEnum.DEPTH.getValue()).collect(Collectors.toList());
				if (!fastTaskList.isEmpty()) {
					WriteExcel.writeExcelAssetsAutoTaskStatistics(fastTaskList, TerminalTypeEnum.getItem(query.getTerminalType()), workBook, "检测数据-快速检测任务");
				}
				if (!aiTaskList.isEmpty()) {
					WriteExcel.writeExcelAssetsAutoTaskStatistics(aiTaskList, TerminalTypeEnum.getItem(query.getTerminalType()), workBook, "检测数据-AI检测任务");
				}
				if (!deepTaskList.isEmpty()) {
					WriteExcel.writeExcelAssetsDeepTaskStatistics(deepTaskList, TerminalTypeEnum.getItem(query.getTerminalType()), workBook);
				}
				// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
				out = new FileOutputStream(file.getAbsolutePath());
				workBook.write(out);
				LOG.info("开始添加行为数据文件隐形水印");
				addWatermarkService.watermarkToReport(file, user);
			}
			return file;
		} catch (Exception ex) {
			LOG.error("生成报告失败", ex);
		} finally {
			if (out != null) {
				try {
					out.flush();
					out.close();
				} catch (IOException e) {
					LOG.error("关闭报告写入流失败", e);
				}
			}
		}
		return null;
	}

	@Override
	public File exportDetectFalsePositivesReport(User user, DetectionStatisticsQuery query) {
		TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getItem(query.getTerminalType());
		if (terminalTypeEnum == null) {
			throw new IjiamiRuntimeException("缺少平台参数");
		}
		FileOutputStream out = null;
		try {
			String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
			File file = new File(reportRootPath + "out"
					+ File.separator + terminalTypeEnum.getName() + "_误报统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
			if (file.createNewFile()) {
				XSSFWorkbook workBook = new XSSFWorkbook();
				List<DetectFalsePositivesReportAssetsInfo> detailList = statisticsService.findDetectFalsePositivesReport(user.getUserId(), query);
				// 违规项和应用行为数据
				WriteExcel.writeExcelDetectFalsePositivesDetail(detailList, workBook);
				// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
				out = new FileOutputStream(file.getAbsolutePath());
				workBook.write(out);
				LOG.info("开始添加行为数据文件隐形水印");
				addWatermarkService.watermarkToReport(file, user);
				return file;
			}
		} catch (Exception ex) {
			LOG.error("生成报告失败", ex);
		} finally {
			if (out != null) {
				try {
					out.flush();
					out.close();
				} catch (IOException e) {
					LOG.error("关闭报告写入流失败", e);
				}
			}
		}
		return null;
	}
}
