package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DetectionItemListVO;
import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.entity.TDetectionItem;
import cn.ijiami.detection.enums.JurisdictionGradeEnum;
import cn.ijiami.detection.mapper.TDetectionItemMapper;
import cn.ijiami.detection.query.TemplateDetectionItemQuery;
import cn.ijiami.detection.service.api.IDetectionItemService;
import cn.ijiami.detection.utils.ComparableUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

/**
 * 检测项接口实现类
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional(rollbackFor = Throwable.class)
public class DetectionItemServiceImpl implements IDetectionItemService {

	private final TDetectionItemMapper detectionItemMapper;

	@Autowired
	public DetectionItemServiceImpl(TDetectionItemMapper detectionItemMapper) {
		this.detectionItemMapper = detectionItemMapper;
	}

	@Override
	public TDetectionItem addDetectionItem(TDetectionItem detectionItem) {
		detectionItemMapper.insert(detectionItem);
		return detectionItem;
	}

	@Override
	public PageInfo<DetectionItemVO> findDetectionItemByQuery(TemplateDetectionItemQuery detectionItemQuery)
			throws IjiamiApplicationException {
		if (detectionItemQuery.getTemplateId() == null) {
			throw new IjiamiApplicationException("模版ID不能为空");
		}
		if (detectionItemQuery.getPage() != null && detectionItemQuery.getRows() != null) {
			PageHelper.startPage(detectionItemQuery.getPage(), detectionItemQuery.getRows());
		}
		List<DetectionItemVO> detectionItemList = detectionItemMapper.selectDetectionItemByQuery(detectionItemQuery);
		ComparableUtil.sortByMethod(detectionItemList, "getTypeId", false);
		return new PageInfo<>(detectionItemList);
	}

	@Override
	public DetectionItemListVO findDetectionItemListVO(TDetectionItem detectionItem) {
		DetectionItemListVO detectionItemListVO = new DetectionItemListVO();
		List<DetectionItemVO> detectionItemList = detectionItemMapper.selectDetectionItemListVO(detectionItem);

		List<DetectionItemVO> highDetectionItemVO = new ArrayList<>();
		List<DetectionItemVO> middleDetectionItemVO = new ArrayList<>();
		List<DetectionItemVO> lowDetectionItemVO = new ArrayList<>();

		for (DetectionItemVO detectionItemVO : detectionItemList) {
			if (detectionItemVO.getGrade().equals(JurisdictionGradeEnum.HIGH)) {
				highDetectionItemVO.add(detectionItemVO);
			} else if (detectionItemVO.getGrade().equals(JurisdictionGradeEnum.MIDDLE)) {
				middleDetectionItemVO.add(detectionItemVO);
			} else {
				lowDetectionItemVO.add(detectionItemVO);
			}
		}

		Map<Object, Integer> countMap = detectionItemMapper.detectionItemCountSize(detectionItem);
		detectionItemListVO.setAndroidSize(countMap.get("android_size"));
		detectionItemListVO.setIosSize(countMap.get("ios_size"));
		detectionItemListVO.setWechatSize(countMap.get("wechat_size"));

		ComparableUtil.sortByMethod(highDetectionItemVO, "getTypeId", false);
		ComparableUtil.sortByMethod(middleDetectionItemVO, "getTypeId", false);
		ComparableUtil.sortByMethod(lowDetectionItemVO, "getTypeId", false);

		detectionItemListVO.setHighDetectionItemVO(highDetectionItemVO);
		detectionItemListVO.setMiddleDetectionItemVO(middleDetectionItemVO);
		detectionItemListVO.setLowDetectionItemVO(lowDetectionItemVO);
		return detectionItemListVO;
	}

	@Override
	public PageInfo<DetectionItemVO> findDetectionItemByPage(TDetectionItem detectionItem) {
		if (detectionItem.getPage() != null && detectionItem.getRows() != null) {
			PageHelper.startPage(detectionItem.getPage(), detectionItem.getRows());
		}
		List<DetectionItemVO> detectionItemVOs = detectionItemMapper.selectDetectionItemByPage(detectionItem);
		ComparableUtil.sortByMethod(detectionItemVOs, "getTypeId", false);
		return new PageInfo<>(detectionItemVOs);
	}
}
