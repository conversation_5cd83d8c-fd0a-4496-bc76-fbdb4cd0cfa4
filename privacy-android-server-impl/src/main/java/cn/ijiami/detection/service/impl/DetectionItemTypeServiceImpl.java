package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.entity.TDetectionItemType;
import cn.ijiami.detection.mapper.TDetectionItemTypeMapper;
import cn.ijiami.detection.service.api.IDetectionItemTypeService;
import cn.ijiami.detection.utils.ComparableUtil;

/**
 * 检测项分类接口实现类
 * 
 * <AUTHOR>
 *
 */
@Service
public class DetectionItemTypeServiceImpl implements IDetectionItemTypeService {

	@Autowired
	private TDetectionItemTypeMapper detectionItemTypeMapper;

	@Override
	public List<TDetectionItemType> findDetectionItemTypeList() {
		List<TDetectionItemType> typeItemList = new ArrayList<TDetectionItemType>();
		typeItemList = detectionItemTypeMapper.selectAll();
		ComparableUtil.sortByMethod(typeItemList, "getId", false);
		return typeItemList;
	}

}
