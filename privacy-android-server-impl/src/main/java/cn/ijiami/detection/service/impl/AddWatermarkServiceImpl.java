package cn.ijiami.detection.service.impl;


import cn.hutool.core.io.FileUtil;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.service.api.IAddWatermarkService;
import cn.ijiami.framework.common.enums.YesNoEnum;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.organ.company.entity.Company;
import cn.ijiami.organ.company.service.api.ICompanyService;
import com.ijiami.ios.tool.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 给文件添加隐形水印服务（.word,.pdf,.xlsx,.xls)
 * cmd命令：
 * 增加水印：/zywa/ijiami/ios/tools/marker -f path1(要执行的源文件） -i path2（水印文件)
 * 查看水印：/zywa/ijiami/ios/tools/marker -f path1 -c
 */
@Service
public class AddWatermarkServiceImpl implements IAddWatermarkService {

    private final static Logger LOG = LoggerFactory.getLogger(AddWatermarkServiceImpl.class);
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private IjiamiCommonProperties commonProperties;

    /**
     * 给源文件生成水印报告
     * @param file 源文件  文件名称不能有空格，否则会提示找不到对应的文件错误，例如City Use_XXX报告要改成City_user_XXX报告
     * @param user 用户信息
     */
    @Override
    public void watermarkToReport(File file, User user) {
    	return;
//        try{
//            if(null == file) return;
//            //查出公司信息
//            List<Company> companyList = companyService.findAllCompanys();
//            if(companyList == null || companyList.size() == 0) return;
//            Company company = companyList.stream().filter(s->s.getIsDelete() == YesNoEnum.NO)
//                    .sorted(Comparator.comparing(Company::getCreatedTime).reversed()).collect(Collectors.toList())
//                    .stream().findFirst().orElse(null);
//            if(company == null) return;
//            Map<String,String> map = new HashMap<>();
//            map.put("company",company.getCnName());
//            map.put("userName",user.getUserName());
//            String data = map.toString();
//            LOG.info("水印数据>>>>>>"+data);
//            //水印工具目录
//            String toolPath = commonProperties.getProperty("ijiami.tools.path");
//            //水印文件要存放的根目录
//            String rootPath = commonProperties.getProperty("ijiami.report.root.path");
//            //水印文件路径
//            String watermarkFilePath = rootPath + File.separator + "watermark" + System.currentTimeMillis() + ".txt";
//            //生成水印文件
//            FileUtil.writeBytes(data.getBytes(),watermarkFilePath);
//            //添加隐藏水印
//            String cmd = String.format(toolPath + File.separator + "marker -f %s -i %s",file.getAbsolutePath(),watermarkFilePath);
//            LOG.info("校验执行签名的命令>>>>>>" + cmd);
//            String cmdResult = CommonUtils.execCMDByArgs(cmd);
//            LOG.info(cmdResult);
//            FileUtil.del(watermarkFilePath);
//        }catch(Exception e){
//            e.printStackTrace();
//        }
    }
}
