package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.mapper.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.CountSharedPrefsNameVO;
import cn.ijiami.detection.VO.CountSharedPrefsTypeVO;
import cn.ijiami.detection.VO.StoragePersonalMessageAndType;
import cn.ijiami.detection.VO.StoragePersonalMessageAndTypeVO;
import cn.ijiami.detection.VO.TPrivacySharedPrefsVO;
import cn.ijiami.detection.VO.detection.privacy.ActionComplianceVO;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.LawResultMarkStatusEnum;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.service.api.IPrivacySharedPrefsService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2020/2/27 10:42
 */
@Service
public class PrivacySharedPrefsServiceImpl implements IPrivacySharedPrefsService {

    private final TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    private final TPrivacyResultMarkMapper privacyResultMarkMapper;
    private final TTaskMapper              taskMapper;
    private final ITaskService taskService;
    private final TAssetsMapper tAssetsMapper;
    private final TTaskAutoReportMapper taskAutoReportMapper;

    public PrivacySharedPrefsServiceImpl(TPrivacySharedPrefsMapper privacySharedPrefsMapper,
                                         TPrivacyResultMarkMapper privacyResultMarkMapper, TTaskMapper taskMapper,
                                         ITaskService taskService, TAssetsMapper tAssetsMapper, TTaskAutoReportMapper taskAutoReportMapper) {
        this.privacySharedPrefsMapper = privacySharedPrefsMapper;
        this.privacyResultMarkMapper = privacyResultMarkMapper;
        this.taskMapper = taskMapper;
        this.taskService = taskService;
        this.tAssetsMapper = tAssetsMapper;
        this.taskAutoReportMapper = taskAutoReportMapper;
    }

    @Override
    public List<CountSharedPrefsTypeVO> countSharedPrefsTypeByTaskId(Long taskId, Integer behaviorStage) {
        return privacySharedPrefsMapper.countSharedPrefsTypeByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<CountSharedPrefsNameVO> countSharedPrefsNameByTaskId(Long taskId, Long typeId, Integer behaviorStage) {
        return privacySharedPrefsMapper.countSharedPrefsNameByTaskId(taskId, typeId, behaviorStage);
    }

    @Override
    public List<TPrivacySharedPrefs> findByTaskIdAndTypeIdAndName(Long taskId, Long typeId, String name, Integer behaviorStage) {
        String tmp = "<span class=\"font-red\" style=\"color:red\">{{word}}</span>";
        List<TPrivacySharedPrefs> sensitiveWords = privacySharedPrefsMapper.findByTaskIdAndTypeIdAndName(taskId, typeId, name, behaviorStage);
        TTask task = taskService.findById(taskId);
        for (TPrivacySharedPrefs sensitiveWord : sensitiveWords) {
            String word = sensitiveWord.getSensitiveWord().toLowerCase();
            String code = sensitiveWord.getContent().toLowerCase();
            int idx = code.indexOf(word);
            if (idx >= 0) {
                String head = sensitiveWord.getContent().substring(0, idx);
                String tail = sensitiveWord.getContent().substring(idx + word.length());
                String newWord = tmp.replace("{{word}}", sensitiveWord.getSensitiveWord());
                String newCode = StringEscapeUtils.escapeHtml4(head) + newWord + StringEscapeUtils.escapeHtml4(tail);
                sensitiveWord.setContent(newCode);
            }
            // 兼容之前的数据
            String stackInfo = sensitiveWord.getStackInfo();
            if (StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && DataHandleUtil.isJSONValid(stackInfo) && task!=null && task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
                sensitiveWord.setActionTime(actionStackBO.getActionTime());
                sensitiveWord.setStackInfo(actionStackBO.getStackInfo());
            }
        }
        return sensitiveWords;
    }

    /**
     * 根据条件查询存储个人信息数据，带分页
     * @param behaviorQuery
     * @param userId
     * @return
     */
    @Override
    public TPrivacySharedPrefsVO findByTaskIdAndBehaviorStageAndQuerys(BehaviorQuery behaviorQuery, Long userId) {
        TPrivacySharedPrefsVO tPrivacySharedPrefsVO = new TPrivacySharedPrefsVO();
        List<TPrivacySharedPrefs> tPrivacySharedPrefs = null;
        PageInfo<TPrivacySharedPrefs> pageInfo = null;
        if(behaviorQuery !=null && (!"".equals(behaviorQuery.getTaskId()) && behaviorQuery.getTaskId()!=null)
                && (behaviorQuery.getTaskId()!=null && !"".equals(behaviorQuery.getTaskId()))){
            Long taskId = behaviorQuery.getTaskId();
            Integer behaviorStage = behaviorQuery.getBehaviorStage();
            String executors = behaviorQuery.getExecutors();
            String executorType = behaviorQuery.getExecutorType();
            String typeName = behaviorQuery.getTypeName();
            String personalName = behaviorQuery.getPersonalName();
            Integer sortType = behaviorQuery.getSortType();
            Integer sortOrder = behaviorQuery.getSortOrder();

            String tmp = "<span class=\"font-red\" style=\"color:red\">{{word}}</span>";
            TTask task = taskService.findById(taskId);
            //增加一个判断是不是当前用户创建的任务，不是的话要传一个表示给前端不让其他用户做修改
            Integer isEdit = 0;//是否可编辑 0不可编辑 1可编辑
            if (task != null && task.getCreateUserId() != null && userId != null) {
                if (userId.equals(task.getCreateUserId())) {
                    isEdit = 1;
                }
            }
            if (behaviorQuery.getPage() != null && behaviorQuery.getRows() != null) {
                PageHelper.startPage(behaviorQuery.getPage(), behaviorQuery.getRows());
            }
            tPrivacySharedPrefs = privacySharedPrefsMapper.findByTaskIdAndBehaviorStageAndQuerys(taskId, behaviorStage, executors, executorType,
                    typeName, personalName, sortType, sortOrder, task != null ? task.getTerminalType().getValue() : null);
            for (TPrivacySharedPrefs tPrivacySharedPref : tPrivacySharedPrefs) {
                //将编辑状态结果记录
                tPrivacySharedPref.setIsEdit(isEdit);
                String word = tPrivacySharedPref.getSensitiveWord().toLowerCase();
                String code = tPrivacySharedPref.getContent().toLowerCase();
                int idx = code.indexOf(word);
                if (idx >= 0) {
                    String head = tPrivacySharedPref.getContent().substring(0, idx);
                    String tail = tPrivacySharedPref.getContent().substring(idx + word.length());
                    String newWord = tmp.replace("{{word}}", tPrivacySharedPref.getSensitiveWord());
                    String newCode = StringEscapeUtils.escapeHtml4(head) + newWord + StringEscapeUtils.escapeHtml4(tail);
                    tPrivacySharedPref.setContent(newCode);
                }
                // 兼容之前的数据
                String stackInfo = tPrivacySharedPref.getStackInfo();
                if (StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && DataHandleUtil.isJSONValid(stackInfo) && task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                    ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
                    tPrivacySharedPref.setActionTime(tPrivacySharedPref.getActionTime()==null?actionStackBO.getActionTime():tPrivacySharedPref.getActionTime());
                    tPrivacySharedPref.setStackInfo(StringUtils.isEmpty(tPrivacySharedPref.getStackInfo())?actionStackBO.getStackInfo():tPrivacySharedPref.getStackInfo());
                }
            }

        }

        if(tPrivacySharedPrefs!=null){
            pageInfo = new PageInfo<>(tPrivacySharedPrefs);
            pageInfo.setList(tPrivacySharedPrefs);
            tPrivacySharedPrefsVO.setPageInfo(pageInfo);
        }

        return tPrivacySharedPrefsVO;
    }

	@Override
	public void updateSharedPrefsStatus(ActionComplianceVO actionComplianceVO) throws IjiamiApplicationException {
		TPrivacySharedPrefs prefs = privacySharedPrefsMapper.selectByPrimaryKey(actionComplianceVO.getId());
		if(prefs==null) {
			throw new IjiamiApplicationException("数据不存在！");
		}

		TTask task = taskMapper.selectByPrimaryKey(prefs.getTaskId());
		if(task==null) {
			throw new IjiamiApplicationException("任务不存在！");
		}

		if(!actionComplianceVO.getCreateUserId().equals(task.getCreateUserId())) {
			throw new IjiamiApplicationException("没有权限修改该数据！");
		}

		TPrivacySharedPrefs up_prefs = new TPrivacySharedPrefs();
		up_prefs.setId(actionComplianceVO.getId());
		up_prefs.setResultStatus(actionComplianceVO.getResultStatus());
		privacySharedPrefsMapper.updateByPrimaryKeySelective(up_prefs);

		TPrivacyResultMark mark = new TPrivacyResultMark();
		mark.setbId(actionComplianceVO.getId());
		mark.setCreateTime(new Date());
		mark.setDescription(actionComplianceVO.getDescription());
		mark.setResultStatus(LawResultMarkStatusEnum.getItem(actionComplianceVO.getResultStatus()));
		mark.setResultType(LawResultTypeEnum.STORAGE);
		privacyResultMarkMapper.insertSelective(mark);
        // 任务数据改变，默认报告修改为失效
        TTaskAutoReport update = new TTaskAutoReport();
        update.setIsDelete(BooleanEnum.TRUE.value);
        update.setUpdateTime(new Date());
        Example example = new Example(TTaskAutoReport.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", task.getTaskId());
        criteria.andEqualTo("isDelete", BooleanEnum.FALSE.value);
        taskAutoReportMapper.updateByExampleSelective(update, example);
	}


    /**
     *获取存储个人信息触发主体
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorStorage(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            TAssets tAssets = tAssetsMapper.selectAssetByTaskId(task.getTaskId());
            List<String> list = privacySharedPrefsMapper.getBehaviorStorage(task.getTaskId(), behaviorStage);
            for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo.replace("\n",""))) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }
            //过滤掉与app名称相同的sdk主体
            result=result.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        }
        return result;
    }

    /**
     *获取存储个人信息信息分类
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorStorageMessageType(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        List<String> list = new ArrayList<>();
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
             list = privacySharedPrefsMapper.getBehaviorStorageMessageType(task.getTaskId(), behaviorStage);
            /*for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo)) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }*/
        }
        return list;
    }

    /**
     *获取存储个人信息
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorStoragePersonalMessage(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        List<String> list = new ArrayList<>();
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            list = privacySharedPrefsMapper.getBehaviorStoragePersonalMessage(task.getTaskId(), behaviorStage);
            /*for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo)) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }*/
        }
        return list;
    }

    /**
     * 获取个人信息与信息分类关联查询条件，选个人信息能得到信息分类数据，选信息分类能得到个人信息
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public StoragePersonalMessageAndTypeVO getBehaviorStoragePersonalMessageAndType(String documentId, Integer behaviorStage) {
        //查询数据库得到的个人信息集合
        List<StoragePersonalMessageAndType> personalMessageList = null;
        //查询数据库得到的信息分类集合
        List<StoragePersonalMessageAndType> messageTypeList = null;

        //存储个人信息关联信息分类得到的集合
        Map<String,List<StoragePersonalMessageAndType>> personalMessageMap=new HashMap<>();
        //存储信息分类关联个人信息得到的集合
        Map<String,List<StoragePersonalMessageAndType>> messageTypeMap=new HashMap<>();

        //最终返回的数据类型
        StoragePersonalMessageAndTypeVO storagePersonalMessageAndTypeVO =new StoragePersonalMessageAndTypeVO();

        TTask task = taskService.findByDocumentId(documentId);
        if(task==null || task.getTaskId()==null || task.getTaskId()==0L){
            return storagePersonalMessageAndTypeVO;
        }
        personalMessageList = privacySharedPrefsMapper.getStoragePersonalMessageList(task.getTaskId(),behaviorStage);
        messageTypeList = privacySharedPrefsMapper.getStorageMessageTypeList(task.getTaskId(),behaviorStage);

        for(StoragePersonalMessageAndType personalMessage:personalMessageList){
            List<StoragePersonalMessageAndType> list1=new ArrayList<>();
            for(StoragePersonalMessageAndType messageType:messageTypeList){
                if(StringUtils.isEmpty(personalMessage.getName()) || StringUtils.isEmpty(messageType.getName())){
                    continue;
                }
                if(personalMessage.getName().equals(messageType.getName())){
                    list1.add(messageType);
                }
            }
            personalMessageMap.put(personalMessage.getName(),list1.stream().distinct().collect(Collectors.toList()));
        }

        for(StoragePersonalMessageAndType message:messageTypeList){
            List<StoragePersonalMessageAndType> list2=new ArrayList<>();
            for(StoragePersonalMessageAndType person:personalMessageList){
                if(StringUtils.isEmpty(message.getTypeName()) || StringUtils.isEmpty(person.getTypeName())){
                    continue;
                }
                if(message.getTypeName().equals(person.getTypeName())){
                    list2.add(person);
                }
            }
            messageTypeMap.put(message.getTypeName(),list2.stream().distinct().collect(Collectors.toList()));
        }

        storagePersonalMessageAndTypeVO.setMessageTypeMap(messageTypeMap);
        storagePersonalMessageAndTypeVO.setPersonalMessageMap(personalMessageMap);

        return storagePersonalMessageAndTypeVO;
    }
}
