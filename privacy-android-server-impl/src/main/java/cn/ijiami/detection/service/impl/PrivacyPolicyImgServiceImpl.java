package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.base.common.file.entity.File;
import cn.ijiami.detection.VO.PrivacyPolicyImgVO;
import cn.ijiami.detection.VO.RiskImageVo;
import cn.ijiami.detection.entity.TPrivacyPolicyImg;
import cn.ijiami.detection.mapper.TPrivacyPolicyImgMapper;
import cn.ijiami.detection.service.api.IPrivacyPolicyImgService;
import cn.ijiami.detection.utils.ImageUtils;
import cn.ijiami.report.service.api.IReportChartService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2019-06-03 14:33
 */
@Service
public class PrivacyPolicyImgServiceImpl implements IPrivacyPolicyImgService {

    private final TPrivacyPolicyImgMapper privacyPolicyImgMapper;
    private final IBaseFileService fileService;
    private final IjiamiCommonProperties commonProperties;
    private final IReportChartService reportChartService;

    public PrivacyPolicyImgServiceImpl(TPrivacyPolicyImgMapper privacyPolicyImgMapper, IBaseFileService fileService, IjiamiCommonProperties commonProperties, IReportChartService reportChartService) {
        this.privacyPolicyImgMapper = privacyPolicyImgMapper;
        this.fileService = fileService;
        this.commonProperties = commonProperties;
        this.reportChartService = reportChartService;
    }

    @Override
    public List<TPrivacyPolicyImg> getPolicyImgsByTaskId(Long taskId) {
        Example example = new Example(TPrivacyPolicyImg.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        return privacyPolicyImgMapper.selectByExample(example);
    }

    @Override
    public List<PrivacyPolicyImgVO> findPolicyImgsByTaskId(Long taskId) {
        List<TPrivacyPolicyImg> privacyPolicyImgs = getPolicyImgsByTaskId(taskId);
        if (privacyPolicyImgs == null || privacyPolicyImgs.size() == 0) {
            return new ArrayList<>();
        }

        List<PrivacyPolicyImgVO> privacyPolicyImgVOList = new ArrayList<>();
        for (TPrivacyPolicyImg privacyPolicyImg : privacyPolicyImgs) {
            File img = fileService.findFileByFileKey(privacyPolicyImg.getFileKey());
            RiskImageVo riskImage = ImageUtils.getRiskImage(commonProperties.getFilePath() + img.getFilePath());
            if (riskImage != null) {
                riskImage.setImageBase64(reportChartService.getImageBASE64(commonProperties.getFilePath() + img.getFilePath()));
                PrivacyPolicyImgVO privacyPolicyImgVO = new PrivacyPolicyImgVO();
                privacyPolicyImgVO.setImg(riskImage);
                privacyPolicyImgVO.setPolicyId(privacyPolicyImg.getPolicyId());
                privacyPolicyImgVOList.add(privacyPolicyImgVO);
            }
        }

        return privacyPolicyImgVOList;
    }

    @Override
    public List<String> listImagesByTaskIdAndPolicyId(Long taskId, Long policyId) {
        return privacyPolicyImgMapper.selectByTaskIdAndPolicyId(taskId, policyId);
    }
}
