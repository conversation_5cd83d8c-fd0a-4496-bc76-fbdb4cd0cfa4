package cn.ijiami.detection.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.VO.DetectionTemplateTypeVO;
import cn.ijiami.detection.VO.DetectionTemplateVO;
import cn.ijiami.detection.entity.TDetectionTemplate;
import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.mapper.TDetectionItemMapper;
import cn.ijiami.detection.mapper.TDetectionTemplateMapper;
import cn.ijiami.detection.query.DetectionTemplateQuery;
import cn.ijiami.detection.query.TemplateDetectionItemQuery;
import cn.ijiami.detection.service.api.IDetectionItemService;
import cn.ijiami.detection.service.api.IDetectionTemplateService;
import cn.ijiami.detection.service.api.ITemplateDetectionItemService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

/**
 * 检测模版接口实现类
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class DetectionTemplateServiceImpl implements IDetectionTemplateService {
	@Autowired
	private TDetectionTemplateMapper detectionTemplateMapper;

	@Autowired
	private ITemplateDetectionItemService templateDetectionItemService;

	@Autowired
	private IDetectionItemService detectionItemService;

	@Autowired
	private TDetectionItemMapper detectionItemMapper;

	@Override
	public TDetectionTemplate addDetectionTemplate(DetectionTemplateVO detectionTemplateVO)
			throws IjiamiApplicationException {
		TDetectionTemplate detectionTemplate = new TDetectionTemplate();
		BeanUtils.copyProperties(detectionTemplateVO, detectionTemplate);

		List<Long> detectionItemIds = detectionTemplateVO.getDetectionItemIds();
		detectionTemplate.setTemplateId(null);
		detectionTemplate.setSensitiveType(SensitiveTypeEnum.DIY);
		detectionTemplate.setDetectionItemCount((Integer) detectionTemplateVO.getDetectionItemIds().size());
		detectionTemplate.setCreateTime(new Date());
		detectionTemplateMapper.insert(detectionTemplate);
		templateDetectionItemService.saveTemplateDetectionItem(detectionTemplate.getTemplateId(), detectionItemIds);
		return detectionTemplate;
	}

	@Override
	public int deleteDetectionTemplateById(Long templateId) throws IjiamiApplicationException {
		if (templateId == null) {
			throw new IjiamiApplicationException("模版ID不能为空");
		}
		templateDetectionItemService.deleteDetectionItemByTemplateId(templateId);

		return detectionTemplateMapper.deleteByPrimaryKey(templateId);
	}

	@Override
	public PageInfo<TDetectionTemplate> findDetectionTemplateByPage(DetectionTemplateQuery detectionTemplateQuery) {
		if (detectionTemplateQuery.getPage() != null && detectionTemplateQuery.getRows() != null) {
			PageHelper.startPage(detectionTemplateQuery.getPage(), detectionTemplateQuery.getRows());
		}
		List<TDetectionTemplate> detectionTemplateList = detectionTemplateMapper
				.selectDetectionTemplateList(detectionTemplateQuery);
		return new PageInfo<TDetectionTemplate>(detectionTemplateList);
	}

	@Override
	public PageInfo<DetectionItemVO> findDetectionTemplateDetail(Long templateId) throws IjiamiApplicationException {
		TemplateDetectionItemQuery detectionItemQuery = new TemplateDetectionItemQuery();
		detectionItemQuery.setTemplateId(templateId);
		PageInfo<DetectionItemVO> detectionItemList = detectionItemService.findDetectionItemByQuery(detectionItemQuery);
		return detectionItemList;
	}

	@Override
	public TDetectionTemplate findById(Long templateId) {

		return detectionTemplateMapper.selectByPrimaryKey(templateId);
	}

	@Override
	public List<TDetectionTemplate> getTemplateList(DetectionTemplateQuery detectionTemplateQuery) {
		List<TDetectionTemplate> detectionTemplateList = detectionTemplateMapper
				.selectDetectionTemplateList(detectionTemplateQuery);
		return detectionTemplateList;
	}

	@Override
	public PageInfo<DetectionTemplateTypeVO> findDetectionTemplateVoByPage(
			DetectionTemplateQuery detectionTemplateQuery) {
		if (detectionTemplateQuery.getPage() != null && detectionTemplateQuery.getRows() != null) {
			PageHelper.startPage(detectionTemplateQuery.getPage(), detectionTemplateQuery.getRows());
		}
		List<DetectionTemplateTypeVO> detectionTemplateList = detectionTemplateMapper
				.selectDetectionTemplateList1(detectionTemplateQuery);
		return new PageInfo<DetectionTemplateTypeVO>(detectionTemplateList);
	}

}
