package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.service.api.IapkCategoryService;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.base.common.file.entity.File;
import cn.ijiami.detection.VO.CountSensitiveNameVO;
import cn.ijiami.detection.VO.CountSensitiveTypeVO;
import cn.ijiami.detection.VO.PersonalMessageAndType;
import cn.ijiami.detection.VO.PersonalMessageAndTypeVO;
import cn.ijiami.detection.VO.RiskImageVo;
import cn.ijiami.detection.VO.SensitiveWordImgVO;
import cn.ijiami.detection.VO.TPrivacySensitiveWordVO;
import cn.ijiami.detection.VO.detection.privacy.ActionComplianceVO;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.LawResultMarkStatusEnum;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.service.api.IPrivacySensitiveWordService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.detection.utils.ImageUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2019-06-06 14:30
 */
@Service
public class PrivacySensitiveWordServiceImpl implements IPrivacySensitiveWordService {

    private final TPrivacySensitiveWordMapper privacySensitiveWordMapper;
    private final IapkCategoryService categoryService;
    private final IjiamiCommonProperties commonProperties;
    private final IBaseFileService fileService;
    private final ITaskService taskService;
    private final TPrivacyResultMarkMapper privacyResultMarkMapper;
    private final TTaskMapper taskMapper;
    private final TAssetsMapper tAssetsMapper;

    private final TTaskAutoReportMapper taskAutoReportMapper;


    public PrivacySensitiveWordServiceImpl(TPrivacySensitiveWordMapper privacySensitiveWordMapper, IapkCategoryService categoryService, IjiamiCommonProperties commonProperties, IBaseFileService fileService,
                                           ITaskService taskService, TPrivacyResultMarkMapper privacyResultMarkMapper,
                                           TTaskMapper taskMapper, TAssetsMapper tAssetsMapper, TTaskAutoReportMapper taskAutoReportMapper) {
        this.privacySensitiveWordMapper = privacySensitiveWordMapper;
        this.categoryService = categoryService;
        this.commonProperties = commonProperties;
        this.fileService = fileService;
        this.taskService = taskService;
        this.privacyResultMarkMapper = privacyResultMarkMapper;
        this.taskMapper = taskMapper;
        this.tAssetsMapper = tAssetsMapper;
        this.taskAutoReportMapper = taskAutoReportMapper;
    }

    @Override
    public List<TPrivacySensitiveWord> findByTaskId(Long taskId, Integer behaviorStage) {
        return privacySensitiveWordMapper.findByTaskIdAndMethod(taskId, null, behaviorStage);
    }

    @Override
    public List<TPrivacySensitiveWord> findByCookie(Long taskId, Integer behaviorStage) {
        return privacySensitiveWordMapper.findByTaskIdAndMethod(taskId, "Cookies", behaviorStage);
    }

    @Override
    public List<SensitiveWordImgVO> findImgByTaskId(Long taskId, Integer behaviorStage) {
        List<SensitiveWordImgVO> imgs = new ArrayList<>();
        List<TPrivacySensitiveWord> sensitiveWords = this.findByTaskId(taskId, behaviorStage);
        for (TPrivacySensitiveWord sensitiveWord : sensitiveWords) {
            File img = fileService.findFileByFileKey(sensitiveWord.getFileKey());
            if (img == null) {
                continue;
            }
            RiskImageVo riskImage = ImageUtils.getRiskImage(commonProperties.getFilePath() + img.getFilePath());
            if (riskImage != null) {
                riskImage.setImageBase64(ImageUtils.getImageBase64(commonProperties.getFilePath() + img.getFilePath()));
                SensitiveWordImgVO sensitiveWordImgVO = new SensitiveWordImgVO();
                sensitiveWordImgVO.setSensitiveWordId(sensitiveWord.getId());
                sensitiveWordImgVO.setSdkId(sensitiveWord.getSdkId());
                sensitiveWordImgVO.setIsCookie("Cookies".equals(sensitiveWord.getMethod()));
                sensitiveWordImgVO.setImg(riskImage);
                imgs.add(sensitiveWordImgVO);
            }
        }

        return imgs;
    }

    @Override
    public List<CountSensitiveTypeVO> countSensitiveTypeByTaskId(Long taskId, Integer behaviorStage) {
        return privacySensitiveWordMapper.countSensitiveTypeByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<CountSensitiveNameVO> countSensitiveNameByTaskId(Long taskId, Long typeId, Integer behaviorStage) {
        return privacySensitiveWordMapper.countSensitiveNameByTaskId(taskId, typeId, behaviorStage);
    }

    @Override
    public List<TPrivacySensitiveWord> findByTaskIdAndTypeIdAndName(Long taskId, Long typeId, String name, Integer behaviorStage) {
        String tmp = "<span class=\"font-red\" style=\"color:red\">$0</span>";
        TTask task = taskService.findById(taskId);
        List<TPrivacySensitiveWord> sensitiveWords = privacySensitiveWordMapper.findByTaskIdAndTypeIdAndName(taskId, typeId, name, behaviorStage);
        for (TPrivacySensitiveWord sensitiveWord : sensitiveWords) {
            try {
        	    if (sensitiveWord == null) {
			        continue; // 跳过空对象
			    }
        	
                // 匹配的关键词
                String word = sensitiveWord.getSensitiveWord();
                if (word.endsWith(":")) {
                    word = word.substring(0, word.indexOf(":"));
                }
                // 符合条件的代码
                String code = sensitiveWord.getCode();
                if (StringUtils.isNotBlank(code)) {
            	
            	
            	// 转义特殊字符
			    String escapedWord = Pattern.quote(word);
			    String newCode = code.replaceAll(escapedWord, tmp);
			    sensitiveWord.setCode(newCode);
            	
//                String newCode = code.replaceAll(word, tmp);
//                sensitiveWord.setCode(newCode);
                if (StringUtils.contains(sensitiveWord.getMethod(), "\n")) {
                    sensitiveWord.setMethod(sensitiveWord.getMethod().split("\n")[1]);
                }
                //Android设备类型特殊处理
                String stackInfo = sensitiveWord.getStackInfo();
                if (task.getTerminalType() == TerminalTypeEnum.ANDROID && StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && JSON.isValid(stackInfo)) {
//                if (task.getTerminalType() == TerminalTypeEnum.ANDROID && StringUtils.isNotBlank(stackInfo) && DataHandleUtil.isJSONValid(stackInfo)) {
				        ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
				        sensitiveWord.setStackInfo(actionStackBO.getStackInfo());
				        sensitiveWord.setDetailsData(actionStackBO.getDetailsData());
				        sensitiveWord.setActionTime(actionStackBO.getActionTime());
				    }
				}
			} catch (Exception e) {
				e.getMessage();
			}
        }

        return sensitiveWords;
    }


    /**
     * 根据所有筛选条件返回传输个人信息的数据
     * @param behaviorQuery
     * @param userId
     * @return
     */
    @Override
    public TPrivacySensitiveWordVO findByTaskIdAndAllQuerys(BehaviorQuery behaviorQuery,Long userId) {
        TPrivacySensitiveWordVO tPrivacySensitiveWordVO = new TPrivacySensitiveWordVO();
        List<TPrivacySensitiveWord> sensitiveWords=null;
        PageInfo<TPrivacySensitiveWord> pageInfo = null;
        if(behaviorQuery !=null && (!"".equals(behaviorQuery.getTaskId()) && behaviorQuery.getTaskId()!=null)
                && (behaviorQuery.getTaskId()!=null && !"".equals(behaviorQuery.getTaskId()))){
            Long taskId = behaviorQuery.getTaskId();
            Integer behaviorStage = behaviorQuery.getBehaviorStage();
            String executors = behaviorQuery.getExecutors();
            String executorType = behaviorQuery.getExecutorType();
            String typeName = behaviorQuery.getTypeName();
            String personalName = behaviorQuery.getPersonalName();
            Integer sortType = behaviorQuery.getSortType();
            Integer sortOrder = behaviorQuery.getSortOrder();

            TTask task = taskService.findById(taskId);
            //增加一个判断是不是当前用户创建的任务，不是的话要传一个表示给前端不让其他用户做修改
            Integer isEdit=0;//是否可编辑 0不可编辑 1可编辑
            if(task!=null && task.getCreateUserId()!=null && userId!=null){
                if(userId.equals(task.getCreateUserId())){
                    isEdit=1;
                }
            }
            if (behaviorQuery.getPage() != null && behaviorQuery.getRows() != null) {
                PageHelper.startPage(behaviorQuery.getPage(), behaviorQuery.getRows());
            }
            sensitiveWords = privacySensitiveWordMapper.findByTaskIdAndAllQuerys(taskId, behaviorStage, executors,
                    executorType, typeName, personalName, sortType, sortOrder, behaviorQuery.getCookieMark(),
                    task != null ? task.getTerminalType().getValue() : null, behaviorQuery.getPlaintextTransmission());
            // 移除乱码
            sensitiveWords.forEach(data -> data.setDetailsData(data.getDetailsData().equals("")?PinfoConstant.DETAILS_EMPTY:data.getDetailsData()));
            for (TPrivacySensitiveWord sensitiveWord : sensitiveWords) {
                //将是否可编辑结果记录添加
                sensitiveWord.setIsEdit(isEdit);
                // 匹配的关键词
                String word = sensitiveWord.getSensitiveWord().toLowerCase();
                if (word.endsWith(":")) {
                    word = word.substring(0, word.indexOf(":"));
                }
                // 符合条件的代码
                String code = sensitiveWord.getCode().toLowerCase();
                if (StringUtils.isNotBlank(code)) {
                    String tmp = "<span class=\"font-red\" style=\"color:red\">{{word}}</span>";
                    int idx = code.indexOf(word);
                    if (idx >= 0) {
                        String head = sensitiveWord.getCode().substring(0, idx);
                        String tail = sensitiveWord.getCode().substring(idx + word.length());
                        String newWord = tmp.replace("{{word}}", sensitiveWord.getSensitiveWord());
                        String newCode = StringEscapeUtils.escapeHtml4(head) + newWord + StringEscapeUtils.escapeHtml4(tail);
                        sensitiveWord.setCode(newCode);
                    }
                    if (StringUtils.contains(sensitiveWord.getMethod(), "\n")) {
                        sensitiveWord.setMethod(sensitiveWord.getMethod().split("\n")[1]);
                    }
                    //Android设备类型特殊处理
                    String stackInfo = sensitiveWord.getStackInfo();
                    if (task.getTerminalType() == TerminalTypeEnum.ANDROID && StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && DataHandleUtil.isJSONValid(stackInfo)) {
                        ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
                        sensitiveWord.setStackInfo(StringUtils.isEmpty(sensitiveWord.getStackInfo())?actionStackBO.getStackInfo():sensitiveWord.getStackInfo());
                        sensitiveWord.setDetailsData(StringUtils.isEmpty(sensitiveWord.getDetailsData())?actionStackBO.getDetailsData():sensitiveWord.getDetailsData());
                        sensitiveWord.setActionTime(sensitiveWord.getActionTime()==null?actionStackBO.getActionTime():sensitiveWord.getActionTime());
                    }
                }
            }

        }

        if(sensitiveWords!=null){
            pageInfo = new PageInfo<>(sensitiveWords);
            pageInfo.setList(sensitiveWords);
            tPrivacySensitiveWordVO.setPageInfo(pageInfo);
        }
        return tPrivacySensitiveWordVO;
    }

    @Override
	@Transactional
	public void updateSensitiveWordStatus(ActionComplianceVO actionComplianceVO) throws IjiamiApplicationException {
		TPrivacySensitiveWord word = privacySensitiveWordMapper.selectByPrimaryKey(actionComplianceVO.getId());
		if(word==null) {
			throw new IjiamiApplicationException("数据不存在");
		}

		TTask task = taskMapper.selectByPrimaryKey(word.getTaskId());
		if(task==null) {
			throw new IjiamiApplicationException("任务不存在！");
		}

		if(!actionComplianceVO.getCreateUserId().equals(task.getCreateUserId())) {
			throw new IjiamiApplicationException("没有权限修改该数据！");
		}

		TPrivacySensitiveWord up_word = new TPrivacySensitiveWord();
		up_word.setId(actionComplianceVO.getId());
		up_word.setResultStatus(actionComplianceVO.getResultStatus());
		privacySensitiveWordMapper.updateByPrimaryKeySelective(up_word);

		TPrivacyResultMark mark = new TPrivacyResultMark();
		mark.setbId(word.getId());
		mark.setCreateTime(new Date());
		mark.setDescription(actionComplianceVO.getDescription());
		mark.setResultStatus(LawResultMarkStatusEnum.getItem(actionComplianceVO.getResultStatus()));
		mark.setResultType(LawResultTypeEnum.TRANSMISSTION);
		privacyResultMarkMapper.insertSelective(mark);

        // 任务数据改变，默认报告修改为失效
        TTaskAutoReport update = new TTaskAutoReport();
        update.setIsDelete(BooleanEnum.TRUE.value);
        update.setUpdateTime(new Date());
        Example example = new Example(TTaskAutoReport.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", task.getTaskId());
        criteria.andEqualTo("isDelete", BooleanEnum.FALSE.value);
        taskAutoReportMapper.updateByExampleSelective(update, example);
	}

    /**
     * 获取触发主体筛选内容
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorTransmission(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            TAssets tAssets = tAssetsMapper.selectAssetByTaskId(task.getTaskId());
            List<String> list = privacySensitiveWordMapper.getBehaviorTransmission(task.getTaskId(), behaviorStage);
            for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo.replace("\n",""))) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }
            //过滤掉与app名称相同的sdk主体
            result=result.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 获取信息分类筛选内容
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorBehaviorMessageType(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        List<String> list = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            list = privacySensitiveWordMapper.getBehaviorBehaviorMessageType(task.getTaskId(), behaviorStage);
            /*for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo)) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }*/
        }
        return list;
    }

    /**
     * 获取个人信息筛选内容
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public List<String> getBehaviorPersonalMessage(String documentId, Integer behaviorStage) {
        List<String> result = new ArrayList<>();
        TTask task = taskService.findByDocumentId(documentId);
        List<String> list = new ArrayList<>();
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
             list= privacySensitiveWordMapper.getBehaviorPersonalMessage(task.getTaskId(), behaviorStage);
            /*for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (result.contains(sdkInfo)) {
                        continue;
                    }
                    result.add(sdkInfo);
                }
            }*/
        }
        return list;
    }

    /**
     * 获取信息分类与个人信息关联的条件数据集合
     * @param documentId
     * @param behaviorStage
     * @return
     * @date 2021/6/8
     */
    @Override
    public PersonalMessageAndTypeVO getBehaviorPersonalMessageAndType(String documentId, Integer behaviorStage) {
        //存放查询到的个人信息集合
        List<PersonalMessageAndType> personalMessageList=null;
        //存放查询到的信息分类集合
        List<PersonalMessageAndType> messageTypeList=null;
        //存放个人信息关联信息分类map集合
        Map<String,List<PersonalMessageAndType>> personalMessageMap = new HashMap();
        //存放信息分类关联个人信息map集合
        Map<String,List<PersonalMessageAndType>> messageTypeMap = new HashMap<>();

        //存放最终数据集合
        PersonalMessageAndTypeVO personalMessageAndTypeVO = new PersonalMessageAndTypeVO();

        TTask task = taskService.findByDocumentId(documentId);
        if(task ==null || task.getTaskId()==null || task.getTaskId()==0L){
            return personalMessageAndTypeVO;
        }
        personalMessageList=privacySensitiveWordMapper.getBehaviorPersonalList(task.getTaskId(),behaviorStage);
        messageTypeList=privacySensitiveWordMapper.getBehaviorMessageTypeList(task.getTaskId(),behaviorStage);
        for(PersonalMessageAndType personMessage:personalMessageList){
            List<PersonalMessageAndType> list1=new ArrayList<>();
            for (PersonalMessageAndType messageType:messageTypeList){
                if(StringUtils.isEmpty(personMessage.getName()) || StringUtils.isEmpty(messageType.getName())){
                    continue;
                }
                if(personMessage.getName().equals(messageType.getName())){
                    list1.add(messageType);
                }
            }

            personalMessageMap.put(personMessage.getName(),list1.stream().distinct().collect(Collectors.toList()));
        }

        for(PersonalMessageAndType type:messageTypeList){
            List<PersonalMessageAndType> list2=new ArrayList<>();
                for (PersonalMessageAndType person:personalMessageList){
                    if(StringUtils.isEmpty(person.getTypeName()) || StringUtils.isEmpty(type.getTypeName())){
                        continue;
                    }
                    if(type.getTypeName().equals(person.getTypeName())){
                        list2.add(person);
                    }
                }
            messageTypeMap.put(type.getTypeName(),list2.stream().distinct().collect(Collectors.toList()));
        }

        personalMessageAndTypeVO.setMessageType(messageTypeMap);
        personalMessageAndTypeVO.setPersonalMessageMap(personalMessageMap);

        return personalMessageAndTypeVO;
    }
}
