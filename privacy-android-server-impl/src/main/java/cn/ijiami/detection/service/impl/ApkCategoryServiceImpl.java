package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.PrivacyCategoryVO;
import cn.ijiami.detection.entity.TApkCategory;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyCategory;
import cn.ijiami.detection.mapper.TApkCategoryMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TPrivacyCategoryMapper;
import cn.ijiami.detection.service.api.IapkCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用类型接口实现类
 *
 * <AUTHOR>
 */
@Service
public class ApkCategoryServiceImpl implements IapkCategoryService {

    private final TApkCategoryMapper apkCategoryMapper;
    private final TPrivacyCategoryMapper privacyCategoryMapper;
    private final TAssetsMapper assetsMapper;

    public ApkCategoryServiceImpl(TApkCategoryMapper apkCategoryMapper, TPrivacyCategoryMapper privacyCategoryMapper, TAssetsMapper assetsMapper) {
        this.apkCategoryMapper = apkCategoryMapper;
        this.privacyCategoryMapper = privacyCategoryMapper;
        this.assetsMapper = assetsMapper;
    }

    @Override
    public List<TApkCategory> findListApkCategory(TApkCategory apkCategory) {
        return apkCategoryMapper.select(apkCategory);
    }

    @Override
    public PrivacyCategoryVO findByTaskId(Long taskId) {
        Example example = new Example(TPrivacyCategory.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        List<TPrivacyCategory> privacyCategories = privacyCategoryMapper.selectByExample(example);


        PrivacyCategoryVO privacyCategoryVO = new PrivacyCategoryVO();
        List<Long> privacyCategoryIds = new ArrayList<>();

        for (TPrivacyCategory privacyCategory : privacyCategories) {
            privacyCategoryIds.add(privacyCategory.getCategoryId());
        }
        //获取上传资产时存储的功能类型数据
        TAssets assets = assetsMapper.getAssetByTaskId(taskId);
        //当获取到资产的功能类型后，需要根据t_privacy_category表里面是否存在数据再展示，避免做了修改之后还展示资产功能数据问题
        if(assets!=null && !StringUtils.isEmpty(assets.getAssetsFunctionType()) && privacyCategoryIds.size()==0){
            String[]functionIds=assets.getAssetsFunctionType().split(",");
            for(String ids:functionIds){
                privacyCategoryIds.add(Long.parseLong(ids));
            }
        }
        privacyCategoryVO.setPrivacyCategoryIds(privacyCategoryIds.stream().distinct().collect(Collectors.toList()));
        privacyCategoryVO.setTaskId(taskId);
        
        if( privacyCategoryVO.getPrivacyCategoryIds()!= null &&  privacyCategoryVO.getPrivacyCategoryIds().size()>0) {
        	List<String> cateNames = apkCategoryMapper.apkCategoryNames(privacyCategoryVO.getPrivacyCategoryIds());
        	privacyCategoryVO.setPrivacyCategoryNames(cateNames);
        }
        return privacyCategoryVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrivacyCategory(PrivacyCategoryVO privacyCategoryVO) {
        Example example = new Example(TPrivacyCategory.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", privacyCategoryVO.getTaskId());
        privacyCategoryMapper.deleteByExample(example);

        List<TPrivacyCategory> privacyCategories = new ArrayList<>();
        String assetsFunctionType="";
        for (Long privacyCategoryId : privacyCategoryVO.getPrivacyCategoryIds()) {
            TPrivacyCategory privacyCategory = new TPrivacyCategory();
            privacyCategory.setTaskId(privacyCategoryVO.getTaskId());
            privacyCategory.setCategoryId(privacyCategoryId);
            privacyCategories.add(privacyCategory);
            assetsFunctionType +=privacyCategoryId.toString()+",";
        }

        if(!StringUtils.isEmpty(assetsFunctionType)){
            assetsFunctionType=assetsFunctionType.substring(0,assetsFunctionType.length()-1);
        }
        //获取上传资产时存储的功能类型数据
        TAssets assets = assetsMapper.getAssetByTaskId(privacyCategoryVO.getTaskId());
        if(assets!=null){
            //只要点了更新，则同步修改资产表里面的功能类型数据避免查询时又复现功能类型
            assetsMapper.updateFunctionTypeById(assets.getId(),assetsFunctionType);
        }
        if (CollectionUtils.isEmpty(privacyCategories)) {
            return;
        }
        privacyCategoryMapper.insertList(privacyCategories);
    }
}
