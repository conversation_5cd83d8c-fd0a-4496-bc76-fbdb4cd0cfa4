package cn.ijiami.detection.websocket.idb;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomWebSocketClient.java
 * @Description 自定义WebSocketClient
 * @createTime 2021年11月04日 18:40:00
 */
@Slf4j
public class CustomWebSocketClient extends WebSocketClient {

    private CustomWebSocketListener listener;

    public CustomWebSocketClient(URI serverUri) {
        super(serverUri);
    }

    public void setSocketListener(CustomWebSocketListener listener) {
        this.listener = listener;
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        if (listener != null) {
            listener.onOpen(handshakedata);
        }
    }

    @Override
    public void onMessage(String message) {
        if (listener != null) {
            listener.onMessage(message);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        if (listener != null) {
            listener.onClose(code, reason, remote);
        }
    }

    @Override
    public void onError(Exception ex) {
        if (listener != null) {
            listener.onError(ex);
        } else {
            log.info("找不到监听器 输出错误到日志中 message={}", ex.getMessage(), ex);
        }
    }

    public interface CustomWebSocketListener {
        void onOpen(ServerHandshake handshakedata);
        void onMessage(String message);
        void onClose(int code, String reason, boolean remote);
        void onError(Exception ex);
    }
}