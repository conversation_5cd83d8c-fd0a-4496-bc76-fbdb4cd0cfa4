package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.idb.WebSocketCloseCode;
import cn.ijiami.detection.utils.CommonUtil;
import org.java_websocket.enums.ReadyState;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.MDC;

import java.net.URI;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseSocketClient.java
 * @Description idbwebsocket基类
 * @createTime 2023年07月21日 11:12:00
 */
public abstract class BaseWebSocketClient implements CustomWebSocketClient.CustomWebSocketListener {

    private final static int RECONNECT_MAX = 8;

    private final AtomicInteger reconnectCount = new AtomicInteger(0);
    private final AtomicBoolean isDelayReconnect = new AtomicBoolean(false);
    private final AtomicBoolean isNormalClose = new AtomicBoolean(false);

    private final AtomicInteger sendHeartBeatCount = new AtomicInteger(0);
    private final AtomicBoolean sendHeartBeatRunning = new AtomicBoolean(false);

    protected ExecutorServiceHelper executorServiceHelper;

    private volatile IdbWebSocketStatusListener statusListener;

    private CustomWebSocketClient webSocketClient;

    public BaseWebSocketClient(URI serverUri) {
        webSocketClient = new CustomWebSocketClient(serverUri);
    }

    public void connect() {
        webSocketClient.setSocketListener(this);
        webSocketClient.connect();
    }

    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        try {
            getLogger().info("WebSocket Open");
            isNormalClose.set(false);
            // 连成功后，重连次数清空，心跳发送次数清空
            reconnectCount.set(0);
            if (statusListener != null) {
                statusListener.onOpen(this);
            }
            startHearBeat();
            executeWaitingSendMessage();
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    @Override
    public void onMessage(String messageStr) {
        if (messageStr.equals("pong")) {
            getLogger().info("WebSocket pong");
            sendHeartBeatCount.set(0);
            return;
        }
        handleReceiveMessage(messageStr);
    }

    protected abstract void handleReceiveMessage(String message);

    protected abstract void executeWaitingSendMessage();

    protected abstract Logger getLogger();

    public boolean isOpen() {
        return webSocketClient.isOpen();
    }

    public void send(String message) {
        webSocketClient.send(message);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        getLogger().info("WebSocket Close code={} reason={} remote={}", code, reason, remote);
        sendHeartBeatRunning.set(false);
        if (code == WebSocketCloseCode.CLOSE_NORMAL.code) {
            isNormalClose.set(true);
        } else {
            isNormalClose.set(false);
            if (code != WebSocketCloseCode.CLOSE_GOING_AWAY.code) {
                autoReconnect();
            }
        }
        if (statusListener != null) {
            statusListener.onClose(this, code, reason, remote);
        }
    }

    @Override
    public void onError(Exception e) {
        getLogger().info("WebSocket Error {}", e.getMessage(), e);
        autoReconnect();
    }

    private void startHearBeat() {
        sendHeartBeatRunning.set(true);
        sendHeartBeatCount.set(0);
        sendHeartBeat();
    }

    private void sendHeartBeat() {
        if (!sendHeartBeatRunning.get()) {
            getLogger().info("WebSocket not sendHeartBeatRunning");
            return;
        }
        // 已经关闭，不再进行心跳
        if (!webSocketClient.isOpen()) {
            getLogger().info("WebSocket sendHeartBeatCount={} webSocket not open", sendHeartBeatCount.get());
            sendHeartBeatRunning.set(false);
            return;
        }
        // 大于一定次数没收到回应，断开重连
        if (sendHeartBeatCount.get() >= 5) {
            getLogger().info("WebSocket sendHeartBeatCount={} timeout", sendHeartBeatCount.get());
            sendHeartBeatRunning.set(false);
            webSocketClient.reconnect();
            return;
        }
        try {
            webSocketClient.send("ping");
            sendHeartBeatCount.incrementAndGet();
            getLogger().info("WebSocket ping");
        } catch (Exception e) {
            getLogger().info("WebSocket sendHeartBeat error", e);
        }
        // 40秒后发下一个心跳
        executorServiceHelper.getScheduledExecutorService().schedule(this::sendHeartBeat, 40, TimeUnit.SECONDS);
    }

    private void autoReconnect() {
        if (reconnectCount.get() < RECONNECT_MAX && !isDelayReconnect.get()) {
            // 按照指数级避让去重连
            long delay = (long) Math.pow(4, reconnectCount.get());
            getLogger().info("WebSocket reconnect delay={}", delay);
            isDelayReconnect.set(true);
            executorServiceHelper.getScheduledExecutorService().schedule(() -> {
                isDelayReconnect.set(false);
                // 已经开启连接的不需要重连
                if (webSocketClient.isOpen()) {
                    getLogger().info("WebSocket reconnect already open");
                    return;
                }
                // 正常关闭的不需要再重连
                if (isNormalClose.get()) {
                    getLogger().info("WebSocket reconnect normal close");
                    return;
                }
                try {
                    reconnect();
                } catch (Throwable t) {
                    getLogger().error("WebSocket reconnect error", t);
                } finally {
                    reconnectCount.incrementAndGet();
                }
                getLogger().info("WebSocket reconnect reconnectCount={}", reconnectCount.get());
            }, delay, TimeUnit.SECONDS);
        } else {
            getLogger().info("WebSocket reconnect ignore reconnectCount={} isDelayReconnect={}", reconnectCount.get(), isDelayReconnect.get());
        }
    }

    protected synchronized void reconnect() {
        getLogger().info("WebSocket not open readyState={}", webSocketClient.getReadyState());
        try {
            if (webSocketClient.getReadyState() == ReadyState.CLOSED) {
                reconnectIdb();
                getLogger().info("WebSocket connect");
            } else if (webSocketClient.getReadyState() == ReadyState.NOT_YET_CONNECTED) {
                reconnectIdb();
                getLogger().info("WebSocket connect");
            }
        } catch (Throwable e) {
            getLogger().error("WebSocket connect error message={}", e.getMessage(), e);
        }
    }

    private synchronized void reconnectIdb() {
        webSocketClient.close();
        webSocketClient.setSocketListener(null);
        // 重新创建WebSocket
        webSocketClient = new CustomWebSocketClient(webSocketClient.getURI());
        connect();
    }

    public void setStatusListener(IdbWebSocketStatusListener statusListener) {
        this.statusListener = statusListener;
    }

    public void setExecutorServiceHelper(ExecutorServiceHelper executorServiceHelper) {
        this.executorServiceHelper = executorServiceHelper;
    }

    public interface IdbWebSocketStatusListener {
        void onClose(BaseWebSocketClient client, int code, String reason, boolean remote);

        void onOpen(BaseWebSocketClient client);
    }
}
