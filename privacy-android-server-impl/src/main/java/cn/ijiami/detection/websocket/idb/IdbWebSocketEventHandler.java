package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.job.TaskSortThread;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.concurrent.TimeUnit;

@Slf4j
public class IdbWebSocketEventHandler implements IdbWebSocketClient.IdbWebSocketStatusListener {

    @Autowired
    private TTaskMapper taskMapper;

    @Lazy
    @Autowired
    private ITaskService taskService;

    @Lazy
    @Autowired
    private TaskSortThread taskSortThread;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Override
    public void onClose(BaseWebSocketClient client, int code, String reason, boolean remote) {

    }

    @Override
    public void onOpen(BaseWebSocketClient client) {
        log.info("重连后检查任务状态");
        executorServiceHelper.schedule(() -> taskSortThread.checkIosDynamicTask(), 5, TimeUnit.SECONDS);
    }

}
