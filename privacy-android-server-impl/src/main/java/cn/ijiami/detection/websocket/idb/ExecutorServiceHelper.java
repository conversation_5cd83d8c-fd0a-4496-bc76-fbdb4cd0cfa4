package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.thread.MDCContinueThreadPoolExecutor;
import cn.ijiami.detection.thread.decorator.MDCContinueRunnableDecorator;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@Scope("singleton")
public class ExecutorServiceHelper implements DisposableBean {

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(
            new ThreadFactoryBuilder().setNameFormat(ExecutorServiceHelper.class.getSimpleName() + "-scheduled-pool-%d").build());

    /**
     * 日志处理线程池
     */
    private final ExecutorService logExecutorService = new MDCContinueThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(30000),
            new ThreadFactoryBuilder().setNameFormat(ExecutorServiceHelper.class.getSimpleName() + "-log-pool-%d").build(),
            new ThreadPoolExecutor.DiscardOldestPolicy() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                    super.rejectedExecution(r, e);
                    log.info("logExecutorService rejected DiscardOldest");
                }
            });

    /**
     * 数据解析处理线程池
     */
    private final ExecutorService dataAnalysisExecutorService = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            Runtime.getRuntime().availableProcessors() * 4,
            120L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadFactoryBuilder().setNameFormat(ExecutorServiceHelper.class.getSimpleName() + "-dataAnalysis-pool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                    super.rejectedExecution(r, e);
                    log.info("dataAnalysisExecutorService rejected CallerRuns");
                }
            });

    /**
     * 通用线程池，用于io密集型多线程任务
     */
    @Autowired
    @Qualifier("commonExecutor")
    private Executor commonExecutor;

    /**
     * 资产解析处理线程池
     */
    private final ExecutorService assetsAnalyzeExecutorService = new ThreadPoolExecutor(
            1,
            1,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(10),
            new ThreadFactoryBuilder().setNameFormat(ExecutorServiceHelper.class.getSimpleName() + "-assetsAnalyze-pool-%d").build(),
            new ThreadPoolExecutor.DiscardOldestPolicy() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                    super.rejectedExecution(r, e);
                    log.info("assetsAnalyzeExecutorService rejected discardOldest");
                }
            });

    public ScheduledExecutorService getScheduledExecutorService() {
        return scheduledExecutorService;
    }

    public void schedule(Runnable runnable, long delay, TimeUnit unit) {
        scheduledExecutorService.schedule(new MDCContinueRunnableDecorator(() -> {
            // 放到通用线程池去处理，避免有时间长的任务，卡住scheduledExecutor
            executeInWithCommonExecutor(runnable);
        }), delay, unit);
    }

    public Executor getCommonExecutor() {
        return commonExecutor;
    }

    /**
     * 调用此方法执行异步任务，会自动保留输出日志的MDC到新线程，保证日志的跟踪
     * @param runnable
     */
    public void executeInWithCommonExecutor(Runnable runnable) {
        commonExecutor.execute(runnable);
    }

    /**
     * 调用此方法执行异步任务，会自动保留输出日志的MDC到新线程，保证日志的跟踪
     * @param runnable
     */
    public void executeInWithDataAnalysisExecutor(Runnable runnable) {
        dataAnalysisExecutorService.execute(new MDCContinueRunnableDecorator(runnable));
    }

    /**
     * 调用此方法执行异步任务，会自动保留输出日志的MDC到新线程，保证日志的跟踪
     * @param runnable
     */
    public void executeInLogMessageExecutor(Runnable runnable) {
        logExecutorService.execute(new MDCContinueRunnableDecorator(runnable));
    }

    public void executeInAssetsAnalyzeExecutor(Runnable runnable) {
        assetsAnalyzeExecutorService.execute(new MDCContinueRunnableDecorator(runnable));
    }

    @Override
    public void destroy() throws Exception {
        log.info("ExecutorServiceHelper destroy");
        scheduledExecutorService.shutdownNow();
        logExecutorService.shutdownNow();
        assetsAnalyzeExecutorService.shutdownNow();
    }
}
