package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.enums.IosDynamicDetectionCmdEnum;
import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.request.BaseIdbRequest;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.utils.CommonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.MDC;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

@Slf4j
public class IdbWebSocketClient extends BaseWebSocketClient {

    /**
     * 存放回调，以requestId作为key.
     * 因为idb命令在逻辑上不是有发送就一定有回复信息，所以需要有个超时自动移除回调的机制，选用cache来存
     * idb都是短时间的操作，回调缓存10分钟就足够了，到期自动释放内存
     */
    private final Cache<String, CallbackInfo> callbackMap = CacheBuilder.newBuilder()
            .recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();

    private final Cache<String, WaitingSendMessage> waitingSendMap = CacheBuilder.newBuilder()
            .recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();

    private volatile MessageListener defaultMessageHandler;

    public IdbWebSocketClient(URI serverUri) {
        super(serverUri);
    }

    /**
     * 默认的消息处理器
     * @param defaultHandler
     */
    public void setDefaultMessageHandler(MessageListener defaultHandler) {
        this.defaultMessageHandler = defaultHandler;
    }

    @Override
    protected void executeWaitingSendMessage() {
        for (Map.Entry<String, WaitingSendMessage> e : waitingSendMap.asMap().entrySet()) {
            log.info("executeWaitingSendMessage requestId={}", e.getKey());
            waitingSendMap.invalidate(e.getKey());
            executeSendMessage(e.getValue().getMessage(), e.getValue().getListener());
        }
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

    @Override
    protected void handleReceiveMessage(String messageStr) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        log.info("WebSocket Message={}", messageStr);
        try {
            IdbCmd<BaseIdbResponse> responseIdbCmd = CommonUtil.jsonToBean(messageStr, new TypeReference<IdbCmd<BaseIdbResponse>>() {
            });
            if (responseIdbCmd == null) {
                log.info("WebSocket 格式错误");
                return;
            }
            if (responseIdbCmd.getRequestId() == null) {
                defaultMessageHandler(responseIdbCmd, messageStr);
                return;
            }
            CallbackInfo messageInfo = callbackMap.getIfPresent(responseIdbCmd.getRequestId());
            if (messageInfo == null || messageInfo.listener == null) {
                defaultMessageHandler(responseIdbCmd, messageStr);
                return;
            }
            messageInfo.isSuccess = true;
            if (IosDynamicDetectionCmdEnum.IDB_ACK.getValue().equals(responseIdbCmd.getCmdType())) {
                messageInfo.listener.onAckSuccess(this, responseIdbCmd);
            } else {
                if (messageInfo.listener.onResponse(this, responseIdbCmd, messageStr)) {
                    // 处理成功，移除回调
                    callbackMap.invalidate(responseIdbCmd.getRequestId());
                } else {
                    defaultMessageHandler(responseIdbCmd, messageStr);
                }
            }
        } catch (Exception e) {
            log.error("WebSocket onMessage error={}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    private void defaultMessageHandler(IdbCmd<BaseIdbResponse> responseIdbCmd, String messageStr) {
        if (defaultMessageHandler != null) {
            defaultMessageHandler.onResponse(this, responseIdbCmd, messageStr);
        }
    }

    public void removeCallbackByRequestId(String requestId) {
        log.info("WebSocket removeCallbackByRequestId requestId={}", requestId);
        callbackMap.invalidate(requestId);
        waitingSendMap.invalidate(requestId);
    }

    public void removeCallback(SendMessageListener callback) {
        log.info("WebSocket removeCallback");
        callbackMap
                .asMap()
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().listener == callback)
                .map(Map.Entry::getKey)
                .findFirst()
                .ifPresent(key -> {
                    callbackMap.invalidate(key);
                    log.info("WebSocket removeCallback success requestId={}", key);
                });
    }

    public void sendMessage(IdbCmd<? extends BaseIdbRequest> message, SendMessageListener listener) {
        // 已经关闭的要重新连接
        if (isOpen()) {
            executeSendMessage(message, listener);
        } else {
            log.info("WebSocket not open add to waitingSendMap requestId={}", message.getRequestId());
            waitingSendMap.put(message.getRequestId(), new WaitingSendMessage(message, listener));
            reconnect();
        }
    }

    private void executeSendMessage(IdbCmd<? extends BaseIdbRequest> message, SendMessageListener callback) {
        try {
            String messageStr = CommonUtil.beanToJson(message);
            log.info("WebSocket send message={}", messageStr);
            send(messageStr);
            if (callback != null) {
                callback.onSendSuccess(this);
            }
        } catch (Exception e) {
            log.info("WebSocket send message error={}", e.getMessage());
            if (callback != null) {
                callback.onSendFailure(this);
            }
            return;
        }
        if (callback != null) {
            callbackMap.put(message.getRequestId(), new CallbackInfo(callback));
            // 15秒后没收到回应则是发送失败
            executorServiceHelper.schedule(() -> {
                CallbackInfo messageInfo = callbackMap.getIfPresent(message.getRequestId());
                if (messageInfo != null && messageInfo.listener != null && !messageInfo.isSuccess) {
                    log.info("WebSocket send message failure requestId={} ", message.getRequestId());
                    callbackMap.invalidate(message.getRequestId());
                    messageInfo.listener.onAckTimeout(IdbWebSocketClient.this);
                }
            }, 15, TimeUnit.SECONDS);
        }
    }


    @Data
    static class CallbackInfo {

        public CallbackInfo(SendMessageListener listener) {
            this.listener = listener;
            this.isSuccess = false;
        }

        private SendMessageListener listener;
        private volatile boolean isSuccess;
    }

    @Data
    static class WaitingSendMessage {

        private IdbCmd<? extends BaseIdbRequest> message;
        private SendMessageListener listener;

        public WaitingSendMessage(IdbCmd<? extends BaseIdbRequest> message, SendMessageListener listener) {
            this.message = message;
            this.listener = listener;
        }
    }

    public interface SendMessageListener extends MessageListener {
        void onSendFailure(IdbWebSocketClient client);

        void onSendSuccess(IdbWebSocketClient client);

        void onAckTimeout(IdbWebSocketClient client);

        void onAckSuccess(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd);
    }

    public interface MessageListener {
        boolean onResponse(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd, String message);
    }

}
