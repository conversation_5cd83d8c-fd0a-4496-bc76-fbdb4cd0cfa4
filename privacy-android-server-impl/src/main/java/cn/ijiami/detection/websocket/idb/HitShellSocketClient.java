package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.utils.CommonUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HitShellSocketClient.java
 * @Description 砸壳websocket连接
 * @createTime 2022年01月17日 15:18:00
 */
@Slf4j
public class HitShellSocketClient extends BaseWebSocketClient {

    private final Cache<String, SendMessageListener> responseMap = CacheBuilder.newBuilder()
            .recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();
    private final Cache<String, WaitingSendMessage> waitingSendMap = CacheBuilder.newBuilder()
            .recordStats().expireAfterAccess(10, TimeUnit.MINUTES).build();

    public HitShellSocketClient(URI serverUri) {
        super(serverUri);
    }

    @Override
    protected void handleReceiveMessage(String messageStr) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        log.info("WebSocket Message={}", messageStr);
        try {
            JSONObject object = JSONObject.fromObject(messageStr);
            String cmdType = object.optString("cmdType");
            String id = object.optString("requestid");
            if (StringUtils.isEmpty(id)) {
                id = object.optString("businessId");
            }
            if (StringUtils.isEmpty(id)) {
                JSONObject resultData = object.optJSONObject("resultData");
                id = resultData.optString("businessId");
            }
            String requestId = id + cmdType;
            SendMessageListener response = responseMap.getIfPresent(requestId);
            if (Objects.nonNull(response)) {
                responseMap.invalidate(requestId);
                response.onResponse(messageStr);
            }
        } catch (Exception e) {
            log.error("WebSocket onMessage error={}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    @Override
    protected void executeWaitingSendMessage() {
        for (Map.Entry<String, WaitingSendMessage> e : waitingSendMap.asMap().entrySet()) {
            log.info("executeWaitingSendMessage requestId={}", e.getKey());
            waitingSendMap.invalidate(e.getKey());
            executeSendMessage(e.getValue().getMessage(), e.getKey(), e.getValue().getListener());
        }
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

    public void sendMessage(String request, String requestId, SendMessageListener listener) {
        // 已经关闭的要重新连接
        if (isOpen()) {
            executeSendMessage(request, requestId, listener);
        } else {
            log.info("WebSocket not open add to waitingSendMap requestId={}", requestId);
            waitingSendMap.put(requestId, new WaitingSendMessage(request, listener));
            reconnect();
        }
    }

    private void executeSendMessage(String message, String requestId, SendMessageListener sendMessageListener) {
        try {
            log.info("WebSocket send message={}", message);
            send(message);
            responseMap.put(requestId, sendMessageListener);
            executorServiceHelper.schedule(() -> {
                SendMessageListener listener = responseMap.getIfPresent(requestId);
                // 处理超时
                if (listener != null) {
                    responseMap.invalidate(requestId);
                    executorServiceHelper.executeInWithCommonExecutor(listener::onTimeout);
                }
            }, sendMessageListener.getTimeoutMillis(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.info("WebSocket send message error={}", e.getMessage());
            responseMap.invalidate(requestId);
            sendMessageListener.onError();
        }
    }

    public interface SendMessageListener {
        void onResponse(String responseMessage);

        void onTimeout();

        void onError();

        long getTimeoutMillis();
    }

    @Data
    static class WaitingSendMessage {

        private String message;
        private SendMessageListener listener;

        public WaitingSendMessage(String message, SendMessageListener listener) {
            this.message = message;
            this.listener = listener;
        }
    }

}
