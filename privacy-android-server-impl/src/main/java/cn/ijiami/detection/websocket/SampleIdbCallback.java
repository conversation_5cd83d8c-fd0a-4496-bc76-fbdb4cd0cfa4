package cn.ijiami.detection.websocket;

import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.service.api.IdbManagerService;

public class SampleIdbCallback<T> implements IdbManagerService.IdbCallback<T> {

    @Override
    public void onResponse(T t) {

    }

    @Override
    public void onSendFailure() {

    }

    @Override
    public void onAckSuccess(IdbCmd<BaseIdbResponse> t) {

    }

    @Override
    public void onSendSuccess() {

    }

    @Override
    public void onResponseTimeout() {

    }

}
