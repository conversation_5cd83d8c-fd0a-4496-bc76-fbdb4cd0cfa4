package cn.ijiami.detection.websocket.idb;

import cn.ijiami.detection.idb.IdbCmd;
import cn.ijiami.detection.idb.response.BaseIdbResponse;
import cn.ijiami.detection.service.api.IosIdbDetectionService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

@Slf4j
public class IdbDefaultMessageHandler implements IdbWebSocketClient.MessageListener {

    @Lazy
    @Autowired
    private IosIdbDetectionService idbDynamicDetectionService;

    @Lazy
    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Override
    public boolean onResponse(IdbWebSocketClient client, IdbCmd<BaseIdbResponse> responseIdbCmd, String message) {
        log.info("默认的idb数据处理");
        executorServiceHelper.executeInWithCommonExecutor(() -> {
            try {
                idbDynamicDetectionService.updateIosDynamic(JSONObject.fromObject(message));
            } catch (Exception e) {
                log.error("idb数据处理失败 message={}", e.getMessage(), e);
            }
        });
        return true;
    }

}
