package cn.ijiami.detection.system;

import lombok.extern.slf4j.Slf4j;

import java.io.FileDescriptor;
import java.net.InetAddress;
import java.security.Permission;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionSecurityManager.java
 * @Description 检查系统的退出操作
 * @createTime 2023年05月25日 14:37:00
 */
@Slf4j
public class DetectionSecurityManager extends SecurityManager {

    private static final List<String> DISALLOWED_EXIT_CALLERS = Arrays.asList("com.android.apksigner.ApkSignerTool");

    @Override
    public void checkExit(int status) {
        // 非正常退出需要判断，防止有些地方的代码擅自调用退出系统，导致系统终止运行
        if (status != 0) {
            Exception e = new Exception();
            StackTraceElement[] elements = e.getStackTrace();
            log.info(Arrays.toString(elements));
            // 从堆栈顶部开始，找到第一个不是System.exit(), Runtime.exit(), 或 SecurityManager.checkExit()的元素
            int i = 0;
            while (i < elements.length) {
                String methodName = elements[i].getMethodName();
                if (!("checkExit".equals(methodName) || "exit".equals(methodName))) {
                    break;
                }
                i++;
            }

            // 如果找到的这个元素在禁止的类名列表中，则抛出异常
            if (i < elements.length && DISALLOWED_EXIT_CALLERS.contains(elements[i].getClassName())) {
                throw new SecurityException("Disallowed call to System.exit by " + elements[i].getClassName());
            }
        }
    }

    @Override
    public void checkPermission(Permission perm) {

    }

    @Override
    public void checkPermission(Permission perm, Object context) {

    }

    @Override
    public void checkCreateClassLoader() {

    }

    @Override
    public void checkAccess(Thread t) {

    }

    @Override
    public void checkAccess(ThreadGroup g) {

    }

    @Override
    public void checkExec(String cmd) {

    }

    @Override
    public void checkLink(String lib) {

    }

    @Override
    public void checkRead(FileDescriptor fd) {

    }

    @Override
    public void checkRead(String file) {

    }

    @Override
    public void checkRead(String file, Object context) {

    }

    @Override
    public void checkWrite(FileDescriptor fd) {

    }

    @Override
    public void checkWrite(String file) {

    }

    @Override
    public void checkDelete(String file) {

    }

    @Override
    public void checkConnect(String host, int port) {

    }

    @Override
    public void checkConnect(String host, int port, Object context) {

    }

    @Override
    public void checkListen(int port) {

    }

    @Override
    public void checkAccept(String host, int port) {

    }

    @Override
    public void checkMulticast(InetAddress maddr) {

    }

    @Override
    public void checkMulticast(InetAddress maddr, byte ttl) {

    }

    @Override
    public void checkPropertiesAccess() {

    }

    @Override
    public void checkPropertyAccess(String key) {

    }

    @Override
    public boolean checkTopLevelWindow(Object window) {
        return true;
    }

    @Override
    public void checkPrintJobAccess() {

    }

    @Override
    public void checkSystemClipboardAccess() {

    }

    @Override
    public void checkAwtEventQueueAccess() {

    }

    @Override
    public void checkPackageAccess(String pkg) {

    }

    @Override
    public void checkPackageDefinition(String pkg) {

    }

    @Override
    public void checkSetFactory() {

    }

    @Override
    public void checkMemberAccess(Class<?> clazz, int which) {

    }

    @Override
    public void checkSecurityAccess(String target) {

    }
}
