package cn.ijiami.detection.handler;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiFileNotFoundException;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import cn.ijiami.detection.enums.DetectionHttpStatusEnum;
import cn.ijiami.detection.exception.RequestLimitException;
import cn.ijiami.framework.common.enums.HttpStatusEnum;
import cn.ijiami.framework.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionGlobalExceptionHandler.java
 * @Description 全局错误处理类
 * @createTime 2021年12月09日 16:43:00
 */
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
public class DetectionGlobalExceptionHandler {

    @ExceptionHandler(RequestLimitException.class)
    @ResponseBody
    public BaseResponse<?> handleRequestLimit(HttpServletRequest req, HttpServletResponse rsp, Exception e) throws Exception {
        BaseResponse<String> response = new BaseResponse<>();
        response.setMessage(e.getMessage());
        response.setStatus(DetectionHttpStatusEnum.TOO_MANY_REQUEST.getValue());
        return response;
    }

    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseBody
    public BaseResponse<?> handleDuplicateKey(HttpServletRequest req, HttpServletResponse rsp, Exception e) throws Exception {
        BaseResponse<String> response = new BaseResponse<>();
        if (StringUtils.contains(e.getMessage(), "tsys_user")) {
            response.setMessage("用户名重复");
        } else {
            response.setMessage("数据重复");
        }
        log.error("执行请求失败,地址:" + req.getRequestURL(), e);
        response.setStatus(HttpStatusEnum.FAIL.getValue());
        return response;
    }

    @ExceptionHandler(IjiamiApplicationException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public BaseResponse<String> handleApplicationError(HttpServletRequest req, Exception e) {
        BaseResponse<String> response = new BaseResponse<>();
        response.setMessage(getMessage(e));
        if(e instanceof IjiamiFileNotFoundException || StringUtils.contains(e.getMessage(), "文件不存在")){
            log.warn("文件不存在, 地址:" + req.getRequestURL());
        } else {
            log.error("执行请求失败,地址:" + req.getRequestURL(), e);
        }
        response.setStatus(HttpStatusEnum.FAIL.getValue());
        return response;
    }

    private String getMessage(Exception e) {
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException) e;
            List<ObjectError> errors = validException.getBindingResult().getAllErrors();
            StringBuffer buffer = new StringBuffer();
            for (ObjectError objectError : errors) {
                if (buffer.length() > 0) {
                    buffer.append("<br/>");
                }
                buffer.append(objectError.getDefaultMessage());
                //break;
            }
            return buffer.toString();
        } else {
            return e.getMessage();
        }
    }

}
