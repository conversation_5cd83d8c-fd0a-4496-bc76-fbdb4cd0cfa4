package cn.ijiami.detection.aspect;

import cn.ijiami.detection.enums.lock.LockFailureAction;
import cn.ijiami.detection.exception.DistributeLockException;
import cn.ijiami.detection.service.api.DistributedLockService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DistributeLockAspect.java
 * @Description 分布式锁的切面定义
 * @createTime 2021年12月07日 17:28:00
 */
@Slf4j
@Aspect
@Component
public class DistributeLockAspect {

    @Autowired
    @Qualifier("redisDistributedLock")
    private DistributedLockService redisDistributedLockService;

    private final ExpressionParser parser = new SpelExpressionParser();

    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    /**
     * 切入点
     */
    @Pointcut("@annotation(cn.ijiami.detection.aspect.DistributeLock)")
    private void lockPoint() {

    }

    @Around("lockPoint()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        DistributeLock lockAction = method.getAnnotation(DistributeLock.class);
        int retryTimes = lockAction.lockFailureAction().equals(LockFailureAction.RETRY) ? lockAction.retryTimes() : 0;
        String key = getLockKey(lockAction, pjp, method);
        boolean lockCancel = false;
        try {
            boolean lock = redisDistributedLockService.tryLock(key, lockAction.timeoutMillis(),
                    retryTimes, lockAction.sleepMillis());
            if (!lock) {
                if (lockAction.lockFailureAction().equals(LockFailureAction.CANCEL)) {
                    lockCancel = true;
                    // 获取失败，方法不执行
                    return null;
                } else {
                    return new DistributeLockException();
                }
            }
            return pjp.proceed();
        } finally {
            // 锁取消的不用去释放
            if (!lockCancel) {
                redisDistributedLockService.unlock(key);
            }
        }
    }

    /**
     * 获得分布式缓存的key
     *
     * @param lockAction 注解对象
     * @param pjp        pjp
     * @param method     method
     * @return String
     */
    private String getLockKey(DistributeLock lockAction, ProceedingJoinPoint pjp, Method method) {
        Object[] args = pjp.getArgs();
        return parse(lockAction.keyPrefix(), method, args) + parse(lockAction.keyValue(), method, args);
    }

    /**
     * 解析spring EL表达式
     *
     * @param key    key
     * @param method method
     * @param args   args
     * @return parse result
     */
    private String parse(String key, Method method, Object[] args) {
        String[] params = discoverer.getParameterNames(method);
        if (null == params || params.length == 0 || !key.contains("#")) {
            return key;
        }
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < params.length; i++) {
            context.setVariable(params[i], args[i]);
        }
        return parser.parseExpression(key).getValue(context, String.class);
    }

}
