package cn.ijiami.detection.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 * @date 2019/12/4 10:06
 */
public class DateUtils {

	// 默认时间格式
	public static final String DATETIME_DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";
    /**
     * 计算两个时间相隔多少天多少小时多少分多少秒
     *
     * @param from 第一个时间
     * @param to   第二个时间
     * @return 相隔时间
     */
    public static String getDistanceTime(Date from, Date to) {
    	if(from== null || to==null) {
    		return "-";
    	}
        long diff;
        if (from.compareTo(to) > 0) {
            diff = from.getTime() - to.getTime();
        } else {
            diff = to.getTime() - from.getTime();
        }
		return getDurationTime(diff);
    }

	public static String getDurationTime(long diff) {
		long day = diff / (24 * 60 * 60 * 1000);
		long hour = (diff / (60 * 60 * 1000) - day * 24);
		long min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
		long sec = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);

		if (diff >= 24 * 60 * 60 * 1000) {
			return day + "天" + hour + "小时" + min + "分" + sec + "秒";
		}
		if (diff >= 60 * 60 * 1000) {
			return hour + "小时" + min + "分" + sec + "秒";
		}
		if (diff >= 60 * 1000) {
			return min + "分" + sec + "秒";
		}
		return sec + "秒";
	}

    /**
	 * 日期格式化
	 * 
	 * @param date
	 * @param formatStr
	 * @return
	 */
	public static String getDateFormat(Date date, String formatStr) {
		if (!StringUtils.isBlank(formatStr)) {
			return new SimpleDateFormat(formatStr).format(date);
		}
		return null;
	}
	
	/**
	 * 日期格式化yyyy-MM-dd
	 * 
	 * @param date
	 * @return
	 */
	public static Date formatDate(String date, String format) {
		try {
			return new SimpleDateFormat(format).parse(date);
		} catch (ParseException e) {
		}
		return null;
	}
	
	public static String getDateTimeFormat(Date date) {
		DateFormat dateTimeFormat = new SimpleDateFormat(DATETIME_DEFAULT_FORMAT);
		return dateTimeFormat.format(date);
	}
    
    public static Long getTime() {
        Long time = System.currentTimeMillis();
        return time;
    }

    public static void main(String[] args) {
        System.out.println(getTime());
    }
}
