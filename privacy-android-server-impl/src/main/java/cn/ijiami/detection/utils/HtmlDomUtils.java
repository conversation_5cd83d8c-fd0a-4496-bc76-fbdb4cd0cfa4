package cn.ijiami.detection.utils;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HtmlDomUtils.java
 * @Description 提取dom节点
 * @createTime 2021年10月18日 11:00:00
 */
public class HtmlDomUtils {

    public static final Set<String> MULTIMEDIA_TAG_NAME_LIST = new HashSet<>(Arrays.asList("video", "audio", "track", "source", "embed", "object", "param"));

    public static final Set<String> NON_EFFECTIVE_NAME_LIST = new HashSet<>(Arrays.asList("script", "body", "meta", "style", "link", "img", "noscript"));

    /**
     * 提取主要内容
     * @param element
     * @return
     */
    public static String extractMainContentHtmlText(Node element) {
        List<org.jsoup.nodes.Node> children = new ArrayList<>();
        HtmlDomUtils.extractContentElement(children, element);
        String htmlText = children.stream()
                .map(HtmlDomUtils::nodeToText)
                .filter(nodeText -> !nodeText.contains("TypeError"))
                .collect(Collectors.joining("\n"));
        return replaceHtml(replaceEmptyLines(htmlText));
    }

    public static String replaceHtml(String html) {
        if (StringUtils.isBlank(html)) {
            return StringUtils.EMPTY;
        }
        String regEx = "<.+?>";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(html);
        String s = m.replaceAll(StringUtils.EMPTY);
        return s.replaceAll("<[^>]*>", "");
    }

    public static String replaceEmptyLines(String html) {
        if (StringUtils.isBlank(html)) {
            return StringUtils.EMPTY;
        }
        String regEx = "[\\f\\n\\s\\r\\t\\v]{2,}";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(html);
        return m.replaceAll("\n");
    }

    private static String nodeToText(Node node) {
        if (node instanceof Element) {
            Element element = ((Element)node);
            if (element.childNodeSize() <= 1) {
                if (element.tagName().equals("br")) {
                    return "\n";
                } else {
                    return element.text();
                }
            } else {
                return element.childNodes().stream().map(HtmlDomUtils::nodeToText).collect(Collectors.joining());
            }
        } else {
            return node.toString();
        }
    }

    /**
     * @description 提取html的内容节点，方便后面计算隐私详情行数和每行加换行符
     * <AUTHOR>
     */
    public static void extractContentElement(List<Node> elementList, Node element) {
        if (isChildrenElement(element)) {
            elementList.add(element);
        } else if (element.childNodeSize() > 0 && !isTitleOrMenuGroup(element) && !isHeaderOrFooter(element) && !isDialogOrSideBar(element)) {
            // 继续寻找子节点
            element.childNodes().forEach(e -> {
                extractContentElement(elementList, e);
            });
        }
    }

    /**
     * 标题列表或者菜单列表
     * @param group
     * @return
     */
    private static boolean isTitleOrMenuGroup(Node group) {
        return group.childNodes().stream().allMatch(node -> {
            if (node instanceof Element) {
                Element element = (Element) node;
                return StringUtils.isNotBlank(element.className()) && (
                        element.className().toLowerCase().contains("title"));
            } else {
                return false;
            }
        });
    }

    private static boolean isHeaderOrFooter(Node group) {
        if (group instanceof Element) {
            Element element = (Element) group;
            String className = element.className().toLowerCase();
            return StringUtils.isNotBlank(element.className()) && (
                    className.contains("footer")
                            || className.contains("head"));
        } else {
            return false;
        }
    }

    private static boolean isDialogOrSideBar(Node group) {
        if (group instanceof Element) {
            Element element = (Element) group;
            String className = element.className().toLowerCase();
            return StringUtils.isNotBlank(element.className()) &&
                    (className.contains("dialog")
                            || (className.contains("sidebar") && !className.contains("no-sidebar"))
                            || className.contains("menu"));
        } else {
            return false;
        }
    }

    private static boolean isChildrenElement(Node node) {
        if (node instanceof TextNode) {
            return true;
        } else if (node instanceof Element) {
            Element element = (Element) node;
            // p标签直接添加，因为有可能子标签是span之类的，如果分开添加会导致每个span换行一次。如果子标签全部是span，那么也添加
            if (element.childNodeSize() == 0) {
                return effectiveChildrenElement(element);
            } else {
                return "p".equals(element.tagName())
                        || element.children().stream().allMatch(c -> "span".equals(c.tagName()));
            }
        }
        return false;
    }

    private static boolean effectiveChildrenElement(Element element) {
        return !NON_EFFECTIVE_NAME_LIST.contains(element.className()) && !MULTIMEDIA_TAG_NAME_LIST.contains(element.tagName());
    }

}
