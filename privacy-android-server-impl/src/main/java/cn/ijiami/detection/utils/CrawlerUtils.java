package cn.ijiami.detection.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.io.Files;

public class CrawlerUtils {


	private static Logger LOGGER = LoggerFactory.getLogger(CrawlerUtils.class);

	/**
	 * 
	 * 使用wget下载文件
	 * @param displayName
	 *            appName
	 * @param category
	 *            分类
	 * @param download_url
	 *            下载地址
	 * @return 成功返回文件路径，失败返回null
	 * 
	 */
//	public static String downloadFileByWget(String appPath, String download_url) {
//		System.out.println("downloadFileByWget,url:"+download_url+",path:"+appPath);
//		if (StringUtils.isBlank(appPath)  || StringUtils.isBlank(download_url)) {
//
//			LOGGER.info("downloadFileByWget ERROR, displayName:{}, download_url:{}",
//					new Object[] { appPath, download_url });
//
//			return null;
//
//		}
//		String filePath = appPath;
//		File file = new File(filePath);
//		try {
//			Files.createParentDirs(file);
//		} catch (IOException e) {
//			LOGGER.warn("IOException", e);
//			return null;
//		}
//
//		int retry = 2;
//		int res = -1;
//		int time = 1;
//		while (retry-- > 0) {
//			ProcessBuilder pb = new ProcessBuilder("wget", download_url, "-t", "2", "-T", "10", "-O", filePath);
//			LOGGER.info("wget shell: {}", pb.command());
//			Process ps = null;
//			try {
//				ps = pb.start();
//			} catch (IOException e) {
//				LOGGER.error("IOException", e);
//			}
//			
//			res = doWaitFor(ps, 4200 * time++);
//			if (res != 0) {
//				LOGGER.warn("Wget download failed...");
//			} else {
//				break;
//			}
//		}
//
//		if (res != 0) {
//			return null;
//		}
//		return filePath;
//
//	}
	
	public static String downloadFileByWget(String appPath, String downloadUrl) {
	    // 检查输入参数是否为空
	    if (StringUtils.isBlank(appPath) || StringUtils.isBlank(downloadUrl)) {
	        LOGGER.error("Invalid input parameters: appPath={}, downloadUrl={}", appPath, downloadUrl);
	        return null;
	    }

	    File file = new File(appPath);
	    try {
	        // 确保目标文件的父目录存在
	        Files.createParentDirs(file);
	    } catch (IOException e) {
	        LOGGER.error("Failed to create parent directories for file: {}", appPath, e);
	        return null;
	    }

	    // 定义最大重试次数和初始等待时间
	    int maxRetries = 2;
	    int retryDelay = 4200; // 初始延迟时间（毫秒）

	    for (int attempt = 1; attempt <= maxRetries; attempt++) {
	        Process process = null;
	        try {
	            // 构建 wget 命令，避免命令注入
	            ProcessBuilder pb = new ProcessBuilder(
	                "wget", 
	                "--tries=2", 
	                "--timeout=10", 
	                "-O", 
	                file.getAbsolutePath(), 
	                downloadUrl
	            );
	            pb.redirectErrorStream(true); // 合并标准输出和错误输出

	            LOGGER.info("Executing wget command (attempt {}): {}", attempt, pb.command());

	            // 启动进程
	            process = pb.start();

	            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
	            StringBuilder output = new StringBuilder();
	            String line;
	            while ((line = reader.readLine()) != null) {
	                output.append(line).append("\n");
	            }
	            LOGGER.debug("Wget command output: {}", output.toString());

	            // 等待进程完成并获取退出码
	            int exitCode = process.waitFor();
	            if (exitCode == 0) {
	                LOGGER.info("File downloaded successfully: {}", appPath);
	                return appPath;
	            } else {
	                LOGGER.warn("Wget download failed with exit code: {}", exitCode);
	            }
	        } catch (IOException | InterruptedException e) {
	            LOGGER.error("Error occurred during wget execution (attempt {})", attempt, e);
	        } finally {
	            // 确保进程被销毁
	            if (process != null) {
	                process.destroy();
	            }
	        }

	        // 如果失败，等待一段时间后重试
	        if (attempt < maxRetries) {
	            try {
	                LOGGER.info("Retrying in {} milliseconds...", retryDelay);
	                Thread.sleep(retryDelay);
	                retryDelay *= 2; // 指数退避
	            } catch (InterruptedException e) {
	                LOGGER.warn("Thread interrupted during retry delay", e);
	            }
	        }
	    }

	    LOGGER.error("Failed to download file after {} attempts: {}", maxRetries, appPath);
	    return null;
	}

	/**
	 * 
	 * @param ps
	 *            sub process
	 * @param timeout
	 *            超时时间，SECONDS
	 * @return 正常结束返回0
	 * 
	 */

	private static int doWaitFor(Process ps, int timeout) {
		int res = -1;
		if (ps == null) {
			return res;
		}
		List<String> stdoutList = new ArrayList<>();
		List<String> erroroutList = new ArrayList<>();

		boolean finished = false;
		int time = 0;

		ThreadUtil stdoutUtil = new ThreadUtil(ps.getInputStream(), stdoutList);
		ThreadUtil erroroutUtil = new ThreadUtil(ps.getErrorStream(), erroroutList);

		// 启动线程读取缓冲区数据
		stdoutUtil.start();
		erroroutUtil.start();
		while (!finished) {
			time++;
			if (time >= timeout) {
				LOGGER.info("Process wget timeout {}s, destroyed!", timeout);
				ps.destroy();
				break;
			}
			try {
				res = ps.exitValue();
				finished = true;
			} catch (IllegalThreadStateException e) {
				try {
					TimeUnit.SECONDS.sleep(1);
				} catch (InterruptedException e1) {

				}
			}
		}
		return res;
	}
}