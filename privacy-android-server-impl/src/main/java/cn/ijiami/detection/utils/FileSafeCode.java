package cn.ijiami.detection.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.zip.CRC32;

import org.apache.commons.codec.digest.DigestUtils;
 
public class FileSafeCode {
	/**
	 * 计算大文件 md5获取getMD5(); SHA1获取getSha1() CRC32获取 getCRC32()
	 */
	protected static char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6',
			'7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
	public static MessageDigest messagedigest = null;
 
	/**
	 * 对一个文件获取md5值
	 * 
	 * @return md5串
	 * @throws NoSuchAlgorithmException
	 */
//	public static String getMd5(File file) throws IOException,
//			NoSuchAlgorithmException {
// 
//		messagedigest = MessageDigest.getInstance("MD5");
//		FileInputStream in = new FileInputStream(file);
//		FileChannel ch = in.getChannel();
//		MappedByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0,
//				file.length());
//		messagedigest.update(byteBuffer);
//		return bufferToHex(messagedigest.digest());
//	}
	
	public static String getMd5(File file) throws IOException, NoSuchAlgorithmException {
	    if (file == null || !file.exists() || !file.isFile()) {
	        throw new IllegalArgumentException("Invalid file: " + file);
	    }

	    MessageDigest messageDigest = null;
	    FileInputStream fileInputStream = null;
	    FileChannel fileChannel = null;
	    MappedByteBuffer mappedByteBuffer = null;

	    try {
	        // 获取 MD5 摘要实例
	        messageDigest = MessageDigest.getInstance("MD5");

	        // 打开文件输入流和通道
	        fileInputStream = new FileInputStream(file);
	        fileChannel = fileInputStream.getChannel();

	        // 将文件内容映射到内存中
	        mappedByteBuffer = fileChannel.map(FileChannel.MapMode.READ_ONLY, 0, file.length());

	        // 更新 MD5 摘要
	        messageDigest.update(mappedByteBuffer);

	        // 计算 MD5 哈希值并转换为十六进制字符串
	        return bytesToHex(messageDigest.digest());
	    } finally {
	        // 确保所有资源被正确关闭
	        if (mappedByteBuffer != null) {
	            cleanMappedByteBuffer(mappedByteBuffer); // 清理 MappedByteBuffer
	        }
	        if (fileChannel != null) {
	            fileChannel.close();
	        }
	        if (fileInputStream != null) {
	            fileInputStream.close();
	        }
	    }
	}
	
//	/**
//	 * 将字节数组转换为十六进制字符串
//	 */
//	private static String bytesToHex(byte[] bytes) {
//	    StringBuilder hexString = new StringBuilder();
//	    for (byte b : bytes) {
//	        String hex = Integer.toHexString(0xff & b);
//	        if (hex.length() == 1) {
//	            hexString.append('0'); // 补齐前导零
//	        }
//	        hexString.append(hex);
//	    }
//	    return hexString.toString();
//	}
//
//	/**
//	 * 清理 MappedByteBuffer，避免内存泄漏
//	 */
//	private static void cleanMappedByteBuffer(MappedByteBuffer buffer) {
//	    if (buffer == null) {
//	        return;
//	    }
//	    try {
//	        buffer.force(); // 强制同步缓冲区内容到文件
//	        sun.misc.Cleaner cleaner = ((sun.nio.ch.DirectBuffer) buffer).cleaner();
//	        if (cleaner != null) {
//	            cleaner.clean(); // 手动清理缓冲区
//	        }
//	    } catch (Exception e) {
//	        // 忽略清理过程中可能发生的异常
//	    }
//	}
 
	 /** 
	 * @param target 字符串 求一个字符串的md5值
	 * @return md5 value
	 */
	public static String stringMd5(String target) {
		return DigestUtils.md5Hex(target);
	}
	
	public static String getSha1(File file) throws IOException, NoSuchAlgorithmException {
	    if (file == null || !file.exists() || !file.isFile()) {
	        throw new IllegalArgumentException("Invalid file: " + file);
	    }

	    MessageDigest messageDigest = null;
	    FileInputStream fileInputStream = null;
	    FileChannel fileChannel = null;
	    MappedByteBuffer mappedByteBuffer = null;

	    try {
	        // 获取 SHA-1 摘要实例
	        messageDigest = MessageDigest.getInstance("SHA-1");

	        // 打开文件输入流和通道
	        fileInputStream = new FileInputStream(file);
	        fileChannel = fileInputStream.getChannel();

	        // 将文件内容映射到内存中（分块处理以避免内存溢出）
	        long fileSize = file.length();
	        long position = 0;
	        while (position < fileSize) {
	            long chunkSize = Math.min(Integer.MAX_VALUE, fileSize - position);
	            mappedByteBuffer = fileChannel.map(FileChannel.MapMode.READ_ONLY, position, chunkSize);
	            messageDigest.update(mappedByteBuffer);
	            position += chunkSize;
	            cleanMappedByteBuffer(mappedByteBuffer); // 清理当前块的 MappedByteBuffer
	        }

	        // 计算 SHA-1 哈希值并转换为十六进制字符串
	        return bytesToHex(messageDigest.digest());
	    } finally {
	        // 确保所有资源被正确关闭
	        if (fileChannel != null) {
	            fileChannel.close();
	        }
	        if (fileInputStream != null) {
	            fileInputStream.close();
	        }
	    }
	}

	/**
	 * 将字节数组转换为十六进制字符串
	 */
	private static String bytesToHex(byte[] bytes) {
	    StringBuilder hexString = new StringBuilder();
	    for (byte b : bytes) {
	        String hex = Integer.toHexString(0xff & b);
	        if (hex.length() == 1) {
	            hexString.append('0'); // 补齐前导零
	        }
	        hexString.append(hex);
	    }
	    return hexString.toString();
	}

	/**
	 * 清理 MappedByteBuffer，避免内存泄漏
	 */
	private static void cleanMappedByteBuffer(MappedByteBuffer buffer) {
	    if (buffer == null) {
	        return;
	    }
	    try {
	        buffer.force(); // 强制同步缓冲区内容到文件
	        sun.misc.Cleaner cleaner = ((sun.nio.ch.DirectBuffer) buffer).cleaner();
	        if (cleaner != null) {
	            cleaner.clean(); 
	        }
	    } catch (Exception e) {
	    	e.getMessage();
	    }
	}
	
	/***
	 * 计算SHA1码
	 * 
	 * @return String 适用于上G大的文件
	 * @throws NoSuchAlgorithmException
	 * */
//	public static String getSha1(File file) throws OutOfMemoryError,
//			IOException, NoSuchAlgorithmException {
//		messagedigest = MessageDigest.getInstance("SHA-1");
//		FileInputStream in = new FileInputStream(file);
//		FileChannel ch = in.getChannel();
//		MappedByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0,
//				file.length());
//		messagedigest.update(byteBuffer);
//		return bufferToHex(messagedigest.digest());
//	}
 
	/**
	 * 获取文件CRC32码
	 * 
	 * @return String
	 * */
	public static String getCrc32(File file) {
		CRC32 crc32 = new CRC32();
		// MessageDigest.get
		FileInputStream fileInputStream = null;
		try {
			fileInputStream = new FileInputStream(file);
			byte[] buffer = new byte[8192];
			int length;
			while ((length = fileInputStream.read(buffer)) != -1) {
				crc32.update(buffer, 0, length);
			}
			return crc32.getValue() + "";
		} catch (FileNotFoundException e) {
			e.getMessage();
			return null;
		} catch (IOException e) {
			e.getMessage();
			return null;
		} finally {
			try {
				if (fileInputStream != null){
					fileInputStream.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
	}
 
	public static String getMd5String(String s) {
		return getMd5String(s.getBytes());
	}
 
	public static String getMd5String(byte[] bytes) {
		messagedigest.update(bytes);
		return bufferToHex(messagedigest.digest());
	}
 
	/**
	 * @Description 计算二进制数据
	 * @return String
	 * */
	private static String bufferToHex(byte bytes[]) {
		return bufferToHex(bytes, 0, bytes.length);
	}
 
	private static String bufferToHex(byte bytes[], int m, int n) {
		StringBuffer stringbuffer = new StringBuffer(2 * n);
		int k = m + n;
		for (int l = m; l < k; l++) {
			appendHexPair(bytes[l], stringbuffer);
		}
		return stringbuffer.toString();
	}
 
	private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
		char c0 = hexDigits[(bt & 0xf0) >> 4];
		char c1 = hexDigits[bt & 0xf];
		stringbuffer.append(c0);
		stringbuffer.append(c1);
	}
 
	public static boolean checkPassword(String password, String md5PwdStr) {
		String s = getMd5String(password);
		return s.equals(md5PwdStr);
	}
	
}