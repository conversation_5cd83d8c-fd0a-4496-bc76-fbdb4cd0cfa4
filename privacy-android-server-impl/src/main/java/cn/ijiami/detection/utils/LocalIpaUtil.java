package cn.ijiami.detection.utils;

import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import com.dd.plist.*;
import com.ijiami.ios.ipatools.FileUtil;
import com.ijiami.ios.ipatools.IpaInfo;
import com.ijiami.ios.ipatools.IpaZipUtil;
import com.ijiami.ios.shelltools.shellUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

@Slf4j
public class LocalIpaUtil {

    public static IpaInfo readIpaInfo(String filePath, String imgDir, String toolPath) {
        File srcFile = new File(filePath);
//        File zipFile = copyIpaZipFile(srcFile);
        String destDir = "";
        IpaInfo ipaInfo = null;

        try {
            String fileName = srcFile.getName().replace(".", "_");
            destDir = srcFile.getParent() + File.separator + fileName;
            Map<String, File> ipaFileMap = IpaZipUtil.unZip(srcFile, destDir);
            if (ipaFileMap == null || ipaFileMap.isEmpty()) {
                return null;
            }

            File ipaInfoFile = ipaFileMap.get("IPA_INFO_FILE");
            ipaInfo = parseIpaInfo(ipaInfoFile);
            if (ipaInfo != null) {
                if (StringUtils.isNotEmpty(imgDir)) {
//                        PNGUtil.cgbi_tool_path = toolPath;
                    ipaInfo.setIcon(IpaZipUtil.unZipIcon(filePath, ipaInfo.getIcon(), imgDir));
                }

                return ipaInfo;
            }

        } catch (Exception e) {
            throw new IjiamiRuntimeException("ios解析异常"+e.getMessage());
        } finally {
            FileUtil.deleteFile(destDir, true);
//            FileUtil.deleteFile(zipFile);
        }

        return ipaInfo;
    }

    private static IpaInfo parseIpaInfo(File file) {
        if (file != null && file.exists()) {
            NSDictionary rootDict = null;

            try {
                rootDict = (NSDictionary) PropertyListParser.parse(file);
            } catch (Exception var5) {
                log.error("解析ipa信息文件失败!,文件路径:" + file.getPath(), var5);
                return null;
            }

            IpaInfo ipaInfo = new IpaInfo();
            NSString parameters = (NSString)rootDict.objectForKey("CFBundleIdentifier");
            ipaInfo.setAppPackageName(parameters.toString());
            parameters = (NSString)rootDict.objectForKey("CFBundleName");
            if (parameters != null) {
                ipaInfo.setAppName(parameters.toString());
            } else {
                ipaInfo.setAppName(ipaInfo.getAppPackageName());
            }

            parameters = (NSString)rootDict.objectForKey("CFBundleShortVersionString");
            if (parameters != null) {
                ipaInfo.setAppVersion(parameters.toString());
            }

            parameters = (NSString)rootDict.objectForKey("CFBundleDisplayName");
            String appDisplayName = parameters != null ? parameters.toString() : ipaInfo.getAppName();
            ipaInfo.setAppDisplayName(appDisplayName);
            ipaInfo.setIcon(getIconName(rootDict));
            ipaInfo.setDeviceType("iOS");
            return ipaInfo;
        } else {
            log.error("ios包解析IPA_INFO_FILE文件不存在！");
            return null;
        }
    }

    private static String getIconName(NSDictionary rootDict) {
        String icon = extractCfBundleIcons(rootDict);
        if (icon != null) {
            return icon;
        } else {
            icon = extractCfbundleIconsIpad(rootDict);
            if (icon != null) {
                return icon;
            } else {
                icon = extractCfBundleIconFiles(rootDict);
                return icon != null ? icon : "icon.png";
            }
        }
    }

    private static String extractCfBundleIconFiles(NSDictionary rootDict) {
        String icon = null;
        NSArray arrayPara = (NSArray)rootDict.objectForKey("CFBundleIconFiles");
        if (arrayPara != null && !StringUtils.isEmpty(arrayPara.objectAtIndex(0).toString())) {
            icon = arrayPara.objectAtIndex(arrayPara.count() - 1).toString();
        }

        if (icon == null) {
            NSObject nsObject = rootDict.objectForKey(IpaInfo.APP_ICON_FILE_KEY);
            if (nsObject != null) {
                icon = nsObject.toString();
            }
        }

        return icon;
    }

    private static String extractCfbundleIconsIpad(NSDictionary rootDict) {
        String icon = null;
        Object obj = rootDict.objectForKey("CFBundleIcons~ipad");
        if (obj != null) {
            NSDictionary logoDic = (NSDictionary)obj;
            NSDictionary logArray = (NSDictionary)logoDic.objectForKey("CFBundlePrimaryIcon");
            if (logArray != null) {
                NSArray arrayPara = (NSArray)logArray.objectForKey("CFBundleIconFiles");
                if (arrayPara != null && !StringUtils.isEmpty(arrayPara.objectAtIndex(0).toString())) {
                    icon = arrayPara.objectAtIndex(arrayPara.count() - 1).toString();
                }
            }
        }

        return icon;
    }

    private static String extractCfBundleIcons(NSDictionary rootDict) {
        String icon = null;
        Object obj = rootDict.objectForKey("CFBundleIcons");
        if (obj != null) {
            NSDictionary logoDic = (NSDictionary)obj;
            NSDictionary logArray = (NSDictionary)logoDic.objectForKey("CFBundlePrimaryIcon");
            if (logArray != null) {
                NSArray arrayPara = (NSArray)logArray.objectForKey("CFBundleIconFiles");
                if (arrayPara != null && !StringUtils.isEmpty(arrayPara.objectAtIndex(0).toString())) {
                    icon = arrayPara.objectAtIndex(arrayPara.count() - 1).toString();
                }
            }
        }

        return icon;
    }


    @SneakyThrows
    public static void main(String[] args) {
//        unZip(new File("f:/拼多多.ipa"), "E:/zywa/ijiami/files/default/iios_ipa");
        shellUtils.isHasShell("E:/zywa/ijiami/files/default/拼多多.ipa", "/zywa/ijiami/ios/shell/shellTools.out");
    }
}
