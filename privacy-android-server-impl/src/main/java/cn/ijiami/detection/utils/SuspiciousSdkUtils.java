package cn.ijiami.detection.utils;

import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.SuspiciousSdkMergeVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.helper.PackageNameExtractHelper;
import cn.ijiami.detection.service.api.SDKWhitelistRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO.buildSuspiciousSdkBehavior;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;
import static cn.ijiami.detection.utils.StreamUtils.distinctByKey;

public class SuspiciousSdkUtils {

    /**
     * 从行为数据中提取疑似sdk包名
     *
     * @param taskDetailVo
     * @param nougat
     * @param sdkNameSet 要排除掉的sdk名
     * @param firstPartyNameSet 要排除掉的第一方包（自研框架或内部sdk）
     * @param twoLevelSdkNameSet
     * @return
     */
    public static List<SuspiciousSdkBO> extractPackageName(TaskDetailVO taskDetailVo, ActionExecutor actionExecutor,
                                                           Set<String> sdkNameSet, Set<String> firstPartyNameSet, Set<String> twoLevelSdkNameSet,
                                                           SDKWhitelistRuleService sdkWhitelistRuleService) {
        if (StringUtils.isBlank(actionExecutor.getStackInfo())) {
            return new ArrayList<>();
        }
        List<String> packageNames = PackageNameExtractHelper.extractFullName(actionExecutor.getStackInfo());
        return packageNames.stream()
                .parallel()
                // 三层级的包名排除，例如提取出来的是com.baidu.location，能被sdk库中的com.baidu.location排除掉
                .filter(name -> sdkNameSet.stream().noneMatch(name::startsWith))
                // 排除第一方包（自研框架或内部sdk）
                .filter(name -> firstPartyNameSet.stream().noneMatch(name::startsWith))
                // 在白名单内的排除
                .filter(packageName -> !sdkWhitelistRuleService.inWhitelist(packageName))
                // 与当前APK包名类似的排除，例如com.taobao.app.util，可以被当前APK包名com.taobao.app.mall排除
                .filter(name -> StringUtils.isBlank(taskDetailVo.getApk_package()) || !name.startsWith(taskDetailVo.getApk_package()))
                // 截取为三层
                .map(PackageNameExtractHelper::extractThreeLevelPackageName)
                // 两层级的包名排除，例如提取出来的是com.baidu，能被sdk库中的com.baidu.location排除掉
                .filter(name -> !twoLevelSdkNameSet.contains(name))
                // 判断是否被混淆压缩过的包名，判断效率比较低，放到后面来
                .filter(SuspiciousSdkUtils::notProguardShrinkingName)
                // 同一个行为下，只保留一个包名记录
                .map(packageName -> new SuspiciousSdkBO(buildSuspiciousSdkBehavior(
                        packageName,
                        actionExecutor.getExecutorType(),
                        actionExecutor.getExecutor(),
                        taskDetailVo.getTaskId()),
                        actionExecutor)
                ).collect(Collectors.toList());
    }

    /**
     * 把sdk的包名都添加进包名列表
     * @param sdkLibraries
     * @return
     */
    public static void addPackageNames(List<TSdkLibrary> sdkLibraries, Collection<String> packageNames) {
        if (sdkLibraries == null) return;
        sdkLibraries.parallelStream()
                // 检查TSdkLibrary对象是否为空
                .filter(Objects::nonNull)
                .map(TSdkLibrary::getPackageList)
                // 检查PackageList是否为空
                .filter(Objects::nonNull)
                .forEach(s -> {
                    // 获取PackageName并添加到sdkNameSet中
                    s.stream()
                            .map(TSdkLibraryPackage::getPackageName)
                            // 过滤掉PackageName为null的情况
                            .filter(Objects::nonNull)
                            .forEach(packageNames::add);
                });
    }


    /**
     * 把sdk的包名截取前2个层级，然后添加进列表
     * @param sdkLibraries
     * @return
     */
    public static void addTwoLevelPackageNames(List<TSdkLibrary> sdkLibraries, Set<String> twoLevelSdkNameSet) {
        if (sdkLibraries != null) {
            sdkLibraries.stream()
                    .map(TSdkLibrary::getPackageList)
                    .forEach(s -> {
                        twoLevelSdkNameSet.addAll(getTwoLevelPackageName(s));
                    });
        }
    }

    /**
     * 截取包名的前2个层级
     * @param sdkLibraryList
     * @return
     */
    public static List<String> getTwoLevelPackageName(List<TSdkLibraryPackage> sdkLibraryList) {
        if (CollectionUtils.isEmpty(sdkLibraryList)) return Collections.emptyList();
        return sdkLibraryList
                .parallelStream()
                .map(s -> PackageNameExtractHelper.extractTwoLevelPackageName(s.getPackageName()))
                .collect(Collectors.toList());
    }

    public static boolean packageNamePrefix(Set<String> allSuspiciousSdkNames, String packageNamePrefix) {
        return allSuspiciousSdkNames.stream().noneMatch(name -> name.length() > packageNamePrefix.length() && name.startsWith(packageNamePrefix));
    }

    /**
     * 混淆压缩过的包名不提取
     *
     * @param name
     * @return
     */
    private static boolean notProguardShrinkingName(String name) {
        if (name.isEmpty()) return false;
        String[] splits = name.split("\\.");
        // 第一层级中的字母，如果有大写是混淆的包名。例如VC.run、Jv.aaa，判断为混淆包名
        if (splits[0].length() <= 2) {
            for (char c:splits[0].toCharArray()) {
                if (Character.isUpperCase(c)) {
                    return false;
                }
            }
        }
        String last = splits[splits.length-1];
        // 最后一层级中的字母少于2，很大概率是混淆的包名。例如com.zzz.bi、例如com.zzz.aa
        if (last.length() <= 2) {
            return false;
        }
        // 最后一层级中的字母，如果有超过2个大写，是混淆的包名。例如com.zzz.CoD、com.zzz.rFulU
        int count=0;
        for (char c:last.toCharArray()) {
            if (Character.isUpperCase(c)) {
                count++;
            }
        }
        if (count >= 2) {
            return false;
        }
        // 第一层级过短
        if (splits[0].length() <= 3) {
            // 第二层级带数字。例如cxe.ia2、abc.io8
            if (haveNumber(splits[1])) {
                return false;
            }
            // 第二层级还是过短。例如ag.d.hasNavBar、hd.c.initView
            if (splits[1].length() <= 2) {
                return false;
            }
        }
        int totalLength = Arrays.stream(splits).mapToInt(String::length).sum();
        // 最少包名长度为3，每多一个层级增加1
        int min = (splits.length - 1) + 4;
        return totalLength > min;
    }

    private static boolean haveNumber(String str) {
        for (char c:str.toCharArray()) {
            if (c <= 57 && c >= 48) {
                return true;
            }
        }
        return false;
    }

    public static List<SuspiciousSdkMergeVO> mapCompanyProductSdkName(List<SuspiciousSdkBehaviorVO> suspiciousSdkList,
                                                                      Map<String, List<TOdpCompanyProduct>> productMap,
                                                                      Map<String, List<TOdpCompanyProduct>> companyMap) {
        return SuspiciousSdkUtils.mergePackageName(suspiciousSdkList)
                .stream()
                .peek( sdk -> {
                    sdk.setPackageName(findCompanyProductSdkName(sdk.getPackageName(), productMap, companyMap));
                })
                .filter(distinctByKey(SuspiciousSdkMergeVO::getPackageName))
                .collect(Collectors.toList());
    }

    /**
     * 过滤混淆的包名和合并相似的包名
     * @param sdkList
     * @return
     */
    public static List<SuspiciousSdkMergeVO> mergePackageName(List<SuspiciousSdkBehaviorVO> sdkList) {
        return mergeSamePackageName(filterShrinkingPackageName(sdkList));
    }

    public static List<SuspiciousSdkBehaviorVO> filterShrinkingPackageName(List<SuspiciousSdkBehaviorVO> sdkList) {
        return sdkList.stream()
                .filter(sdk -> !PackageNameExtractHelper.isShrinkingPackageName(sdk.getPackageName()))
                .peek(sdk -> sdk.setPackageName(PackageNameExtractHelper.extractThreeLevelPackageName(sdk.getPackageName())))
                // 处理完再次去重
                .filter(distinctByKey(SuspiciousSdkBehaviorVO::getPackageName))
                .collect(Collectors.toList());
    }

    public static List<SuspiciousSdkMergeVO> mergeSamePackageName(List<SuspiciousSdkBehaviorVO> sdkList) {
        List<SuspiciousSdkMergeVO> mergeList = new ArrayList<>();
        sdkList.forEach(b -> {
            String name = PackageNameExtractHelper.extractMergePackageName(b.getPackageName());
            if (StringUtils.isNotBlank(name)) {
                // 如果不包含 . 说明是abc这种单层结构的无意义包名，直接过滤掉，不参与合并。只有abc.abc, abc.abc.abc及以上层级的才有意义
                if (name.contains(".")) {
                    Optional<SuspiciousSdkMergeVO> mergeVO = mergeList.stream().filter(m -> m.getPackageName().equals(name)).findFirst();
                    if (mergeVO.isPresent()) {
                        mergeVO.get().getSuspiciousSdkIds().add(b.getSuspiciousSdkId());
                    } else {
                        mergeList.add(SuspiciousSdkMergeVO.makeSuspiciousSdkMergeVO(name, b.getSuspiciousSdkId()));
                    }
                }
            } else {
                mergeList.add(SuspiciousSdkMergeVO.makeSuspiciousSdkMergeVO(b.getPackageName(), b.getSuspiciousSdkId()));
            }
        });
        return mergeList;
    }

    public static Optional<TOdpCompanyProduct> findCompanyProduct(String packageName,
                                                                  Map<String, List<TOdpCompanyProduct>> productMap,
                                                                  Map<String, List<TOdpCompanyProduct>> companyMap) {
        List<String> keywordList = getMatchKeywordList(packageName);
        return keywordList
                .stream()
                .map(keyword -> findSdkProduct(keyword, productMap, companyMap))
                .filter(Optional::isPresent)
                .map(opt -> {
                    TOdpCompanyProduct product = opt.get();
                    // 产品名字后面加SDK
                    if (!product.getProductName().contains("SDK")) {
                        product.setProductName(product.getProductName() + "SDK");
                    }
                    return product;
                })
                .findFirst();
    }

    public static String findCompanyProductSdkName(String packageName,
                                                   Map<String, List<TOdpCompanyProduct>> productMap,
                                                   Map<String, List<TOdpCompanyProduct>> companyMap) {
        if (StringUtils.isBlank(packageName)) {
            return packageName;
        }
        if (packageName.equals(DETAILS_EMPTY)) {
            return "";
        }
        Optional<TOdpCompanyProduct> companyProduct = findCompanyProduct(packageName, productMap, companyMap);
        return companyProduct.map(TOdpCompanyProduct::getProductName).orElse(packageName);
    }

    private static Optional<TOdpCompanyProduct> findSdkProduct(String target, List<TOdpCompanyProduct> productList) {
        List<TOdpCompanyProduct> targetProductList = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .filter(company -> company.getProductEnglishName().equals(target))
                .collect(Collectors.toList());
        return targetProductList.stream().findFirst();
    }

    private static Optional<TOdpCompanyProduct> findSdkProduct(String target,
                                                               Map<String, List<TOdpCompanyProduct>> productMap,
                                                               Map<String, List<TOdpCompanyProduct>> companyMap) {
        List<TOdpCompanyProduct> targetProductList = productMap.get(target);
        // 把公司名也作为一个产品加进去，都匹配不上产品的时候，可能匹配的上公司名
        if (CollectionUtils.isEmpty(targetProductList)) {
            targetProductList = companyMap.get(target);
        }
        if (CollectionUtils.isEmpty(targetProductList)) {
            return Optional.empty();
        } else {
            return Optional.of(targetProductList.get(0));
        }
    }

    private static List<String> getMatchKeywordList(String packageName) {
        List<String> keywordList = buildKeywordList(packageName);
        StringJoiner joiner = new StringJoiner(".");
        for (String keyword:keywordList) {
            joiner.add(keyword);
        }
        keywordList.add(joiner.toString());
        keywordList.add(packageName);
        return keywordList;
    }

    private static List<String> buildKeywordList(String packageName) {
        Set<String> domainNameSuffix = buildDomainNameSuffix();
        String[] packageNamePartArray = packageName.split("\\.");
        List<String> keywordList = new ArrayList<>(packageNamePartArray.length);
        for (String part : packageNamePartArray) {
            if (domainNameSuffix.contains(part)) {
                continue;
            }
            keywordList.add(part);
        }
        return keywordList;
    }

    private static Set<String> buildDomainNameSuffix() {
        Set<String> domainNameSuffix = new HashSet<>();
        domainNameSuffix.add("com");
        domainNameSuffix.add("net");
        domainNameSuffix.add("cn");
        domainNameSuffix.add("org");
        domainNameSuffix.add("gov");
        domainNameSuffix.add("mil");
        domainNameSuffix.add("edu");
        domainNameSuffix.add("www");
        return domainNameSuffix;
    }

    private String reversalWebsite(String website) {
        String[] websitePartArray = website.split("\\.");
        reverseStringArray(websitePartArray);
        StringJoiner joiner = new StringJoiner(".");
        for (int i=0; i<websitePartArray.length && i<2; i++) {
            joiner.add(websitePartArray[i]);
        }
        return joiner.toString();
    }

    public static List<TOdpCompanyProduct> buildCompanyMainProduct(List<TOdpCompany> companyList) {
        return companyList.stream().map(company -> {
            TOdpCompanyProduct product = new TOdpCompanyProduct();
            product.setCompanyId(company.getId());
            product.setProductName(company.getCompanyAbbrName());
            List<String> keywordList = buildKeywordList(company.getWebsiteUrl());
            product.setProductEnglishName(StringUtils.join(keywordList, "."));
            return product;
        }).collect(Collectors.toList());
    }

    private static void reverseStringArray(String[] array) {
        String temp;
        for (int i = 0; i < array.length / 2; i++) {
            temp = array[i];
            array[i] = array[array.length - i - 1];
            array[array.length - i - 1] = temp;
        }
    }

    public static void setSuspiciousSdkExecutorAndPackageName(ActionExecutor nougat,
                                                              List<SuspiciousSdkBO> suspiciousSdkBOS,
                                                              Map<String, List<TOdpCompanyProduct>> productMap,
                                                              Map<String, List<TOdpCompanyProduct>> companyMap) {
        if (suspiciousSdkBOS.isEmpty()) {
            return;
        }
        List<SuspiciousSdkBehaviorVO> suspiciousSdks = suspiciousSdkBOS.stream()
                .map(SuspiciousSdkBO::getSuspiciousSdk)
                .collect(Collectors.toList());
        // 如果有检测到疑似SDK，类型改为SDK
        if (!suspiciousSdks.isEmpty()) {
            List<String> packageList = SuspiciousSdkUtils.mergePackageName(suspiciousSdks)
                    .stream()
                    .map(SuspiciousSdkMergeVO::getPackageName)
                    .collect(Collectors.toList());
            List<String> executorList = packageList.
                    stream()
                    .map(packageName -> SuspiciousSdkUtils.findCompanyProductSdkName(packageName, productMap, companyMap))
                    .distinct()
                    .collect(Collectors.toList());
            boolean isApp = ExecutorTypeEnum.APP.getValue().equals(nougat.getExecutorType());
            nougat.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            // 如果原来是SDK类型，包名和主题都是在原来的基础上新增
            nougat.setPackageName(buildNewExecutorPackageName(nougat.getPackageName(), isApp ? "" : nougat.getPackageName(), packageList));
            nougat.setExecutor(buildNewExecutor(nougat.getExecutor(), isApp ? "" : nougat.getExecutor(), executorList));
        }
    }

    public static String buildNewExecutorPackageName(String oldPackageName, String head, List<String> executorList) {
        if (CollectionUtils.isNotEmpty(executorList)) {
            StringJoiner joiner = new StringJoiner(",");
            if (StringUtils.isNotBlank(head)) {
                joiner.add(head);
            }
            executorList.forEach(joiner::add);
            return joiner.toString();
        } else {
            return oldPackageName;
        }
    }

    public static String buildNewExecutor(String oldExecutor, String head, List<String> executorList) {
        if (CollectionUtils.isNotEmpty(executorList)) {
            StringJoiner joiner = new StringJoiner(",");
            if (StringUtils.isNotBlank(head)) {
                joiner.add(head);
            }
            executorList.forEach(joiner::add);
            return joiner.toString();
        } else {
            return oldExecutor;
        }
    }

}
