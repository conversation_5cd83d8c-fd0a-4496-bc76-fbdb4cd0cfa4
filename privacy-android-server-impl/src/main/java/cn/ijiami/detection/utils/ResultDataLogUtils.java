package cn.ijiami.detection.utils;

import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResultDataLogUtils.java
 * @Description 检测数据中的截图数据解析
 * @createTime 2022年02月25日 16:44:00
 */
public class ResultDataLogUtils {

    public static String getOcrText(ResultDataLogBO resultDataLogBO) {
        if (StringUtils.isBlank(resultDataLogBO.getDetails())) {
            return StringUtils.EMPTY;
        }
        ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
        });
        if (detailsBO == null || StringUtils.isBlank(detailsBO.getOcr())) {
            return StringUtils.EMPTY;
        }
        return detailsBO.getOcr();
    }

    /**
     * 是否权限弹窗
     * @param resultLog
     * @return
     */
    public static boolean isUiAuthor(ResultDataLogBO resultLog) {
        if (resultLog.getUiDumpResult() == null) {
            return false;
        }
        if (resultLog.getType() == ResultDataLogBoTypeEnum.PERMISSION_SETTING.type) {
            return false;
        }
        return resultLog.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue()
                || resultLog.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP2.getValue()
                || resultLog.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_AGREE.getValue()
                || resultLog.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_DENIED.getValue();
    }

}
