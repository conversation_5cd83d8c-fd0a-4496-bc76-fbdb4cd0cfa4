package cn.ijiami.detection.utils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

public class MD5Util {

    /**
     * 获取文件md5方法
     *
     * @param filePath
     * @return
     */
    public static String getFileMD5(String filePath) {
        return md5Hex(filePath);
    }

    /**
     * 在原有基础上引入apache中计算文件MD5方法
     *
     * @param filePath
     * @return
     */
//    public static String md5Hex(String filePath) {
//        if (StringUtils.isEmpty(filePath)) {
//            return null;
//        }
//
//		// 调用自带的获取MD5值
//		try(InputStream in = new FileInputStream(filePath)) {
//            return DigestUtils.md5DigestAsHex(in);
//		} catch (IOException e) {
//			e.getMessage();
//		}
//
//		// 获取失败按照自定义进行
//		File file = new File(filePath);
//        MessageDigest digest = null;
//        FileInputStream in = null;
//        byte buffer[] = new byte[1024];
//        int len;
//        try {
//            digest = MessageDigest.getInstance("MD5");
//            in = new FileInputStream(file);
//            while ((len = in.read(buffer, 0, 1024)) != -1) {
//                digest.update(buffer, 0, len);
//            }
//            in.close();
//
//        } catch (NoSuchAlgorithmException e) {
//            e.getMessage();
//        } catch (FileNotFoundException e) {
//            e.getMessage();
//        } catch (IOException e) {
//            e.getMessage();
//        } BigInteger bigInt = new BigInteger(1, digest.digest());
//        return bigInt.toString(16);
//
//    }

    /**
     * 获取文件md5方法
     *
     * @param in
     * @return
     */
    public static String getFileMD5(InputStream in) {
        if (in == null) {
            throw new IllegalArgumentException("Input stream cannot be null");
        }

        // 尝试使用 DigestUtils 计算 MD5
        try {
            return DigestUtils.md5DigestAsHex(in);
        } catch (IOException e) {
            // 如果 DigestUtils 失败，记录日志并继续自定义实现
           	e.getMessage();
        }

        // 自定义实现计算 MD5
        MessageDigest digest = null;
        byte[] buffer = new byte[1024];
        int len;

        try {
            digest = MessageDigest.getInstance("MD5");

            // 重新包装输入流以支持多次读取
            in = new BufferedInputStream(in);

            while ((len = in.read(buffer)) != -1) {
                digest.update(buffer, 0, len);
            }
        } catch (NoSuchAlgorithmException | IOException e) {
            e.getMessage();
            return null; // 或者抛出自定义异常
        } finally {
            // 确保输入流被正确关闭
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.getMessage();
            }
        }

        // 将字节数组转换为 32 位十六进制字符串
        byte[] md5Bytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        for (byte b : md5Bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0'); // 补齐前导零
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }

    /**
     * 获取字符串加密MD5
     * @param password
     * @return
     */
    public static String encode(String password, String salt) {
        if (password == null || salt == null) {
            throw new IllegalArgumentException("Password and salt cannot be null");
        }

        // 拼接密码和盐值
        String combined = password + salt;

        // 计算 MD5 哈希值
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to initialize MD5 digest", e);
        }

        // 使用 UTF-8 编码将字符串转换为字节数组
        byte[] byteArray = combined.getBytes(StandardCharsets.UTF_8);

        // 计算 MD5 摘要
        byte[] md5Bytes = md5.digest(byteArray);

        // 将字节数组转换为 32 位十六进制字符串
        StringBuilder hexValue = new StringBuilder();
        for (byte b : md5Bytes) {
            int val = b & 0xff; // 转换为无符号整数
            if (val < 16) {
                hexValue.append('0'); // 补齐前导零
            }
            hexValue.append(Integer.toHexString(val));
        }

        return hexValue.toString();
    }
    
    public static String md5Hex(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            throw new IllegalArgumentException("File path cannot be null or empty");
        }

        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            throw new IllegalArgumentException("Invalid file: " + filePath);
        }

        // 尝试使用 DigestUtils 计算 MD5
        try (FileInputStream in = new FileInputStream(file)) {
            return DigestUtils.md5DigestAsHex(in);
        } catch (IOException e) {
            e.getMessage();
        }

        // 自定义实现计算 MD5
        MessageDigest digest = null;
        try (FileInputStream in = new FileInputStream(file)) {
            digest = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                digest.update(buffer, 0, len);
            }
        } catch (NoSuchAlgorithmException | IOException e) {
        	e.getMessage();
            return null; // 或者抛出自定义异常
        }

        // 将字节数组转换为 32 位十六进制字符串
        byte[] md5Bytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        for (byte b : md5Bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0'); // 补齐前导零
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }
    
   
}
