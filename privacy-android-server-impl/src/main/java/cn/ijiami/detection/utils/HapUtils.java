package cn.ijiami.detection.utils;

import static com.ijiami.ios.ipatools.FileUtil.LOG;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.ijiami.detection.utils.hap.HapInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HapUtils.java
 * @Description 鸿蒙包解析
 * @createTime 2024年07月05日 14:49:00
 */
@Slf4j
public class HapUtils {

    public static HapInfo readHapInfo(String hapPath, String iconFolder) {
        File srcFile = new File(hapPath);
        String fileName = srcFile.getName().replace(".", "_");
        String decompilePath = srcFile.getParent() + File.separator + fileName;
        try {
            ZipUtil.unzip(srcFile.getAbsolutePath(), decompilePath);
            Optional<String> module = CommonUtil.listAllFilesInDir(decompilePath, new String[]{"json"}, true)
                    .stream()
                    .filter(path -> FilenameUtils.getBaseName(path).equals("module"))
                    .findFirst();
            if (module.isPresent()) {
                String moduleJson = FileUtils.readFileToString(new File(module.get()), StandardCharsets.UTF_8);
                HapInfo hapInfo = CommonUtil.jsonToBean(moduleJson, HapInfo.class);
                Optional<String> resources = CommonUtil.listAllFilesInDir(decompilePath, new String[]{"index"}, true)
                        .stream()
                        .filter(path -> FilenameUtils.getBaseName(path).equals("resources"))
                        .findFirst();
                if (resources.isPresent()) {
                    String appName = findResource(resources.get(), hapInfo.getApp().getLabel());
                    if (StringUtils.isNotBlank(appName)) {
                        hapInfo.getApp().setLabel(appName);
                    }
                    String appIcon = findResource(resources.get(), hapInfo.getApp().getIcon());
                    if (StringUtils.isNotBlank(appIcon)) {
                        File resourceFile = new File(decompilePath + File.separator + removeFirstPath(appIcon));
                        String iconName = System.currentTimeMillis() + appName + "." + CommonUtil.getFileExtName(appIcon);
                        File iconFile = new File(iconFolder, iconName);
                        if (resourceFile.exists()) {
                            try {
                                FileUtils.copyFile(resourceFile, iconFile);
                            } catch (IOException e) {
                                LOG.info("icon copy fail " + e.getMessage());
                            }
                            hapInfo.getApp().setIcon(iconFile.getAbsolutePath());
                        }
                    }
                }
                return hapInfo;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            FileUtil.del(decompilePath);
        }
        return null;
    }

    private static String removeFirstPath(String path) {
        int index = path.indexOf("/");
        if (index > 0) {
            return path.substring(index);
        } else {
            return path;
        }
    }

    private static String findResource(String resources, String key) {
        return searchResourceValueInFile(resources, stringToByteArrayWithNullTerminator(key.replaceFirst("\\$\\S+:", "")));
    }

    public static byte[] stringToByteArrayWithNullTerminator(String input) {
        byte[] stringBytes = input.getBytes(); // 将字符串转换为字节数组
        byte[] result = new byte[stringBytes.length + 1]; // 创建一个新的字节数组，比原来的多一个字节

        System.arraycopy(stringBytes, 0, result, 0, stringBytes.length); // 复制原始字节数组到新的字节数组
        result[result.length - 1] = 0; // 在新数组的最后一个位置添加空字节

        return result;
    }

    public static String searchResourceValueInFile(String filePath, byte[] searchArray) {
        File file = new File(filePath);
        byte[] fileData = null;

        try (FileInputStream fis = new FileInputStream(file)) {
            fileData = new byte[(int) file.length()];
            fis.read(fileData);
        } catch (IOException e) {
            e.getMessage();
            return "";
        }
        int index = indexOf(fileData, searchArray);
        if (index - 4 > 0) {
            byte[] values = findAndExtract(fileData, index - 4);
            if (values.length > 0) {
                return new String(values);
            }
        }
        return "";
    }

    private static int indexOf(byte[] fileData, byte[] searchArray) {
        for (int i = 0; i <= fileData.length - searchArray.length; i++) {
            if (matches(fileData, searchArray, i)) {
                return i;
            }
        }
        return -1;
    }

    private static boolean matches(byte[] fileData, byte[] searchArray, int start) {
        for (int i = 0; i < searchArray.length; i++) {
            if (fileData[start + i] != searchArray[i]) {
                return false;
            }
        }
        return true;
    }

    public static byte[] findAndExtract(byte[] byteArray, int position) {
        // Validate the position
        if (position < 0 || position >= byteArray.length) {
            throw new IllegalArgumentException("Position out of bounds");
        }

        // Search backwards for the first byte with value 0
        int zeroByteIndex = -1;
        for (int i = position; i >= 0; i--) {
            if (byteArray[i] == 0) {
                zeroByteIndex = i;
                break;
            }
        }

        // If no zero byte is found, return an empty array
        if (zeroByteIndex == -1) {
            return new byte[0];
        }

        // Calculate the length of the resulting array
        int resultLength = position - zeroByteIndex;

        // Extract the sub-array from zeroByteIndex + 1 to position (inclusive)
        byte[] resultArray = new byte[resultLength];
        System.arraycopy(byteArray, zeroByteIndex + 1, resultArray, 0, resultLength);

        return resultArray;
    }

}
