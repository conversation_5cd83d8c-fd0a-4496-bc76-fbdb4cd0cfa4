package cn.ijiami.detection.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/12/12
 */
@Slf4j
public class EngineCommandUtil {
    /**
     * cmd命令执行
     */
    public static boolean runCmd(String cmd, String[] instructs) {
        Process p = null;
        try {
            p = Runtime.getRuntime().exec(cmd);
            doInstructs(instructs, p);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            if (p != null) {
                p.destroy();
            }
        }
        return true;
    }

    /**
     * cmd命令执行并返回结果
     */
    public static List<String> runCmdResult(String cmd, String[] instructs) {
        return runCmdResult(cmd, instructs, false, false, 0L, null);
    }


    /**
     * cmd命令执行并返回结果
     *
     * @param errorOut 是否返回错误流输出 默认不返回
     * @param timeOut  是否设置线程任务超时
     */
    public static List<String> runCmdResult(String cmd, String[] instructs, boolean errorOut, boolean timeOut, long timeout, TimeUnit unit) {
        Process p = null;
        List<String> outputs = new ArrayList<>();
        FutureTask<Void> normalTask = null;
        FutureTask<Void> errorTask = null;
        try {
            p = Runtime.getRuntime().exec(cmd);
            NormalStreamOutput normalGobbler = new NormalStreamOutput(p, instructs, outputs);
            ErrorStreamOutput errorGobbler = new ErrorStreamOutput(p.getErrorStream(), errorOut, outputs);
            normalTask = new FutureTask<>(normalGobbler);
            errorTask = new FutureTask<>(errorGobbler);
            new Thread(normalTask).start();
            new Thread(errorTask).start();
            if (timeOut) {
                normalTask.get(timeout, unit);
            } else {
                normalTask.get();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (p != null) {
                p.destroy();
            }
            if (normalTask != null) {
                try {
                    normalTask.cancel(true);
                } catch (Exception e) {
                    //静默取消
                }
            }
            if (errorTask != null) {
                try {
                    errorTask.cancel(true);
                } catch (Exception e) {
                    //静默取消
                }
            }
        }
        return outputs;
    }

    /**
     * 执行命令
     */
    private static void doInstructs(String[] instructs, Process p) throws Exception {
        DataOutputStream out = new DataOutputStream(p.getOutputStream());
        for (String instruct : instructs) {
            out.writeBytes(instruct + " \n");
            log.info("输出指令：" + instruct);
            out.flush();
        }
        out.writeBytes("exit \n");
        out.flush();
        out.close();
        p.waitFor();
    }

    /**
     * 输出结果中是否包含成功标识
     *
     * @param result  输出结果
     * @param content 标识
     * @return 是否包含
     */
    public static boolean containsFlag(List<String> result, String content) {
        if (CollUtil.isEmpty(result)) {
            return false;
        }
        for (int i = result.size() - 1; i >= 0; i--) {
            String line = result.get(i);
            if (StrUtil.isNotBlank(line) && line.contains(content)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 输出结果中是否有成功标识
     *
     * @param result  输出结果
     * @param content 标识
     * @return 是否有
     */
    public static boolean equalsFlag(List<String> result, String content) {
        if (CollUtil.isEmpty(result)) {
            return false;
        }
        for (int i = result.size() - 1; i >= 0; i--) {
            String line = result.get(i);
            if (StrUtil.isNotBlank(line) && line.trim().equals(content)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 脚本运行时，输出流监听类
     */
    static class ErrorStreamOutput implements Callable<Void> {
        InputStream inputStream;
        private String charset = "UTF-8";
        private List<String> outputs;
        private boolean errorOut;

        public ErrorStreamOutput(InputStream inputStream, boolean errorOut, List<String> outputs) {
            this.inputStream = inputStream;
            this.outputs = outputs;
            this.errorOut = errorOut;
        }

        @Override
        public Void call() {
            InputStreamReader isr = null;
            BufferedReader br = null;
            try {
                if (inputStream == null) {
                    return null;
                }
                isr = new InputStreamReader(inputStream, charset);
                br = new BufferedReader(isr);
                String line;
                while ((line = br.readLine()) != null) {
                    if (StrUtil.isNotBlank(line)) {
                        String logLine = "eOut " + line;
                        log.warn(logLine);
                        if (errorOut) {
                            outputs.add(logLine);
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                IoUtil.close(br);
                IoUtil.close(isr);
                IoUtil.close(inputStream);
            }
            return null;
        }
    }

    static class NormalStreamOutput implements Callable<Void> {
        private Process p;
        private String[] instructs;
        private List<String> outputs;

        public NormalStreamOutput(Process p, String[] instructs, List<String> outputs) {
            this.p = p;
            this.instructs = instructs;
            this.outputs = outputs;
        }

        @Override
        public Void call() throws Exception {
            DataOutputStream out = new DataOutputStream(p.getOutputStream());
            for (String instruct : instructs) {
                out.writeBytes(instruct + " \n");
                log.info("输出指令：" + instruct);
                out.flush();
            }
            out.writeBytes("exit \n");
            out.flush();
            out.close();
            InputStreamReader isr = null;
            BufferedReader br = null;
            InputStream inputStream = p.getInputStream();
            try {
                isr = new InputStreamReader(p.getInputStream(), "UTF-8");
                br = new BufferedReader(isr);
                String line;
                while ((line = br.readLine()) != null) {
                    log.info("line " + line);
                    outputs.add(line);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                IoUtil.close(br);
                IoUtil.close(isr);
                IoUtil.close(inputStream);
            }
            return null;
        }
    }
}
