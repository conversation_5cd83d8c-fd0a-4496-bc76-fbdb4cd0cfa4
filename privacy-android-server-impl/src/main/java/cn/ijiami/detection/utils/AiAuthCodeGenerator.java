package cn.ijiami.detection.utils;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiAuthCodeGenerator.java
 * @Description ai授权码生成
 * @createTime 2025年03月24日 18:24:00
 */
public class AiAuthCodeGenerator {

    /**
     * 生成授权码
     *
     * @param clientId 客户ID（数字类型）
     * @return 生成的授权码，格式为 SAAS-C{clientId补零到3位}-{随机8位字符}
     */
    public static String generateAuthCode(long clientId) {
        // 将数字转换为带前缀 "C" 和补零到3位的字符串
        String formattedClientId = String.format("C%03d", clientId);

        // 随机生成8位字符（包含大写字母和数字）
        String randomPart = generateRandomString(8);

        // 拼接授权码
        return String.format("SAAS-%s-%s", formattedClientId, randomPart);
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 随机字符串的长度
     * @return 包含大写字母和数字的随机字符串
     */
//    private static String generateRandomString(int length) {
//        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
//        Random random = new Random();
//        StringBuilder sb = new StringBuilder();
//
//        for (int i = 0; i < length; i++) {
//            int index = random.nextInt(chars.length());
//            sb.append(chars.charAt(index));
//        }
//
//        return sb.toString();
//    }
    
    private static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int index = secureRandom.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

}