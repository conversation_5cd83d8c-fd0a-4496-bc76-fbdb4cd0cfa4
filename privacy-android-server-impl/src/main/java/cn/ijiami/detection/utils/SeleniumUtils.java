package cn.ijiami.detection.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.ijiami.base.common.config.IjiamiCommonProperties;

@Slf4j
@Configuration
public class SeleniumUtils {
	
	private IjiamiCommonProperties commonProperties;
	
	public static String privacyHtmlRegex = "\\S+(隐私权政策|隐私政策|隐私协议|隐私权协议|隐私条款|用户条款|用户协议|服务协议|权限)|(sdk[\\s\\S]*){3,}";  
	
	public void getPrivacyByPythonScript(String url, String uncompress){
		String tempPath = uncompress + File.separator + "privacyhtml"+ File.separator;
		File f = new File(tempPath);
		f.mkdirs();
//		long t1 = System.currentTimeMillis();
		String filePath = tempPath +UUID.randomUUID().toString()+".html";
//		String toolPath = commonProperties==null ?"F:/zywa/ijiami/ios/tools/": (commonProperties.getToolsPath()+"/");
//        String cmdStr = "python "+toolPath +"get_html.py "+url +" "+filePath;
//        long t1 = System.currentTimeMillis();
//        Runtime rt = Runtime.getRuntime();
//        try {
//            rt.exec(cmdStr);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        
        cmdPythonSh(url, filePath);
//        long t2 = System.currentTimeMillis();
//        System.out.println("隐私政策python爬取耗时="+(t2-t1)+"ms");
	}
	
	public static List<String> getUrl(String uncompress) {
        List<String> filePaths = CommonUtil.listAllFilesInDir(uncompress + File.separator + "privacyurl", new String[]{".txt", ".TXT"}, true);
        if (filePaths == null || filePaths.size() == 0) {
            return null;
        }
        List<String> linksList = new ArrayList<>();
        for (int i = 0; i < filePaths.size(); i++) {
        	List<String> links = Text2Pic.readTxtFileList(filePaths.get(i));
        	if(links==null || links.size()==0) {
        		continue;
        	}
        	linksList.addAll(links);
		}
        if (linksList == null || linksList.size() == 0) {
            return null;
        }
        Set<String> set = new HashSet<>(linksList);
        linksList = new ArrayList<>(set);
		log.info("SeleniumUtils linksList={}", linksList);
        return linksList;
    }
	
	
	private void cmdPythonSh(String url, String filePath){
		try {
			long t1 = System.currentTimeMillis();
			String base_url = String.format("http://127.0.0.1:8001/?p1=%s&p2=%s", url, filePath);
			String result = HttpUtils.httpGetPageContent(base_url,null);
			long t2 = System.currentTimeMillis();
			log.info("http 隐私政策python爬取耗时获取结果="+result+";耗时="+(t2-t1));
		} catch (Exception e) {
			log.warn("http 隐私政策python爬取错误 msg={}", e.getMessage());
        	String cmdStr = "python3 "+commonProperties.getToolsPath() +"/get_html.py \"" + url +"\" " + filePath;
        	long t1 = System.currentTimeMillis();
        	try {
				List<String> result = EngineCommandUtil.runCmdResult("/bin/bash", new String[]{cmdStr});
				long t2 = System.currentTimeMillis();
				log.info("script 隐私政策python爬取耗时获取结果 cmd={} result={} 耗时={}", cmdStr, StringUtils.join(result, "\n"), (t2-t1));
        	} catch (Exception e1) {
				log.error("隐私政策python爬取失败", e1);
        	}
		}
	}
	
	@Bean
    public SeleniumUtils echoService() {
		
		commonProperties = (IjiamiCommonProperties) AppContextHandel.getApplicationContext().getBean("ijiamiCommonProperties");
		
//		options = new ChromeOptions();
//		options.addArguments("-headless");
//		options.addArguments("--disable-gpu");
//		options.addArguments("--headless");
//		options.addArguments("--no-sandbox");
//		options.addArguments("--disable-dev-shm-usage");
//		commonProperties = (IjiamiCommonProperties) AppContextHandel.getApplicationContext().getBean("ijiamiCommonProperties");
//		String osName = System.getProperty("os.name");
//        String suffix = Objects.nonNull(osName) && osName.toLowerCase().contains("windows") ? ".exe" : "";
//        
//        String path = commonProperties.getToolsPath()+"/chromedriver"+suffix;
//        if(!new File(path).exists()) {
//        	System.err.println("chromedriver不存在"+path);
//        	 return new SeleniumUtils();
//        }
//		// 设置驱动
//		System.setProperty("webdriver.chrome.driver", path);
//		// 不加载图片, 提升速度
//		options.addArguments("--blink-settings=imagesEnabled=false");
//		driver = new ChromeDriver(options);
        return new SeleniumUtils();
    }
	
//	public ChromeOptions options = null;
//	public WebDriver driver = null;
	
	
//	public String getContent(String url,String privacyHtmlRegex){
//		// 访问地址
//		try {
//			driver.get(url);
//			//延时一下，等页面加载
//			Thread.sleep(200);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		String sourceContent = driver.getPageSource();
//		return PrivacyPolicyHtmlHelper.getPrivacyDetailByHtml(sourceContent, privacyHtmlRegex);
//	}
	
//	public static void main(String[] args) {
//		String url = "https://open.oppomobile.com/new/developmentDoc/info?id=10288";
//		ChromeOptions options = new ChromeOptions();
//		// 设置无头模式
////		options..setHeadless(Boolean.TRUE);
//		// 设置无轨 开发时还是不要加，可以看到浏览器效果
//		options.addArguments("-headless");
//		options.addArguments("--disable-gpu");
//		options.addArguments("--headless");
//		options.addArguments("--no-sandbox");
//		options.addArguments("--disable-dev-shm-usage");
//		// 不加载图片, 提升速度
//		options.addArguments("--blink-settings=imagesEnabled=false");
//		
//		// 设置驱动
//		System.setProperty("webdriver.chrome.driver", "F:/zywa/ijiami/ios/tools/chromedriver.exe");
//		
//		
//		WebDriver driver = new ChromeDriver(options);
//		// HashMap中保存下载地址
////		Map<String, String> map = new HashMap<>();
////		map.put("download.default_directory", "E:/a.txt");
//		// ChromeOptions中设置下载路径
////		options.setExperimentalOption("prefs",map);
//		// 新建一个WebDriver 的对象，但是new 的是谷歌的驱动
//		
//		// 访问地址
//		driver.get(url);
//		
//		String source = driver.getPageSource();
//		
//		System.out.println(source);
//		
//		String html=source;
//		String privacyHtmlRegex = "\\S+(隐私政策|隐私协议|隐私条款|用户条款|用户协议|服务协议|权限)|(sdk[\\s\\S]*){3,}";
//		String result = PrivacyPolicyHtmlHelper.getPrivacyDetailByHtml(html, privacyHtmlRegex);
//		System.out.println("==================================");
//		System.out.println(result);
//	}
}
