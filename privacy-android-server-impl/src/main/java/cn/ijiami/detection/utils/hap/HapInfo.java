package cn.ijiami.detection.utils.hap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HapInfo.java
 * @Description
 * @createTime 2024年07月05日 14:58:00
 */
@NoArgsConstructor
@Data
public class HapInfo {

    @JsonProperty("app")
    private AppDTO app;
    @JsonProperty("module")
    private ModuleDTO module;

    @NoArgsConstructor
    @Data
    public static class AppDTO {
        @JsonProperty("apiReleaseType")
        private String apiReleaseType;
        @JsonProperty("bundleName")
        private String bundleName;
        @JsonProperty("compileSdkType")
        private String compileSdkType;
        @JsonProperty("compileSdkVersion")
        private String compileSdkVersion;
        @JsonProperty("distributedNotificationEnabled")
        private Boolean distributedNotificationEnabled;
        @JsonProperty("icon")
        private String icon;
        @JsonProperty("iconId")
        private Integer iconId;
        @JsonProperty("label")
        private String label;
        @JsonProperty("labelId")
        private Integer labelId;
        @JsonProperty("minApiVersion")
        private Integer minApiVersion;
        @JsonProperty("singleton")
        private Boolean singleton;
        @JsonProperty("targetApiVersion")
        private Integer targetApiVersion;
        @JsonProperty("vendor")
        private String vendor;
        @JsonProperty("versionCode")
        private Integer versionCode;
        @JsonProperty("versionName")
        private String versionName;
    }

    @NoArgsConstructor
    @Data
    public static class ModuleDTO {
        @JsonProperty("abilities")
        private List<AbilitiesDTO> abilities;
        @JsonProperty("compileMode")
        private String compileMode;
        @JsonProperty("deliveryWithInstall")
        private Boolean deliveryWithInstall;
        @JsonProperty("dependencies")
        private List<?> dependencies;
        @JsonProperty("description")
        private String description;
        @JsonProperty("descriptionId")
        private Integer descriptionId;
        @JsonProperty("deviceTypes")
        private List<String> deviceTypes;
        @JsonProperty("extensionAbilities")
        private List<ExtensionAbilitiesDTO> extensionAbilities;
        @JsonProperty("installationFree")
        private Boolean installationFree;
        @JsonProperty("mainElement")
        private String mainElement;
        @JsonProperty("name")
        private String name;
        @JsonProperty("pages")
        private String pages;
        @JsonProperty("requestPermissions")
        private List<RequestPermissionsDTO> requestPermissions;
        @JsonProperty("srcEntrance")
        private String srcEntrance;
        @JsonProperty("type")
        private String type;
        @JsonProperty("uiSyntax")
        private String uiSyntax;
        @JsonProperty("virtualMachine")
        private String virtualMachine;

        @NoArgsConstructor
        @Data
        public static class AbilitiesDTO {
            @JsonProperty("description")
            private String description;
            @JsonProperty("descriptionId")
            private Integer descriptionId;
            @JsonProperty("icon")
            private String icon;
            @JsonProperty("iconId")
            private Integer iconId;
            @JsonProperty("label")
            private String label;
            @JsonProperty("labelId")
            private Integer labelId;
            @JsonProperty("name")
            private String name;
            @JsonProperty("skills")
            private List<SkillsDTO> skills;
            @JsonProperty("srcEntrance")
            private String srcEntrance;
            @JsonProperty("startWindowBackground")
            private String startWindowBackground;
            @JsonProperty("startWindowBackgroundId")
            private Integer startWindowBackgroundId;
            @JsonProperty("startWindowIcon")
            private String startWindowIcon;
            @JsonProperty("startWindowIconId")
            private Integer startWindowIconId;
            @JsonProperty("visible")
            private Boolean visible;

            @NoArgsConstructor
            @Data
            public static class SkillsDTO {
                @JsonProperty("actions")
                private List<String> actions;
                @JsonProperty("entities")
                private List<String> entities;
            }
        }

        @NoArgsConstructor
        @Data
        public static class ExtensionAbilitiesDTO {
            @JsonProperty("description")
            private String description;
            @JsonProperty("descriptionId")
            private Integer descriptionId;
            @JsonProperty("label")
            private String label;
            @JsonProperty("labelId")
            private Integer labelId;
            @JsonProperty("name")
            private String name;
            @JsonProperty("srcEntrance")
            private String srcEntrance;
            @JsonProperty("type")
            private String type;
        }

        @NoArgsConstructor
        @Data
        public static class RequestPermissionsDTO {
            @JsonProperty("name")
            private String name;
        }
    }
}
