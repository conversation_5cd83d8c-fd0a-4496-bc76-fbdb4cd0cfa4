package cn.ijiami.detection.utils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.stf.StfDeviceBatteryHealthEnum;
import cn.ijiami.detection.enums.stf.StfDeviceBatteryStatusEnum;
import cn.ijiami.detection.enums.stf.StfDevicePlatformEnum;
import cn.ijiami.detection.enums.stf.StfDeviceStatusEnum;
import cn.ijiami.detection.enums.stf.StfNetworkTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;


/**
 * STF工具类
 *
 * <AUTHOR>
 * @Date 2020/8/29
 **/
@Slf4j
public class StfUtils {

    private static final Logger LOG = LoggerFactory.getLogger(StfUtils.class);

//    private static final String STF_HOST_URL = "http://************:7100";
    private static final String STF_HOST_URL = "";

//    private static final String STF_TOKEN = "2b138e7e7d3b43e0b268605d675e2896761de37b069f4f898438280e41961cd6";
    private static final String STF_TOKEN = "";

    private static final String COOKIE_URL = "/auth/mock/";

    private static final String LOGIN_URL = "/auth/api/v1/mock";

    private static String STF_COOKIE = "";

    private static String STF_XSRF_TOKEN = "";

    private static String STF_JWT_URL = "";

    private static Date STF_JWT_EFFECT_DATE = new Date();

//    private static String STF_NAME = "admin";
//    private static String STF_EMAIL = "<EMAIL>";

    //{"serial":"*************:5555"}

    private static List<JSONObject> freeDevices = new ArrayList<>();
    private static List<JSONObject> busyDevices = new ArrayList<>();

    private static Map<String, String> STF_HEAD_MAP = getHeadMap();

    public static Map<String, String> getHeadMap() {
        Map<String, String> headMap = new HashMap<>();
        headMap.put("Authorization", "Bearer " + STF_TOKEN);
        return headMap;
    }

    public static void queryDevices() {
        JSONObject jsonObject = HttpUtils.httpGet(STF_HOST_URL + "/api/v1/devices", STF_HEAD_MAP);
        if (jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("devices");
            for (int index = 0; index < jsonArray.size(); index++) {
                JSONObject device = jsonArray.getJSONObject(index);
                // 有效设备
                if (device.getBoolean("present")) {
                    if (device.getBoolean("using")) {
                        busyDevices.add(device);
                    } else {
                        freeDevices.add(device);
                    }
                }
                // 无效设备
                else {

                }
            }
        }
    }

    public static void applyDevice() {
        queryDevices();
        JSONObject device = freeDevices.get(0);
        System.out.println("device:" + device.toString());
        JSONObject param = new JSONObject();
        param.put("serial", device.getString("serial"));
        System.out.println(param.toString());
        String result = HttpUtils.post(param, STF_HOST_URL + "/api/v1/user/devices", STF_HEAD_MAP);
        System.out.println(result);
    }

    public static void getDeviceInfo(String serial) {
        JSONObject jsonObject = HttpUtils.httpGet(STF_HOST_URL + "/api/v1/devices/" + serial, STF_HEAD_MAP);
        System.out.println(jsonObject.toString());
    }

    public static boolean releaseDevice(String url, String serial) {
        JSONObject jsonObject = HttpUtils.httpDelete(url + "/api/v1/user/devices/" + serial, STF_HEAD_MAP);
        if (jsonObject != null && jsonObject.containsKey("success")) {
            return jsonObject.getBoolean("success");
        }
        return false;
    }

    // 获取一小时内有效的jwt
    public static String getEffactJWT(String url, String name, String email) {
        if (StringUtils.isBlank(url) || StringUtils.isBlank(name) || StringUtils.isBlank(email)) {
            return STF_JWT_URL;
        }
        synchronized (StfUtils.class) {
            if (StringUtils.isBlank(STF_JWT_URL) ||
                    (new Date().getTime() - STF_JWT_EFFECT_DATE.getTime()) > 1 * 60 * 60 * 1000) {
                try {
                    getCookie(url);
                    getJWT(url, name, email);
                    STF_JWT_EFFECT_DATE = new Date();
                } catch (Exception e) {
                    LOG.error("StfUtils getEffactJWT error", e);
                }
            }
        }
        return STF_JWT_URL;

    }

    // 获取jwt
    public static void getJWT(String url, String name, String email) {
        if (StringUtils.isBlank(STF_XSRF_TOKEN) ||
                StringUtils.isBlank(STF_COOKIE)) {
            return;
        }
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("email", email);
        Map<String, String> headParam = new HashMap<>();
        headParam.put("Cookie", STF_COOKIE);
        headParam.put("XSRF-TOKEN", STF_XSRF_TOKEN);
        headParam.put("Referer", url);
        String result = HttpUtils.post(json, url + LOGIN_URL, headParam);
        JSONObject jwtObj = JSONObject.fromObject(result);
        STF_JWT_URL = jwtObj.getString("redirect");
    }

    // 获取cookie信息
    public static void getCookie(String url) {
        try {
            CloseableHttpClient client = HttpClients.createDefault();
            HttpGet request = new HttpGet(url + COOKIE_URL);
            HttpResponse response = client.execute(request);
            StringBuilder sb = new StringBuilder();
            if (response.getStatusLine().getStatusCode() == 200) {
                Header[] responseHeader = response.getHeaders("Set-Cookie");
                for (Header header : responseHeader) {
                    if (StringUtils.equalsIgnoreCase("Set-Cookie", header.getName())) {
                        sb.append(header.getValue().substring(0, header.getValue().indexOf(";") + 1));
                        if (StringUtils.startsWith(header.getValue(), "XSRF-TOKEN=")) {
                            STF_XSRF_TOKEN = header.getValue().substring("XSRF-TOKEN=".length(), header.getValue().indexOf(";"));
                        }
                    }
                }
                STF_COOKIE = sb.toString();
            }
        } catch (IOException e) {
            LOG.error("StfUtils getCookie fail", e);
        }
    }

    public static int totalDeviceNum(String stfUrl, String stfToken) {
        int totalNum = 0;
        try {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Authorization", "Bearer " + stfToken);
            JSONObject jsonObject = HttpUtils.httpsGet(stfUrl + "/api/v1/devices", headMap, 30000);
            if (jsonObject != null && jsonObject.getBoolean("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("devices");
                for (int index = 0; index < jsonArray.size(); index++) {
                    JSONObject device = jsonArray.getJSONObject(index);
                    // 有效设备
                    if (device.getBoolean("present") && device.getInt("status") == 3) {
                        totalNum++;
                    }
                }
            }
        } catch (Exception e) {
            log.error("请求stf失败", e);
        }
        return totalNum;
    }

    public static int queryFreeDeviceNum(String stfUrl, String stfToken) {
    	if(StringUtils.isBlank(stfUrl)) {
    		return 0;
    	}
        int freeDeviceNum = 0, busyDeviceNum = 0;
        try {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Authorization", "Bearer " + stfToken);
            JSONObject jsonObject = HttpUtils.httpsGet(stfUrl + "/api/v1/devices", headMap, 30000);
            if (jsonObject != null && jsonObject.getBoolean("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("devices");
                for (int index = 0; index < jsonArray.size(); index++) {
                    JSONObject device = jsonArray.getJSONObject(index);
                    // 有效设备
                    if (device.getBoolean("present") && device.getInt("status") == 3) {
                        if (device.getBoolean("using") || !StringUtils.equals(device.getString("owner"), "null")) {
                            busyDeviceNum++;
                        } else {
                            freeDeviceNum++;
                        }
                    }
                    // 无效设备
                    else {

                    }
                }
            }
        } catch (Exception e) {
            log.error("请求stf失败", e);
        }
        return freeDeviceNum;
    }
    
    /**
     * 获取所有设备
     *
     * @param token
     * @return
     */
    public static List<StfDeviceInfo> findStfDeviceAll(String stfUrl, String stfToken) {
        List<StfDeviceInfo> deviceInfos = new ArrayList<>();
        try {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Authorization", "Bearer " + stfToken);
            com.alibaba.fastjson.JSONObject jsonObject = HttpUtils.httpsGetFastJson(stfUrl + "/api/v1/devices", headMap);
            if (jsonObject != null && jsonObject.getBoolean("success")) {
            	com.alibaba.fastjson.JSONArray jsonArray = jsonObject.getJSONArray("devices");
                for (int index = 0; index < jsonArray.size(); index++) {
                	com.alibaba.fastjson.JSONObject device = jsonArray.getJSONObject(index);
                	deviceInfos.add(buildStfDeviceInfo(device));
                    // 有效设备
//                    if (device.getBoolean("present") && device.getIntValue("status") == 3) {
//                        if (device.getBoolean("using") || !StringUtils.equals(device.getString("owner"), "null")) {
////                            busyDeviceNum++;
//                        } else {
////                            freeDeviceNum++;
//                        	deviceInfos.add(buildStfDeviceInfo(device));
//                        }
//                    }// 无效设备
//                    else {}
                }
            }
        } catch (Exception e) {
            log.error("请求stf失败", e);
        }
        return deviceInfos;
    }
    
    
    /**
     * 获取所有设备
     *
     * @param token
     * @return
     */
    public static StfDeviceInfo findStfDeviceIsFree(String stfUrl, String stfToken, String deviceSerial) {
    	List<StfDeviceInfo> deviceList = findStfDeviceAll(stfUrl, stfToken);
    	List<StfDeviceInfo> freeListFree = deviceList.stream().filter(stfDeviceInfo -> stfDeviceInfo.getStatus() == StfDeviceStatusEnum.STF_DEVICE_FREE && stfDeviceInfo.getDeviceSerial().equals(deviceSerial)).collect(Collectors.toList());
        if(freeListFree != null && freeListFree.size()>0) {
        	return freeListFree.get(0);
        }
    	return null;
    }
    
    public static String getDeviceIframeUrl(TTask task, IjiamiCommonProperties commonProperties, String deviceSerial) {
        String stfToken = "";
        if (StringUtils.isNotBlank(task.getStfToken())) {
            stfToken = task.getStfToken();
        } else {
           return null;
        }
        return String.format("%s/stf/device?stfToken=%s&serial=%s", commonProperties.getProperty("ijiami.stf.console"), stfToken, deviceSerial);
    }
    
    
    /**
     * 构建设备信息
     *
     * @param deviceJSON
     * @return
     */
    private static StfDeviceInfo buildStfDeviceInfo(com.alibaba.fastjson.JSONObject deviceJSON) {
        StfDeviceInfo deviceInfo = new StfDeviceInfo();
        deviceInfo.setDeviceSerial(deviceJSON.getString("serial"));
        deviceInfo.setRemoteConnectUrl(deviceJSON.getString("remoteConnectUrl"));
        if (deviceJSON.containsKey("display")) {
        	com.alibaba.fastjson.JSONObject deviceDisplay = deviceJSON.getJSONObject("display");
            deviceInfo.setWebSocketUrl(deviceDisplay.getString("url"));
            deviceInfo.setDisplayWidth(deviceDisplay.getInteger("width"));
            deviceInfo.setDisplayHeight(deviceDisplay.getInteger("height"));
        }
        deviceInfo.setOsVersion(deviceJSON.getString("version"));
        deviceInfo.setCpuPlatform(deviceJSON.getString("abi"));
        if (deviceJSON.containsKey("provider")) {
        	com.alibaba.fastjson.JSONObject providerObject = deviceJSON.getJSONObject("provider");
            deviceInfo.setLocationName(providerObject.getString("name"));
        }
        deviceInfo.setModel(deviceJSON.getString("model"));
        deviceInfo.setManufacturer(deviceJSON.getString("manufacturer"));
        // 手机信息
        if (deviceJSON.containsKey("phone")) {
        	com.alibaba.fastjson.JSONObject devicePhone = deviceJSON.getJSONObject("phone");
            deviceInfo.setImei(devicePhone.getString("imei"));
            deviceInfo.setImsi(devicePhone.getString("imsi"));
            deviceInfo.setPhoneNumber(devicePhone.getString("phoneNumber"));
        }
        // 网络信息
        if (deviceJSON.containsKey("network")) {
            String networkType = deviceJSON.getJSONObject("network").getString("type");
            for (StfNetworkTypeEnum networkTypeEnum : StfNetworkTypeEnum.values()) {
                if (StringUtils.equalsIgnoreCase(networkTypeEnum.getName(), networkType)) {
                    deviceInfo.setNetworkType(networkTypeEnum);
                    break;
                }
            }
        }
        if (deviceInfo.getNetworkType() == null) {
            deviceInfo.setNetworkType(StfNetworkTypeEnum.NETWORK_UNKNOWN);
        }
        // 平台信息
        String platform = deviceJSON.getString("platform");
        for (StfDevicePlatformEnum platformEnum : StfDevicePlatformEnum.values()) {
            if (StringUtils.equalsIgnoreCase(platformEnum.getName(), platform)) {
                deviceInfo.setPlatformType(platformEnum);
                break;
            }
        }
        if (deviceInfo.getPlatformType() == null) {
            deviceInfo.setPlatformType(StfDevicePlatformEnum.STF_PLATFORM_IOS);
        }
        // 电池信息
        if (deviceJSON.containsKey("battery")) {
        	com.alibaba.fastjson.JSONObject batteryJSON = deviceJSON.getJSONObject("battery");
            deviceInfo.setBatteryScale(batteryJSON.getFloat("scale"));
            deviceInfo.setBatteryTemp(batteryJSON.getFloat("temp"));
            deviceInfo.setSource(batteryJSON.getString("source"));
            for (StfDeviceBatteryHealthEnum healthEnum : StfDeviceBatteryHealthEnum.values()) {
                if (StringUtils.equals(healthEnum.getName(), batteryJSON.getString("health"))) {
                    deviceInfo.setBatteryHealth(healthEnum);
                    break;
                }
            }
            if (deviceInfo.getBatteryHealth() == null) {
                deviceInfo.setBatteryHealth(StfDeviceBatteryHealthEnum.STF_BATTERY_BAD);
            }

            for (StfDeviceBatteryStatusEnum statusEnum : StfDeviceBatteryStatusEnum.values()) {
                if (StringUtils.equals(statusEnum.getName(), batteryJSON.getString("status"))) {
                    deviceInfo.setBatteryStatus(statusEnum);
                    break;
                }
            }
            if (deviceInfo.getBatteryStatus() == null) {
                deviceInfo.setBatteryStatus(StfDeviceBatteryStatusEnum.STF_BATTERY_OTHER);
            }
        }

        // 判断设备状态
        boolean present = deviceJSON.getBoolean("present");
        int status = deviceJSON.getIntValue("status");
        boolean ready = deviceJSON.getBoolean("ready");
        String owner = deviceJSON.getString("owner");
        boolean using = deviceJSON.getBoolean("using");
        // 不在线
        if (!present) {
            deviceInfo.setStatus(StfDeviceStatusEnum.STF_DEVICE_DEAD);
        }
        // 待连接
        else if (!ready) {
            deviceInfo.setStatus(StfDeviceStatusEnum.STF_DEVICE_WAIT);
        } else if (!(status == 3 && (StringUtils.isNotBlank(owner) || using)) || StringUtils.isBlank(owner)) {
            deviceInfo.setStatus(StfDeviceStatusEnum.STF_DEVICE_FREE);
        } else {
            deviceInfo.setStatus(StfDeviceStatusEnum.STF_DEVICE_BUSY);
        }
        return deviceInfo;
    }


    public static void main(String[] args) {
//        System.out.println(queryFreeDeviceNum("https://uatstf.zywa.com.cn:7100", "dbfd888c4368466c92565e270dabee2759a4cc770d5644d98b49251f8c783668"));
//    	String stfUrl = "http://172.10.3.107:7100";
//    	String stfToken = "1c9c9d653901499cbab9482eed8010b4af30f133e73848828f2d43893d6a0b23";
//    	List<StfDeviceInfo> list = findStfDeviceAll(stfUrl, stfToken);
//    	list.forEach(device->{
//    		if(StfDeviceStatusEnum.STF_DEVICE_FREE == device.getStatus()){
//    			System.out.println(device.getDeviceSerial()+","+device.getOsVersion());
////    			System.out.println(device.getOsVersion());
//    		}
//    		
//    	});
//    	
//    	Set<String> set = new HashSet<>();
//		List<StfDeviceInfo> freeList = findStfDeviceAll(stfUrl, stfToken);
//		if(freeList== null || freeList.size() == 0) {
//			return;
//		}
//		freeList.forEach(devcie->{
//			set.add(devcie.getOsVersion());
//		});
//		
//		System.out.println(new ArrayList<String>(set));
//    	
		
		
//		Set<String> setOsVersion = new HashSet<>();
//		Set<String> setModel = new HashSet<>(); 
//		
//		StfDeviceTypeVO vo = new StfDeviceTypeVO();
//		
//		List<StfDeviceInfo> freeList = findStfDeviceAll(stfUrl, stfToken);
//		if(freeList== null || freeList.size() == 0) {
//			return;
//		}
//		
//		List<StfDeviceInfo> freeListFree = new ArrayList<>();
//		freeList.forEach(device->{
////    		if(StfDeviceStatusEnum.STF_DEVICE_FREE == device.getStatus()){
////    			freeListFree.add(device);
////    		}
//    		if(StfDeviceStatusEnum.STF_DEVICE_DEAD != device.getStatus()){
//    			freeListFree.add(device);
//    		}
//    	});
//		
//		freeListFree.forEach(devcie->{
//			if(StringUtils.isNoneBlank(devcie.getOsVersion())) {
//				setOsVersion.add(devcie.getOsVersion());
//			}
//			if(StringUtils.isNoneBlank(devcie.getModel())) {
//				setModel.add(devcie.getModel());
//			}
//		});
		
//		vo.setModel(new ArrayList<String>(setModel));
//		vo.setVersion(new ArrayList<String>(setOsVersion));
		
//		System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(vo));
		
//		List<StfDeviceTypeVO> typeList = new ArrayList<>();
//		Set<String>  setOsVersionList = freeListFree.stream().map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
//		List<String> osVersionList = new ArrayList<String>(setOsVersionList);
//		System.out.println(osVersionList);
//		for (String string : osVersionList) {
//			StfDeviceTypeVO type= new StfDeviceTypeVO();
//			Set<String>  setModelList1 = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getOsVersion()!=null && stfDeviceInfo.getOsVersion().equals(string)).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
//			System.out.println(setModelList1);
//			
//			type.setModel(new ArrayList<>(setModelList1));
////			type.setVersion(string);
//			typeList.add(type);
//		}
//		System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(typeList));
//		
//		Set<String>  setModelList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getModel()!=null).map(StfDeviceInfo::getModel).collect(Collectors.toSet());
//		Set<String>  setVersionList = freeListFree.stream().filter(stfDeviceInfo -> stfDeviceInfo.getOsVersion()!=null).map(StfDeviceInfo::getOsVersion).collect(Collectors.toSet());
//		StfDeviceInfo resultPerson2 = freeListFree.stream().filter(t -> "172.10.23.104:5555".equals(t.getDeviceSerial())).findAny().orElse(null);
//		
//		System.out.println(setModelList);
//		System.out.println(setVersionList);
//		System.out.println(JSON.toJSONString(resultPerson2));
		
//    	String result = HttpUtils.httpGetString("https://uatstf.zywa.com.cn:30002/api/device/getAllDevice", null, 3);
//		List<String> deviceList = com.alibaba.fastjson.JSONObject.parseArray(result, String.class);
//		if(deviceList == null || deviceList.size()==0) {
//			return;
//		}
//		List<UseDeviceVO> newDevices = new ArrayList<UseDeviceVO>();
//		for (String string : deviceList) {
//			UseDeviceVO vo = new UseDeviceVO();
//			if(string.contains("_")) {
//				String str[] = string.split("_");
//				vo.setDeviceSerial(str[3]);
//				vo.setPlatform(str[0]);
//				vo.setTaskId(str[1]==null?null:Long.parseLong(str[1]));
//				newDevices.add(vo);
//			}
//		}
//		System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(newDevices));
    }
}
