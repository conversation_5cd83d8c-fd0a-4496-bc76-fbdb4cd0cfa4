package cn.ijiami.detection.utils.excel;


import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.VO.CustomIosSdkClass;
import cn.ijiami.detection.VO.CustomSdkSave;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryLabel;
import cn.ijiami.detection.entity.TSdkLibraryType;
import cn.ijiami.detection.enums.SdkSourceEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.service.ISdkService;
/**
 * Copyright (C) 2025 爱加密 All rights reserved.
 * <p>
 * 解析SDK工具类
 * <p>
 * Author: bing
 * Email: <EMAIL>
 * Date: 2025-3-18 15:12
 */
@Component
public class ExcelSDKParser {

    @Resource
    private TPermissionMapper permissionMapper;
    @Resource
    private ISdkService sdkService;
    @Resource
    private TSdkLibraryMapper sdkLibraryMapper;

    public static void main(String[] args) {
//        String filePath = "E:/download/自定义SDK库导入表.xlsx"; //文件路径
//        List<TSdkLibrary> sdkList = parseExcel2(filePath); //解析数据
//        System.out.println(sdkList); //打印结果
    }

    private static final Map<String, Integer> CHARGE_TYPE_MAP = new HashMap<>();
    static {
        // 初始化映射表，支持大小写不敏感
        CHARGE_TYPE_MAP.put("收费".toLowerCase(), 2);
        CHARGE_TYPE_MAP.put("免费".toLowerCase(), 1);
        CHARGE_TYPE_MAP.put("开源".toLowerCase(), 3);
        CHARGE_TYPE_MAP.put("自研框架".toLowerCase(), 4);
    }

    public static Integer parseChargeType(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        // 忽略大小写进行匹配
        return CHARGE_TYPE_MAP.get(value.trim().toLowerCase());
    }


    public List<CustomSdkSave> parseExcel(String filePath) {
        List<CustomSdkSave> sdkList = new ArrayList<>();

        //获取SDK分类
        List<TSdkLibraryType> sdkLibraryTypeList = sdkService.findSdkTypeList();
        Map<String, TSdkLibraryType> sdkLibraryTypeMap = sdkLibraryTypeList.stream()
                .collect(Collectors.toMap(TSdkLibraryType::getTypeName, item -> item));

        //获取不良标签
        List<TSdkLibraryLabel> sdkLibraryLabelList =sdkService.findSdkBadLabelList();
        Map<String, TSdkLibraryLabel> sdkLibraryLabelMap = sdkLibraryLabelList.stream()
                .collect(Collectors.toMap(TSdkLibraryLabel::getLabelName, item -> item));

        //获取权限信息 权限key
        List<TPermission> permissionVOList = permissionMapper.selectAll();
        Map<String, TPermission> permissionVOListMap = permissionVOList.stream()
        	    .collect(Collectors.toMap(
        	        item -> item.getName() + "-" + item.getTerminalType().getValue(), // 复合键：name-terminalType
        	        item -> item, 
        	        (existing, replacement) -> existing // 如果键重复，保留现有的值
        	    ));
        
        //权限名
        Map<String, TPermission> permissionRemarkMap = permissionVOList.stream()
        	    .collect(Collectors.toMap(
        	        item -> item.getRemark() + "-" + item.getTerminalType().getValue(), // 复合键：name-terminalType
        	        item -> item, 
        	        (existing, replacement) -> existing // 如果键重复，保留现有的值
        	    ));
        
        //权限code
        Map<String, TPermission> permissionCodeMap = permissionVOList.stream()
        	    .collect(Collectors.toMap(
        	        item -> item.getPermissionCode() + "-" + item.getTerminalType().getValue(), // 复合键：name-terminalType
        	        item -> item, 
        	        (existing, replacement) -> existing // 如果键重复，保留现有的值
        	    ));

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 第一个工作表
            int rowStart = 2; // 数据从第4行开始（0-based）

            for (int i = rowStart; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                CustomSdkSave sdk = new CustomSdkSave();

                // 按列索引解析数据
                String label = getCellValue(row.getCell(0));   // A列：标签 - 来源
                if(label.contains("全国")) {
                	 sdk.setSdkSource(SdkSourceEnum.CAICTAC.itemValue());
                }
                if(label.contains("自研SDK")) {
               	 	sdk.setSdkSource(SdkSourceEnum.CUSTOM.itemValue());
                }
                if(StringUtils.isBlank(label) || sdk.getSdkSource() == null) {
                	continue;
                }
                
                String sdkName = getCellValue(row.getCell(1)); // SDK名称
                if(StringUtils.isBlank(sdkName)) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，SDK名称不能为空");
                }
                
                sdk.setName(sdkName);      // B列：*SDK名称
                sdk.setVersion(getCellValue(row.getCell(2)));      // C列：*SDK版本
                if(StringUtils.isBlank(sdk.getVersion())) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，SDK版本不能为空");
                }
                sdk.setAlias(getCellValue(row.getCell(3))); // D列：关键词
                sdk.setUniqueNo(getCellValue(row.getCell(4))); // E列：SDK唯一编码
                sdk.setGroupId(getCellValue(row.getCell(5))); // F列：groupId
                sdk.setArtifactId(getCellValue(row.getCell(6))); // G列：artifactId

                
                // *系统类型
                String systemTypeValue = getCellValue(row.getCell(14));
                if(StringUtils.isBlank(systemTypeValue)) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，系统类型不能为空");
                }
                switch (systemTypeValue) {
				case "安卓":
					sdk.setTerminalType(TerminalTypeEnum.ANDROID.getValue());
					break;
				case "苹果":
					sdk.setTerminalType(TerminalTypeEnum.IOS.getValue());
					break;
				case "鸿蒙":
					sdk.setTerminalType(TerminalTypeEnum.HARMONY.getValue());
					break;
				default:
					break;
				}
                
                List<CustomIosSdkClass> classFunctionList = new ArrayList<>();

                String classs = getCellValue(row.getCell(7));
                String function = getCellValue(row.getCell(8));
                if(sdk.getTerminalType() == TerminalTypeEnum.IOS.getValue() && (StringUtils.isBlank(classs) || StringUtils.isBlank(function))){
                	throw new IllegalArgumentException("第"+(i+1)+"行，IOS函数名称、或者方法名称不能为空");
                }
                CustomIosSdkClass cInfo = new CustomIosSdkClass();
                cInfo.setClazz(classs);       // H列：类名
                cInfo.setFunction(function);  // I列：函数名
                classFunctionList.add(cInfo);
                sdk.setClassFunctionList(classFunctionList);
                
                
                // 头文件名
                sdk.setIosHeaderFileName(getCellValue(row.getCell(9)));
                // ohpm坐标
                sdk.setCoordinate(getCellValue(row.getCell(10)));
                // ohpm仓库地址
                sdk.setDownloadUrl(getCellValue(row.getCell(11)));
                //  SDK包地址
                sdk.setDownloadUrl(getCellValue(row.getCell(12)));
                // *描述
                sdk.setSdkDescribe(getCellValue(row.getCell(13)));
                if(StringUtils.isBlank(sdk.getSdkDescribe())) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，描述(SDK描述)不能为空");
                }
                // *系统类型
                if(StringUtils.isBlank(systemTypeValue)) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，系统类型不能为空");
                }
                switch (systemTypeValue) {
				case "安卓":
					sdk.setTerminalType(TerminalTypeEnum.ANDROID.getValue());
					break;
				case "苹果":
					sdk.setTerminalType(TerminalTypeEnum.IOS.getValue());
					break;
				case "鸿蒙":
					sdk.setTerminalType(TerminalTypeEnum.HARMONY.getValue());
					break;
				default:
					break;
				}
                if(sdk.getTerminalType()==null){
                	throw new IllegalArgumentException("第"+(i+1)+"行，系统类型不合法，请按照表格选择");
                }
               
                //MD5
                sdk.setSdkMd5(getCellValue(row.getCell(15)));
                //编译版本
                if(isNumeric(getCellValue(row.getCell(16)))) {
                    sdk.setCompileSdkVersion(Integer.parseInt(getCellValue(row.getCell(16))));
                } else {
                    sdk.setCompileSdkVersion(null);
                }

                //最低版本
                if(isNumeric(getCellValue(row.getCell(17)))) {
                    sdk.setMinSdkVersion(Integer.parseInt(getCellValue(row.getCell(17))));
                } else {
                    sdk.setMinSdkVersion(null);
                }

                //新版本特性
                sdk.setNewVersionFeature(getCellValue(row.getCell(18)));
                //SDK包路径
                sdk.setSdkPath(getCellValue(row.getCell(19)));
                //WEB地址
                sdk.setWebsiteUrl(getCellValue(row.getCell(20)));
                //*功能分类
                String typeName = getCellValue(row.getCell(21));
                if(!StringUtils.isEmpty(typeName) && sdkLibraryTypeMap.get(typeName) != null) {
                    sdk.setSdkTypeId(Integer.parseInt(sdkLibraryTypeMap.get(typeName.trim()).getId().toString()));
                }
                if(sdk.getSdkTypeId() == null){
                	throw new IllegalArgumentException("第"+(i+1)+"行，功能分类不合法，请按照表格选择");
                }

                //App-key
                sdk.setAppKey(getCellValue(row.getCell(22)));
                //Api链接地址
                sdk.setApiUrl(getCellValue(row.getCell(23)));
                //隐私链接地址
                sdk.setPrivacyUrl(getCellValue(row.getCell(24)));
                //IP
                sdk.setIp(getCellValue(row.getCell(25)));
                // 域名
                sdk.setHost(getCellValue(row.getCell(26)));
                //是否收费 1 免费，2 收费，3 开源， 4 自研框架
                sdk.setChargeType(parseChargeType(getCellValue(row.getCell(27))));

                // 不良标签
                String badLabelName = getCellValue(row.getCell(28));
                if(!StringUtils.isEmpty(badLabelName) && sdkLibraryLabelMap.get(badLabelName) != null) {
                    sdk.setBadLabelId(Integer.parseInt(sdkLibraryLabelMap.get(badLabelName).getId().toString()));
                }

                // 包名信息
                List<String> packageNameList = splitPackageNames(getCellValue(row.getCell(29)));
                sdk.setPackageList(packageNameList);
                
                if((sdk.getTerminalType() == TerminalTypeEnum.ANDROID.getValue() || sdk.getTerminalType() == TerminalTypeEnum.HARMONY.getValue()) &&  
                		(packageNameList == null || packageNameList.size() == 0)){
                	throw new IllegalArgumentException("第"+(i+1)+"行，"+ TerminalTypeEnum.getItem(sdk.getTerminalType()).name()+"包名信息不能为空");
                }

                //*公司名称
                sdk.setSdkManufacturer(getCellValue(row.getCell(30)));
                if(StringUtils.isBlank(sdk.getSdkManufacturer())) {
                	throw new IllegalArgumentException("第"+(i+1)+"行，公司名称不能为空");
                }
                //官网地址
                sdk.setSdkManufacturerWebsiteUrl(getCellValue(row.getCell(31)));
                //公司地址
                sdk.setSdkManufacturerAddress(getCellValue(row.getCell(32)));
                
                //权限key
                String permissionKey = getCellValue(row.getCell(33)); //权限key
                String permissionName = getCellValue(row.getCell(34));// 权限名
                String permissionCode = getCellValue(row.getCell(35)); // 权限code
                List<String> permissionCodeList = new ArrayList<>();
                
                if(StringUtils.isNotBlank(permissionKey)){
                	 List<String> permissionKeyList = splitPackageNames(permissionKey);
                     for (String key : permissionKeyList) {
                         TPermission permissionVO = permissionVOListMap.get(key+"-"+sdk.getTerminalType());
                         if(permissionVO == null) {
                        	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限key查询数据库不存在");
                         }
                         if(permissionVO.getTerminalType().getValue() != sdk.getTerminalType()) {
                        	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限key查询数据库不存在");
                         }
                         if(!permissionCodeList.contains(permissionVO.getPermissionCode())) {
                        	 permissionCodeList.add(permissionVO.getPermissionCode());
                         }
                     }
                }
                
                // 权限名
                if(!StringUtils.isBlank(permissionName)) {
                    List<String> permissionNameList = splitPackageNames(permissionName);
                    for (String name : permissionNameList) {
                        TPermission permissionVO = permissionRemarkMap.get(name+"-"+sdk.getTerminalType());
                        if(permissionVO == null) {
                       	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限名查询数据库不存在");
                        }
                        if(permissionVO.getTerminalType().getValue() != sdk.getTerminalType()) {
                       	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限名查询数据库不存在");
                        }
                        if(!permissionCodeList.contains(permissionVO.getPermissionCode())) {
                        	permissionCodeList.add(permissionVO.getPermissionCode());
                        }
                    }
                }
                
                //权限code
                if(StringUtils.isNotBlank(permissionCode)){
               	 List<String> permissionKeyList = splitPackageNames(permissionCode);
                    for (String key : permissionKeyList) {
                        TPermission permissionVO = permissionCodeMap.get(key+"-"+sdk.getTerminalType());
                        if(permissionVO == null) {
                       	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限code查询数据库不存在");
                        }
                        if(permissionVO.getTerminalType().getValue() != sdk.getTerminalType()) {
                       	 	throw new IllegalArgumentException("第"+(i+1)+"行，权限code查询数据库不存在");
                        }
                        if(!permissionCodeList.contains(permissionVO.getPermissionCode())) {
                       	 	permissionCodeList.add(permissionVO.getPermissionCode());
                        }
                    }
               }
               sdk.setPermissionCodeList(permissionCodeList);
                
                //查询SDK包名是否已经存在
                List<TSdkLibrary> sdkLibrary = sdkLibraryMapper.findInPackageNameSource(packageNameList, sdk.getSdkSource(), sdk.getTerminalType().intValue());
                if(sdkLibrary != null && sdkLibrary.size()>0) {
                	sdk.setId(sdkLibrary.get(0).getId());
                }
                
                // 验证必填字段
                sdkList.add(sdk);
            }
        } catch (Exception e) {
            throw new RuntimeException("解析Excel失败:"+e.getMessage(), e);
        }
        return sdkList;
    }

    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("-?\\d+"); // 匹配整数（包括负数）
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) return "";
        DataFormatter formatter = new DataFormatter();
        return formatter.formatCellValue(cell).trim();
    }

//    private static List<String> splitPackageNames(String name) {
//        return Arrays.asList(name.split("\\s*,\\s*"));
//    }
    
    private static List<String> splitPackageNames(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Collections.emptyList();
        }

        // 按逗号分隔并去除前后空白字符
        String[] parts = name.split("\\s*,\\s*");
        List<String> result = new ArrayList<>();

        // 过滤掉空字符串和无效的包名
        for (String part : parts) {
            if (part != null && !part.trim().isEmpty()) {
                result.add(part.trim());
            }
        }

        return result; // 返回过滤后的列表
    }

}
