package cn.ijiami.detection.utils;

import org.apache.commons.io.Charsets;

import java.io.BufferedReader;

import java.io.IOException;

import java.io.InputStream;

import java.io.InputStreamReader;

import java.util.List;

public class ThreadUtil implements Runnable {

    // 设置读取的字符编码

    private String character = Charsets.UTF_8.displayName();

    private List<String> list;

    private InputStream inputStream;

    public ThreadUtil(InputStream inputStream, List<String> list) {

        this.inputStream = inputStream;

        this.list = list;

    }

    public void start() {

        Thread thread = new Thread(this);

        thread.setDaemon(true);//将其设置为守护线程

        thread.start();

    }

    public void run() {

        BufferedReader br = null;

        try {
            br = new BufferedReader(new InputStreamReader(inputStream, character));
            String line = null;
            while ((line = br.readLine()) != null) {
                list.add(line);
            }

        } catch (IOException e) {
            e.getMessage();
        } finally {
            try {
                //释放资源
                inputStream.close();
                if (br != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.getMessage();
            }
        }
    }

}