package cn.ijiami.detection.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PhantomJsUtils.java
 * @Description
 * @createTime 2021年10月18日 15:04:00
 */
@Slf4j
public class PhantomJsUtils {

    public static String getHtml(String phantomjsPath, String url) {
        String osName = System.getProperty("os.name");
        String suffix = Objects.nonNull(osName) && osName.toLowerCase().contains("windows") ? ".exe" : "";
        String cmdStr = phantomjsPath + File.separator + "phantomjs" + suffix + " " + phantomjsPath + File.separator + "getHtml.js " + url;
        Runtime rt = Runtime.getRuntime();
        log.info("phantomjs cmd {}", cmdStr);
        try {
            File path = new File(phantomjsPath);
            Process ps = rt.exec(cmdStr, (String[])null, path);
            BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            StringBuilder sb = new StringBuilder();

            String line;
            while((line = br.readLine()) != null) {
                sb.append(line);
            }

            String result = sb.toString();
            return result;
        } catch (Exception e) {
            log.warn("phantomjs error {}", e.getMessage(), e);
            return "";
        }
    }

    public static String getImage(String phantomjsPath, String url,String outPahth) {
        String osName = System.getProperty("os.name");
        String suffix = Objects.nonNull(osName) && osName.toLowerCase().contains("windows") ? ".exe" : "";
        phantomjsPath = phantomjsPath+ File.separator + "PhantomJS" + File.separator;
        String cmdStr = phantomjsPath  +"phantomjs"+ suffix + " "+ phantomjsPath + "render_pdf.js " + url +" "+ outPahth;
        Runtime rt = Runtime.getRuntime();
        log.info("phantomjs cmd {}", cmdStr);
        try {
            File path = new File(phantomjsPath);
            Process ps = rt.exec(cmdStr, (String[])null, path);
            BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            StringBuilder sb = new StringBuilder();

            String line;
            while((line = br.readLine()) != null) {
                sb.append(line);
            }

            String result = sb.toString();
            return result;
        } catch (Exception e) {
            log.warn("phantomjs error {}", e.getMessage(), e);
            return "";
        }
    }
}
