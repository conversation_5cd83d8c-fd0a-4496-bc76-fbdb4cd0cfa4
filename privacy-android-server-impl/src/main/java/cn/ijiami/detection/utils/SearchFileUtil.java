package cn.ijiami.detection.utils;

import java.io.File;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.framework.kit.utils.FileUtil;


/**
 * 文件服务帮助类
 * 
 * <AUTHOR>
 *
 */
public class SearchFileUtil {
	private static final Logger LOG = LoggerFactory.getLogger(SearchFileUtil.class);

	/**
	 * 搜素文件
	 * 
	 * @param baseDirName    文件所在根目录
	 * @param targetFileName 文件后缀 (支持多个通配符用“|”隔开，如“*.txt|*.png”)
	 * @param fileList       搜索到的文件集合
	 */
	public static void seachFiles(String baseDirName, String targetFileName, List<File> fileList, Integer seachSize) {
		File baseDir = new File(baseDirName); // 创建一个File对象
		int count = 0;
		boolean isBreak = false;
		if (!baseDir.exists() || !baseDir.isDirectory()) { // 判断目录是否存在
			LOG.info(String.format("文件查找失败：%s", baseDirName + "不是一个目录！"));
		}
		String tempName = null;
		// 判断目录是否存在
		File tempFile;
		File[] files = baseDir.listFiles();
		for (int i = 0; i < files.length; i++) {
			if (isBreak) {
				break;
			}
			tempFile = files[i];
			if (tempFile.isDirectory()) {
				seachFiles(tempFile.getAbsolutePath(), targetFileName, fileList, seachSize);
			} else if (tempFile.isFile()) {
				tempName = tempFile.getName();
				// 支持多个文件后缀
				String[] patterns = targetFileName.split("\\|");
				for (String pattern : patterns) {
					if (wildcardMatch(pattern, tempName)) {
						// 匹配成功，将文件名添加到结果集
						if (seachSize != null && count == seachSize) {
							isBreak = true;
							break;
						}
						fileList.add(tempFile.getAbsoluteFile());
						count++;
					}
				}
			}
		}
	}

	/**
	 * 通配符匹配
	 * 
	 * @param pattern 通配符模式
	 * @param str     待匹配的字符串
	 * @return 匹配成功则返回true，否则返回false
	 */
	private static boolean wildcardMatch(String pattern, String str) {
		int patternLength = pattern.length();
		int strLength = str.length();
		int strIndex = 0;
		char ch;
		for (int patternIndex = 0; patternIndex < patternLength; patternIndex++) {
			ch = pattern.charAt(patternIndex);
			if (ch == '*') {
				// 通配符星号*表示可以匹配任意多个字符
				while (strIndex < strLength) {
					if (wildcardMatch(pattern.substring(patternIndex + 1), str.substring(strIndex))) {
						return true;
					}
					strIndex++;
				}
			} else if (ch == '?') {
				// 通配符问号?表示匹配任意一个字符
				strIndex++;
				if (strIndex > strLength) {
					// 表示str中已经没有字符匹配?了。
					return false;
				}
			} else {
				if ((strIndex >= strLength) || (ch != str.charAt(strIndex))) {
					return false;
				}
				strIndex++;
			}
		}
		return (strIndex == strLength);
	}

	public static void main(String[] args) {
		/*
		 * List<File> fileList = new ArrayList<File>();
		 * SearchFileUtil.seachFiles("E:/gongju/file/images/WeChat",
		 * "*.icon|*.png|*.bmp|*.jpg", fileList); for (File file : fileList) {
		 * System.out.println(file.getName()+"==="+file.getAbsolutePath()); }
		 */

		System.out.println(FileUtil.decodeData("L2RlZmF1bHQvYmFja2FnZTE1NDMzNzUyMzI3NTgucG5n"));
	}
}
