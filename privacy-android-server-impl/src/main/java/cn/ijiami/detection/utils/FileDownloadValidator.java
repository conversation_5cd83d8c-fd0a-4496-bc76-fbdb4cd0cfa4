package cn.ijiami.detection.utils;

import cn.ijiami.detection.constant.PinfoConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;

import java.io.IOException;

import static cn.ijiami.detection.utils.CommonUtil.getDefaultClient;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FileDownloadValidator.java
 * @Description 文件下载校验
 * @createTime 2024年06月21日 15:47:00
 */
@Slf4j
public class FileDownloadValidator {

    public static boolean isValidFileDownloadURL(String fileDownloadURL) {
        try {

            // 获取文件名和扩展名进行验证
            String filename = CommonUtil.getFilenameFromURLIgnoringQuery(fileDownloadURL);
            if (filename != null && !filename.isEmpty()) {
                String ext = CommonUtil.getFileExtName(filename);
                if (isAllowedExtension(ext)) {
                    return true;
                }
            }
            // 找不到文件名用 HEAD 请求验证
            if (isValidContentType(fileDownloadURL)) {
                return true;
            }
        } catch (Exception e) {
            log.info("isValidFileDownloadURL failure {}", e.getMessage());
        }
        return false;
    }

    public static boolean isAllowedExtension(String ext) {
        return ConstantsUtils.SUFFIX_APK.equalsIgnoreCase(ext)
                || ConstantsUtils.SUFFIX_AAB.equalsIgnoreCase(ext)
                || ConstantsUtils.SUFFIX_IPA.equalsIgnoreCase(ext);
    }

    public static boolean isValidContentType(String fileDownloadURL) {
        try (CloseableHttpClient client = getDefaultClient()) {
            HttpHead headRequest = new HttpHead(fileDownloadURL);
            HttpResponse response = client.execute(headRequest);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                String contentType = response.getFirstHeader("Content-Type").getValue();
                return "application/octet-stream".equalsIgnoreCase(contentType);
            }
        } catch (IOException e) {
            log.info("isValidContentType failure {}", e.getMessage());
        }
        return false;
    }

}
