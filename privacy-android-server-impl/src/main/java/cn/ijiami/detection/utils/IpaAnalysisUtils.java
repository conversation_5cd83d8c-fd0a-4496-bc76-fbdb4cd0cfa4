package cn.ijiami.detection.utils;

import cn.ijiami.framework.kit.exception.IjiamiUtilException;
import com.dd.plist.NSDictionary;
import com.dd.plist.NSObject;
import com.dd.plist.PropertyListFormatException;
import com.dd.plist.PropertyListParser;
import com.ijiami.ios.bean.DMessageBean;
import com.ijiami.ios.bean.TaskBean;
import com.ijiami.ios.callback.DCallback;
import com.ijiami.ios.config.InitConfig;
import com.ijiami.ios.detect.AssistantDump;
import com.ijiami.ios.detect.AssistantUnzip;
import com.ijiami.ios.ipatools.FileUtil;
import com.ijiami.ios.ipatools.Ziptools;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IpaPermissionUtils.java
 * @Description ipa解析工具
 * @createTime 2024年06月06日 12:16:00
 */
@Slf4j
public class IpaAnalysisUtils {

    private static void unZip(String ipaPath, String savePath) {
        try {
            Ziptools.unzipFileUseCmd(ipaPath, savePath);
        } catch (Exception e) {
            log.error("解压失败", e);
        }
    }

    public static List<String> extractPermissionInfo(String ipaPath) {
        File srcFile = new File(ipaPath);
        String fileName = srcFile.getName().replace(".", "_");
        String dumpPath = srcFile.getParent() + File.separator + fileName;
        List<String> listPermissions = null;
        File dumpFile = new File(dumpPath);
        if (!dumpFile.exists()) {
            dumpFile.mkdirs();
        }
        try {
            unZip(ipaPath, dumpPath);
            if (dumpFile.listFiles() == null || dumpFile.listFiles().length == 0) {
                log.info("解压文件夹为空");
                return Collections.emptyList();
            }
            /** 获取文件根目录* */
            File rootFile = getRootPath(dumpPath);
            String rootPath = rootFile.getAbsolutePath();
            /** 解析plist文件* */
            File pListFile = new File(rootFile, "Info.plist");
            try {
                // 第三方jar包提供
                NSDictionary rootDict = (NSDictionary) PropertyListParser
                        .parse(pListFile);
                listPermissions = getAppPermisstions(rootDict);
                // 读取多语言中的权限信息
                String stringFilePath = getLocalizableInfoPlist(rootPath);
                if (stringFilePath != null) {
                    // PropertyListFormatException
                    List<String> stringFileListPermission = getStringFilePermisstions(stringFilePath);
                    if (stringFileListPermission != null && !stringFileListPermission.isEmpty()) {
                        for (String tmp : stringFileListPermission) {
                            if (!listPermissions.contains(tmp)) {
                                listPermissions.add(tmp);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("提取权限失败", e);
            }
        } catch (Throwable e) {
            log.error("解压失败", e);
        } finally {
            FileUtil.deleteFile(dumpFile);
        }
        log.info("extractPermissionInfo listPermissions={}", listPermissions);
        return listPermissions == null ? Collections.emptyList() : listPermissions;
    }

    /**
     * 获取资源文件检测跟目录
     *
     */
    private static File getRootPath(String dumpCachePath) {
        // LOGGER.debug("antiComDirPath-->" + antiComDirPath);
        File plistFile = new File(dumpCachePath, "Payload");
        File fs[] = plistFile.listFiles();
        for (File f : fs) {
            if (f.getName().endsWith("app")) {
                plistFile = f;
                break;
            }
        }
        return plistFile;
    }

    /*
     * 查找本地合适的Info.strings文件
     * */
    private static String getLocalizableInfoPlist(String rootPath) {

        // 优先找到中文plist文件夹下的InfoPlist.strings
        String stringFilePath = rootPath + File.separator + "zh-Hans.lproj" + File.separator + "InfoPlist.strings";
        File file = new File(stringFilePath);
        if (file.exists()) {
            return stringFilePath;
        }

        // 继续寻找Base文件夹下的InfoPlist.strings
        stringFilePath = rootPath + File.separator + "Base.lproj" + File.separator + "InfoPlist.strings";
        file = new File(stringFilePath);
        if (file.exists()) {
            return stringFilePath;
        }

        // 正常寻找*.lproj/InfoPlist.strings
        File rootDirFile = new File(rootPath);
        File fs[] = rootDirFile.listFiles();
        stringFilePath = null;
        for (File f : fs) {
            if (f.getName().endsWith("lproj") && f.isDirectory()) {
                file = new File(f.getAbsolutePath() + File.separator + "InfoPlist.strings");
                if (file.exists()) {
                    stringFilePath = file.getAbsolutePath();
                    break;
                }
            }
        }

        return stringFilePath;
    }

    /*
     * 读取Info.strings plist/文本格式文件中存在的权限信息
     * */
    private static List<String> getStringFilePermisstions(String stringFilePath)
    {
        List<String> stringFilelistPermisstion = null;
        try {
            File stringFile = new File(stringFilePath);
            NSDictionary stringFileDict = (NSDictionary) PropertyListParser.parse(stringFile);
            stringFilelistPermisstion = getAppPermisstions(stringFileDict);
        } catch (PropertyListFormatException e) {
            // TODO: handle exception
            // plist格式错误, 进行文本格式解析
            stringFilelistPermisstion = getTextFilePermisstions(stringFilePath);

        } catch (Exception e) {
            // TODO: handle exception
            e.getMessage();
        }
        return stringFilelistPermisstion;
    }

    /**
     * 获取包信息
     *
     * @return
     * @throws Exception
     */
    private static List<String> getAppPermisstions(NSDictionary rootDict) throws Exception {
        List<String> listPermisstions = new ArrayList<String>();
        String strs[] = rootDict.allKeys();
        for (int i = 0; i < strs.length; i++) {
            if (!strs[i].startsWith("NS")) {
                continue;
            }
            NSObject obj = rootDict.get(strs[i]);
            if (obj instanceof NSDictionary) {
                listPermisstions.addAll(getAppPermisstions((NSDictionary)obj));
            } else {
                listPermisstions.add(strs[i]);
            }
        }
        return listPermisstions;
    }

    /*
     * 读取Info.strings文本文件中存在的权限信息
     * */
    private static List<String> getTextFilePermisstions(String textFilePath)
    {
        List<String> listPermisstions = new ArrayList<String>();
        try {
            BufferedReader filereader=new BufferedReader(new FileReader(textFilePath));
            String str;
            while((str = filereader.readLine()) != null){
                String[] strs = str.split("=");
                if (strs.length >= 2) {
                    String key = strs[0].replace("\"", "").replace(" ", "");
                    if (!key.startsWith("NS")) {
                        continue;
                    }
                    listPermisstions.add(key);
                }
            }
            filereader.close();

        } catch (Exception e) {
            // TODO: handle exception
            e.getMessage();
        }

        return listPermisstions;
    }
}
