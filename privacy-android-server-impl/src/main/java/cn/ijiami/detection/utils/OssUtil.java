package cn.ijiami.detection.utils;

import java.io.File;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;

/**
 * 腾讯文件保存工具类
 */

public class OssUtil {
//    private String secretId= "AKID0qSnsk9jKehV2Ibm26vTHq89uRhg7q3z";
//    private String secretKey="sUyDBsITN0MQFo55FAoAOM4WhIejXskO";
    private String secretId= "";
    private String secretKey="";
    private static COSClient cosClient;
    private static OssUtil ossUtil;
    //这个是存储桶Name
    private static String bucket="ijmtx-1305598689";
    private static String PathKey = "tengxunapk/";
    private static String ossUrl = "";

    //初始化该类
    private OssUtil() {
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置 bucket 的区域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
        Region region = new Region("ap-guangzhou");
        ClientConfig clientConfig = new ClientConfig(region);
        // 3 生成 cos 客户端。
        cosClient = new COSClient(cred, clientConfig);
    }

    /**
     * 实例化该类
     *
     * @return
     */
    public static OssUtil getInstance() {
        synchronized (OssUtil.class) {
            ossUtil = new OssUtil();
        }
        return ossUtil;
    }


    /**
     * 上传普通文件路径
     *
     * @param path 本地上传文件路径
     * @return 返回线上地址
     */
    public int putObject(String path) {
        File localFile = new File(path);
        String a = localFile.getName();
        String key = PathKey + a;
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, localFile);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            if (putObjectResult != null) {
                //String imageUrl = ossUrl + key;
                return 1;
            }
        } catch (Throwable e) {
            e.getMessage();
            System.out.println("上传文件失败");
        }
        return 0;
    }

    /**
     * 删除单个文件
     *
     * @param fileName 上传时的文件名称
     * @return
     */
    public int deleteObject(String fileName) {
        String key = PathKey + fileName;
        try {
            cosClient.deleteObject(bucket, key);
            return 1;
        } catch (Throwable e) {
            e.getMessage();
            System.out.println(e.toString() + "删除文件失败");
        }
        return 0;
    }

    /**
     * 下载
     * @param downSavePath  下载保存本地路径
     * @param fileName 服务器存储的文件名称（与上传名称一致）
     * @return
     */
    public int downFile (String downSavePath,String fileName){
        try{
            File localFile = new File(fileName);
            String a = localFile.getName();
            String key = PathKey + a;
            File downFile = new File(downSavePath+a);
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucket, key);
            ObjectMetadata downObjectMeta = cosClient.getObject(getObjectRequest, downFile);
            return 1;
        } catch (Throwable e) {
            e.getMessage();
        }
        return 0;
    }

    public static void main(String[] args) {

        //注意：上传时文件名称名称（t1.xlsx） 删除和下载时文件名称也必须一样
        OssUtil u = getInstance();
        //上传
//        int s = u.putObject("F:\\rBAzCWBlm1mAIZUeAUKJ-rmpwEI873.APK");
//        System.out.println("==================== :" +s);
        //删除
        /*int s1 =  u.deleteObject("1.apk");
        System.out.println("=======1111============= :" +s1);*/
        //下载 （）
//        int s2 =  u.downFile("F:\\app\\","rBAzCWBlm1mAIZUeAUKJ-rmpwEI873.APK");
//        int s2 =  u.downFile("F:\\app\\","7511e22b98d2714b0e03e7bf8531694909472418.apk");
        
//        System.out.println("=======222============= :" +s2);
    }
}