package cn.ijiami.detection.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import cn.ijiami.detection.job.TaskSortThread;

/**
 * 注册applicationContext
 *
 * <AUTHOR>
 */

@Configuration
public class ApplicationConfig implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        AppContextHandel.setApplicationContext(applicationContext);
        // bean实例化完成启动排序进程
        TaskSortThread taskSortThread = applicationContext.getBean(TaskSortThread.class);
        taskSortThread.start();
        taskSortThread.checkTaskSortListAsSoonASPossible();
    }

}
