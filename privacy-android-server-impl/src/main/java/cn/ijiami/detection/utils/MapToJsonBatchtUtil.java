package cn.ijiami.detection.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;
import java.util.HashMap;

public class MapToJsonBatchtUtil {
	
	// 单例模式创建 ObjectMapper 实例
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String mapToJson(Map<String, Object> map) {
        try {
            return mapper.writeValueAsString(map);
        } catch (Exception e) {
            e.getMessage();
            return JSONObject.toJSONString(map);
        }
    }

    // 分批处理
    public static String mapToJsonBatch(Map<String, Object> map, int batchSize) {
        StringBuilder sb = new StringBuilder();
        int size = map.size();
        int numBatches = (size + batchSize - 1) / batchSize;

        for (int i = 0; i < numBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, size);

            Map<String, Object> batch = new HashMap<>();
            int index = 0;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (index >= start && index < end) {
                    batch.put(entry.getKey(), entry.getValue());
                }
                index++;
            }

            try {
//                sb.append(mapper.writeValueAsString(batch));
                sb.append(JSONObject.toJSONString(batch));
            } catch (Exception e) {
                e.getMessage();
                sb.append(JSONObject.toJSONString(batch));
            }
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        // 填充 map 数据
        map.put("key1", "value1");
        map.put("key2", "value2");

        String body = mapToJson(map);
        System.out.println(body);

        // 分批处理示例
        String bodyBatch = mapToJsonBatch(map, 1000);
        System.out.println(bodyBatch);
    }
}
