package cn.ijiami.detection.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 常量类
 * 
 * <AUTHOR>
 *
 */
public final class ConstantsUtils {
	/**
	 * 安卓后缀
	 */
	public static final String SUFFIX_APK = "APK";

	public static final String SUFFIX_AAB = "AAB";

	public static final String SUFFIX_IOS = "IPA";

	/**
	 * 鸿蒙
	 */
	public static final String SUFFIX_HAP = "HAP";

	/**
	 * 鸿蒙
	 */
	public static final String SUFFIX_END_HAP = ".hap";

	public static final String SUFFIX_PNG = ".png";

	public static final String SUFFIX_IPA = ".ipa";

	public static final String SUFFIX_END_APK = ".apk";
	
	public static final long BYTENUM = 1024;
	
	public static final String IOSSTARTPATH="/common/ios_detection/tool/startDetect1";
	
	//内容检测
	public static final String SUFFIX_TEXT = ".txt";
	public static final String TEXTSTARTPATH="/common/detection/start_detect";
	public static final String UPLOADFILE="/common/detection/upload_file";
	public static final String STOP_DETECT="/common/detection/stop_detect";
	public static final String SINGLE_FILE_DETECTION="/common/detection/single_file_detection";

	/**
	 * 用于上传资产
	 */
	public static final String FILE_NAME_SEPARATOR = "_";
	/**
	 * 文件有效信息
	 */
	public static final String ASSET_SIGN = "ijiami_";

	public static final int LAW_191_PRIVACY_POLICY_MIN_LENGTH = 500;
	
	/**
	 * 报告下载勾选编号
	 */
	//	存在风险report
	public static final String REPORT_NUM_1001 = "1001";
	//	未发现风险
	public static final String REPORT_NUM_1002 = "1002";
	//	权限使用情况
	public static final String REPORT_NUM_2001 = "2001";
	//	应用行为分析
	public static final String REPORT_NUM_2002 = "2002";
	//	通讯行为分析
	public static final String REPORT_NUM_2003 = "2003";
	//	传输个人信息
	public static final String REPORT_NUM_2004 = "2004";
	//	存储个人信息
	public static final String REPORT_NUM_2005 = "2005";
	//	SDK分析
	public static final String REPORT_NUM_2006 = "2006";
	//	个人信息风险漏洞
	public static final String REPORT_NUM_2007 = "2007";
	//	小程序引用插件
	public static final String REPORT_NUM_2008 = "2008";
	//	小程序服务及数据
	public static final String REPORT_NUM_2009 = "2009";
	//	存在风险
	public static final String REPORT_NUM_3001 = "3001";
	//	未发现风险
	public static final String REPORT_NUM_3002 = "3002";
	//行为文件编号
	public static final String REPORT_NUM_4001 = "4001";
	//	35273存在风险
	public static final String REPORT_NUM_5001 = "5001";
	//	35273未发现风险
	public static final String REPORT_NUM_5002 = "5002";
	//	41391存在风险
	public static final String REPORT_NUM_6001 = "6001";
	//	41391未发现风险
	public static final String REPORT_NUM_6002 = "6002";
	
	//行为文件下载查询编码
	public static final int ACTION_NUM_99 = 99;

	/**
	 * ui解析时隐私政策的文本判断长度
	 */
	public static final int PARSER_PRIVACY_DETAIL_TEXT_LENGTH = 300;
	
	//大量隐私政策对比后大于1500比较合理
	public static final int PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH = 1200;

    // 申请隐私弹窗的最大文本
    public static final int APPLY_POLICY_DETAIL_MAX_TEXT_LENGTH = 700;

    //个人信息文本判断词
    public static final String PRIVACY_CONTENT_WORD = "个人信息保护政策|隐私权政策|隐私政策|隐私保护声明|隐私保护政策";

    public static final int SDK_USAGE_STATISTICS_LIMIT = 20;

    public static final int DEFAULT_STATISTICS_LIMIT = 10;
    public static final int STATISTICS_UNLIMITED = -1;

    public static final String LAW_164 = "164";
    public static final String LAW_191 = "191";
    public static final String LAW_35273 = "35273";
    public static final String LAW_41391 = "41391";
    
    public static Map<Long, Long> deepDetectionStartTemp = new ConcurrentHashMap<>();

    /**
     * 常用接口
     */
    public static class Url {
        // IP归属地查询
        public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp?ip=%s&json=true";
    }
}
