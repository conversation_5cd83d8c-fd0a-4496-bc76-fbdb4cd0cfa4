package cn.ijiami.detection.utils;

import cn.hutool.core.util.XmlUtil;
import cn.ijiami.detection.entity.TBroadcast;
import cn.ijiami.framework.apk.entity.ApkInfo;
import cn.ijiami.framework.apk.entity.ImpliedFeatureVo;
import cn.ijiami.framework.apk.mediator.ApkMediator;
import com.alibaba.fastjson.JSON;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import test.AXMLPrinter;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * apk帮助类
 * 
 * <AUTHOR>
 *
 */
public class APKUtil {
	private static final Logger LOG = LoggerFactory.getLogger(APKUtil.class);

	/**
	 * 判断是否是apk
	 * 
	 * @param apkPath
	 * @return
	 * @throws IOException
	 */
	@SuppressWarnings("finally")
	public static boolean checkIsApk(String apkPath) throws IOException {
		ZipFile zf = null;
		boolean isApk = false;
		try {
			int count = 0;
			zf = new ZipFile(apkPath); // 支持中文
			Enumeration<?> e = zf.entries(); // 遍历Zip文件夹目录
			while (e.hasMoreElements()) {
				ZipEntry zentry = (ZipEntry) e.nextElement();
				String pathName = zentry.getName();
				if (pathName.contains("res") || pathName.contains("AndroidManifest.xml")
						|| pathName.contains("classes.dex") || pathName.contains("resources.arsc")) {
					count++;
				}
			}
			if (count >= 2) {// 只要包含两种，就认为是apk
				isApk = true;
			}
		} catch (Exception e) {
			LOG.error(String.format("解压失败%s", e.getMessage()));
			e.getMessage();
		} finally {
			if (null != zf) {
				zf.close();
			}
			return isApk;
		}
	}

	public static ApkInfo readApkInfo(String apkPath) {
		ApkMediator apkMediator = new ApkMediator();
		BufferedReader reader = null;
		try {
		    reader = apkMediator.open(apkPath).getReader();
			String line = reader.readLine();
			if (line == null) {
				throw new Exception("参数不正确，无法正常解析APK包。");
			}
			LOG.info("readApkInfo line={}", line);
			ApkInfo apkInfo = new ApkInfo();
			do {
				setApkInfoProperty(apkInfo, line);
			} while ((line = reader.readLine()) != null);
			
			if(StringUtils.isBlank(apkInfo.getApplicationLable())) {
				throw new Exception("解析APK失败，获取不到应用名称");
			}
			return apkInfo;
		} catch (Exception e) {
			LOG.error("解析APK失败", e);
			return null;
		} finally {
			try {
				reader.close();
				apkMediator.close();
			} catch (IOException e) {
				e.getMessage();
			}
			
		}
	}

	public static final String VERSION_CODE = "versionCode";
	public static final String VERSION_NAME = "versionName";
	public static final String SDK_VERSION = "sdkVersion";
	public static final String TARGET_SDK_VERSION = "targetSdkVersion";
	public static final String USES_PERMISSION = "uses-permission";
	public static final String APPLICATION_LABEL = "application-label";
	public static final String APPLICATION_ICON = "application-icon";
	public static final String USES_FEATURE = "uses-feature";
	private static final String USES_IMPLIED_FEATURE = "uses-implied-feature";
	public static final String SUPPORTS_SCREENS = "supports-screens";
	public static final String SUPPORTS_ANY_DENSITY = "supports-any-density";
	public static final String DENSITIES = "densities";
	public static final String PACKAGE = "package";
	public static final String APPLICATION = "application:";
	public static final String LAUNCHABLE_ACTIVITY = "launchable-activity";
	private static final String SPLIT_REGEX = "(: )|(=')|(' )|'";
	private static final String FEATURE_SPLIT_REGEX = "(:')|(',')|'";

	private static void setApkInfoProperty(ApkInfo apkInfo, String source) {
		try {
			if (source.startsWith(PACKAGE)) {
				splitPackageInfo(apkInfo, source);
			} else if (source.startsWith(LAUNCHABLE_ACTIVITY)) {
				apkInfo.setLaunchableActivity(getPropertyInQuote(source));
				if(StringUtils.isBlank(apkInfo.getApplicationLable()) && source.contains("label")) {
					apkInfo.setApplicationLable(getPropertyInQuoteLabel(source));
				}
			} else if (source.startsWith(SDK_VERSION)) {
				apkInfo.setSdkVersion(getPropertyInQuote(source));
			} else if (source.startsWith(TARGET_SDK_VERSION)) {
				apkInfo.setTargetSdkVersion(getPropertyInQuote(source));
			} else if (source.startsWith(USES_PERMISSION)) {
				apkInfo.addToUsesPermissions(getPropertyInQuote(source));
			} else if (source.startsWith(APPLICATION_LABEL) || source.startsWith("application: label") || source.contains("label")) {
				if (source.startsWith(APPLICATION_LABEL) && StringUtils.isBlank(apkInfo.getApplicationLable())) {
					apkInfo.setApplicationLable(getPropertyInQuote(source));
				} else if (StringUtils.isBlank(apkInfo.getApplicationLable()) && ( source.startsWith("application-label-zh") || source.startsWith("application-label-zh_CN")
						|| source.startsWith("application: label") || source.startsWith(APPLICATION_LABEL))) {
					apkInfo.setApplicationLable(getPropertyInQuote(source));
				} else if(source.contains("label") && StringUtils.isBlank(apkInfo.getApplicationLable())) {
					apkInfo.setApplicationLable(getPropertyInQuoteLabel(source));
				}
				
				//中文名称
				if (source.toLowerCase().startsWith("application-label-zh") && !source.toLowerCase().contains("application-label-zh-hk") && !source.toLowerCase().contains("application-label-zh-tw")) {
					String lable = getPropertyInQuote(source);
					apkInfo.setApplicationLable(StringUtils.isBlank(lable)?apkInfo.getApplicationLable():lable);
				}
			}
			
			if (source.startsWith(APPLICATION_ICON)) {
				apkInfo.addToApplicationIcons(getKeyBeforeColon(source), getPropertyInQuote(source));
			} else if (source.startsWith(APPLICATION)) {
				String[] rs = source.split("( icon=')|'");
				if (rs.length <= 2) {
					apkInfo.setApplicationIcon("");
				} else {
					apkInfo.setApplicationIcon(rs[rs.length - 1]);
				}
			} else if (source.startsWith(USES_FEATURE)) {
				apkInfo.addToFeatures(getPropertyInQuote(source));
			} else if (source.startsWith(USES_IMPLIED_FEATURE)) {
				apkInfo.addToImpliedFeatures(getFeature(source));
			} else {
			}
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}
	}

	private static String getPropertyInQuoteLabel(String source) {
		int index = source.indexOf("label='") + 7;
		return source.substring(index, source.indexOf('\'', index));
	}

	private static ImpliedFeatureVo getFeature(String source) {
		String[] result = source.split(FEATURE_SPLIT_REGEX);
		ImpliedFeatureVo impliedFeature = new ImpliedFeatureVo(result[1], result[2]);
		return impliedFeature;
	}

	private static String getPropertyInQuote(String source) {
		int index = source.indexOf("'") + 1;
		return source.substring(index, source.indexOf('\'', index));
	}

	private static String getKeyBeforeColon(String source) {
		return source.substring(0, source.indexOf(':'));
	}

	private static void splitPackageInfo(ApkInfo apkInfo, String packageSource) {
		String[] packageInfo = packageSource.split(SPLIT_REGEX);
		apkInfo.setPackageName(packageInfo[2]);
		apkInfo.setVersionCode(packageInfo[4]);
		apkInfo.setVersionName(packageInfo.length > 6 ? packageInfo[6] : null);
	}
	
	@SuppressWarnings("resource")
	public static ApkMeta parserReadApkInfo(String apkPath) throws Exception{
		ApkMeta apkMeta = null;
		try {
			ApkFile apkFile = new ApkFile(new File(apkPath));
			apkMeta = apkFile.getApkMeta();
			LOG.info(JSON.toJSONString(apkMeta));
		} catch (Exception e) {
			e.getMessage();
			LOG.info("");
			throw new Exception("无法正常解析APK包。");
		}
		return apkMeta;
	}
	
	/**
     * 获取正在检测的apk的接收的对应的广播的action
     * @param apkPath
     * @return K：class V：action集合
     */
    public static Map<String,String> getBroadcastPackageNameMap(String apkPath) {
        Map<String,String> map = new HashMap<>();
        try {
            String xml = AXMLPrinter.getManifestXMLFromAPK(apkPath);
            NodeList nodeList = XmlUtil.parseXml(xml).getElementsByTagName("receiver");

            for (int i = 0; i < nodeList.getLength(); i++) {
                Node item = nodeList.item(i);
                if(!item.hasAttributes()){
                    continue;
                }
                Node namedItem = item.getAttributes().getNamedItem("android:name");
                if (namedItem == null) {
                    continue;
                }
                map.put(namedItem.getNodeValue(),XmlUtil.toStr(item));
            }
        }catch (Exception e){
           e.getMessage();
        }
        return map;
    }
	
	public static List<TBroadcast> sendStandardBroadcast(String packageName, String apkPath, List<TBroadcast> baseList) {
    	List<TBroadcast> broadcastList = new ArrayList<>();
		try {
			Map<String,String> broadcastPackageNameMap = getBroadcastPackageNameMap(apkPath);
			for (TBroadcast action : baseList) {
			    if (!broadcastPackageNameMap.isEmpty()) {
			        for (Map.Entry<String, String> stringStringEntry : broadcastPackageNameMap.entrySet()) {
			            if (stringStringEntry.getValue().contains(action.getCode())) {
			            	TBroadcast broadcast = new TBroadcast();
			            	broadcast.setCode(action.getCode());
			            	broadcast.setDescription(action.getDescription());
//			            	broadcast.setReceiverPackage(stringStringEntry.getKey());
			            	broadcastList.add(broadcast);
			                System.out.println("获取到广播为：" + "shell am broadcast -f 0x01000000 -a " + action + " -n " + packageName + "/" + stringStringEntry.getKey());
			            }
			        }
			    }
			}
		} catch (Exception e) {
			e.getMessage();
		}
        return broadcastList;
    }
	
	public static void main(String[] args) {
//		String apkPath = "F:/salehouse.apk";
//		ApkInfo info = readApkInfo(apkPath);
//		System.out.println(JSON.toJSONString(info));
//		{"applicationIcon":"res/mipmap-anydpi-v26/ic_launcher.xml","applicationIcons":{"application-icon-65534":"res/mipmap-anydpi-v26/ic_launcher.xml","application-icon-320":"res/mipmap-anydpi-v26/ic_launcher.xml","application-icon-640":"res/mipmap-anydpi-v26/ic_launcher.xml","application-icon-240":"res/mipmap-anydpi-v26/ic_launcher.xml","application-icon-160":"res/mipmap-anydpi-v26/ic_launcher.xml","application-icon-480":"res/mipmap-anydpi-v26/ic_launcher.xml"},"applicationLable":"中原找房","features":[],"impliedFeatures":[],"launchableActivity":"com.centaline.androidsalesblog.ui.LauncherActivity","packageName":"com.centaline.androidsalesblog","sdkVersion":"21","targetSdkVersion":"28","usesPermissions":["com.centaline.androidsalesblog.permission.JPUSH_MESSAGE","android.permission.CAMERA","com.centaline.androidsalesblog.permission.MIPUSH_RECEIVE","android.permission.BLUETOOTH","android.permission.BLUETOOTH_ADMIN","android.permission.MODIFY_AUDIO_SETTINGS","android.permission.BROADCAST_STICKY","android.permission.READ_PHONE_STATE","android.permission.CHANGE_NETWORK_STATE","com.centaline.androidsalesblog.permission.RECEIVE_MSG","com.asus.msa.SupplementaryDID.ACCESS","freemme.permission.msa","android.permission.INTERNET","android.permission.READ_EXTERNAL_STORAGE","android.permission.WRITE_EXTERNAL_STORAGE","android.permission.ACCESS_COARSE_LOCATION","android.permission.ACCESS_LOCATION_EXTRA_COMMANDS","android.permission.ACCESS_FINE_LOCATION","android.permission.ACCESS_WIFI_STATE","android.permission.ACCESS_NETWORK_STATE","android.permission.CHANGE_WIFI_STATE","android.permission.CALL_PHONE","android.permission.RECORD_AUDIO","android.permission.REQUEST_INSTALL_PACKAGES","android.permission.FLASHLIGHT","android.permission.VIBRATE","android.permission.READ_LOGS","android.permission.INTERACT_ACROSS_USERS","android.permission.GET_TASKS","android.permission.WRITE_SETTINGS","android.permission.WAKE_LOCK","android.permission.SYSTEM_ALERT_WINDOW","android.permission.RECEIVE_USER_PRESENT","android.permission.MOUNT_UNMOUNT_FILESYSTEMS","com.huawei.android.launcher.permission.CHANGE_BADGE","android.permission.ACCESS_BACKGROUND_LOCATION","com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE","com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE","com.centaline.androidsalesblog.permission.PROCESS_PUSH_MSG","com.centaline.androidsalesblog.permission.PUSH_PROVIDER","android.permission.RECEIVE_BOOT_COMPLETED","android.permission.FOREGROUND_SERVICE","com.huawei.appmarket.service.commondata.permission.GET_COMMON_DATA"],"versionCode":"7210","versionName":"7.2.1"}
//		apkPath = "F:/salehouse.apk";
//		info = readApkInfo(apkPath);
//		System.out.println(JSON.toJSONString(info));
		
		
		String apkPath = "F:\\download\\rAEy2WONsWOAQYhMBdx0vDdsQrE569.APK";
//		try {
//			
//			ApkInfo info = readApkInfo(apkPath);
////			System.out.println(info.getApplicationLable());
////			System.out.println(JSON.toJSONString(info));
////			ApkMeta apkInfo = parserReadApkInfo(apkPath);
////			ApkUtil.extractFileFromApk(apkPath, apkInfo.getIcon(), "f:/abc.png");
//			StackTraceElement[] stackTrace = new Throwable().getStackTrace();
//		} catch (Exception e1) {
//			e1.printStackTrace();
//		}
		
		try {
//			ApkFile apkFile = new ApkFile(new File(apkPath));
//			ApkMeta apkMeta = apkFile.getApkMeta();
//			System.out.println(apkFile.getManifestXml());
//          System.out.println(apkFile.getApkSingers());
//          System.out.println(apkFile.verifyApk());
//			System.out.println(JSON.toJSONString(apkMeta));
//			System.out.println(apkFile.getAllCertificateMetas());
//			System.out.println(apkFile.getAllIcons());
//			System.out.println(apkFile.getApkSingers());
//			System.out.println(apkFile.getApkV2Singers());
//			System.out.println(apkFile.getIconFile());
//			System.out.println(apkMeta.getLabel());
//			System.out.println(apkMeta.getPackageName());
//			System.out.println(apkMeta.getVersionCode());
//			System.out.println(apkMeta.getVersionName());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.getMessage();
		}
		
		String filePath = "G:\\WeChat\\WeChat Files\\wxid_5xc3sugftlju21\\FileStorage\\File\\2023-04\\智慧社区-残联平台.apk";
		String apkSignInfo = cn.ijiami.framework.apk.util.ApkUtil.getApkSign(filePath);
		System.out.println(apkSignInfo);
	}
}
