package cn.ijiami.detection.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UriUtils.java
 * @Description uri解析类
 * @createTime 2021年12月17日 15:09:00
 */
@Slf4j
public class UriUtils {

    public static String getHttpPath(String uri) {
        if (uri.contains("http://") || uri.contains("https://")) {
            try {
                String path = URI.create(uri).getRawPath();
                return path.startsWith("/") ? path.substring(1) : path;
            } catch (IllegalArgumentException e) {
                log.error("uri解析错误", e);
                return uri;
            }
        } else {
            if (uri.startsWith("/")) {
                return uri.substring(1);
            } else {
                return uri;
            }
        }
    }

    public static String getHttpUrl(String path, String host) {
        return host + "/" + getHttpPath(path);
    }

}
