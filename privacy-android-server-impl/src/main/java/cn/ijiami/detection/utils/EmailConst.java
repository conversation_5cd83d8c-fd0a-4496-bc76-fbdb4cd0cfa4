package cn.ijiami.detection.utils;

public class EmailConst {
	public static final String HOST_PREFIX = "spring.mail.host=";

    public static final String PORT_PREFIX = "spring.mail.port=";

    public static final String USERNAME_PREFIX = "spring.mail.username=";

    public static final String PASSWORD_PREFIX = "spring.mail.password=";

    public static final String PROTOCAL_PREFIX = "spring.mail.protocol=";

    public static final String ENCODING_PREFIX = "spring.mail.default-encoding=";


    public static final String HOST_KEY_PREFIX = "spring.mail.host";

    public static final String PORT_KEY_PREFIX = "spring.mail.port";

    public static final String USERNAME_KEY_PREFIX = "spring.mail.username";

    public static final String PASSWORD_KEY_PREFIX = "spring.mail.password";

    public static final String PROTOCAL_KEY_PREFIX = "spring.mail.protocol";

    public static final String ENCODING_KEY_PREFIX = "spring.mail.default-encoding";

    //閭欢閰嶇疆璧峰琛�
    public static final String MAIL_CONFIG_START = "##spring email configuration";
}
