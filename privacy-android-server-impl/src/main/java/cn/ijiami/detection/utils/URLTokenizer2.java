package cn.ijiami.detection.utils;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper;

public class URLTokenizer2 {

	//URL正则
	private static final Pattern WEB_URL = Pattern.compile(
			"((?:(http|https|Http|Https):\\/\\/(?:(?:[a-zA-Z0-9\\$\\-\\_\\.\\+\\!\\*\\'\\(\\)\\,\\;\\?\\&\\=]|(?:\\%[a-fA-F0-9]{2})){1,64}(?:\\:(?:[a-zA-Z0-9\\$\\-\\_\\.\\+\\!\\*\\'\\(\\)\\,\\;\\?\\&\\=]|(?:\\%[a-fA-F0-9]{2})){1,25})?\\@)?)?((?:(?:[a-zA-Z0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF][a-zA-Z0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF\\-]{0,64}\\.)+(?:(?:aero|arpa|asia|a[cdefgilmnoqrstuwxz])|(?:biz|b[abdefghijmnorstvwyz])|(?:cat|com|coop|c[acdfghiklmnoruvxyz])|d[ejkmoz]|(?:edu|e[cegrstu])|f[ijkmor]|(?:gov|g[abdefghilmnpqrstuwy])|h[kmnrtu]|(?:info|int|i[delmnoqrst])|(?:jobs|j[emop])|k[eghimnprwyz]|l[abcikrstuvy]|(?:mil|mobi|museum|m[acdeghklmnopqrstuvwxyz])|(?:name|net|n[acefgilopruz])|(?:org|om)|(?:pro|p[aefghklmnrstwy])|qa|r[eosuw]|s[abcdeghijklmnortuvyz]|(?:tel|travel|t[cdfghjklmnoprtvwz])|u[agksyz]|v[aceginu]|w[fs]|(?:xn\\-\\-0zwm56d|xn\\-\\-11b5bs3a9aj6g|xn\\-\\-80akhbyknj4f|xn\\-\\-9t4b11yi5a|xn\\-\\-deba0ad|xn\\-\\-g6w251d|xn\\-\\-hgbk6aj7f53bba|xn\\-\\-hlcj6aya9esc7a|xn\\-\\-jxalpdlp|xn\\-\\-kgbechtv|xn\\-\\-zckzah)|y[etu]|z[amw]))|(?:(?:25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9])\\.(?:25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(?:25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(?:25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[0-9])))(?:\\:\\d{1,5})?)(\\/(?:(?:[a-zA-Z0-9\\;\\/\\?\\:\\@\\&\\=\\#\\~\\-\\.\\+\\!\\*\\'\\(\\)\\,\\_])|(?:\\%[a-fA-F0-9]{2}))*)?(?:\\b|$)");
	
	
	//获取PDF连接
	public static List<String> segment(String text) {
		List<String> termList = new LinkedList<String>();
		Matcher matcher = WEB_URL.matcher(text);
		while (matcher.find()) {
			String machUrl = matcher.group();
			if(machUrl==null){
				continue;
			}
			if(!machUrl.toLowerCase().startsWith("http")) {
				continue;
			}
			termList.add(machUrl);
		}
		return termList;
	}
	
	public static List<String> segmentUrl(String text) {
		List<String> termList = new LinkedList<String>();
		Matcher matcher = WEB_URL.matcher(text);
		while (matcher.find()) {
			String machUrl = matcher.group();
			if(machUrl==null){
				continue;
			}
			termList.add(machUrl);
		}
		return termList;
	}
	
	
	public static List<String> threadSegment(List<String> list) throws InterruptedException, ExecutionException{
		List<String> newList = new ArrayList<String>();
		// 每10条数据开启一条线程
        int threadSize = 1;
        // 总数据条数
        int dataSize = list.size();
        // 线程数
        int threadNum = dataSize / threadSize + 1;
        // 定义标记,过滤threadNum为整数
        boolean special = dataSize % threadSize == 0;

        // 创建一个线程池
        ExecutorService exec = Executors.newFixedThreadPool(threadNum);
        // 定义一个任务集合
        List<Callable<List<String>>> tasks = new ArrayList<Callable<List<String>>>();
        Callable<List<String>> task = null;
        List<String> cutList = null;

        // 确定每条线程的数据
        for (int i = 0; i < threadNum; i++) {
            if (i == threadNum - 1) {
                if (special) {
                    break;
                }
                cutList = list.subList(threadSize * i, dataSize);
            } else {
                cutList = list.subList(threadSize * i, threadSize * (i + 1));
            }

            final List<String> listStr = cutList;
            task = new Callable<List<String>>() {
                @Override
                public List<String> call() throws Exception {
//                    System.out.println(Thread.currentThread().getName() + "线程：" + listStr);
                    List<String> res = new ArrayList<>();
                    listStr.forEach(path->{
                    	try {
                    		List<String> termList = segment(Text2Pic.readTxtFile1(path));
                    		if(termList!=null && termList.size()>0) {
                    			res.addAll(termList);
                    		}
            			} catch (Exception e) {
            				e.getMessage();
            			}
                    });
                    return res;
                }
            };
            tasks.add(task);
        }

        List<Future<List<String>>> results = exec.invokeAll(tasks);

        for (Future<List<String>> future : results) {
            newList.addAll(future.get());
        }
        // 关闭线程池
        exec.shutdown();
		return newList;
	}

	public static void main(String[] args) {
//		String txtPath = "C:/Users/<USER>/Downloads/且慢_20220422094601/205f0b6c85faa32e5bce73c24cfa1b77_26605_AUTO/capture_info/";
//		List<String> filePaths = CommonUtil.listAllFilesInDir(txtPath, new String[]{".txt",".TXT"}, true);
//		if(filePaths==null || filePaths.size()==0) {
//			return;
//		}
//		long t1 = System.currentTimeMillis();
//		try {
//			List<String> list = threadSegment(filePaths);
//			System.out.println(list);
//		} catch (InterruptedException e) {
//			e.printStackTrace();
//		} catch (ExecutionException e) {
//			e.printStackTrace();
//		}
//		
//		long t2 = System.currentTimeMillis();
//		System.out.println("识别耗时："+(t2-t1));
		
//		String txtPath = "C:/Users/<USER>/Downloads/且慢_20220422094601/205f0b6c85faa32e5bce73c24cfa1b77_26605_AUTO/";
//		List<PrivacyPolicyTextInfo> list = new ArrayList<>();
//		try {
//			pdfPrivacyContent(txtPath, list);
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		
//		if(list!=null) {
//			for (PrivacyPolicyTextInfo privacyPolicyTextInfo : list) {
//				System.out.println(privacyPolicyTextInfo.content);
//			}
//		}
		
		
		String text = "https://hweb-channel.huazhu.com/channel/detail/queryChannelDetailProduct?articleId=8742&cityName=上海&latitude=0.0&longitude=0.0&newCurrentCityName=";
		List<String> list = segmentUrl(text);
		list.forEach(str->{
			System.out.println(str);
			try {
				URL u = new URL(str);
				System.out.println(u.getProtocol());
				System.out.println(u.getPort());
				System.out.println(u.getHost());
				System.out.println(u.getAuthority());
			} catch (Exception e) {
				e.getMessage();
			}
		});
	}
}
