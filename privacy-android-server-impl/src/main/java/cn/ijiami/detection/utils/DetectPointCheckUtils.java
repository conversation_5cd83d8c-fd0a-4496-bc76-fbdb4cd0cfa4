package cn.ijiami.detection.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPointCheckUtils.java
 * @Description
 * @createTime 2022年04月25日 14:49:00
 */
public class DetectPointCheckUtils {


    /**
     * 往前找是否有相关文字内容描述的界面
     * @param resultDataLogBOList
     * @param beginIndex
     * @return
     */
    protected ResultDataLogBO findBeforeAuthorPopup(List<ResultDataLogBO> resultDataLogBOList, int beginIndex) {
        // 往前找是否有相关文字内容描述的界面
        for (; beginIndex>=0; beginIndex--) {
            ResultDataLogBO resultDataLogBO = resultDataLogBOList.get(beginIndex);
            if (equalsUiType(resultDataLogBO, Arrays.asList(
                    MiitUITypeEnum.BEFORE_AUTHOR_POPUP, MiitUITypeEnum.APPLY_POLICY))) {
                return resultDataLogBO;
            } else if (equalsUiType(resultDataLogBO, MiitUITypeEnum.POLICY_DETAIL)
                    // 超过1000个字的是隐私详情了
                    && resultDataLogBO.getUiDumpResult().getFullText().length() < 1000) {
                return resultDataLogBO;
            }
        }
        return null;
    }
    /**
     * 往前找是否有相关文字内容描述的界面
     * @param resultDataLogBOList
     * @param beginIndex
     * @return
     */
    protected ResultDataLogBO findBeforePolicyPopup(List<ResultDataLogBO> resultDataLogBOList, int beginIndex) {
        // 往前找是否有相关文字内容描述的界面
        for (; beginIndex>=0; beginIndex--) {
            ResultDataLogBO result = resultDataLogBOList.get(beginIndex);
            if (isPolicyPopup(result)) {
                return result;
            }
        }
        return null;
    }

    protected boolean equalsUiType(ResultDataLogBO resultDataLogBO, MiitUITypeEnum type) {
        return equalsUiType(resultDataLogBO, Collections.singletonList(type));
    }

    protected boolean equalsUiType(ResultDataLogBO resultDataLogBO, List<MiitUITypeEnum> typeList) {
        return resultDataLogBO.getUiDumpResult() != null &&
                typeList.stream().anyMatch(miitUITypeEnum -> miitUITypeEnum.getValue() == resultDataLogBO.getUiDumpResult().getUiType());
    }

    protected boolean isPolicyPopup(ResultDataLogBO result) {
        return equalsUiType(result, Arrays.asList(MiitUITypeEnum.POLICY_DETAIL, MiitUITypeEnum.APPLY_POLICY))
                || equalsUiType(result, MiitUITypeEnum.OTHER) && result.getUiDumpResult().getFullText().contains("权限");
    }

    protected boolean isPermissionPopup(ResultDataLogBO result) {
        return equalsUiType(result, Arrays.asList(MiitUITypeEnum.AGREE_PERMISSION, MiitUITypeEnum.APPLY_PERMISSION, MiitUITypeEnum.DISAGREE_PERMISSION));
    }
}
