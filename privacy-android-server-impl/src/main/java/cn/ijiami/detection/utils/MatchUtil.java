package cn.ijiami.detection.utils;

import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Set;

/**
 * 随机数生成
 * 
 * <AUTHOR>
 *
 */
public class MatchUtil {
	/**
	 * 随机指定范围内N个不重复的数 最简单最基本的方法
	 * 
	 * @param min 指定范围最小值
	 * @param max 指定范围最大值
	 * @param n   随机数个数
	 */
//	public static int[] randomCommon(int min, int max, int n) {
//		if (n > (max - min + 1) || max < min) {
//			return null;
//		}
//		int[] result = new int[n];
//		int count = 0;
//		while (count < n) {
//			int num = (int) (Math.random() * (max - min)) + min;
//			boolean flag = true;
//			for (int j = 0; j < n; j++) {
//				if (num == result[j]) {
//					flag = false;
//					break;
//				}
//			}
//			if (flag) {
//				result[count] = num;
//				count++;
//			}
//		}
//		return result;
//	}
	
	public static int[] randomCommon(int min, int max, int n) {
	    // 参数校验
	    if (max < min || n > (max - min + 1) || n < 0) {
	        return new int[0]; // 返回空数组表示无效输入
	    }

	    // 使用 SecureRandom 提高随机性安全性
	    SecureRandom secureRandom = new SecureRandom();
	    Set<Integer> resultSet = new HashSet<>();
	    
	    // 生成 n 个不重复的随机数
	    while (resultSet.size() < n) {
	        int num = secureRandom.nextInt(max - min + 1) + min; // 生成 [min, max] 范围内的随机数
	        resultSet.add(num); // 利用 HashSet 自动去重
	    }

	    // 将结果转换为数组
	    int[] result = new int[n];
	    int index = 0;
	    for (int num : resultSet) {
	        result[index++] = num;
	    }

	    return result;
	}
	
	/**
	 * 转化成String
	 */
	public static String convertoString(int[] array) {
		StringBuilder sb = new StringBuilder();
		for (int i : array) {
			sb.append(i);
		}
		return sb.toString();
	}
}
