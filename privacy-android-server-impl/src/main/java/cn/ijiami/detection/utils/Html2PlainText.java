package cn.ijiami.detection.utils;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;

public class Html2PlainText {
	public static String convert(String html) {
		if (StringUtils.isEmpty(html)) {
			return ("");
		}
		Document document = Jsoup.parse(html);
		Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);
		document.outputSettings(outputSettings);
		document.select("br").append("\\n");
		document.select("p").prepend("\\n");
		document.select("p").append("\\n");
		String newHtml = document.html().replaceAll("\\\\n", "\n");
		String plainText = Jsoup.clean(newHtml, "", Whitelist.none(), outputSettings);
		String result = StringEscapeUtils.unescapeHtml(plainText.trim());
		return (result);
	}
	
	public static void main(String[] args) {
//		String html = Text2Pic.readStringFile("F:/privacy");
//    	System.out.println(html);
//    	String t = convert(html);
//    	System.out.println(t);
    	
//    	System.out.println("------------------------");
//    	String result = StringEscapeUtils.unescapeHtml(html);
//    	System.out.println(result);
    	
	}
}
