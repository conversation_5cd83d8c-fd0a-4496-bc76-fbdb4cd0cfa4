package cn.ijiami.detection.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @ClassName: HtmlToPdfInterceptor
 * @Description: TODO()
 * @date 2016-12-8 上午10:17:33
 */

public class HtmlToPdfInterceptor extends Thread {
    private InputStream is;

    public HtmlToPdfInterceptor(InputStream is) {
        this.is = is;
    }

    @Override
    public void run() {
        try {
            InputStreamReader isr = new InputStreamReader(is, "utf-8");
            BufferedReader br = new BufferedReader(isr);
            String line = null;
            while ((line = br.readLine()) != null) {
                //输出内容
                System.out.println(line);
            }
        } catch (IOException e) {
            e.getMessage();
        }
    }
}