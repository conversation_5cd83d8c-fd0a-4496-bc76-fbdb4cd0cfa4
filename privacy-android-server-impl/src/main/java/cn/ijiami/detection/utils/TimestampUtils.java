package cn.ijiami.detection.utils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TimestampUtils.java
 * @Description 时间工具
 * @createTime 2025年02月24日 18:35:00
 */

public class TimestampUtils {

    /**
     * 获取当月的开始时间戳（毫秒级）
     *
     * @return 当月开始时间的时间戳
     */
    public static long getStartOfMonthTimestamp() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 调整到当月的第一天
        LocalDate startOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        // 设置时间为当天的零点零分零秒
        LocalDateTime startOfMonthDateTime = startOfMonth.atStartOfDay();
        // 转换为时间戳（毫秒级）
        return startOfMonthDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取当年的开始时间戳（毫秒级）
     *
     * @return 当年开始时间的时间戳
     */
    public static long getStartOfYearTimestamp() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 调整到当年的第一天
        LocalDate startOfYear = now.with(TemporalAdjusters.firstDayOfYear());
        // 设置时间为当天的零点零分零秒
        LocalDateTime startOfYearDateTime = startOfYear.atStartOfDay();
        // 转换为时间戳（毫秒级）
        return startOfYearDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}