package cn.ijiami.detection.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.ijiami.ios.shelltools.shellUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * ipa脱壳对接工具类
 *
 * <AUTHOR>
 * @date 2020-06-13 11:30
 */
public class IpaShellInfoUtils {

    private IpaShellInfoUtils() {
    }

    /**
     * 获取appId请求路径
     */
    private static final String GET_IPA_APP_ID    = "https://itunes.apple.com/cn/lookup?bundleId=";
    /**
     * 获取bundleId请求路径
     */
    private static final String GET_IPA_BUNDLE_ID = "https://itunes.apple.com/cn/lookup?id=";

    private static final String JSON_KEY_RESULT_COUNT = "resultCount";
    private static final String JSON_KEY_RESULTS      = "results";
    private static final String JSON_KEY_BUNDLE_ID    = "bundleId";
    private static final String JSON_KEY_TRACK_ID     = "trackId";

    /**
     * 检测ipa是否加壳
     *
     * @param path ipa路径
     * @param tool 工具路径
     * @return
     * @throws Exception
     */
    public static boolean isHasShell(String path, String tool) throws Exception {
        return shellUtils.isHasShell(path, tool);
    }

    /**
     * 通过appId获取bundleId
     *
     * @param appId
     * @return
     */
    public static String getBundleIdByAppId(String appId) {
        JSONObject jsonObject = HttpUtils.httpGet(GET_IPA_BUNDLE_ID + appId);
        // 是否存在有效结果
        if (!isResultValid(jsonObject)) {
            return null;
        }
        JSONArray results = jsonObject.getJSONArray(JSON_KEY_RESULTS);
        JSONObject result = results.getJSONObject(0);
        return result.getString(JSON_KEY_BUNDLE_ID);
    }

    /**
     * 通过bundleId获取appId
     *
     * @param bundleId
     * @return
     */
    public static String getAppIdByBundleId(String bundleId) {
        JSONObject jsonObject = HttpUtils.httpGet(GET_IPA_APP_ID + bundleId);
        // 是否存在有效结果
        if (!isResultValid(jsonObject)) {
            return null;
        }
        JSONArray results = jsonObject.getJSONArray(JSON_KEY_RESULTS);
        JSONObject result = results.getJSONObject(0);
        return result.getString(JSON_KEY_TRACK_ID);
    }

    /**
     * 校验获取到的数据是否有效
     *
     * @param jsonObject
     * @return
     */
    public static boolean isResultValid(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return false;
        }
        if (!jsonObject.containsKey(JSON_KEY_RESULT_COUNT) || !jsonObject.containsKey(JSON_KEY_RESULTS)) {
            return false;
        }
        int resultCount = jsonObject.getInt(JSON_KEY_RESULT_COUNT);
        if (resultCount < 1) {
            return false;
        }
        return true;
    }
    
    private static final String models = "{\"iPhone1,1\":\"iPhone\",\"iPhone1,2\":\"iPhone 3G\",\"iPhone2,1\":\"iPhone 3GS\",\"iPhone3,1\":\"iPhone 4\",\"iPhone3,2\":\"iPhone 4\",\"iPhone3,3\":\"iPhone 4\",\"iPhone4,1\":\"iPhone 4S\",\"iPhone5,1\":\"iPhone 5\",\"iPhone5,2\":\"iPhone 5\",\"iPhone5,3\":\"iPhone 5c\",\"iPhone5,4\":\"iPhone 5c\",\"iPhone6,1\":\"iPhone 5s\",\"iPhone6,2\":\"iPhone 5s\",\"iPhone7,2\":\"iPhone 6\",\"iPhone7,1\":\"iPhone 6 Plus\",\"iPhone8,1\":\"iPhone 6s\",\"iPhone8,2\":\"iPhone 6s Plus\",\"iPhone8,4\":\"iPhone SE\",\"iPhone9,1\":\"iPhone 7\",\"iPhone9,3\":\"iPhone 7\",\"iPhone9,2\":\"iPhone 7 Plus\",\"iPhone9,4\":\"iPhone 7 Plus\",\"iPhone10,1\":\"iPhone 8\",\"iPhone10,4\":\"iPhone 8\",\"iPhone10,2\":\"iPhone 8 Plus\",\"iPhone10,5\":\"iPhone 8 Plus\",\"iPhone10,3\":\"iPhone X\",\"iPhone10,6\":\"iPhone X\",\"iPhone11,8\":\"iPhone XR\",\"iPhone11,6\":\"iPhone XS Max\",\"iPhone12,1\":\"iPhone 11\",\"iPhone13,1\":\"iPhone 12 mini\",\"iPhone13,2\":\"iPhone 12\",\"iPhone14,5\":\"iPhone 13\",\"iPhone14,7\":\"iPhone 14\"}";
    public static String iphoneModels(String identifier){
    	if(org.apache.commons.lang3.StringUtils.isBlank(identifier)) {
    		return "";
    	}
    	com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(models);
    	String generation = json.get(identifier)== null ? "":json.getString(identifier);
    	
    	return generation;
    }
    
    public static String iphoneGeneration(String generation){
    	if(org.apache.commons.lang3.StringUtils.isBlank(generation)) {
    		return "";
    	}
    	com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(models);
    	for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
    	     if(generation.equals(entry.getValue())) {
    	    	 return entry.getKey();
    	     }
    	}
    	return "";
    }
    
    //https://www.theiphonewiki.com/wiki/Models
    public static List<String> iphoneGenerations(String generation){
    	if(org.apache.commons.lang3.StringUtils.isBlank(generation)) {
    		return null;
    	}
    	List<String> list = new ArrayList<>();
    	com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(models);
    	for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
    	     if(generation.equals(entry.getValue())) {
    	    	 list.add(entry.getKey());
    	     }
    	}
    	return list;
    }

    public static void main(String[] args) {
//        String s = "com.lemon.lv";
//        String appId = getAppIdByBundleId(s);
//        String bundleId = getBundleIdByAppId(appId);
//        System.err.println(appId);
//        System.err.println(bundleId);
//        String path =
//                "L3p5d2EvaWppYW1pL2ZpbGVzL2RlZmF1bHQvNTQ1ZDM5YjEtNDAyOS00YzNiLThlNmMtYWQ0YjIwZDEyNzBlX2Y3NzEwOGI2YWUzZmUwYjY1YzFmYmY0ZGExZWZkOGNmMTU4OTk1NzQ4NjY0OC5hcGs=";
//        System.err.println(FileUtil.decodeData(path));
        
//        String identifier = "iPhone8,1";
//        String generation = iphoneModels(identifier);
//        System.out.println(iphoneModels(identifier));
//        System.out.println(iphoneGeneration(generation));
//        
////        String base = "[{\"proVersion\":\"14.7.1\",\"devicename\":\"iPhone\",\"deviceID\":\"4cb08838a66ec502e7a9c452975886f6e3920a09\",\"productType\":\"iPhone8,1\",\"deviceState\":1}]";
////        JSONArray array = JSONArray.fromObject(base);
////        List<IdbDevice> deviceList = (List<IdbDevice>) JSONArray.toList(array, IdbDevice.class);
////        IdbDevice dev = deviceList.remove(0);
////        System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(dev));
//        
////        账号：MGHY
////        原密码：Aa123456
////        新密码：Aa123123
//        String password = "cb16979fd5ba7497bbcec4b1157aad11";
//        System.out.println(PasswordUtil.encode(password, "MGHY"));
//        
//        String base = "[{\"deviceId\":\"96dd7d45a28391f60d80425d0e20cfffa5ae1a01\",\"deviceName\":\"checkr1n_2\",\"deviceState\":0,\"proVersion\":\"14.1\",\"productType\":\"iPhone10,3\"},{\"deviceId\":\"4ceaea4b8534b40ce782eedb0165aa3084eb7fc3\",\"deviceName\":\"iPhone\",\"deviceState\":0,\"proVersion\":\"15.8.1\",\"productType\":\"iPhone8,1\"},{\"deviceId\":\"92f775756f6db12a1cdd812c7b70fd4b73154d10\",\"deviceName\":\"checkr1n_1\",\"deviceState\":0,\"proVersion\":\"14.7.1\",\"productType\":\"iPhone8,1\"},{\"deviceId\":\"8e606e76913ab46d2ff4b4eddad8e2b0823f4e51\",\"deviceName\":\"checkr1n_4\",\"deviceState\":0,\"proVersion\":\"14.4.2\",\"productType\":\"iPhone10,3\"},{\"deviceId\":\"2f333b5ec48f8b7670b9bffbba6703e5d186c54b\",\"deviceName\":\"checkr1n_5\",\"deviceState\":0,\"proVersion\":\"14.7.1\",\"productType\":\"iPhone10,3\"}]";
//        JSONArray array = JSONArray.fromObject(base);
//        List<IdbDevice> deviceList = (List<IdbDevice>) JSONArray.toList(array, IdbDevice.class);
//        String version = "14.7.1";
//      //型号(iphone 6s)-->identifier(iPhone8,1)
//    	String model = IpaShellInfoUtils.iphoneGeneration("iPhone 6s");
//    	for (IdbDevice idbDevice : deviceList) {
//    		
//    		if(StringUtils.isNotBlank(model) && StringUtils.isNotBlank(version) && 
//    				idbDevice.getProductType().equals(model) && 
//    				idbDevice.getProVersion().equals(version)) {
//    			System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
//    		}
//    		
//    		if(StringUtils.isNotBlank(model) && StringUtils.isBlank(version) && 
//    				idbDevice.getProductType().equals(model)) {
//    			System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
//			}
//    		
//    		if(StringUtils.isBlank(model) && StringUtils.isNotBlank(version) && 
//    				idbDevice.getProVersion().equals(version)) {
//    			System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
//			}
    		
//    		if(StringUtils.isNotBlank(model) && 
//    				idbDevice.getProductType().equals(model) && 
//    				StringUtils.isNotBlank(version) && 
//    				idbDevice.getProVersion().equals(version)){
//    			System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
//    			break;
//    		} else{
//    			if((StringUtils.isNotBlank(model) && idbDevice.getProductType().equals(model)) ||  
//        				(StringUtils.isNotBlank(version) && version.equals(idbDevice.getProVersion()))) {
//        			System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(idbDevice));
//        			break;
//        		}
//    		}
//		}
    }

}
