package cn.ijiami.detection.utils;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import com.ijm.ios.RuntimeDetection.data.InfoAct;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionNougatUtils.java
 * @Description 行为工具
 * @createTime 2022年12月13日 09:58:00
 */
@Slf4j
public class ActionNougatUtils {

    public static void setTriggerCycle(TPrivacyActionNougat nougat) {
        nougat.setTriggerCycle(nougat.getTriggerCycleTime() == null || nougat.getTriggerCycleTime() == 0
                ? PinfoConstant.DETAILS_EMPTY : String.valueOf(nougat.getTriggerCycleTime() / 1000));
    }



    private static final DateTimeFormatter MILLIS_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter SECOND_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static Long parseLogTime(InfoAct iosLog) {
        try {
            return LocalDateTime.parse(iosLog.getStrTime(), MILLIS_FORMAT).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            // 有可能是毫秒数缺失
            int last = iosLog.getStrTime().lastIndexOf(".");
            if (last > 0) {
                String newDate = iosLog.getStrTime().substring(0, last);
                try {
                    return LocalDateTime.parse(newDate, SECOND_FORMAT).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                } catch (Exception e2) {
                    log.error("parseLogTime failure", e2);
                    return 0L;
                }
            } else {
                log.error("parseLogTime failure", e);
                return 0L;
            }
        }
    }

}