package cn.ijiami.detection.utils;

import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TransactionSynchronizationUtils.java
 * @Description
 * @createTime 2021年11月18日 16:19:00
 */
public class TransactionSynchronizationUtils {


    public static void afterCommit(TransactionSynchronization func) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    func.afterCommit();
                }
            });
        } else {
            func.afterCommit();
        }
    }


}
