package cn.ijiami.detection.utils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HttpPacketFormatter.java
 * @Description HttpPacketFormatter
 * @createTime 2025年03月18日 17:20:00
 */

public class HttpPacketFormatter {

    // 定义所有支持的HTTP方法
    private static final Set<String> HTTP_METHODS = new HashSet<>(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT"
    ));

    public static String formatHttpPacket(String rawPacket) {
        if (isBlank(rawPacket)) {
            return "";
        }

        // 预处理 - 处理冒号后面的换行符
        rawPacket = rawPacket.replaceAll(":\\s*\n", ":");

        // 找到正文的起始位置（第一个非头部字段的内容）
        int bodyStartIndex = findBodyStartIndex(rawPacket);
        if (bodyStartIndex == -1) {
            return rawPacket; // 如果没有找到正文，直接返回原始字符串
        }

        // 分割响应头和正文
        String headersPart = rawPacket.substring(0, bodyStartIndex).trim();
        String bodyPart = rawPacket.substring(bodyStartIndex).trim();

        // 解析头部字段（包括伪头部字段和普通头部字段）
        List<String> formattedHeaders = parseHeaders(headersPart);

        // 构造目标头部
        StringBuilder result = new StringBuilder();
        for (String header : formattedHeaders) {
            result.append(header).append(" \n");
        }

        // 返回最终结果，如果有正文则添加正文
        String finalResult = result.toString();

        // 移除末尾多余的换行标记（如果有）
        if (finalResult.endsWith("\n")) {
            finalResult = finalResult.substring(0, finalResult.length() - 1);
        }

        if (!isBlank(bodyPart)) {
            finalResult += bodyPart;
        }

        return finalResult;
    }

    private static int findBodyStartIndex(String rawPacket) {
        // 尝试找到第一个 { 或 [ 的位置，这可能是JSON数据的开始
        int curlyBraceIndex = rawPacket.indexOf('{');
        int squareBracketIndex = rawPacket.indexOf('[');

        // 如果没有找到 { 或 [，则尝试找到最后一个头部字段的结束位置
        if (curlyBraceIndex == -1 && squareBracketIndex == -1) {
            // 找到最后一个头部字段的结束位置（以空格或换行分隔）
            int lastHeaderEndIndex = rawPacket.lastIndexOf('\n');
            if (lastHeaderEndIndex == -1) {
                lastHeaderEndIndex = rawPacket.lastIndexOf(' ');
            }
            return lastHeaderEndIndex + 1; // 正文从最后一个空格或换行后开始
        }

        // 如果找到了 { 或 [，返回其位置
        if (curlyBraceIndex == -1) {
            return squareBracketIndex;
        }
        if (squareBracketIndex == -1) {
            return curlyBraceIndex;
        }
        return Math.min(curlyBraceIndex, squareBracketIndex);
    }

    private static List<String> parseHeaders(String headersPart) {
        List<String> headers = new ArrayList<>();

        // 将输入按换行符或空格分割成可能的头部块
        String normalizedHeaders = headersPart.replaceAll("\n", " ").trim();
        String[] parts = normalizedHeaders.split("\\s+");

        StringBuilder currentHeader = new StringBuilder();

        // 处理请求行或响应行（第一行）
        if (parts.length >= 3) {
            if (isHttpMethod(parts[0])) {
                // HTTP请求，如 GET /favicon.ico HTTP/1.1
                headers.add(parts[0] + " " + parts[1] + " " + parts[2]);
            } else if (parts[0].startsWith("HTTP/")) {
                // HTTP响应，如 HTTP/1.1 200 OK
                headers.add(parts[0] + " " + parts[1] + " " + parts[2]);
            }
        }

        // 处理剩余的头部字段
        for (int i = 3; i < parts.length; i++) {
            String part = parts[i];

            if (part.contains(":")) {
                // 如果已经有一个正在处理的头部，先添加它
                if (currentHeader.length() > 0) {
                    headers.add(currentHeader.toString());
                    currentHeader = new StringBuilder();
                }

                // 开始一个新的头部
                currentHeader.append(part);
            } else if (currentHeader.length() > 0) {
                // 当前部分属于前一个头部的值
                currentHeader.append(" ").append(part);
            }
        }

        // 添加最后一个头部（如果有）
        if (currentHeader.length() > 0) {
            headers.add(currentHeader.toString());
        }

        return headers;
    }

    // 检查字符串是否为HTTP方法
    private static boolean isHttpMethod(String method) {
        return HTTP_METHODS.contains(method);
    }

    // 替代StringUtils.isBlank
    private static boolean isBlank(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
}