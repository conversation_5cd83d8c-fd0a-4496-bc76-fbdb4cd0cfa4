package cn.ijiami.detection.utils;

import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.tools.ant.Project;
import org.apache.tools.ant.taskdefs.Expand;
import org.apache.tools.ant.taskdefs.Zip;
import org.apache.tools.ant.types.FileSet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.ijiami.detection.VO.ReportResultVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.kit.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;

/**
 * <AUTHOR>
 * @date 2019-05-31 10:20
 */
@Slf4j
public class CommonUtil {

    public static final String[] IMAGE_EXT_NAMES = new String[]{".jpg", ".jpeg", ".png", ".bmp"};

    public static String getIpLocation(String ip) {
        String ipSearchApi = String.format("http://ip.tool.chinaz.com/%s", ip);
        String result = "未知";
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(ipSearchApi);
            HttpResponse httpResponse = httpClient.execute(httpGet);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = httpResponse.getEntity();
                String jsonStr = EntityUtils.toString(entity, "utf-8");
                int startIndex = jsonStr.indexOf("w50-0\">");
                startIndex = jsonStr.indexOf("w50-0\">", startIndex + 1);
                if (-1 != startIndex) {
                    startIndex += "w50-0\">".length();
                    int endIndex = jsonStr.indexOf("</span>", startIndex);
                    result = jsonStr.substring(startIndex, endIndex);
                    result = jsonStr.substring(startIndex, jsonStr.indexOf(" ", startIndex));
                }
            }
        } catch (Exception e) {
            ipSearchApi = String.format("http://www.baidu.com/s?wd=%s", ip);
            result = "未知";
            try {
                HttpClient httpClient = new DefaultHttpClient();
                HttpGet httpGet = new HttpGet(ipSearchApi);
                HttpResponse httpResponse = httpClient.execute(httpGet);
                if (httpResponse.getStatusLine().getStatusCode() == 200) {
                    HttpEntity entity = httpResponse.getEntity();
                    String jsonStr = EntityUtils.toString(entity, "utf-8");
                    int startIndex = jsonStr.indexOf(">IP地址:");
                    startIndex = jsonStr.indexOf("</span>", startIndex);
                    if (-1 != startIndex) {
                        startIndex += "</span>".length();
                        int endIndex = jsonStr.indexOf(" ", startIndex);
                        result = jsonStr.substring(startIndex, endIndex);
                    }
                }
            } catch (Exception e1) {
                e1.getMessage();
                result = "未知";
            }
        }
        return result;
    }

    /**
     * 根据IP地址获取地理位置
     * <br>
     * 淘宝ip查询工具做了限流，会影响查询效率，慎用
     *
     * @param ip IP地址
     * @return 地理位置
     */
    public static String getIPAddress(String ip) {
        String url = String.format("http://ip.taobao.com/service/getIpInfo.php?ip=%s", ip);
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            CloseableHttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String string = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            JSONObject jsonObject = JSON.parseObject(string);
            if (jsonObject.getIntValue("code") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                String address = data.getString("country") + data.getString("area") + data.getString("region")
                        + data.getString("city");
                return address.replaceAll("XX", "");
            }
            return "未知";
        } catch (IOException e) {
            e.getMessage();
            return "未知";
        }
    }

    /**
     * 压缩文件
     *
     * @param srcDir  压缩源路径
     * @param desPath 压缩目标文件路径
     * @return 压缩成功返回目标文件路径
     */
    public static String compress(String srcDir, String desPath) {
        File dir = new File(srcDir);
        File zipFile = new File(desPath);
        if (dir.exists()) {
            try {
                Project p = new Project();
                FileSet fileSet = new FileSet();
                fileSet.setProject(p);
                fileSet.setDir(dir);

                Zip zip = new Zip();
                zip.setProject(p);
                zip.setDestFile(zipFile);
                zip.setEncoding("utf-8");
                zip.addFileset(fileSet);
                zip.execute();
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return desPath;
    }

    /**
     * 解压缩文件
     *
     * @param filePath       文件路径
     * @param uncompressPath 解压之后的路径
     * @return 返回解压缩路径
     */
    public static String uncompress(String filePath, String uncompressPath) {
        if (null == filePath)
            return null;
        if (null == uncompressPath || "".equals(uncompressPath))
            uncompressPath = "./uncompress";// 若未给出解压路径,则使用当前程序路径的uncompress文件夹作为解压之后的路径
        File tempFile = new File(uncompressPath);
        if (tempFile.exists() && null != tempFile.list())
            return uncompressPath;// 若文件夹存在，且文件夹下存在子文件，则认为已经解压过，无需再次解压，直接返回
        try {
            Project p = new Project();
            Expand ep = new Expand();
            ep.setProject(p);
            ep.setSrc(new File(filePath));
            ep.setOverwrite(false);
            ep.setDest(new File(uncompressPath));
            ep.execute();
        } catch (Exception e) {
            e.getMessage();
        }
        return uncompressPath;// 返回解压缩之后的路径
    }

    /**
     * 解压缩文件
     *
     * @param filePath       文件路径
     * @param uncompressPath 解压之后的路径
     * @return 返回解压缩路径
     */
    public static String uncompressOverwrite(String filePath, String uncompressPath) {
        if (null == filePath)
            return null;
        if (null == uncompressPath || uncompressPath.isEmpty())
            uncompressPath = "./uncompress";// 若未给出解压路径,则使用当前程序路径的uncompress文件夹作为解压之后的路径
        try {
            Project p = new Project();
            Expand ep = new Expand();
            ep.setProject(p);
            ep.setSrc(new File(filePath));
            ep.setOverwrite(false);
            ep.setDest(new File(uncompressPath));
            ep.execute();
        } catch (Exception e) {
            throw new UncompressFailException(e);
        }
        return uncompressPath;// 返回解压缩之后的路径
    }

    /**
     * 解压缩文件
     *
     * @param filePath       文件路径
     * @param uncompressPath 解压之后的路径
     * @return 返回解压缩路径
     */
    public static String uncompressTaskData(String filePath, String uncompressPath) {
        if (null == filePath)
            return null;
        if (null == uncompressPath || uncompressPath.isEmpty())
            uncompressPath = "./uncompress";// 若未给出解压路径,则使用当前程序路径的uncompress文件夹作为解压之后的路径
        File tempFile = new File(uncompressPath);
        if (tempFile.exists() && null != tempFile.list())
            return uncompressPath;// 若文件夹存在，且文件夹下存在子文件，则认为已经解压过，无需再次解压，直接返回
        try {
            Project p = new Project();
            Expand ep = new Expand();
            ep.setProject(p);
            ep.setSrc(new File(filePath));
            ep.setOverwrite(false);
            ep.setDest(new File(uncompressPath));
            ep.execute();
        } catch (Exception e) {
            throw new UncompressFailException(e);
        }
        return uncompressPath;// 返回解压缩之后的路径
    }

    /**
     * 获取目录下给定文件后缀的所有文件路径
     *
     * @param dir       需要搜索的目录
     * @param endkeys   需要搜索的文件后缀类型,若为null,则返回所有文件
     * @param recursion 是否递归搜索,false不递归搜索,true递归搜索
     * @return 返回所有搜索到的文件路径
     */
    public static List<String> listAllFilesInDir(String dir, String[] endkeys, boolean recursion) {
        List<String> result = new ArrayList<String>();
        File[] files = new File(dir).listFiles();
        if (null != files && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (recursion)
                        result.addAll(listAllFilesInDir(file.getAbsolutePath(), endkeys, recursion));// 若为目录，则递归搜索
                } else if (null != endkeys) {
                    String temp = file.getName();
                    for (String endkey : endkeys)
                        if (temp.endsWith(endkey))
                            result.add(file.getAbsolutePath().replaceAll("\\\\", "/"));// 若为给定类型，则获取绝对路径并加入结果链表
                } else
                    result.add(file.getAbsolutePath().replaceAll("\\\\", "/"));// fileTypes为null,则将所有类型文件加入结果链表
            }
        }
        return result;// 返回结果
    }

    /**
     * 删除文件或文件夹
     *
     * @param filePath 文件或文件夹路径
     */
    public static void deleteFile(String filePath) {
        if (null == filePath || filePath.equals(""))
            return;
        File file = new File(filePath);
        if (file.exists()) {
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File childFile : files) {
                    if (childFile.isDirectory())
                        deleteFile(childFile.getAbsolutePath());
                    childFile.delete();
                }
            }
            file.delete();
        }
    }

    /**
     * 判断字符串是否为打印字符
     *
     * @param str 字符串
     * @return 结果
     */
    public static boolean isPrint(String str) {
        if (StringUtils.isEmpty(str)) {
            return true;
        }

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == 10 || c == 9 || c == 13) { // \n \t \r
                continue;
            }

            if (c <= 31 || c >= 127)
                return false;
        }
        return true;
    }

    /**
     * 根据域名获取ip地址
     *
     * @param host 域名
     * @return ip地址
     */
    public static String getAddressByHost(String host) {
        InetAddress address;
        try {
            address = InetAddress.getByName(host.trim());
        } catch (UnknownHostException e) {
            e.getMessage();
            return "未知";
        }

        String ip = address.getHostAddress();
        return CommonUtil.getIpLocation(ip);
    }

    /**
     * 读取文本文件到字符串
     *
     * @param filePath 文本文件路径
     * @return 返回文本文件字符串
     */
    public static String readFileToString(String filePath) {
        StringBuffer stringBuffer = new StringBuffer();
        InputStream is = null;
        BufferedReader reader = null;
        try {
            is = new FileInputStream(filePath);
            reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
            String line = null;
            while (null != (line = reader.readLine())) {
                stringBuffer.append(line);
                stringBuffer.append("\n");
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            CommonUtil.closeIO(reader);
            CommonUtil.closeIO(is);
        }
        return stringBuffer.toString();
    }

    /**
     * 读取文本文件到字符串,舍弃非打印字符
     *
     * @param filePath 文本文件路径
     * @return 返回文本文件字符串
     */
    public static String readFileToPrintString(String filePath) {
        StringBuilder stringBuffer = new StringBuilder();
        InputStream is = null;
        BufferedReader reader = null;
        try {
            is = new FileInputStream(filePath);
            reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
            String line = null;
            while (null != (line = reader.readLine())) {
                if (!CommonUtil.isPrint(line)) continue;
                stringBuffer.append(line);
                stringBuffer.append("\n");
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            CommonUtil.closeIO(reader);
            CommonUtil.closeIO(is);
        }
        return stringBuffer.toString();
    }

    private static final Pattern PRINT_STARING_PATTERN = Pattern.compile("[ -~\n]{8,}");

    public static String filterPrintString(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        Matcher matcher = PRINT_STARING_PATTERN.matcher(str);
        StringJoiner joiner = new StringJoiner(" ");
        while (matcher.find()) {
            joiner.add(matcher.group());
        }
        return joiner.toString();
    }


    /**
     * 输入输出流关闭
     *
     * @param io 输入输出流对象
     */
    public static void closeIO(Closeable io) {
        if (null == io)
            return;
        try {
            io.close();
        } catch (Exception e) {
            e.getMessage();
        }
    }

    public static final int VERSION_GT = 1;
    public static final int VERSION_LT = -1;

    public static boolean isGtVersion(String version1, String version2) {
        return compareVersion(version1, version2) == VERSION_GT;
    }

    /**
     * 对比版本号，左边大于右边返回 1 相等返回0，小于返回-1
     * @param version1
     * @param version2
     * @return
     */
    public static int compareVersion(String version1, String version2) {
        if (version1.equals(version2)) {
            return 0;
        }
        String[] version1Array = version1.split("\\.");
        String[] version2Array = version2.split("\\.");
        int index = 0;
        int minLen = Math.min(version1Array.length, version2Array.length);
        BigInteger zero = new BigInteger("0");
        BigInteger diff = null;
        BigInteger version1BigInt = new BigInteger(version1Array[index]);
        BigInteger version2BigInt = new BigInteger(version2Array[index]);
        while (index < minLen && (diff = version1BigInt.subtract(version2BigInt)).equals(zero)) {
            index ++;
        }
        if (diff.equals(zero)) {
            for (int i = index; i < version1Array.length; i ++) {
                if (new BigInteger(version1Array[i]).compareTo(zero) > 0) {
                    return VERSION_GT;
                }
            }
            for (int i = index; i < version2Array.length; i ++) {
                if (new BigInteger(version2Array[i]).compareTo(zero) > 0) {
                    return VERSION_LT;
                }
            }
            return 0;
        } else {
            return diff.compareTo(zero) > 0 ? VERSION_GT : VERSION_LT;
        }
    }

    public static String getDomainNameAndPort(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        Matcher matcher = Pattern.compile("(http(s)?://)?(([\\w-]+\\.)+\\w+(:\\d{1,5})?)").matcher(url);
        if (matcher.find()) {
            return matcher.group(3);
        } else {
            return "";
        }
    }

    public static <T> List<T> jsonToList(String json, Class<T> clazz) {
        return jsonToBean(json, new TypeReference<List<T>>() {
            @Override
            public Type getType() {
                return TypeUtils.parameterize(ArrayList.class, clazz);
            }
        }, false);
    }

    public static <T> T jsonToBean(String json, TypeReference<T> toValueTypeRef) {
        return jsonToBean(json, toValueTypeRef, false);
    }
    public static <T> T jsonToBean(String json, TypeReference<T> toValueTypeRef, boolean failOnUnknown) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return getObjectMapper(failOnUnknown).readValue(json, toValueTypeRef);
        } catch (IOException e) {
            log.info("jsonConvertToBean failure={}", e.getMessage());
            return null;
        }
    }

    public static <T> T jsonToBean(String json, Class<T> clazz) {
        return jsonToBean(json, clazz, false);
    }

    public static <T> T jsonToBean(String json, Class<T> clazz, boolean failOnUnknown) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return getObjectMapper(failOnUnknown).readValue(json, clazz);
        } catch (IOException e) {
            log.info("jsonConvertToBean failure={}", e.getMessage());
            return null;
        }
    }


    public static ObjectMapper getObjectMapper(boolean failOnUnknown) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, failOnUnknown);
        objectMapper.enable(JsonParser.Feature.ALLOW_SINGLE_QUOTES);
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        return objectMapper;
    }

    public static String beanToJson(Object object) {
        if (Objects.isNull(object)) {
            return StringUtils.EMPTY;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.getMessage();
            return StringUtils.EMPTY;
        }
    }

    //正则匹配，除字母、数字、[\ / . - _]、中文外其他字符全部转换为_
    private final static Pattern PATH_PATTERN=Pattern.compile("[^0-9a-zA-Z_\\-.\\u4E00-\\u9FA5\\u9FA6-\\u9FEF\\\\/]");
    public static String filterFileName(String str){
        String newStr=str;
        if(StringUtils.isEmpty(str)){
            return newStr;
        }
        Matcher matcher = PATH_PATTERN.matcher(str);
        if(matcher.find()){
            newStr=matcher.replaceAll("_");
        }
        return newStr;
    }

    /**
     * 解析存在mongodb文档里面的app TargetSdkVersion
     * @param taskDetail
     * @return
     */
    public static int findTargetSdkVersion(TaskDetailVO taskDetail) {
        int targetSdkVersion = 0;
        net.sf.json.JSONArray array = net.sf.json.JSONArray.fromObject(taskDetail.getDetection_result());
        for (int i = 0; i < array.size(); i++) {
            try {
                net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(array.get(i));
                // 获取基础信息
                if (json.get("detection_item_id") != null && json.get("detection_item_id").equals("0101")) {
                    net.sf.json.JSONObject resultContent = getResultContent(json);
                    if (resultContent.containsKey("targetSdkVersion")) {
                        targetSdkVersion = resultContent.getInt("targetSdkVersion");
                    }
                    break;
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return targetSdkVersion;
    }

    public static net.sf.json.JSONObject getResultContent(net.sf.json.JSONObject json) {
        try {
			String result_content = json.getString("result_content");
			result_content = result_content.replaceAll("NumberLong", "");
			result_content = result_content.replaceAll("\\(", "");
			result_content = result_content.replaceAll("\\)", "");
			return net.sf.json.JSONObject.fromObject(result_content);
		} catch (Exception e) {
			e.getMessage();
		}
        return null;
    }

    public static net.sf.json.JSONObject getResultContent(TaskDetailVO taskDetailVO) {
        JSONArray array = JSONArray.fromObject(taskDetailVO.getDetection_result());
        for (Object o : array) {
            net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(o);
            if (json.isNullObject()) {
                log.info("isNullObject {}", o);
            } else if (json.optString("detection_item_id").equals("0101")) {
                return CommonUtil.getResultContent(json);
            }
        }
        log.info("isNullObject end {}", taskDetailVO.getDetection_result());
        return null;
    }

    public static String getIosExecutableName(TaskDetailVO taskDetailVO) {
        net.sf.json.JSONObject appInfo = getResultContent(taskDetailVO);
        String executableName = appInfo == null ? StringUtils.EMPTY : appInfo.optString("executableName");
        return StringUtils.isBlank(executableName) ? analysisApplicationName(taskDetailVO.getApk_package()) : executableName;
    }

    public static String analysisApplicationName(String packageName) {
        if (StringUtils.isNotBlank(packageName)) {
            int index = packageName.lastIndexOf(".");
            if (index >= 0 && index + 1 < packageName.length()) {
                return packageName.substring(index + 1);
            }
        }
        return StringUtils.EMPTY;
    }

    //判断字符串是否是base64位编码
    public static boolean isBase64(String str) {
        if (str == null || str.trim().length() == 0) {
            return false;
        } else {
            if (str.length() % 4 != 0) {
                return false;
            }

            char[] strChars = str.toCharArray();
            for (char c:strChars) {
                if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9')
                        || c == '+' || c == '/' || c == '=') {
                } else {
                    return false;
                }
            }
            return true;
        }
    }


    //判断字符串中是否包含乱码
    public static boolean hasMessyCode(String str){
        return !java.nio.charset.Charset.forName("GBK").newEncoder().canEncode(str);
    }

    /**
     * 判断是base64位字符就转码，最多进行两次转码
     * @param str
     * @return
     */
    public static String checkBase64(String str){
        if (StringUtils.isBlank(str)){
            return str;
        }
        if (CommonUtil.isBase64(str)){
            str = FileUtil.decodeData(str);
            if (CommonUtil.isBase64(str)) {
                return FileUtil.decodeData(str);
            }
        }
        if (CommonUtil.hasMessyCode(str)) {
            return CommonUtil.filterPrintString(str);
        }
        return str;
    }

    public static boolean containsItemNo(List<String> itemList, String target) {
        return containsItemNo((String[]) itemList.toArray(), target);
    }

    public static boolean containsItemNo(String[] itemArray, String target) {
        String first = itemArray[0];
        int begin = target.length() - first.length();
        if (begin > 0 && begin < target.length()) {
            return ArrayUtils.contains(itemArray, target.substring(begin));
        } else {
            return ArrayUtils.contains(itemArray, target);
        }
    }

    public static boolean strOrContains(String text, String... words) {
        for (String word : words) {
            if (StringUtils.contains(text, word)) {
                return true;
            }
        }
        return false;
    }

    public static boolean strAndContains(CharSequence seq, String... searchSeqs) {
        for (String searchSeq : searchSeqs) {
            if (!StringUtils.contains(seq, searchSeq)) {
                return false;
            }
        }
        return true;
    }

    public static String getFileExtName(String fileName) {
        int extNameIndex = fileName.lastIndexOf(".");
        int begin = extNameIndex + 1;
        if (extNameIndex > 0 && begin < fileName.length()) {
            int end = fileName.lastIndexOf("?");
            if (end > 0 && end > begin) {
                return fileName.substring(begin, end).toUpperCase();
            } else {
                return fileName.substring(begin).toUpperCase();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getLaw41391ByTerminalType(TerminalTypeEnum terminalType) {
        if (terminalType == TerminalTypeEnum.IOS) {
            return PrivacyLawId.IOS_LAW_41391.id;
        } else if (terminalType == TerminalTypeEnum.ANDROID) {
            return PrivacyLawId.ANDROID_LAW_41391.id;
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            return PrivacyLawId.WECHAT_APPLET_LAW_41391.id;
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            return PrivacyLawId.ALIPAY_APPLET_LAW_41391.id;
        } else if (terminalType == TerminalTypeEnum.HARMONY) {
            return PrivacyLawId.HARMONY_LAW_41391.id;
        }else {
            throw new IjiamiRuntimeException("terminalType lawId not found");
        }
    }
    public static Integer getLaw35273ByTerminalType(TerminalTypeEnum terminalType) {
        if (terminalType == TerminalTypeEnum.IOS) {
            return PrivacyLawId.IOS_LAW_35273.id;
        } else if (terminalType == TerminalTypeEnum.ANDROID) {
            return PrivacyLawId.ANDROID_LAW_35273.id;
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            return PrivacyLawId.WECHAT_APPLET_LAW_35273.id;
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            return PrivacyLawId.ALIPAY_APPLET_LAW_35273.id;
        } else if (terminalType == TerminalTypeEnum.HARMONY) {
            return PrivacyLawId.HARMONY_LAW_35273.id;
        } else {
            throw new IjiamiRuntimeException("terminalType lawId not found");
        }
    }

    public static Integer getLaw191ByTerminalType(TerminalTypeEnum terminalType) {
        if (terminalType == TerminalTypeEnum.IOS) {
            return PrivacyLawId.IOS_LAW_191.id;
        } else if (terminalType == TerminalTypeEnum.ANDROID) {
            return PrivacyLawId.ANDROID_LAW_191.id;
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            return PrivacyLawId.WECHAT_APPLET_LAW_191.id;
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            return PrivacyLawId.ALIPAY_APPLET_LAW_191.id;
        } else if (terminalType == TerminalTypeEnum.HARMONY) {
            return PrivacyLawId.HARMONY_LAW_191.id;
        } else {
            throw new IjiamiRuntimeException("terminalType lawId not found");
        }
    }

    public static Integer getLaw164ByTerminalType(TerminalTypeEnum terminalType) {
        if (terminalType == TerminalTypeEnum.IOS) {
            return PrivacyLawId.IOS_LAW_164.id;
        } else if (terminalType == TerminalTypeEnum.ANDROID) {
            return PrivacyLawId.ANDROID_LAW_164.id;
        } else if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            return PrivacyLawId.WECHAT_APPLET_LAW_164.id;
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            return PrivacyLawId.ALIPAY_APPLET_LAW_164.id;
        } else if (terminalType == TerminalTypeEnum.HARMONY) {
            return PrivacyLawId.HARMONY_LAW_164.id;
        } else {
            throw new IjiamiRuntimeException("terminalType lawId not found");
        }
    }

    public static String getHost(String url) throws MalformedURLException {
        return new URL(url).getHost();
    }

    /**
     * 读取文件
     *
     * @param file       文件内容
     * @return 解析后的字符串
     */
    public static List<String> readeFile(File file) {
        // 空或者是目录
        if (Objects.isNull(file) || file.isDirectory()) {
            return Collections.emptyList();
        }
        if (!file.exists()) {
            return Collections.emptyList();
        }
        List<String> lines = new ArrayList<>();
        // 处理文件流操作
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while((line = reader.readLine()) != null) {
                if (StringUtils.isNotBlank(line)) {
                    lines.add(line);
                }
            }
        } catch (IOException e) {
            e.getMessage();
        }
        return lines;
    }

    public static boolean isAppletLoginApp(String msg) {
        return StringUtils.contains(msg, "未登录") && StringUtils.containsAny(msg, "微信", "支付宝");
    }

    public static boolean isAppletLogoutApp(String msg) {
        return StringUtils.contains(msg, "自动退出") && StringUtils.containsAny(msg, "微信", "支付宝");
    }

    public static boolean isAIDetectLoginApp(String msg) {
        return StringUtils.containsIgnoreCase(msg, "登录通知") && StringUtils.containsIgnoreCase(msg, "ai");
    }

    public static String optDetails(String value) {
        return StringUtils.isBlank(value) ? PinfoConstant.DETAILS_EMPTY : value;
    }

    public static boolean isEmptyDetails(String value) {
        return StringUtils.isBlank(value) || StringUtils.equals(value, PinfoConstant.DETAILS_EMPTY);
    }

    public static Date getPreemptTimeout() {
        return new Date(System.currentTimeMillis() - PinfoConstant.PREEMPT_TIMEOUT_MILLIS);
    }

    public static Date getDynamicTaskTimeout() {
        return new Date(System.currentTimeMillis() - PinfoConstant.DYNAMIC_TASK_TIMEOUT_MILLIS);
    }

    public static boolean isSamePlugins(TComplianceAppletPlugins plugins, TComplianceAppletPlugins plugins2) {
        if (StringUtils.equals(plugins.getPluginAppid(), PinfoConstant.DETAILS_EMPTY)
                && StringUtils.equals(plugins2.getPluginAppid(), PinfoConstant.DETAILS_EMPTY)) {
            // 没有appid对比名字
            return StringUtils.equals(plugins.getPluginName(), plugins2.getPluginName());
        } else {
            return StringUtils.equals(plugins.getPluginAppid(), plugins2.getPluginAppid());
        }
    }

    /**
     * 从给定的URL字符串中获取文件名，忽略查询参数。
     * @param path 路径
     * @return 文件名，忽略任何查询参数。如果URL不包含文件名，则返回一个空字符串。
     */
    public static String getFilenameFromURLIgnoringQuery(String path) {
        try {
            // 找到路径中最后一个"/"的位置
            int lastSlashIndex = path.lastIndexOf('/');
            // 如果找不到"/"或"/"是路径的最后一个字符，则认为URL不包含文件名
            if (lastSlashIndex == -1 || lastSlashIndex == path.length() - 1) {
                return ""; // 或者可以返回一个特定的提示信息
            }
            // 提取文件名
            String filename = path.substring(lastSlashIndex + 1);
            // 检查文件名中是否包含查询参数，并移除
            int queryParamIndex = filename.indexOf('?');
            if (queryParamIndex != -1) {
                filename = filename.substring(0, queryParamIndex);
            }
            return filename;
        } catch (Exception e) {
            log.error("Malformed URL: ", e);
            return "";
        }
    }

    public static String encodeURL(String url) {
        try {
            // 分割协议和域名部分
            int protocolIndex = url.indexOf("://");
            String protocol = url.substring(0, protocolIndex + 3);
            String remainder = url.substring(protocolIndex + 3);

            // 分割路径和查询参数
            int queryIndex = remainder.indexOf('?');
            String baseUrl = (queryIndex == -1) ? remainder : remainder.substring(0, queryIndex);
            String query = (queryIndex == -1) ? "" : remainder.substring(queryIndex + 1);

            // 对路径部分进行编码
            String[] parts = baseUrl.split("/");
            StringBuilder encodedUrl = new StringBuilder(protocol);
            for (int i = 0; i < parts.length; i++) {
                if (i == 0) {
                    // 不编码域名部分
                    encodedUrl.append(parts[i]);
                } else {
                    encodedUrl.append("/").append(URLEncoder.encode(parts[i], "UTF-8"));
                }
            }

            // 对查询参数部分进行编码
            if (!query.isEmpty()) {
                encodedUrl.append("?");
                String[] queryParams = query.split("&");
                Map<String, String> encodedQueryParams = new LinkedHashMap<>();
                for (String param : queryParams) {
                    String[] keyValue = param.split("=", 2);
                    String encodedKey = URLEncoder.encode(keyValue[0], "UTF-8");
                    String encodedValue = keyValue.length > 1 ? conditionallyEncodeValue(keyValue[1]) : "";
                    encodedQueryParams.put(encodedKey, encodedValue);
                }
                for (Map.Entry<String, String> entry : encodedQueryParams.entrySet()) {
                    encodedUrl.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                // 移除末尾多余的 '&'
                encodedUrl.deleteCharAt(encodedUrl.length() - 1);
            }

            return encodedUrl.toString();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Encoding not supported", e);
        }
    }

    private static String conditionallyEncodeValue(String value) throws UnsupportedEncodingException {
        // 检查值是否已经编码，如果未编码则进行编码
        String decodedValue;
        try {
            decodedValue = URLDecoder.decode(value, "UTF-8");
        } catch (IllegalArgumentException e) {
            // 如果解码失败，假设值已经编码
            return value;
        }
        return value.equals(decodedValue) ? URLEncoder.encode(value, "UTF-8") : value;
    }

    public static String transformDecompilePath(String originalPath) {
        // 创建File对象
        File file = new File(originalPath);
        // 获取父目录路径
        String parentPath = file.getParent();
        // 获取文件名
        String fileName = file.getName();
        // 去掉文件后缀
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            fileName = fileName.substring(0, lastDotIndex);
        }
        // 形成新的路径
        return parentPath + File.separator + fileName + File.separator + "decompile";
    }


    public static String genTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    public static CloseableHttpClient getDefaultClient() {
        return createHttpClientWithNoSSLVerification();
    }

    private static CloseableHttpClient createHttpClientWithNoSSLVerification() {
        try {
            SSLContextBuilder builder = new SSLContextBuilder();
            builder.loadTrustMaterial((chain, authType) -> true);
            return HttpClients.custom()
                    .setSSLContext(builder.build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void checkAppFileExt(String fileName) throws IjiamiApplicationException {
        if (StringUtils.isBlank(fileName)) {
            throw new IjiamiApplicationException("文件名为空");
        }
        if (!FileUtil.isApp(fileName.toLowerCase()) && !StringUtils.endsWithIgnoreCase(fileName, ConstantsUtils.SUFFIX_END_HAP)) {
            throw new IjiamiApplicationException("错误的文件类型");
        }
    }

    public static String analysisErrorMsg(Throwable e) {
        return e instanceof IjiamiRuntimeException ? e.getMessage() : "数据包解析失败";
    }

    public static String extractBaseUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        try {
            URI uri = new URI(url);
            String scheme = uri.getScheme(); // 获取协议部分 (http, https, etc.)
            String host = uri.getHost();     // 获取主机部分 (**********)
            int port = uri.getPort();        // 获取端口部分 (7861)

            // 构建基础URL
            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(scheme).append("://").append(host);

            // 如果端口存在且不是默认端口，则添加到基础URL中
            if (port != -1) {
                baseUrl.append(":").append(port);
            }

            return baseUrl.toString();
        } catch (URISyntaxException e) {
            log.error("extractBaseUrl fail", e);
            return null;
        }
    }

    /**
     * 获取excel的单元格文本，如果为空或者单元格不是字符串类型，返回--
     * @param cell
     * @return
     */
    public static String getCellAsString(Cell cell) {
        if (Objects.isNull(cell)) {
            return DETAILS_EMPTY;
        }
        if (CellType.STRING == cell.getCellType()) {
            return cell.getStringCellValue();
        } else if (CellType.NUMERIC == cell.getCellType()) {
            return String.valueOf(cell.getNumericCellValue());
        } else if (CellType.FORMULA == cell.getCellType()) {
            return DETAILS_EMPTY;
        } else if (CellType.BLANK == cell.getCellType()) {
            return DETAILS_EMPTY;
        } else if (CellType.BOOLEAN == cell.getCellType()) {
            return String.valueOf(cell.getBooleanCellValue());
        } else if (CellType.ERROR == cell.getCellType()) {
            return DETAILS_EMPTY;
        } else {
            return DETAILS_EMPTY;
        }
    }

    public static String reportZipName(ReportResultVO resultVO) {
        return StringUtils.trim(resultVO.appName()) + "_" + StringUtils.trim(resultVO.versionCode()) + "_" +
                resultVO.terminalType().getName() + "_" +
                "个人信息安全检测报告" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
    }

    public static String reportDocName(TaskDetailVO taskDetailVO, String lawName) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return taskDetailVO.getApk_name().trim() +
                "_" +
                taskDetailVO.getApk_version() +
                "_" +
                TerminalTypeEnum.getAndValid(taskDetailVO.getTerminal_type()).getName() +
                "_个人信息安全检测报告_" + lawName + "_" +
                simpleDateFormat.format(new Date());
    }
    
    public static net.sf.json.JSONObject findTaskDetailItemResult(TaskDetailVO taskDetail, String itemNo) {
        net.sf.json.JSONArray array = net.sf.json.JSONArray.fromObject(taskDetail.getDetection_result());
        for (int i = 0; i < array.size(); i++) {
            try {
                net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(array.get(i));
                // 获取基础信息
                if (json.get("detection_item_id") != null && json.get("detection_item_id").equals(itemNo)) {
                    net.sf.json.JSONObject resultContent = getResultContent(json);
                    return resultContent;
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        return null;
    }

    public static int cookieMark(TPrivacyOutsideAddress outsideAddress) {
        return StringUtils.isBlank(outsideAddress.getCookie()) || StringUtils.equals(outsideAddress.getCookie(), DETAILS_EMPTY)
                ? BooleanEnum.FALSE.value : BooleanEnum.TRUE.value;
    }

    public static boolean isSameMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR)
                && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    public static boolean isSameYear(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
    }

    /**
     * 移除文本中大于1行的空白。
     *
     * @param input 输入的文本
     * @return 清理后的文本
     */
    public static String removeExtraBlankLines(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 使用正则表达式匹配两个或更多连续的换行符，并将其替换为单个换行符
        return input.replaceAll("(\\r?\\n){2,}", "\n");
    }

    public static String getReportBehaviorStageName(Integer behaviorStage, Integer detectionType) {
        if (behaviorStage == null || BehaviorStageEnum.getItem(behaviorStage) == null) {
            return "";
        }
        if (detectionType == TaskDetectionTypeEnum.AI.getValue() && behaviorStage != BehaviorStageEnum.BEHAVIOR_GRANT.getValue()) {
            return BehaviorStageEnum.getItem(behaviorStage).getName() + "（登录后）";
        } else {
            return BehaviorStageEnum.getItem(behaviorStage).getName();
        }
    }
}
