package cn.ijiami.detection.utils;

import cn.hutool.core.util.RuntimeUtil;
import cn.ijiami.framework.kit.utils.FileUtil;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cms.CMSException;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.util.Store;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
public class AppSignatureInfoUtil {

    @Data
    @ToString
    public static class AppSignatureInfo {
        private String md5;
        private String sha1;
        private String sha256;
        private long startDate;
        private long endDate;
        private String serialNumber;
        private String issuerDN;
        private String subjectDN;
        private String sigAlgName;
        private int version;
    }

    public static AppSignatureInfo extractApkSignatureInfo(String filePath) {
        AppSignatureInfo result = null;
        File file = new File(filePath);
        if (file.exists()) {
            try (ZipFile zipFile = new ZipFile(file);
                 InputStream inputStream = getSignatureInputStream(zipFile, filePath)) {
                result = extractCertificateInfo(inputStream);
            } catch (IOException | CertificateException | CMSException e) {
                log.error("Error extracting signature info", e);
            }
        }
        return result;
    }

    public static AppSignatureInfo extractHapSignatureInfo(String toolsPath, String filePath) {
        AppSignatureInfo result = null;
        File file = new File(filePath);
        if (file.exists()) {
            File outCertChainFile = new File(file.getParent(), "outCertChain.cer");
            File outProfile = new File(file.getParent(), "outProfile.p7b");
            try {
                List<String> lines = extractHapSignatureFile(toolsPath, filePath, outCertChainFile.getAbsolutePath(), outProfile.getAbsolutePath());
                result = extractHapCertificateInfo(lines);
            } catch (Exception e) {
                log.error("Error extracting signature info", e);
            } finally {
                FileUtil.deleteFile(outCertChainFile);
                FileUtil.deleteFile(outProfile);
            }
        }
        return result;
    }

    public static List<String> extractHapSignatureFile(String toolsPath, String hapPath, String outCertChainFile, String outProfile) {
        String cmd =
                String.format("java -jar %s/hap-sign-tool.jar verify-app -inFile %s -outCertChain %s -outProfile %s",
                        toolsPath, hapPath, outCertChainFile, outProfile);
        return RuntimeUtil.execForLines(cmd);
    }

    private static AppSignatureInfo extractHapCertificateInfo(List<String> lines) {
        List<AppSignatureInfo> signatureInfoList = new ArrayList<>();
        AppSignatureInfo signatureInfo = new AppSignatureInfo();
        for (String line : lines) {
            if (line.contains("- Subject: ")) {
                signatureInfo.setSubjectDN(line.substring(line.indexOf("Subject:") + "Subject: ".length()));
            } else if (line.contains("- Issuer: ")) {
                signatureInfo.setIssuerDN(line.substring(line.indexOf("Issuer:") + "Issuer: ".length()));
            } else if (line.contains("- SerialNumber:")) {
                signatureInfo.setSerialNumber(line.substring(line.indexOf("SerialNumber:") + "SerialNumber: ".length()));
            } else if (line.contains("- SHA256:")) {
                signatureInfo.setSha256(line.substring(line.indexOf("SHA256:") + "SHA256: ".length()));
            } else if (line.contains("- Signature Algorithm:")) {
                signatureInfo.setSigAlgName(line.substring(line.indexOf("Signature Algorithm:") + "Signature Algorithm: ".length()));
            } else if (line.contains("- Cert Version:")) {
                String versionStr = line.substring(line.indexOf("Cert Version:") + "Cert Version: ".length());
                signatureInfo.setVersion(Integer.parseInt(versionStr.replace("V", "")));
                signatureInfoList.add(signatureInfo);
                signatureInfo = new AppSignatureInfo();
            }
        }
        for (AppSignatureInfo info:signatureInfoList) {
            if (StringUtils.containsIgnoreCase(info.getIssuerDN(), "Huawei CBG Developer Relations CA G2")) {
                return info;
            }
        }
        return null;
    }


    private static InputStream getSignatureInputStream(ZipFile zipFile, String filePath) throws IOException {
        if (filePath.toLowerCase().endsWith(".apk")) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                if (entry.getName().endsWith(".RSA") || entry.getName().endsWith(".DSA")) {
                    return zipFile.getInputStream(entry);
                }
            }
        } else if (filePath.endsWith(".RSA") || filePath.endsWith(".DSA")) {
            return Files.newInputStream(Paths.get(filePath));
        }
        return null;
    }

    public static AppSignatureInfo extractCertificateInfo(InputStream inputStream) throws IOException, CertificateException, CMSException {
        if (inputStream == null) return null;
        AppSignatureInfo result = null;
        try {
            X509Certificate cert = extractCertificate(inputStream);
            if (cert != null) {
                result = new AppSignatureInfo();
                result.setMd5(calculateMd5(cert.getEncoded()));
                result.setSha1(calculateSha1(cert.getEncoded()));
                result.setSha256(calculateSha256(cert.getEncoded()));
                result.setStartDate(cert.getNotBefore().getTime());
                result.setEndDate(cert.getNotAfter().getTime());
                result.setSerialNumber(cert.getSerialNumber().toString());
                result.setIssuerDN(cert.getIssuerDN().toString());
                result.setSubjectDN(cert.getSubjectDN().toString());
                result.setSigAlgName(cert.getSigAlgName());
                result.setVersion(cert.getVersion());
            }
        } finally {
            inputStream.close();
        }
        return result;
    }

    public static X509Certificate extractCertificate(InputStream inputStream) throws CMSException, CertificateException {
        // 解析 PKCS#7 数据
        CMSSignedData signedData = new CMSSignedData(inputStream);

        // 获取证书存储（返回 Store<X509CertificateHolder>）
        Store<X509CertificateHolder> certStore = signedData.getCertificates();

        // 获取所有证书
        Collection<X509CertificateHolder> certHolders = certStore.getMatches(null);
        if (certHolders != null && !certHolders.isEmpty()) {
            // 将第一个 X509CertificateHolder 转换为 X509Certificate
            X509CertificateHolder certHolder = certHolders.iterator().next();
            return convertToX509Certificate(certHolder);
        }

        return null;
    }

    /**
     * 将 X509CertificateHolder 转换为 X509Certificate
     */
    private static X509Certificate convertToX509Certificate(X509CertificateHolder certHolder) throws CertificateException {
        // 使用 JcaX509CertificateConverter 进行转换
        return new org.bouncycastle.cert.jcajce.JcaX509CertificateConverter()
                .setProvider("BC") // 指定 Bouncy Castle 提供者
                .getCertificate(certHolder);
    }

    public static String calculateMd5(byte[] bytes) {
        try {
            String result = DigestUtils.md5Hex(bytes);
            return padResult(result);
        } catch (Exception e) {
            log.error("Error calculating MD5", e);
            return null;
        }
    }

    public static String calculateSha1(byte[] bytes) {
        try {
            String result = DigestUtils.sha1Hex(bytes);
            return padResult(result);
        } catch (Exception e) {
            log.error("Error calculating SHA-1", e);
            return null;
        }
    }

    public static String calculateSha256(byte[] bytes) {
        try {
            String result = DigestUtils.sha256Hex(bytes);
            return padResult(result);
        } catch (Exception e) {
            log.error("Error calculating SHA-256", e);
            return null;
        }
    }

    private static String padResult(String result) {
        return result.length() < 32 ? "0" + result : result;
    }

    public static AppSignatureInfo analyzeApkSignatureInformation(String decompilePath) {
        AppSignatureInfo result = null;
        List<String> signatureFiles = CommonUtil.listAllFilesInDir(decompilePath, new String[]{"RSA", "DSA"}, true);
        if (!signatureFiles.isEmpty()) {
            result = extractApkSignatureInfo(signatureFiles.get(0));
        }
        return result;
    }

}