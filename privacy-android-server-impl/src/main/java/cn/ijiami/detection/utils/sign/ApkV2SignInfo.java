package cn.ijiami.detection.utils.sign;

import java.io.IOException;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

import org.apache.commons.lang3.StringUtils;

import com.android.apksig.ApkVerifier;
import com.android.apksigner.ApkSignerTool;

import cn.ijiami.framework.apk.entity.ApkInfo;
import cn.ijiami.framework.apk.util.ApkUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import net.dongliu.apk.parser.bean.ApkMeta;

public class ApkV2SignInfo {
	
	/**
	 * 判断v1~v4签名是否通过
	 */
	public static boolean getApkSingInfo(String apkPath) {
		String[] arg = {"--print-certs","--min-sdk-version=24","-v",apkPath};
		try {
			ApkVerifier.Result result = ApkSignerTool.verify2(arg);
			return (result != null && result.isVerified()) ? true : false;
		} catch (Throwable e) {
			e.getMessage();
			return false;
		}
	}
	
	public static void main22(String[] args) {
		String apkPath = "G:\\WeChat\\WeChat Files\\wxid_5xc3sugftlju21\\FileStorage\\File\\2023-03\\解析导致系统崩溃.apk";
//		String[] arg = {"--print-certs","--min-sdk-version=24","-v",apkPath};
		String[] arg = {"--verbose","--print-certs",apkPath};

//		java -jar apksigner.jar verify –print-certs –min-sdk-version=24 -v 11.apk

		try {
           //java -jar apksigner-1.0.jar verify --print-certs --min-sdk-version=24 -v 12.apk
//			ApkSignerTool.main(arg);
			ApkSignerTool.verify(arg);
//			 ApkVerifier.Result result = ApkSignerTool.verify2(arg);
//			 System.out.println(result.isVerified());
//			 System.out.println(JSON.toJSONString(result));
		} catch (Exception e) {
			e.getMessage();
		}
		
		
	}
	
	public static void main(String[] args) throws IOException {
		String apkPath = "F:/2222.apk";
		
		
		apkPath = "G:\\WeChat\\WeChat Files\\wxid_5xc3sugftlju21\\FileStorage\\File\\2023-03\\com.seewo.pinco.student.2302232339.apk";
//        ApkSignInfo apkSignInfo = ApkUtil.getApkSignInfo(apkPath);
//        System.out.println("apk信息：" + JSON.toJSONString(apkSignInfo));
//        
//        String[] arg = {"--print-certs","--min-sdk-version=24","-v",apkPath};
//        try {
//            //java -jar apksigner-1.0.jar verify --print-certs --min-sdk-version=24 -v 12.apk
//// 			ApkSignerTool.main(arg);
// 			 ApkVerifier.Result result = ApkSignerTool.verify2(arg);
// 			 System.out.println(result.isVerified());
// 			 System.out.println(JSON.toJSONString(result));
// 		} catch (Exception e) {
// 			e.printStackTrace();
// 		}

//        
//        cn.ijiami.framework.apk.entity.ApkInfo apkInfo = cn.ijiami.framework.apk.util.ApkUtil.getApkInfo(apkPath);
//        System.out.println("apkInfo:" + apkInfo);
		apkPath = "G:\\WeChat\\WeChat Files\\wxid_5xc3sugftlju21\\FileStorage\\File\\2023-05\\123.apk";
        String apkSignInfo = ApkUtil.getApkSign(apkPath);
        	System.out.println("apkSignInfo:" + apkSignInfo);
        	 ApkMeta apkMeta = null;
             ApkInfo apkInfo = null;
             String apkName = "";
             String icon = "";
             String versionName = "";
             String packageName = "";
        	
        	try {
        		apkInfo = cn.ijiami.detection.utils.APKUtil.readApkInfo(apkPath);
    	        apkName = apkInfo.getApplicationLable();
    			icon = apkInfo.getApplicationIcon();
    			versionName = apkInfo.getVersionName();
    			packageName = apkInfo.getPackageName();
    		} catch (Exception e2) {
    			try {
    				apkMeta = cn.ijiami.detection.utils.APKUtil.parserReadApkInfo(apkPath);
    			} catch (Exception e) {
    				e.getMessage();
    			}
    			apkName = apkMeta.getName();
    			icon = apkMeta.getIcon();
    			versionName = apkMeta.getVersionName();
    			packageName = apkMeta.getPackageName();
    		}
        	System.out.println(apkName);
//        getApkSingInfo(apkPath);

    }

}
