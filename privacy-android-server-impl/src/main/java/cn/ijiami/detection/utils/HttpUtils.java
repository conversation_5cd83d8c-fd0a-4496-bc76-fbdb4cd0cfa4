package cn.ijiami.detection.utils;

import static cn.ijiami.detection.utils.CommonUtil.getDefaultClient;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.charset.UnsupportedCharsetException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.Semaphore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.AuthSchemes;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import net.sf.json.JSONObject;

/**
 * http帮助类
 *
 * <AUTHOR>
 */
public class HttpUtils {
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);
    public static final int CACHE = 10 * 1024;
    public static final boolean ISWINDOWS;
    public static final String SPLASH;
    public static final String ROOT;
    // 连接超时时间 指客户端和服务器建立连接的timeout，就是http请求的三个阶段，一：建立连接；二：数据传送；三，断开连接。超时后会ConnectionTimeOutException
    public static final int CONNECTION_TIMEOUT = 60 * 60 * 1000;
    // 请求连接超时时间 指从连接池获取连接的timeout
    public static final int CONNECTION_REQUEST_TIMEOUT = 60 * 60 * 1000;
    // socket超时时间 指客户端从服务器读取数据的timeout，超出后会抛出SocketTimeOutException
    public static final int SOCKET_TIMEOUT = 10 * 60 * 1000;
    // UTF-8编码
    public static final String UTF_8 = "UTF-8";

    public static final String RANGE = "Range";
    public static final String ACCEPT_RANGES = "Accept-Ranges";
    public static final String CONTENT_RANGES = "Content-Ranges";

    static {
        if (System.getProperty("os.name") != null && System.getProperty("os.name").toLowerCase().contains("windows")) {
            ISWINDOWS = true;
            SPLASH = "\\";
            ROOT = "D:";
        } else {
            ISWINDOWS = false;
            SPLASH = "/";
            ROOT = "/search";
        }
    }

    /**
     * 根据url下载文件，文件名从response header头中获取
     *
     * @param url
     * @return
     */
    public static void download(String url, Semaphore semaphore) {
        download(url, null, semaphore);
    }

    /**
     * 根据url下载文件，保存到filepath中
     *
     * @param url
     * @param filepath
     * @return
     */
    public static void download(String url, String filepath, Semaphore semaphore) {
        try {
            HttpClient client = CommonUtil.getDefaultClient();
            HttpGet httpget = new HttpGet(encodeUrl(url));
            HttpResponse response = client.execute(httpget);

            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();
            if (filepath == null){
                filepath = getFilePath(response);
            }
            File file = new File(filepath);
            file.getParentFile().mkdirs();
            FileOutputStream fileout = new FileOutputStream(file);
            /**
             * 根据实际运行效果 设置缓冲区大小
             */
            byte[] buffer = new byte[CACHE];
            int ch = 0;
            while ((ch = is.read(buffer)) != -1) {
                fileout.write(buffer, 0, ch);
            }
            is.close();
            fileout.flush();
            fileout.close();

        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (semaphore != null) {
                semaphore.release();
            }
        }
    }

    public static String encodeUrl(String url) throws UnsupportedEncodingException {
        int queryIndex = url.indexOf("?");
        int begin = queryIndex + 1;
        if (queryIndex > 0 && begin < url.length()) {
            String queryStr = url.substring(begin, url.length() - 1);
            String encodeStr = URLEncoder.encode(queryStr, "UTF-8");
            url = url.substring(0, begin) + encodeStr;
        }
        return url;
    }

    /**
     * 获取response要下载的文件的默认路径
     *
     * @param response
     * @return
     */
    public static String getFilePath(HttpResponse response) {
        String filepath = ROOT + SPLASH;
        String filename = getFileName(response);

        if (filename != null) {
            filepath += filename;
        } else {
            filepath += getRandomFileName();
        }
        return filepath;
    }

    /**
     * 获取response header中Content-Disposition中的filename值
     *
     * @param response
     * @return
     */
    public static String getFileName(HttpResponse response) {
        Header contentHeader = response.getFirstHeader("Content-Disposition");
        String filename = null;
        if (contentHeader != null) {
            HeaderElement[] values = contentHeader.getElements();
            if (values.length == 1) {
                NameValuePair param = values[0].getParameterByName("filename");
                if (param != null) {
                    try {
                        // filename = new String(param.getValue().toString().getBytes(), "utf-8");
                        // filename=URLDecoder.decode(param.getValue(),"utf-8");
                        filename = param.getValue();
                    } catch (Exception e) {
                        e.getMessage();
                    }
                }
            }
        }
        return filename;
    }

    /**
     * 获取随机文件名
     *
     * @return
     */
    public static String getRandomFileName() {
        return String.valueOf(System.currentTimeMillis());
    }

    public static void outHeaders(HttpResponse response) {
        Header[] headers = response.getAllHeaders();
        for (int i = 0; i < headers.length; i++) {
            System.out.println(headers[i]);
        }
    }

    /**
     * httpPost
     *
     * @param url       请求的url
     * @param jsonParam 请求数据
     * @return
     */
    public static JSONObject httpPost(String url, JSONObject jsonParam) {
        return httpPost(url, jsonParam, false);
    }

    public static String post(JSONObject json, String url) {
        return post(json, url, null);
    }

    public static String post(JSONObject json, String url, Map<String, String> headParam) {
        return post(Objects.isNull(json) ? "" : json.toString(), url, headParam);
    }

    public static String post(String json, String url, Map<String, String> headParam) {
        String result = "";
        HttpPost post = new HttpPost(url);
        try {
            CloseableHttpClient httpClient = getDefaultClient();
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    post.setHeader(entry.getKey(), entry.getValue());
                }
            } else {
                post.addHeader("Authorization", "Basic YWRtaW46");
            }
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            // 兼容无参
            if (StringUtils.isNotBlank(json)) {
                StringEntity postingString = new StringEntity(json, "utf-8");
                post.setEntity(postingString);
            }
            HttpResponse response = httpClient.execute(post);

            InputStream in = response.getEntity().getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber = new StringBuilder();
            String line = null;
            while ((line = br.readLine()) != null) {
                strber.append(line + '\n');
            }
            br.close();
            in.close();
            result = strber.toString();
            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                result = null;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            post.abort();
        }
        return result;
    }

    /**
     * httpPost
     *
     * @param url            请求url
     * @param jsonParam
     * @param noNeedResponse 是否获取接口response
     * @return
     */
    public static JSONObject httpPost(String url, JSONObject jsonParam, boolean noNeedResponse) {
        CloseableHttpClient httpClient = getDefaultClient();

        JSONObject jsonResult = null;
        HttpPost method = new HttpPost(url);
        try {
            if (null != jsonParam) {
                StringEntity entity = new StringEntity(jsonParam.toString(), "UTF-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                method.setEntity(entity);
            }
            HttpResponse result = httpClient.execute(method);
            url = URLDecoder.decode(url, "UTF-8");
            if (result.getStatusLine().getStatusCode() == 200) {
                String str = "";
                try {
                    str = EntityUtils.toString(result.getEntity());
                    if (noNeedResponse) {
                        return null;
                    }
                    jsonResult = JSONObject.fromObject(str);
                } catch (Exception e) {
                    System.out.println("posturl:" + url + ", err=" + e.getMessage());
                }
            }
        } catch (IOException e) {
            System.out.println("posturl:" + url + ", err=" + e.getMessage());
        }
        return jsonResult;
    }

    public static JSONObject httpGet(String url) {
        return httpGet(url, null);
    }

    /**
     * httpGet
     *
     * @param url
     * @return
     */
    public static JSONObject httpGet(String url, Map<String, String> headParam) {
        JSONObject jsonResult = null;
        try {
            CloseableHttpClient client = getDefaultClient();
            HttpGet request = new HttpGet(url);
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse response = client.execute(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                String strResult = EntityUtils.toString(response.getEntity());
                jsonResult = JSONObject.fromObject(strResult);
                url = URLDecoder.decode(url, "UTF-8");
            } else {
                System.out.println("geturl:" + url);
            }
        } catch (IOException e) {
            System.out.println("geturl" + url + ", err=" + e.getMessage());
        }
        return jsonResult;
    }

    public static String httpGetString(String url, Map<String, String> headParam, int retry) {
        String strResult = "";
        try {
            strResult = executeGet(url, headParam);
        } catch (Exception e) {
            if (retry > 0) {
                strResult = httpGetString(url, headParam, retry - 1);
            } else {
                LOG.info("请求失败 {}", url, e);
            }
        }
        return strResult;
    }

    private static String getHtmlEquivCharset(String htmlText) {
        String charsetFlag = "charset=";
        int charsetBegin = htmlText.indexOf(charsetFlag);
        if (charsetBegin >= 0) {
            int charsetEnd = htmlText.indexOf("\"", charsetBegin);
            return htmlText.substring(charsetBegin + charsetFlag.length(), charsetEnd);
        }
        return "utf-8";
    }

    private static String getHtmlMateCharset(String htmlText) {
        String charsetFlag = "<meta charset=\"";
        int charsetBegin = htmlText.indexOf(charsetFlag);
        if (charsetBegin >= 0) {
            int charsetEnd = htmlText.indexOf("\"", charsetBegin + charsetFlag.length());
            return htmlText.substring(charsetBegin + charsetFlag.length(), charsetEnd);
        }
        return "utf-8";
    }

    /**
     * httpGet
     *
     * @param url
     * @return
     */
    public static String httpGetString(String url, Map<String, String> headParam) {
        String strResult = "";
        try {
            strResult = executeGet(url, headParam);
        } catch (IOException e) {
            LOG.info("请求失败 {}", url, e);
        }
        return strResult;
    }

    private static String executeGet(String url, Map<String, String> headParam) throws IOException {
        CloseableHttpClient client = getDefaultClient();
        HttpGet request = new HttpGet(url);
        if (null != headParam) {
            for (Map.Entry<String, String> entry : headParam.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }
        HttpResponse response = client.execute(request);
        if (response.getStatusLine().getStatusCode() == 200) {
            try {
                byte[] htmlByteArray = EntityUtils.toByteArray(response.getEntity());
                String htmlText = new String(htmlByteArray, getContentType(response.getEntity(), "UTF-8").getCharset());
                if (htmlText.contains("http-equiv=Content-Type") || htmlText.contains("http-equiv=\"Content-Type\"")) {
                    String charset = getHtmlEquivCharset(htmlText);
                    htmlText = new String(htmlByteArray, charset);
                } else if (htmlText.contains("<meta charset=")) {
                    String charset = getHtmlMateCharset(htmlText);
                    htmlText = new String(htmlByteArray, charset);
                }
                return htmlText;
            } catch (UnsupportedEncodingException e) {
                LOG.error("解析html文本失败 {}", url, e);
                return StringUtils.EMPTY;
            }
        } else {
            return StringUtils.EMPTY;
        }
    }

    private static ContentType getContentType(HttpEntity entity, String defaultCharset) throws UnsupportedEncodingException {
        ContentType contentType = null;
        try {
            contentType = ContentType.get(entity);
        } catch (UnsupportedCharsetException var4) {
            if (defaultCharset == null) {
                throw new UnsupportedEncodingException(var4.getMessage());
            }
        }
        if (contentType != null) {
            if (contentType.getCharset() == null) {
                contentType = contentType.withCharset(defaultCharset);
            }
        } else {
            contentType = ContentType.DEFAULT_TEXT.withCharset(defaultCharset);
        }
        return contentType;
    }

    private static HttpClient getHttpsClient() {
        // 创建Registry
        RequestConfig requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD_STRICT)
                .setExpectContinueEnabled(Boolean.TRUE)
                .setTargetPreferredAuthSchemes(Arrays.asList(AuthSchemes.NTLM, AuthSchemes.DIGEST))
                .setProxyPreferredAuthSchemes(Collections.singletonList(AuthSchemes.BASIC))
                .build();
        return HttpClients.custom()
                .setDefaultRequestConfig(requestConfig).build();
    }

    public static JSONObject httpsGet(String url, Map<String, String> headParam) {
        return httpsGet(url, headParam, -1);
    }

    public static JSONObject httpsGet(String url, Map<String, String> headParam, int timeout) {
        JSONObject jsonResult = null;
        try {
            HttpClient client = getHttpsClient();
            HttpGet request = new HttpGet(url);
            if (timeout > 0) {
                RequestConfig.Builder configBuilder;
                if (Objects.nonNull(request.getConfig())) {
                    configBuilder = RequestConfig.copy(request.getConfig());
                } else {
                    configBuilder = RequestConfig.custom();
                }
                configBuilder.setSocketTimeout(timeout);
                configBuilder.setConnectTimeout(timeout);
                configBuilder.setConnectionRequestTimeout(timeout);
                request.setConfig(configBuilder.build());
            }
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse response = client.execute(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                String strResult = EntityUtils.toString(response.getEntity());
                jsonResult = JSONObject.fromObject(strResult);
                url = URLDecoder.decode(url, "UTF-8");
            } else {
                LOG.error("get url:" + url + " statusCode=" + response.getStatusLine().getStatusCode());
            }
        } catch (IOException e) {
            LOG.error("get url:" + url + ", err=", e);
        }
        return jsonResult;
    }

    public static com.alibaba.fastjson.JSONObject httpsGetFastJson(String url, Map<String, String> headParam) {
    	com.alibaba.fastjson.JSONObject jsonResult = null;
        try {
            HttpClient client = CommonUtil.getDefaultClient();
            HttpGet request = new HttpGet(url);
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse response = client.execute(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                String strResult = EntityUtils.toString(response.getEntity());
                jsonResult = com.alibaba.fastjson.JSONObject.parseObject(strResult);
                url = URLDecoder.decode(url, "UTF-8");
            } else {
                System.out.println("geturl:" + url);
            }
        } catch (IOException e) {
            System.out.println("geturl" + url + ", err=" + e.getMessage());
        }
        return jsonResult;
    }

    /**
     * httpDelete
     *
     * @param url            请求url
     * @param headParam
     * @return
     */
    public static JSONObject httpDelete(String url, Map<String, String> headParam) {
        CloseableHttpClient httpClient = getDefaultClient();

        JSONObject jsonResult = null;
        HttpDelete method = new HttpDelete(url);
        try {
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    method.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse result = httpClient.execute(method);
            url = URLDecoder.decode(url, "UTF-8");
            if (result.getStatusLine().getStatusCode() == 200) {
                String str = "";
                try {
                    str = EntityUtils.toString(result.getEntity());
                    jsonResult = JSONObject.fromObject(str);
                } catch (Exception e) {
                    System.out.println("posturl:" + url + ", err=" + e.getMessage());
                }
            }
        } catch (IOException e) {
            System.out.println("posturl:" + url + ", err=" + e.getMessage());
        }
        return jsonResult;
    }

    /**
     * 发起post文件请求(multipart/form-data)
     *
     * @param header
     * @param url
     * @param rquestParams
     * @return
     * @throws ClientProtocolException
     * @throws IOException
     */
    public static CloseableHttpResponse postFileMultiPart(CloseableHttpClient httpclient, Map<String, String> header,
                                                          String url, Map<String, ContentBody> rquestParams) {
        boolean isClose = false;
        if (httpclient == null) {
            httpclient = getDefaultClient();
            isClose = true;
        }
        try {
            // 1.创建httpPost请求
            HttpPost httpPost = new HttpPost(url);
            // 2.添加header参数
            if (header != null) {
                for (Entry<String, String> param : header.entrySet()) {
                    httpPost.addHeader(param.getKey(), param.getValue());
                }
            }
            // 3.设置requestConfig
            RequestConfig defaultRequestConfig = RequestConfig.custom().setConnectTimeout(CONNECTION_TIMEOUT)
                    .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
            httpPost.setConfig(defaultRequestConfig);
            // 4.添加请求参数
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            for (Entry<String, ContentBody> param : rquestParams.entrySet()) {
                multipartEntityBuilder.addPart(param.getKey(), param.getValue());
            }
            HttpEntity reqEntity = multipartEntityBuilder.build();
            httpPost.setEntity(reqEntity);
            // 5.执行post请求.
            return httpclient.execute(httpPost);
        } catch (ClientProtocolException e) {
            LOG.info("获取http连接失败！message={}", e.getMessage());
        } catch (IOException e) {
            LOG.info("执行http请求失败！message={}", e.getMessage());
        } finally {
            // 关闭连接,释放资源
            if (isClose) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    LOG.warn("关闭http连接失败！", e);
                }
            }

        }
        return null;
    }

    /**
     * 构建参数
     *
     * @param params
     * @return Map<String, ContentBody>
     */
    public static Map<String, ContentBody> buildExcuteParams(Map<String, Object> params) {

        // 构建参数
        Map<String, ContentBody> requestParams = new HashMap<String, ContentBody>();
        for (Map.Entry<String, Object> param : params.entrySet()) {
            if (params.get(param.getKey()) instanceof File) {
                requestParams.put(param.getKey(), new FileBody(new File(params.get(param.getKey()).toString())));
            } else {
                requestParams.put(param.getKey(),
                        new StringBody(params.get(param.getKey()).toString(), ContentType.MULTIPART_FORM_DATA));
            }
        }

        return requestParams;
    }

    /**
     * 请求提返回数据字符串
     *
     * @param httpResponse
     * @return
     */
    public static String coverHttpResponseContent(CloseableHttpResponse httpResponse) {
        StringBuilder entityStringBuilder = null;
        try {
            HttpEntity entity = httpResponse.getEntity();
            entityStringBuilder = new StringBuilder();
            if (null != entity) {
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(httpResponse.getEntity().getContent(), "UTF-8"), 8 * 1024);
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    entityStringBuilder.append(line);
                }
            }

        } catch (Exception e) {
            e.getMessage();
        }
        return entityStringBuilder.toString();
    }

    public static void proxyDownloadResource(String url, HttpServletResponse response, HttpServletRequest request) throws Exception {
        HttpClient client = getDefaultClient();
        HttpGet httpget = new HttpGet(url);
        setRequestRange(httpget, request);
        HttpResponse httpResponse = client.execute(httpget);
        HttpEntity entity = httpResponse.getEntity();
        InputStream is = entity.getContent();
        if (url.lastIndexOf('/') > 0) {
            String fileName = url.substring(url.lastIndexOf('/') + 1);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        setResponseRangeData(httpResponse, response);
        response.addHeader("Content-Length", String.valueOf(entity.getContentLength()));
        response.setContentType(entity.getContentType().getValue());
        byte[] buffer = new byte[2048];
        int ch = 0;
        while ((ch = is.read(buffer)) != -1) {
            response.getOutputStream().write(buffer, 0, ch);
        }
        is.close();
    }

    public static void proxyDownloadResource2(Map<String,String> fileMap, HttpServletResponse response, HttpServletRequest request) throws Exception {
        String fileName=fileMap.get("fileName");
        String url=fileMap.get("url");
        HttpClient client = getDefaultClient();
        HttpGet httpget = new HttpGet(url);
        setRequestRange(httpget, request);
        HttpResponse httpResponse = client.execute(httpget);
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        // 处理请求失败的情况
        if (statusCode != HttpStatus.SC_OK && statusCode != HttpStatus.SC_PARTIAL_CONTENT) {
            response.setStatus(httpResponse.getStatusLine().getStatusCode());
            return;
        }
        // 文件名乱码问题
        // IE的话，通过URLEncoder对filename进行UTF8编码。而其他的浏览器（firefox、chrome、safari、opera），则要通过字节转换成ISO8859-1了
        String header = request.getHeader("User-Agent").toUpperCase();
        if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
            fileName = URLEncoder.encode(fileName, "utf-8");
            // IE下载文件名空格变+号问题
            fileName = fileName.replace("+", "%20");
        } else {
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), "ISO8859-1");
        }


        HttpEntity entity = httpResponse.getEntity();
        InputStream is = entity.getContent();
        setResponseRangeData(httpResponse, response);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        response.addHeader("Content-Length", String.valueOf(entity.getContentLength()));
        response.setContentType(entity.getContentType().getValue());
        byte[] buffer = new byte[2048];
        int ch = 0;
        while ((ch = is.read(buffer)) != -1) {
            response.getOutputStream().write(buffer, 0, ch);
        }
        is.close();
    }

    private static void setRequestRange(HttpGet httpget, HttpServletRequest request) {
        if (StringUtils.isNotBlank(request.getHeader(RANGE))) {
            httpget.setHeader(RANGE, request.getHeader(RANGE));
        }
    }

    private static void setResponseRangeData(HttpResponse httpResponse, HttpServletResponse response) {
        if (httpResponse.containsHeader(ACCEPT_RANGES)) {
            Header[] headers = httpResponse.getHeaders(ACCEPT_RANGES);
            response.setHeader(ACCEPT_RANGES, headers[0].getValue());
        }
        if (httpResponse.containsHeader(CONTENT_RANGES)) {
            Header[] headers = httpResponse.getHeaders(CONTENT_RANGES);
            response.setHeader(CONTENT_RANGES, headers[0].getValue());
        }
    }

    public static void copyFile(File file, HttpServletRequest request, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            if (file.exists()) {
                // 获取文件名称
                String fileName = file.getName().replace(" ", "");
                String utf8Name = fileName;
                // 文件名乱码问题
                // IE的话，通过URLEncoder对filename进行UTF8编码。而其他的浏览器（firefox、chrome、safari、opera），则要通过字节转换成ISO8859-1了
                String header = request.getHeader("User-Agent").toUpperCase();
                if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                    fileName = URLEncoder.encode(fileName, "utf-8");
                    // IE下载文件名空格变+号问题
                    fileName = fileName.replace("+", "%20");
                    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
                } else {
                    response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode(utf8Name, "utf-8"));
                }
                // 设置数据种类
                response.setContentType("application/octet-stream");
                response.setHeader("X-Frame-OPTIONS", "ALLOW-FROM");
                response.setHeader("fileName", URLEncoder.encode(utf8Name, "utf-8"));
                response.setHeader("Access-Control-Expose-Headers", "fileName,Content-Disposition");
                response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
                response.setHeader("Access-Control-Allow-Credentials", "true");
                // 获取返回体输出权
                in = Files.newInputStream(file.toPath());
                out = response.getOutputStream();
                // 写文件
                IOUtils.copy(in, out);
            } else {
                response.setStatus(201);  //文件不存在
                response.setHeader("message", URLEncoder.encode("文件不存在，下载失败", "utf-8"));
            }
        } catch (Exception e) {
            LOG.error("下载出错", e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                LOG.error("关闭流失败", e);
            }
        }
    }
    
    public static String httpGetPageContent(String url, Map<String, String> headParam) throws IjiamiApplicationException {
    	String result = null;
        try {
            CloseableHttpClient client = getDefaultClient();
            HttpGet request = new HttpGet(url);
            if (null != headParam) {
                for (Map.Entry<String, String> entry : headParam.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse response = client.execute(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = EntityUtils.toString(response.getEntity());
                url = URLDecoder.decode(url, "UTF-8");
            } else {
                LOG.info("geturl:" + url);
            }
        } catch (IOException e) {
            throw new IjiamiApplicationException(e.getMessage());
        }
        return result;
    }
    
    public static void main(String[] args) {
    	String url = "https://sdk.zywa.com.cn/odp-manager-server/restSdk/exportSdk?identification=1714977001902432049&version=1";
		System.out.println(httpGetString(url, null, 1));
	}
}