package cn.ijiami.detection.utils;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.http.HttpStatus;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/1/14 09:58
 */
public class WordPageUtils {
    public static void urlPost(String url, String filePath) {
        urlPost(url, filePath, null);
    }

    public static void urlPost(String url, String filePath, String output) {
    	urlPost(url, filePath, output, null);
    }

    public static void urlPost(String url, String filePath, String output, String requestFileNameParam) {
    	urlPost(url, new File(filePath), output, requestFileNameParam);
    }

    public static void urlPost(String url, File file, String output, String requestFileNameParam) {
        if (StringUtils.isEmpty(requestFileNameParam)) {
            requestFileNameParam = "wordFile";
        }
        if (StringUtils.isEmpty(output)) {
            String fileName = file.getName();
            String absolutePath = file.getAbsolutePath();
            output = absolutePath.substring(0, absolutePath.lastIndexOf("\\"));
            output = output.concat("\\").concat("new-").concat(fileName);
        }
        PostMethod postMethod = null;
        try {
            postMethod = new PostMethod(url);
            Part[] parts = {new FilePart(requestFileNameParam, file)};
            MultipartRequestEntity entity = new MultipartRequestEntity(parts, postMethod.getParams());
            postMethod.setRequestEntity(entity);
            HttpClient client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(50000);
            //执行postMethod
            int statusCode = client.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                InputStream inputStream = postMethod.getResponseBodyAsStream();
                writeToLocal(output, inputStream);
            }
        } catch (IOException e) {
            e.getMessage();
        } finally {
            if (postMethod != null) {
                postMethod.releaseConnection();
            }
        }
    }

    /**
     * 写入到本地
     *
     * @param destination
     * @param input
     */
    public static void writeToLocal(String destination, InputStream input) {
        try {
            int index;
            byte[] bytes = new byte[1024];
            FileOutputStream downloadFile = new FileOutputStream(destination);
            while ((index = input.read(bytes)) != -1) {
                downloadFile.write(bytes, 0, index);
                downloadFile.flush();
            }
            downloadFile.close();
            input.close();
        } catch (IOException e) {
            e.getMessage();
        }
    }

    public static void main(String[] args) {
//        String url = "http://172.10.23.26:8080/jareport/words";
//        String path = "/Users/<USER>/Downloads/真融宝_6.9.0_个人信息安全检测报告_开发者_GBT35273_20200103163543.docx";
//        urlPost(url, path, path);
//        System.out.println(path);
    }
}
