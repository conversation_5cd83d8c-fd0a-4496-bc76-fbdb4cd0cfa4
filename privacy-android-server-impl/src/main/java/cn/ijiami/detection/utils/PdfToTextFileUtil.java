package cn.ijiami.detection.utils;

import java.io.IOException;

import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.SimpleTextExtractionStrategy;
import com.itextpdf.text.pdf.parser.TextExtractionStrategy;

public class PdfToTextFileUtil {
	/**
	 * 将PDF文件分页解析为txt格式文件
	 * 
	 * @param fileName
	 * @throws IOException
	 */
	public static String getPdfFileText(String fileName) throws IOException {
		StringBuilder sf = new StringBuilder();
		PdfReader reader = new PdfReader(fileName);
		PdfReaderContentParser parser = new PdfReaderContentParser(reader);
		TextExtractionStrategy strategy = null;

		for (int i = 0; i < reader.getNumberOfPages(); i++) {
			strategy = parser.processContent(i+1, new SimpleTextExtractionStrategy());
			sf.append(strategy.getResultantText().toString());
		}
		return sf.toString();
	}

	public static void main(String[] args) {

		String fileName = "C:/Users/<USER>/Desktop/image/1.pdf";
		try {
			System.out.println(getPdfFileText(fileName));
		} catch (IOException e) {
			e.getMessage();
		}
	}

}
