package cn.ijiami.detection.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 项目基本配置
 * 
 * <AUTHOR>
 * @date 2020/3/27 11:01
 **/
public class PlatformConstant {

	/**
	 * 首页地址
	 * http://privacy.ijiami.cn:8080/#/detection-manager/views/AssetOverview
	 */
	public static final String REDIRECT_URL = "/#/login";
//	public static final String COOKIE_DOMAIN = "abd.ijiami.cn";
	/**
	 * 获取token接口地址
	 */
	public static final String OAUTH_TOKEN_URL = "/detection/oauth/token";

	public static final String COOKIE_TOKEN_INFO = "_token_info_";

	public static final String COOKIE_USER = "_user_";
	
	public static Map<Long,Long> map = new HashMap<Long,Long>();
	
	public static Map<String,String> queue_map = new HashMap<String,String>();
	
	public static Map<String,String> device_map = new HashMap<String,String>();
}
