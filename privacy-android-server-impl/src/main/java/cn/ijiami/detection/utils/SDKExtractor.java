package cn.ijiami.detection.utils;

import cn.ijiami.detection.VO.CheckList;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SDKExtractor.java
 * @Description
 * @createTime 2023年06月19日 11:03:00
 */
public class SDKExtractor {

    public static List<String> extractSdkName(String text) {
        Matcher matcher = Pattern.compile("（\\d+?）(\\S+? SDK)").matcher(text);
        List<String> sdkNameList = new ArrayList<>();
        while (matcher.find()) {
            String name = matcher.group(1).trim();
            if (!sdkNameList.contains(name)) {
                sdkNameList.add(name);
            }
        }
        return sdkNameList;
    }

    public static CheckList extractSDKInformation(String text, List<String> sdkList) {
        CheckList checkList = new CheckList();
        StringJoiner fullTextBuilder = new StringJoiner("\n");
        List<CheckList.Row> extractedText = new ArrayList<>();
        int sdkCount = 1;
        for (String sdk : sdkList) {
            String regex = "（" + sdkCount + "）" + Pattern.quote(sdk) + ".*?(?=(（" + (sdkCount + 1) + "）|$))";
            Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                String sdkInfo = matcher.group();
                fullTextBuilder.add(sdkInfo);
                String permission = extractPermission(sdkInfo);
                if (StringUtils.isNotBlank(permission)) {
                    CheckList.Row checkListRow = new CheckList.Row();
                    checkListRow.setSdkName(sdk);
                    checkListRow.setPermission(permission);
                    checkListRow.setCompany(extractCompany(sdkInfo));
                    checkListRow.setPurpose(extractPurpose(sdkInfo));
                    checkListRow.setPrivacyPolicyLink(extractLink(sdkInfo));
                    extractedText.add(checkListRow);
                }
            }
            sdkCount++;
        }
        checkList.setFullText(fullTextBuilder.toString());
        checkList.setRowList(extractedText);
        return checkList;
    }

    private static String extractCompany(String text) {
        Matcher matcher = Pattern.compile("(主体|公司)\\S??：(.+?)[\\s\\n][^a-zA-z]", Pattern.DOTALL).matcher(text);
        if (matcher.find()) {
            return matcher.group(2);
        }
        return StringUtils.EMPTY;
    }

    private static String extractPurpose(String text) {
        Matcher matcher = Pattern.compile("(用途|目的)\\S??：(.+?)[\\s\\n][^a-zA-z]", Pattern.DOTALL).matcher(text);
        if (matcher.find()) {
            return matcher.group(2);
        }
        return StringUtils.EMPTY;
    }

    private static String extractPermission(String text) {
        Matcher matcher = Pattern.compile("(用途|类型|权限)\\S??：(.+?)[\\s\\n][^a-zA-z]", Pattern.DOTALL).matcher(text);
        if (matcher.find()) {
            return matcher.group(2);
        }
        return StringUtils.EMPTY;
    }

    private static String extractLink(String text) {
        Matcher matcher = Pattern.compile("(链接|隐私|官网)\\S??：(http.+?)[\\s\\n][^a-zA-z]", Pattern.DOTALL).matcher(text);
        if (matcher.find()) {
            return matcher.group(2);
        }
        return StringUtils.EMPTY;
    }

}
