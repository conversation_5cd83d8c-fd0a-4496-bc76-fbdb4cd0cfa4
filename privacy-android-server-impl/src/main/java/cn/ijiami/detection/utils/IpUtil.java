package cn.ijiami.detection.utils;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.stereotype.Component;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Subdivision;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.miit.kit.MiitWordKit;

/**
 * <AUTHOR>
 * @date 2019/11/12 19:15
 */
@Slf4j
@Component
public class IpUtil {

	private static final String province = "河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|北京|天津|上海|重庆|内蒙|广西|宁夏|新疆|西藏";
    private final IjiamiCommonProperties commonProperties;
  //存储IP解析，避免重复解析
    public static final Map<String,String> ipMap = new ConcurrentHashMap<>();
    private Searcher searcher;
    private static Boolean offline = false;
    private static String ipPostUrl = "https://abd.ijiami.cn/armp-gateway/armp/tool/v1/ip-is-oversea-data-cloud/%s";

    public IpUtil(IjiamiCommonProperties commonProperties) {
        this.commonProperties = commonProperties;
        initSearcher();
        if(StringUtils.isNoneBlank(commonProperties.getProperty("detection.offline.mode")) && commonProperties.getProperty("detection.offline.mode").trim().equals("true")) {
        	offline = true;
        }

        if(StringUtils.isNoneBlank(commonProperties.getProperty("bigdata.ip.post.url"))){
        	ipPostUrl =  commonProperties.getProperty("bigdata.ip.post.url");
        }

    }
    
    private void initSearcher(){
    	String dbPath = commonProperties.getProperty("ijiami.tools.path")+"/ip2region.xdb";
		byte[] cBuff = null;
        try {
           cBuff = Searcher.loadContentFromFile(dbPath);
        } catch (Exception e) {
           System.out.printf("failed to load content from `%s`: %s\n", dbPath, e);
        }
        try {
           searcher = Searcher.newWithBuffer(cBuff);
        } catch (Exception e) {
           System.out.printf("failed to create content cached searcher: %s\n", e);
        }
    }

	public String getAddressFromIP(String ip) {
		if (StringUtils.isNotBlank(ip)) {
			return "";
		}
		if (StringUtils.contains(ip, ":")) {
			// 避免端口获取不到具体地址信息
			return getAddress(ip.split(":")[0]);
		} else {
			return getAddress(ip);
		}
	}

    public String getAddress(String host) {
        try {
        	if(StringUtils.isBlank(host)) {
        		return "";
        	}
        	if(host.contains("127.0.0.1")) {
        		return "";
        	}
        	
        	if(ipMap.get(host)!=null) {
        		return ipMap.get(host).toString();
        	}
        	
        	boolean offline = false; 
        	if(offline) {
        		char firstChar = host.charAt(0);
            	if (Character.isLetter(firstChar)) {
            	    return "";
            	} 
        	}
        	
	    	 InetAddress ipAddress = InetAddress.getByName(host.trim());
	         if(ipAddress==null) {
	         	return "";
	         }
        	
            String ip  = ipAddress.getHostAddress();
        	String address =  searcherAddress(ip);
        	if(StringUtils.isBlank(address)) {
//        		File database = new File(commonProperties.getProperty("ijiami.mmdb.path"));
//                DatabaseReader reader = new DatabaseReader.Builder(database).build();
//
//                CityResponse response = reader.city(ipAddress);
//                Country country = response.getCountry();
//                Subdivision subdivisions = response.getLeastSpecificSubdivision();
//                City city = response.getCity();
//                String guojia = country.getNames().get("zh-CN");
//                String shengfen = subdivisions.getNames().get("zh-CN");
//                String chengshi = city.getNames().get("zh-CN");
//                address = String.join(" ", asList(guojia, shengfen, chengshi));
        		if(!offline){
            		address = getHttpCityInfo(ip, address);
            	}
        	}
        	address = sensitiveFilter(address);
        	
        	//境外
        	if(!offline && isOutSide(address) ==1){
        		address = getHttpCityInfo(ip, address);
        	}
        	ipMap.put(host, address);
        	return address;
        } catch (Exception e) {
            e.getMessage();
            return "";
        }
    }
    
    
    public static String hostByIp(String host) {
    	String IP = "";
    	if(StringUtils.isBlank(host)) {
    		return IP;
    	}
    	
    	System.out.println("hostByIp:"+host);
		if(offline && checkFirthChat(host.trim())) {
			return "";
		}
    	
    	host = host.replace("http://", "").replace("https://", "").replaceAll("/", "");
    	try {
    		InetAddress ipAddress = InetAddress.getByName(host.trim());
    		IP = ipAddress.getHostAddress();
    	} catch (UnknownHostException e) {
    		System.err.println(e.getMessage());
    	}
    	return IP;
    }

    /**
     * 是否境外
     * 境内指中国大陆，其他地区均为境外
     *
     * @param address
     * @return 0：境内，1：境外
     */
    public static int isOutSide(String address) {
        if (StringUtils.isBlank(address)) {
            return 0;
        }
        if (address.contains("内网")) {
            return 0;
        }
        if (address.contains("保留")) {
            return 0;
        }
        if (address.contains("香港") || address.contains("澳门") || address.contains("台湾")) {
            return 1;
        }
        return StringUtils.startsWith(address, "中国") ? 0 : 1;
    }

    private List<String> asList(String... args) {
        List<String> strings = new ArrayList<>();
        for (String arg : args) {
            Optional.ofNullable(arg).ifPresent(strings::add);
        }
        return strings;
    }
    
    /**
     * 根据ip获取详细地址
     */
    public static String getHttpCityInfo(String ip, String address) {
		long startTime = System.currentTimeMillis();
        String add = null;;
		try {
			String api = String.format(ConstantsUtils.Url.IP_URL, ip);
			if(StringUtils.isNoneBlank(ipPostUrl)) {
				api = String.format(ipPostUrl, ip);
			}
			JSONObject object = JSONUtil.parseObj(HttpUtil.get(api));
			System.out.println("识别Ip异常:"+object);
			if(StringUtils.isNoneBlank(ipPostUrl)) {
				add = object.get("address", String.class);
			}else {
				add = object.get("addr", String.class);
			}
			if(StringUtils.isBlank(add)) {
				return address;
			}
			if(!add.contains("中国") && MiitWordKit.checkTextMeetTheRegex(add, province)) {
				add = "中国 "+ add;
			}
		} catch (Exception e) {
			System.out.println("识别Ip异常:"+e.getMessage());
			add = address;
		}
		log.info("IP地址：{}，获取地址：{}，耗时：{}ms", ip, add, System.currentTimeMillis() - startTime);
        return add;
    }

    /**
     * 特殊地址整理
     *
     * @param country
     * @return
     */
    private String sensitiveFilter(String country) {
        if (StringUtils.isEmpty(country)) {
            return null;
        }
        country = country.replaceFirst("香港", "中国 香港");
        country = country.replaceFirst("澳门", "中国 澳门");
        country = country.replaceFirst("台湾", "中国 台湾");
        country = country.replaceFirst("中华民国", "中国 台湾");
        return country;
    }
    
    /**
     * 校验是否是域名
     * @param host
     * @return
     */
    private static boolean checkFirthChat(String host){
    	char firstChar = host.charAt(0);
    	if (Character.isLetter(firstChar)) {
    	    return true;
    	}
    	return false;
    }

    
    
    /**
	 * 获取地址
	 * @param ip
	 * @return
	 */
	public String searcherAddress(String ip){
		 try {
			long sTime = System.nanoTime();
			 String region = searcher.search(ip).trim();
			 if(StringUtils.isBlank(region)) {
				return ""; 
			 }
			 region = region.replaceAll("0", "").replaceAll("\\|", " ");
			 long cost = TimeUnit.NANOSECONDS.toMicros((long) (System.nanoTime() - sTime));
			 System.out.printf("{region: %s, ioCount: %d, took: %d μs}\n", region, searcher.getIOCount(), cost);
			 return region;
		} catch (Exception e) {
			e.getMessage();
		}
		return null;
	}
    
    public static void main(String[] args) {
//    	System.out.println(getHttpCityInfo("***********",""));
	}
}
