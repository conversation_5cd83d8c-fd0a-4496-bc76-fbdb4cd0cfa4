package cn.ijiami.detection.utils.ftp;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.StringTokenizer;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

/**
 * FTP 操作类
 * <AUTHOR>
 *
 */
public class FtpUtil {
    
    public static void main(String[] args) {
//        FtpUtil ftpUtil = new FtpUtil();
//        String path = "/zywa/ftpdata/201/370000/215/20210105/";
//        String fileName = "123.json";
//        boolean res = ftpUtil.uploadFile(path, fileName, "F:/1561.TXT");
//        System.out.println(res);
//        ftpUtil.deleteFile("/1", "1.zip");
//        ftpUtil.downLoadFile("/zywa/ftpdata/", "83ba84138aec4a44add7968cabb57702_base_apk_1609905852390_add.json", "F:/");
        
        
//        String p = "/zywa/ftpdata/201/370000/215/20210105/2131.apk";
//        System.out.println(p.substring(0,p.lastIndexOf("/")+1));
//        System.out.println(p.substring(p.lastIndexOf("/")+1,p.length()));
    }
    
    /**
     * Ftp 服务器地址
     **/
    public String hostname = "";
    /**
     * Ftp 端口号
     **/
    public int port = 2100;
    /**
     * Ftp 登录账号
     **/
//    public String username = "ftpuser";
    public String username = "";
    /**
     * Ftp 登录密码
     **/
//    public String password = "ftpuser123";
    public String password = "";
    /**
     * 设置缓冲区大小4M
     **/
    private static final int BUFFER_SIZE = 1024 * 1024 * 4;
    /**
     * Ftp 操作对象
     **/
    public FTPClient ftpClient = null;

    /**
     * 连接FTP服务器
     *
     * @param address  地址
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     */
    private void login() {
        ftpClient = new FTPClient();
        ftpClient.setControlEncoding("utf-8");
        try {
            ftpClient.connect(hostname, port);
            ftpClient.login(username, password);
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);//文件类型为二进制文件
            ftpClient.setBufferSize(BUFFER_SIZE);//限制缓冲区大小
            int reply = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                closeConnect();
                System.out.println("FTP服务器连接失败");
            }
        } catch (Exception e) {
            System.out.println("FTP登录失败" + e.getMessage());
        }
    }

    /**
     * 关闭FTP连接
     */
    private void closeConnect() {
        if (ftpClient != null && ftpClient.isConnected()) {
            try {
                ftpClient.logout();
                ftpClient.disconnect();
            } catch (IOException e) {
                System.out.println("关闭FTP连接失败" + e.getMessage());
            }
        }
    }
    
    /**
     * 下载文件
     * @param ftpPath FTP文件目录
     * @param fileName 需下载的文件名
     * @param savePath 下载后的文件路径
     * @return 返回是否下载成功 true
     */
    public Boolean downLoadFile(String ftpPath, String fileName, String savePath){
        login();
        OutputStream os = null;
        if (ftpClient != null) {
            try {
                //判断是否存在该目录
                if (!ftpClient.changeWorkingDirectory(ftpPath)) {
                    System.out.println("/" + ftpPath + "该目录不存在");
                    return false;
                }
                ftpClient.enterLocalPassiveMode();//设置被动模式，开通一个端口来传输数据

                FTPFile[] ftpFiles = ftpClient.listFiles();
                // 判断该目录下是否有文件
                if (ftpFiles == null || ftpFiles.length == 0) {
                    System.out.println("/" + ftpPath + "该目录下无文件");
                    return false;
                }
                for(FTPFile file : ftpFiles){
//                    if(fileName.equals("") || fileName.equalsIgnoreCase(file.getName())){//文件名称为""，下载指定文件
                    if ("".equals(fileName) || file.getName().equalsIgnoreCase(fileName)) {
                        if(!file.isDirectory()){//是否文件夹 
                            File saveFile = new File(savePath + "/" + file.getName()); 
                            os = new FileOutputStream(saveFile); 
                            ftpClient.retrieveFile(file.getName(), os); 
                            os.close(); 
                        }
                    }
                }
                return true;
            } catch (IOException e) {
                System.out.println("下载文件失败" + e.getMessage());
            } finally {
                if(null != os){
                    try {
                        os.close();
                    } catch (IOException e) {
                        e.getMessage();
                    } 
                }
                closeConnect();
            }
        }
        return false;
    }
    
    public Boolean downLoadFile(String ftpPath, String savePath){
        login();
        OutputStream os = null;
        String fileName = "";
        if (ftpClient != null) {
            try {
                //判断是否存在该目录
                if (!ftpClient.changeWorkingDirectory(ftpPath)) {
                    System.out.println("/" + ftpPath + "该目录不存在");
                    return false;
                }
                ftpClient.enterLocalPassiveMode();//设置被动模式，开通一个端口来传输数据

                FTPFile[] ftpFiles = ftpClient.listFiles();
                // 判断该目录下是否有文件
                if (ftpFiles == null || ftpFiles.length == 0) {
                    System.out.println("/" + ftpPath + "该目录下无文件");
                    return false;
                }
                for(FTPFile file : ftpFiles){
//                    if(fileName.equals("") || fileName.equalsIgnoreCase(file.getName())){//文件名称为""，下载指定文件
                    if ("".equals(fileName) || file.getName().equalsIgnoreCase(fileName)) {
                        if(!file.isDirectory()){//是否文件夹 
                            File saveFile = new File(savePath + "/" + file.getName()); 
                            os = new FileOutputStream(saveFile); 
                            ftpClient.retrieveFile(file.getName(), os); 
                            os.close(); 
                        }
                    }
                }
                return true;
            } catch (IOException e) {
                System.out.println("下载文件失败" + e.getMessage());
            } finally {
                if(null != os){
                    try {
                        os.close();
                    } catch (IOException e) {
                        e.getMessage();
                    } 
                }
                closeConnect();
            }
        }
        return false;
    }

    /**
    * 上传文件
    * @param savePath FTP保存目录
    * @param fileName 上传到FTP的文件名
    * @param filePath 待上传文件的名称（绝对地址）
    * @return
    */
    public boolean uploadFile(String savePath, String fileName,String filePath){
        login();
        boolean flag = false;
        InputStream inputStream = null;
        if (ftpClient != null) {
            try{
            	File srcFile = new File(filePath); 
            	inputStream = new FileInputStream(srcFile); 
                // 判断是否有对应的目录
                if (!ftpClient.changeWorkingDirectory(savePath)) {
                    // 创建目录
                    mkdir(ftpClient, savePath);
                }
                ftpClient.changeWorkingDirectory(savePath); //切换目录
                ftpClient.setBufferSize(1024); 
                ftpClient.setControlEncoding("GBK"); 
                //设置为被动模式
                ftpClient.enterLocalPassiveMode();
                //设置文件类型（二进制） 
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE); 
                ftpClient.storeFile(fileName, inputStream); 
            	
                inputStream.close();
                flag = true;
            }catch (Exception e) {
                e.getMessage();
            }finally{
                if(null != inputStream){
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        e.getMessage();
                    } 
                }
                closeConnect();
            }
        }
        return flag;
    }

    /** * 删除文件 * 
    * @param pathname FTP服务器保存目录
    * @param filename 要删除的文件名称 
    * @return */ 
    public boolean deleteFile(String filePath, String filename){
        login();
        boolean flag = false; 
        if (ftpClient != null) {
            try { 
                ftpClient.changeWorkingDirectory(filePath);
                ftpClient.dele(filename); 
                flag = true; 
            } catch (Exception e) { 
                e.getMessage(); 
            } finally {
                closeConnect();
            }
        }
        return flag; 
    }
    
    /**
     * 上传文件
     * @param hostname ftp地址
     * @param username 用户名
     * @param password 密码
     * @param uploadFilePath 本地文件的路径
     * @param fileName 上传后的名称
     * @param ftpWorkPath ftp目录
     * @param port  端口
     * @throws RuntimeException
     */
//    public static void uploadFtpFile(String hostname,String username,
//			 String password,String uploadFilePath,String fileName,String ftpWorkPath,int port) 
//					 throws RuntimeException{ 
//       FTPClient ftpClient = new FTPClient(); 
//       FileInputStream fis = null; 
//
//       try { 
//           ftpClient.connect(hostname,port); 
//           ftpClient.login(username, password); 
//
//           File srcFile = new File(uploadFilePath); 
//           fis = new FileInputStream(srcFile); 
//           // 判断是否有对应的目录
//           if (!ftpClient.changeWorkingDirectory(ftpWorkPath)) {
//               // 创建目录
//               mkdir(ftpClient, ftpWorkPath);
//           }
//           ftpClient.changeWorkingDirectory(ftpWorkPath); //切换目录
//           ftpClient.setBufferSize(1024); 
//           ftpClient.setControlEncoding("GBK"); 
//           
//           //设置为被动模式
//           ftpClient.enterLocalPassiveMode();
//           
//           //设置文件类型（二进制） 
//           ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE); 
//           ftpClient.storeFile(fileName, fis); 
//       } catch (IOException e) { 
//           e.printStackTrace(); 
//           throw new RuntimeException("FTP客户端出错！", e); 
//       } finally { 
//           IOUtils.closeQuietly(fis); 
//           try { 
//               ftpClient.disconnect(); 
//           } catch (IOException e) { 
//               e.printStackTrace(); 
//               throw new RuntimeException("关闭FTP连接发生异常！", e); 
//           } 
//           System.out.println("已上传至FTP服务器路径！"+ftpWorkPath+fileName);
//       } 
//   }
    
   private static void mkdir(FTPClient ftpClient, String path){
	   StringTokenizer s = new StringTokenizer(path, "/");
	   s.countTokens(); 
	   String pathName = ""; 
	   while (s.hasMoreElements()) { 
	   	   pathName = pathName + "/" + (String) s.nextElement(); 
	   	   try { 
	   		   ftpClient.mkd(pathName); 
	   	   } catch (Exception e) { 
	   		   System.out.println("ftp文件夹创建失败");
	   	   } 
	   } 
   }

}