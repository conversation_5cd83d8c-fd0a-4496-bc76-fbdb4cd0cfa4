package cn.ijiami.detection.utils;

import org.dom4j.io.SAXReader;
import org.xml.sax.SAXException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SAXReaderFactory.java
 * @createTime 2023年03月10日 16:05:00
 */
public class SAXReaderFactory {

    private SAXReaderFactory() {

    }

    /**
     * 防止XXE攻击
     * @return
     * @throws SAXException
     */
    public static SAXReader createSafeSaxReader() throws SAXException {
        SAXReader reader = new SAXReader();
        reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
        reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        return reader;
    }

}