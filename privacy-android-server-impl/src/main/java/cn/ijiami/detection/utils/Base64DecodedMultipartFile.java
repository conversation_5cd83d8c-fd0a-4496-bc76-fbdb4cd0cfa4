package cn.ijiami.detection.utils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 用于接收base64图片
 *
 * <AUTHOR>
 * @date 2020-08-25 14:44
 */
public class Base64DecodedMultipartFile implements MultipartFile {
    private static final String BASE64_PREFIX = "data:";
    private final        byte[] imgContent;
    private final        String header;

    public Base64DecodedMultipartFile(byte[] imgContent, String header) {
        this.imgContent = imgContent;
        this.header = header.split(";")[0];
    }

    @Override
    public String getName() {
        return UuidUtil.uuid() + "." + header.split("/")[1];
    }

    @Override
    public String getOriginalFilename() {
        return UuidUtil.uuid() + "." + header.split("/")[1];
    }

    @Override
    public String getContentType() {
        return header.split(":")[1];
    }

    @Override
    public boolean isEmpty() {
        return imgContent == null || imgContent.length == 0;
    }

    @Override
    public long getSize() {
        return imgContent.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return imgContent;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(imgContent);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        new FileOutputStream(dest).write(imgContent);
    }

    /**
     * base64 转 MultipartFile
     *
     * @param base64
     * @return
     */
    public static MultipartFile base64ToMultipart(String base64) throws IOException {
        if (!base64.startsWith(BASE64_PREFIX)) {
            base64 = "data:image/png;base64," + base64;
        }
        String[] strings = base64.split(",");
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] b;
        b = decoder.decode(strings[1]);
        for (int i = 0; i < b.length; ++i) {
            if (b[i] < 0) {
                b[i] += 256;
            }
        }
        return new Base64DecodedMultipartFile(b, strings[0]);
    }
}





