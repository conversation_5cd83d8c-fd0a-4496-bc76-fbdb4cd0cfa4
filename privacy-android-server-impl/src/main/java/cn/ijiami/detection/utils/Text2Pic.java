package cn.ijiami.detection.utils;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * 将Text转变这个一个图片：
 * 一、支持自动换行功能；
 * 二、支持文本高亮的功能；（高亮部分的文本使用<em> </em>扩起来）
 *
 * <AUTHOR>
 */
public class Text2Pic {
    private String text;//字符串内容
    public int fontsize = 18;//字符大小
    private int margin = 1;//边
    private int countOneLine = 60;//每一行的字符个数
    public Font fontNormal = new Font("微软雅黑", Font.PLAIN, fontsize);
    private int imageWidth = 0;//图片的最大宽度
    private int imageHeight = 0;//图片的最大高度
    private double oneLineTextHeight = 0;
    private String PicType = PictureType.TP_PNG.toString();
    //高亮符号位置信息信息
    List<block> emChrList = new ArrayList<block>();

    public enum PictureType {
        TP_PNG("png"), TP_JPG("jpeg"), TP_GIF("gif");
        private final String type;

        private PictureType(String type) {
            this.type = type;
        }
    }

    ;

    enum wordType {
        normal,
        highlight
    }

    ;

    //线段
    class block {
        public int beginpos;
        public int endpos;
        public String text;
        boolean isHighLight = false;

        public block(int begin, int end) {
            beginpos = begin;
            endpos = end;
        }

        public block() {
        }
    }

    //字符串块
    class highLightText {
        public String text;
        public wordType type;

        public highLightText(String newText, wordType newType) {
            text = newText;
            type = newType;
        }
    }

    /**
     * 构造函数
     *
     * @param strText
     */
    public Text2Pic(String strText) {
        text = strText;
    }

    /**
     * 设置图片的类型
     *
     * @param type
     */
    public void SetPicType(PictureType type) {
        PicType = type.type;
    }

    /**
     * 设置字体
     *
     * @param theFont
     */
    public void SetFont(Font theFont) {
        fontNormal = theFont;
    }

    /**
     * 判断一个点是否在一个线段当中
     *
     * @param pos
     * @param b
     * @return
     */
    public boolean isInblock(int pos, block b) {
        return (pos >= b.beginpos && pos <= b.endpos);
    }

    void Text2Pic(int chrCountOneLine, int fontSize) {
        countOneLine = chrCountOneLine;
        fontsize = fontSize;
    }

    /**
     * 将字符串按照一定长度分割
     *
     * @param s
     * @param len
     * @return
     */
    protected String[] stringSpilt(String s, int len) {
        int spiltNum;//len->想要分割获得的子字符串的长度
        int sLen = s.length();
        spiltNum = sLen / len;

        if ((sLen % len) > 0)
            spiltNum++;

        String[] subs = new String[spiltNum];//创建可分割数量的数组
        //可用一个全局变量保存分割的数组的长度
        //System.out.println(subs.length);
        int start = 0;
        for (int i = 0; i < subs.length; i++, start += len) {
            if (i == subs.length - 1) {
                subs[i] = s.substring(start);
            } else {
                subs[i] = s.substring(start, start + len);
            }
        }
        return subs;
    }

    /**
     * 获取到行子当中所有因为高亮符号分割的子线段
     *
     * @param text
     * @param beginpos
     * @return
     */
    private List<block> GetHightLightBlocks(String text, int beginpos) {
        List<block> blocks = new ArrayList<block>();

        int endpos = beginpos + text.length();

        int beginIdx = -1;
        int endIdx = -1;
        boolean isBeginposHighlight = false;
        block lineBlock = new block(beginpos, endpos);
        Set<Integer> pointSet = new TreeSet<>();
        pointSet.add(beginpos);
        pointSet.add(endpos);
        //1.找到高亮文字的范围，获取到行当中一个线段中所有的端点
        for (int i = 0; i < emChrList.size(); i++) {
            block item = emChrList.get(i);
            if (beginIdx < 0) {
                if (beginpos < item.endpos) {
                    beginIdx = i;
                    isBeginposHighlight = isInblock(beginpos, item);
                }
            }
            if (beginIdx >= 0) {
                if (isInblock(item.beginpos, lineBlock))
                    pointSet.add(item.beginpos);

                if (isInblock(item.endpos, lineBlock))
                    pointSet.add(item.endpos);

                if (endIdx < 0 && endpos < item.beginpos) {
                    endIdx = i;
                    break;
                }
            }
        }
        //2.根据高亮范围切分那些是高亮的块，那些是不高亮的快
        int i = 0;
        Integer lastVal = 0;
        for (Integer val : pointSet) {
            if (i > 0) {
                block item = new block(lastVal, val);
                item.isHighLight = isBeginposHighlight;
                item.text = text.substring(lastVal - beginpos, val - beginpos);

                isBeginposHighlight = !isBeginposHighlight;

                blocks.add(item);
            }
            lastVal = val;
            i++;
        }

        return blocks;
    }

    /**
     * 将文字中高亮的部分解析出来并且安装字符串长度自动换行
     *
     * @param text
     * @return
     */
    protected List<highLightText> fromatString(String text) {
        List<highLightText> blocks = new ArrayList<highLightText>();

        String patternString = "\n.*?";
        Pattern pattern = Pattern.compile(patternString);

        //获取高亮符号的位置信息
        String patternString1 = "<em>[\\s\\S]*?</em>";
        Pattern pattern1 = Pattern.compile(patternString1);

        Matcher matcher = pattern1.matcher(text);

        //高亮的标记符先去掉，并记录位置到所有的开始和结束的位置
        int count = 0;
        String newText = text;
        while (matcher.find()) {
            int pattenLen = 9;
            block b = new block();
            b.beginpos = matcher.start() - count * pattenLen;
            b.endpos = matcher.end() - count * pattenLen;
            b.endpos -= pattenLen;
            count++;
            StringBuffer str11 = new StringBuffer(newText);

            //删除字符串
            str11.delete(b.endpos + pattenLen - 5, b.endpos + pattenLen);
            str11.delete(b.beginpos, b.beginpos + 4);
            newText = str11.toString();
//            System.out.println("found: " + count + " : " + b.beginpos + " - " + b.endpos + " == " + newText.substring(b.beginpos, b.endpos));
            emChrList.add(b);
        }
        //按照字符长度进行格式化
        String[] split = pattern.split(newText);
        int posBegin = 0;
        int posEnd = 0;
        FontRenderContext fontContext = new FontRenderContext(AffineTransform.getScaleInstance(1, 1), false, false);
        for (int i = 0; i < split.length; i++, posBegin += 2) {
            int textLen = split[i].length();
            posEnd = posBegin + textLen;
            String lineText = split[i] + " ";

            //将字符串分割成多行
            String[] texts = stringSpilt(lineText, countOneLine);
            for (int j = 0; j < texts.length; j++) {
                //计算图片的最大宽度
                Rectangle2D r = fontNormal.getStringBounds(texts[j], fontContext);

                int tempWidth = (int) Math.floor(r.getWidth());
                if (tempWidth > imageWidth)
                    imageWidth = tempWidth;

                blocks.add(new highLightText(texts[j], wordType.normal));
            }
        }

        //获取font的样式应用在str上的整个矩形
        String text10chr = new String("0123456789");
        Rectangle2D r = fontNormal.getStringBounds(text10chr, fontContext);
        oneLineTextHeight = r.getHeight();

        imageHeight = (int) Math.floor(oneLineTextHeight * (blocks.size() + 1));
        return blocks;
    }

    /**
     * @lqc
     * @根据指定的str去生成图片
     */
    public byte[] buildImage(File outFile) {
        Font textfont = null;
        return buildImage(outFile, textfont);
    }

    /**
     * 将Text转变这个一个图片
     *
     * @param outFile
     * @param textfont
     * @return
     */
    public byte[] buildImage(File outFile, Font textfont) {
        if (text == null || text.length() == 0)
            return null;

        if (textfont != null)
            fontNormal = textfont;

        //首先，将字符串格式化
        List<highLightText> formatText = fromatString(text);

        //创建图片
        BufferedImage image = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_BGR);
        Graphics g = image.getGraphics();

        g.setColor(Color.WHITE);
        //先用白色填充整张图片,也就是背景
        g.fillRect(0, 0, imageWidth, imageHeight);
        //在换成黑色
        g.setColor(Color.black);
        //设置画笔字体
        g.setFont(fontNormal);
        //获取到字体的格式
        FontMetrics fm = g.getFontMetrics(fontNormal);

        //画出字符串
        int i = 1;
        int pos = 0;
        for (highLightText tmp : formatText) {
//            System.out.println(tmp.text);
            List<block> tiems = GetHightLightBlocks(tmp.text, pos);
            for (block item : tiems) {
                if (item.isHighLight)
                    g.setColor(Color.red);
                else
                    g.setColor(Color.black);
                int stringWidth = fm.stringWidth(tmp.text.substring(0, item.beginpos - pos));
                g.drawString(item.text, margin + stringWidth, (int) Math.floor(i * oneLineTextHeight));
            }

            i++;
            pos += tmp.text.length();
        }
        g.dispose();
        //Image数据量的功能暂不提供
        try {
            if (outFile != null) {
                //输出png图片到指定目录
                ImageIO.write(image, PicType, outFile);
            }
            //返回数据流
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            boolean flag = ImageIO.write(image, PicType, out);
            byte[] b = out.toByteArray();
            return b;
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }
    
    /**使用FileOutputStream来写入txt文件
     * @param txtPath txt文件路径
     * @param content 需要写入的文本
     */
    public static void writeTxt(String txtPath,String content){    
       FileOutputStream fileOutputStream = null;
       File file = new File(txtPath);
       try {
           if(file.exists()){
               //判断文件是否存在，如果不存在就新建一个txt
               file.createNewFile();
           }
           content = content +"\r\n";
           fileOutputStream = new FileOutputStream(file,true);
           fileOutputStream.write(content.getBytes());
           fileOutputStream.flush();
           
       } catch (Exception e) {
           e.getMessage();
       }finally {
    	   try {
			fileOutputStream.close();
		} catch (IOException e) {
			e.getMessage();
		}
       }
    }
    
    /**
     * 将数据分批次进行序列化和写入操作
     * @param txtPath
     * @param map
     * @param chunkSize
     */
    public static void writeLargeMapInChunks(String txtPath, Map<String, Object> map, int chunkSize) {
        FileOutputStream fileOutputStream = null;
        File file = new File(txtPath);
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            
            fileOutputStream = new FileOutputStream(file, true);
            int count = 0;
            JSONObject chunk = new JSONObject();
            
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                chunk.put(entry.getKey(), entry.getValue());
                count++;
                
                if (count % chunkSize == 0) {
                    String content = JSONObject.toJSONString(chunk) + "\r\n";
                    fileOutputStream.write(content.getBytes());
                    fileOutputStream.flush();
                    chunk.clear();
                }
            }
            
            if (!chunk.isEmpty()) {
                String content = JSONObject.toJSONString(chunk) + "\r\n";
                fileOutputStream.write(content.getBytes());
                fileOutputStream.flush();
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    e.getMessage();
                }
            }
        }
    }

    
    /**
     * 功能：Java读取txt文件的内容
     * 步骤：1：先获得文件句柄
     * 2：获得文件句柄当做是输入一个字节码流，需要对这个输入流进行读取
     * 3：读取到输入流后，需要读取生成字节流
     * 4：一行一行的输出。readline()。
     * 备注：需要考虑的是异常情况
     * @param filePath
     */
    public static String readTxtFile(String filePath){
    	StringBuilder result = new StringBuilder();
        try {
            String encoding="GBK";
            File file=new File(filePath);
            if(file.isFile() && file.exists()){ //判断文件是否存在
                InputStreamReader read = new InputStreamReader(
                new FileInputStream(file),encoding);//考虑到编码格式
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while((lineTxt = bufferedReader.readLine()) != null){
                    result.append(lineTxt);
                }
                read.close();
        }else{
            System.out.println("找不到指定的文件");
        }
        } catch (Exception e) {
            System.out.println("读取文件内容出错");
            e.getMessage();
        }
        return result.toString();
    }
    
    public static String readTxtFileUtf8(String filePath){
    	StringBuilder result = new StringBuilder();
        try {
            String encoding="UTF-8";
            File file=new File(filePath);
            if(file.isFile() && file.exists()){ //判断文件是否存在
                InputStreamReader read = new InputStreamReader(
                new FileInputStream(file),encoding);//考虑到编码格式
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while((lineTxt = bufferedReader.readLine()) != null){
                    result.append(lineTxt);
                }
                read.close();
        }else{
            System.out.println("找不到指定的文件");
        }
        } catch (Exception e) {
            System.out.println("读取文件内容出错");
            e.getMessage();
        }
        return result.toString();
    }
    
    /**
     * 功能：Java读取txt文件的内容
     * 步骤：1：先获得文件句柄
     * 2：获得文件句柄当做是输入一个字节码流，需要对这个输入流进行读取
     * 3：读取到输入流后，需要读取生成字节流
     * 4：一行一行的输出。readline()。
     * 备注：需要考虑的是异常情况
     * @param filePath
     */
    public static String readTxtFile1(String filePath){
    	StringBuilder result = new StringBuilder();
        try {
            String encoding="GBK";
            File file=new File(filePath);
            if(file.isFile() && file.exists()){ //判断文件是否存在
                InputStreamReader read = new InputStreamReader(
                new FileInputStream(file),encoding);//考虑到编码格式
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while((lineTxt = bufferedReader.readLine()) != null){
                    result.append(lineTxt+"\r\n");
                }
                read.close();
        }else{
            System.out.println("找不到指定的文件");
        }
        } catch (Exception e) {
            System.out.println("读取文件内容出错");
            e.getMessage();
        }
        return result.toString();
    }
    
    public static List<String> readTxtFileList(String filePath){
    	List<String> list = new ArrayList<>();
        try {
            String encoding="GBK";
            File file=new File(filePath);
            if(file.isFile() && file.exists()){ //判断文件是否存在
                InputStreamReader read = new InputStreamReader(
                new FileInputStream(file),encoding);//考虑到编码格式
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while((lineTxt = bufferedReader.readLine()) != null){
                	if(StringUtils.isBlank(lineTxt)) {
                		continue;
                	}
                    list.add(lineTxt);
                }
                read.close();
        }else{
            System.out.println("找不到指定的文件");
        }
        } catch (Exception e) {
            System.out.println("读取文件内容出错");
            e.getMessage();
        }
        
        return list;
    }
    
    private static boolean hasWord(String fullText, String... words) {
        if (StringUtils.isBlank(fullText) || words.length == 0) {
            return false;
        }
        return Arrays.stream(words).anyMatch(fullText::contains);
    }
    
    public static List<String> readTxtFileList1(String filePath){
    	List<String> list = new ArrayList<>();
        try {
            String encoding="UTF-8";
            File file=new File(filePath);
            if(file.isFile() && file.exists()){ //判断文件是否存在
                InputStreamReader read = new InputStreamReader(
                new FileInputStream(file),encoding);//考虑到编码格式
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while((lineTxt = bufferedReader.readLine()) != null){
                	if(StringUtils.isBlank(lineTxt) && !lineTxt.contains("sql错误：")) {
                		continue;
                	}
                	if(!lineTxt.contains("sql错误：")) {
                		continue;
                	}
                    list.add(lineTxt);
                }
                read.close();
        }else{
            System.out.println("找不到指定的文件");
        }
        } catch (Exception e) {
            System.out.println("读取文件内容出错");
            e.getMessage();
        }
        
        return list;
    }
    

    public static void main(String[] args) {
//        String text = "text1:我是一个测试用的实用的实用的实用的实验<em>1</em>\n我是一个测试用的实用的实用的实<em>2</em>一个正则表达式。<em>是一个用于文本搜索的文本模式。换句话说,在文本中搜索出现的\n模式。例如，你可以用正则表达式搜索网页中的邮箱地址或超链接。是否是一个合法超链接的一部分,如包含域名和后缀,一旦\n获得了Pattern对象，接着</em>可以获得Matcher对象。Matcher 示例用于匹配文本中的模式.示例如下\n我是一个测试用的实用的实用的实用的实验我是一个测试用的实</em>用的实用的实用的实验\n我是张三\n我是李四";
//        String text2 = "text2:我是一个测试用的实用的实用的实用的实验<em>1\n我是\n\n\n一个\n测试用的实用的实用的实2</em>一个正则表达式是一个用于文本搜";
//        text += text2;
//        text += text2;
//        text += text;
//        Text2Pic text2pic = new Text2Pic(text);
//        text2pic.SetPicType(PictureType.TP_JPG);
//        text2pic.buildImage(new File("/Users/<USER>/Desktop/1.jpg"));
//		text2pic.buildImage(text2, new File("c:\\Users\\<USER>\\Desktop\\29.png"));
        
        
//       System.out.println(hasWord("有67741位同城异性在线 登陆后可见其他登陆方式阅读并同意《用户协议》 及《隐私政策》", "登录", "注册", "欢迎"));
//    	String aaa = "E:\\lbex.log";
//    	String wt = "E:\\new.log";
//    	String sql = "E:\\sql.log";
//    	String billcode = "E:\\billcode.log";
//    	List<String> list = readTxtFileList1(aaa);
//    	for (String string : list) {
//    		System.out.println(string);
//    		
//    		// 正则表达式
//            String regex = "<waybillCode>(.*?)</waybillCode>";
//            Pattern pattern = Pattern.compile(regex);
//            Matcher matcher = pattern.matcher(string);
//
//            // 查找匹配
//            if (matcher.find()) {
//                // 提取匹配的内容
//                String waybillCode = matcher.group(1);
//                System.out.println("提取的 waybillCode: " + waybillCode);
//                writeTxt(billcode, waybillCode);
//            } else {
//                System.out.println("未找到匹配的 waybillCode");
//            }
//    		writeTxt(wt, string);
//    		
//    		String sp[] = string.split("======");
//    		writeTxt(sql, sp[1]);
//		}
    	
    	String blacklist = readTxtFileUtf8("C:\\Users\\<USER>\\Desktop\\ai_wihite.json");
    	ObjectMapper objectMapper = new ObjectMapper();
		Map<String, String> mapBlacklist = null;
		Map<String, String> mapWhitelist = null;
		String packageName = "com.cloud.zbqcc";
		String blackWords = null;
		String whiteWords = null;
    	try {
        	if(StringUtils.isNotBlank(blacklist)) {
        		mapBlacklist = objectMapper.readValue(blacklist, new TypeReference<Map<String, String>>() {});
        		blackWords = mapBlacklist.get(packageName);
        		System.out.println(blackWords);
        		System.out.println(mapBlacklist.get("allWord"));
        		
        		//获取全局黑名单
        		String allBlackWord = mapBlacklist.get("allWord");
        		if(StringUtils.isNotBlank(allBlackWord)) {
        			blackWords = blackWords==null ? allBlackWord : (allBlackWord+","+blackWords);
        		}
        		
        		System.out.println(blackWords);
        		
        	}
//        	if(StringUtils.isNotBlank(whitelist)) {
//        		mapWhitelist = objectMapper.readValue(whitelist, new TypeReference<Map<String, String>>() {});
//        		whiteWords = mapWhitelist.get(packageName);
//        	}
        } catch (Exception e) {
            e.getMessage();
        }
    }
}