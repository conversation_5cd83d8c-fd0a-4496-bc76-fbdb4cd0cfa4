package cn.ijiami.detection.utils;

import cn.ijiami.detection.VO.LawActionDetailVO;
import org.apache.commons.lang.StringUtils;

import static cn.ijiami.detection.utils.CommonUtil.containsItemNo;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawActionDetailUtils.java
 * @Description 法规依据工具
 * @createTime 2022年12月12日 17:27:00
 */
public class LawActionDetailUtils {


    //191号文检测项行为数据显示不同表头
    private final static String[] FREQUENCY_ITEM_NO = {"120101", "130101", "130401", "150101"};

    public static void setTimeUnit(LawActionDetailVO actionDetail) {
        if (actionDetail.getIntervalTime() != null) {
            actionDetail.setTimeUnit("秒/次");
            actionDetail.setTriggerNum(Integer.valueOf(actionDetail.getIntervalTime().toString()));
            return;
        }
        if (containsItemNo(FREQUENCY_ITEM_NO, actionDetail.getItemNo())) {
            actionDetail.setTimeUnit("次");
            return;
        }
        actionDetail.setTimeUnit("次/秒");
    }

}
