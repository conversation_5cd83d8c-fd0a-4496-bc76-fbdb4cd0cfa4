package cn.ijiami.detection.utils;

import cn.ijiami.detection.VO.detection.privacy.dto.AppletInputParamDTO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletReturnDataDTO;
import cn.ijiami.detection.constant.PinfoConstant;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletActionUtils.java
 * @Description 小程序日志解析工具
 * @createTime 2023年04月03日 18:12:00
 */
public class AppletActionUtils {

    public static String getStatusCode(AppletReturnDataDTO returnData) {
        if (Objects.isNull(returnData)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(returnData.getStatusCode())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        return returnData.getStatusCode().toString();
    }

    public static String getMethod(AppletInputParamDTO param) {
        if (Objects.isNull(param)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(param.getMethod())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (StringUtils.isBlank(param.getMethod())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        return param.getMethod();
    }

    public static String getCookie(AppletInputParamDTO param) {
        if (Objects.isNull(param)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(param.getHeader())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(param.getHeader().get("cookie"))) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        String cookie = param.getHeader().get("cookie").toString();
        if (StringUtils.isBlank(cookie)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        return cookie;
    }

    public static String getParams(AppletInputParamDTO param) {
        if (Objects.isNull(param)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(param.getData())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        return param.getData().toString();
    }

    public static String getResponseData(AppletReturnDataDTO returnData) {
        if (Objects.isNull(returnData)) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        if (Objects.isNull(returnData.getData())) {
            return PinfoConstant.DETAILS_EMPTY;
        }
        return returnData.getData().toString();
    }
}
