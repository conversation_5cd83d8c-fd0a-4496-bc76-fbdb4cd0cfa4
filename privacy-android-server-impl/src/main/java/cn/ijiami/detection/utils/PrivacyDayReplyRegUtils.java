package cn.ijiami.detection.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

import cn.ijiami.detection.VO.PrivacyDayReplyRegVO;

public class PrivacyDayReplyRegUtils {
	public static void main(String[] args) {
		String text = "三十天内回复，30天内回复，15个工作日回复你，十五个工作日回复你。";
		text = "我们管理人员会根据您的反馈与建议，尽快审核所涉问题，并在验证您的用户身份，一般情况下，我们将在20个工作日内啊啊啊回复，我们将在25天个工作日日回复来全面保障您的隐私安全问题";
		PrivacyDayReplyRegVO vo = privacyDayReplyRegs(text);
		if(vo != null) {
			System.out.println(JSONObject.toJSONString(vo));
		}
		
//		int day = privacyDayReplyReg(text);
//		System.out.println(day);
	}

//	public static int privacyDayReplyReg(String content) {
//		// String text = "三十天内回复，30天内回复，15个工作日回复你，十五个工作日回复你。";
//		// 匹配中文数字或阿拉伯数字的正则表达式
//		Pattern pattern = Pattern.compile("(?:[一二三四五六七八九十百千万亿零两]+|\\d+)(?:天|天内|个工作日|工作日)(.*?)?(回复|答复|处理)");
//		Matcher matcher = pattern.matcher(content);
//		Map<String, Integer> chineseToArabicMap = createChineseToArabicMap();
//		System.out.println("匹配的数字:");
//		int day = -1;
//		while (matcher.find()) {
//			String matchedText = matcher.group();
//			System.out.println(matchedText);
//			// 提取数字部分（去除"天内回复"或"个工作日回复"）
//			String numberPart = matchedText.replaceAll("[^一二三四五六七八九十百千万亿零两\\d]+", "");
//			// 将中文数字转换为阿拉伯数字
//			if (isNumeric(numberPart)) {
//				System.out.println(matchedText + " 对应的阿拉伯数字是：" + numberPart);
//				day = Integer.valueOf(numberPart);
//			} else {
//				int arabicNumber = convertChineseToArabic(numberPart, chineseToArabicMap);
//				System.out.println(matchedText + " 对应的阿拉伯数字是：" + arabicNumber);
//				day = arabicNumber;
//			}
//		}
//		return day;
//	}
	
	public static int privacyDayReplyReg(String content) {
	    // 优化后的正则表达式，减少回溯风险
	    Pattern pattern = Pattern.compile(
	        "(?<number>[一二三四五六七八九十百千万亿零两]+|\\d+)(?<unit>天|天内|个工作日|工作日)(?<action>回复|答复|处理)"
	    );
	    Matcher matcher = pattern.matcher(content);

	    Map<String, Integer> chineseToArabicMap = createChineseToArabicMap();
	    System.out.println("匹配的数字:");

	    int day = -1;

	    while (matcher.find()) {
	        // 提取数字部分（通过命名捕获组）
	        String numberPart = matcher.group("number");
	        String unit = matcher.group("unit");
	        String action = matcher.group("action");

	        // 打印完整匹配的内容
	        System.out.println("匹配到的内容: 数字=" + numberPart + ", 单位=" + unit + ", 动作=" + action);

	        // 将中文数字转换为阿拉伯数字
	        if (isNumeric(numberPart)) {
	            System.out.println(numberPart + " 对应的阿拉伯数字是：" + numberPart);
	            day = Integer.parseInt(numberPart);
	        } else {
	            int arabicNumber = convertChineseToArabic(numberPart, chineseToArabicMap);
	            System.out.println(numberPart + " 对应的阿拉伯数字是：" + arabicNumber);
	            day = arabicNumber;
	        }
	    }

	    return day; // 返回最后一个匹配的天数
	}
	
	public static PrivacyDayReplyRegVO privacyDayReplyRegs(String content) {
	    if (StringUtils.isBlank(content)) {
	        return null;
	    }

	    PrivacyDayReplyRegVO vo = new PrivacyDayReplyRegVO();
	    Map<String, Integer> chineseToArabicMap = createChineseToArabicMap();

	    try {
	        // 优化后的正则表达式，减少回溯风险
	        Pattern pattern = Pattern.compile(
	            "(?<number>[一二三四五六七八九十百千万亿零两]+|\\d+)" +
	            "(?<unit>天|天内|个工作日|工作日)" +
	            "(?<action>回复|答复|处理)"
	        );
	        Matcher matcher = pattern.matcher(content);

	        System.out.println("匹配的数字:");
	        int day = -1;

	        while (matcher.find()) {
	            // 提取数字部分（通过命名捕获组）
	            String numberPart = matcher.group("number");
	            String unit = matcher.group("unit");
	            String action = matcher.group("action");

	            // 打印完整匹配的内容
	            System.out.println("匹配到的内容: 数字=" + numberPart + ", 单位=" + unit + ", 动作=" + action);

	            // 将中文数字转换为阿拉伯数字
	            if (isNumeric(numberPart)) {
	                System.out.println(numberPart + " 对应的阿拉伯数字是：" + numberPart);
	                day = Integer.parseInt(numberPart);
	            } else {
	                day = convertChineseToArabic(numberPart, chineseToArabicMap);
	                System.out.println(numberPart + " 对应的阿拉伯数字是：" + day);
	            }
	            vo.setDay(day); // 设置天数
	        }

	        if (day < 0) {
	            return null; // 如果未匹配到任何天数，返回 null
	        }

	        // 匹配上下文内容
	        Pattern patternContent = Pattern.compile(
	            ".{0,100}" +
	            "(?<number>[一二三四五六七八九十百千万亿零两]+|\\d+)" +
	            "(?<unit>天|天内|个工作日|工作日)" +
	            "(?<action>回复|答复|处理)" +
	            ".{0,100}"
	        );
	        Matcher matcherContent = patternContent.matcher(content);

	        while (matcherContent.find()) {
	            String matchedContent = matcherContent.group();
	            vo.setContent(matchedContent); // 设置上下文内容
	        }
	    } catch (Exception e) {
	        // 记录日志或抛出异常
	        System.err.println("处理过程中发生错误: " + e.getMessage());
	        return null;
	    }

	    return vo;
	}

	public static boolean isNumeric(String str) {
		// 匹配纯数字的正则表达式
		Pattern pattern = Pattern.compile("^\\d+$");
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}

	// 创建中文数字与阿拉伯数字的映射表
	public static Map<String, Integer> createChineseToArabicMap() {
		Map<String, Integer> chineseToArabicMap = new HashMap<>();
		chineseToArabicMap.put("一", 1);
		chineseToArabicMap.put("二", 2);
		chineseToArabicMap.put("三", 3);
		chineseToArabicMap.put("四", 4);
		chineseToArabicMap.put("五", 5);
		chineseToArabicMap.put("六", 6);
		chineseToArabicMap.put("七", 7);
		chineseToArabicMap.put("八", 8);
		chineseToArabicMap.put("九", 9);
		chineseToArabicMap.put("十", 10);
		chineseToArabicMap.put("十一", 11);
		chineseToArabicMap.put("十二", 12);
		chineseToArabicMap.put("十三", 13);
		chineseToArabicMap.put("十四", 14);
		chineseToArabicMap.put("十五", 15);
		chineseToArabicMap.put("十六", 16);
		chineseToArabicMap.put("十七", 17);
		chineseToArabicMap.put("十八", 18);
		chineseToArabicMap.put("十九", 19);
		chineseToArabicMap.put("二十", 20);
		chineseToArabicMap.put("二十一", 21);
		chineseToArabicMap.put("二十二", 22);
		chineseToArabicMap.put("二十三", 23);
		chineseToArabicMap.put("二十四", 24);
		chineseToArabicMap.put("二十五", 25);
		chineseToArabicMap.put("二十六", 26);
		chineseToArabicMap.put("二十七", 27);
		chineseToArabicMap.put("二十八", 28);
		chineseToArabicMap.put("二十九", 29);
		chineseToArabicMap.put("三十", 30);
		chineseToArabicMap.put("三十一", 31);
		chineseToArabicMap.put("三十二", 32);
		chineseToArabicMap.put("三十三", 33);
		chineseToArabicMap.put("三十四", 34);
		chineseToArabicMap.put("三十五", 35);
		chineseToArabicMap.put("三十六", 36);
		chineseToArabicMap.put("三十七", 37);
		chineseToArabicMap.put("三十八", 38);
		chineseToArabicMap.put("三十九", 39);
		chineseToArabicMap.put("四十", 40);
		chineseToArabicMap.put("四十一", 41);
		chineseToArabicMap.put("四十二", 42);
		chineseToArabicMap.put("四十三", 43);
		chineseToArabicMap.put("四十四", 44);
		chineseToArabicMap.put("四十五", 45);
		return chineseToArabicMap;
	}

	// 将中文数字转换为阿拉伯数字的方法
	public static int convertChineseToArabic(String chineseNumber, Map<String, Integer> map) {
		int result = 0;
		int temp = 0;
		Integer num;
		for (int i = 0; i < chineseNumber.length(); i++) {
			num = map.get(String.valueOf(chineseNumber.charAt(i)));
			if (num == null) {
				continue;
			}
			if (num < 10) {
				temp = num;
			} else {
				if (temp == 0) {
					temp = 1;
				}
				result += temp * num;
				temp = 0;
			}
		}
		result += temp;
		return result;
	}

}

