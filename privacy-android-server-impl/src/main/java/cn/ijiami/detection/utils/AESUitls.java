package cn.ijiami.detection.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;

public class AESUitls {

    private static final String CIPHERMODEPADDING = "AES/CBC/PKCS5Padding";// 加密模式
    private static final String SECRET_KEY        = "IBmlcYjz5odiJk!#";// 加密密钥 key必须为16位，可更改为自己的key

    /**
     * 加密
     *
     * @param content 加密原文
     * @param offset  偏移量
     * @return
     */
    public static String encrypt(String content, String offset) {
        try {
            if (null == content) {
                return "加密原文不能为null";
            }
            if (null == offset) {
                return "偏移量不能为null";
            }
            byte[] plaintext = content.getBytes("Utf-8");
            byte[] skAsByteArray = SECRET_KEY.getBytes("ASCII");
            SecretKeySpec forAes = new SecretKeySpec(skAsByteArray, "AES");
            byte[] iv = offset.getBytes();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            byte[] ciphertext = encrypt(CIPHERMODEPADDING, forAes, ivSpec, plaintext);
            String base64Ciphertext = HEXUitl.printHexString(ciphertext);
            return base64Ciphertext;
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }

    /**
     * 解密
     *
     * @param content 解密原文
     * @param offset  偏移量
     * @return
     */
    public static String decrypt(String content, String offset) {
        try {
            if (null == content) {
                return "解密原文不能为null";
            }
            if (null == offset) {
                return "偏移量不能为null";
            }
            byte[] s = HEXUitl.hexStringToBytes(content);
            byte[] skAsByteArray = SECRET_KEY.getBytes();
            SecretKeySpec skforAes = new SecretKeySpec(skAsByteArray, "AES");

            byte[] iv = offset.getBytes();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            String decrypted = new String(decrypt(CIPHERMODEPADDING, skforAes, ivSpec, s));
            return decrypted;
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }

    private static byte[] encrypt(String cmp, SecretKey sk, IvParameterSpec iv, byte[] msg) {
        try {
            Cipher c = Cipher.getInstance(cmp);
            c.init(Cipher.ENCRYPT_MODE, sk, iv);
            return c.doFinal(msg);
        } catch (Exception nsae) {
            nsae.getMessage();
        }
        return null;
    }

    private static byte[] decrypt(String cmp, SecretKey sk, IvParameterSpec iv, byte[] cipherText) {
        try {
            Cipher c = Cipher.getInstance(cmp);
            c.init(Cipher.DECRYPT_MODE, sk, iv);
            return c.doFinal(cipherText);
        } catch (Exception nsae) {
        }
        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content
     * @param password
     * @return
     */
    public static String decrypt2(String content, String offset) {

        try {
            //实例化
            Cipher cipher = Cipher.getInstance(CIPHERMODEPADDING);

            byte[] iv = offset.getBytes();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            //使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(SECRET_KEY.getBytes(), "AES"), ivSpec);

            //执行操作
            byte[] result = cipher.doFinal(HEXUitl.hexStringToBytes(content));

            return new String(result, "utf-8");
        } catch (Exception ex) {
        }

        return null;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
//        String content = "{\"u\":\"weibingtie\", \"p\":\"weibingtie0326\"}";
//        System.out.println("需要加密的内容：" + content);
//        long time = System.currentTimeMillis();
//        long l = time * 1000;
//        System.out.println(l);
//        
//        long tt =  DateUtils.formatDate("2030-06-15 12:12:12", DateUtils.DATETIME_DEFAULT_FORMAT).getTime();
//        System.out.println(tt);
//        l = 1907727132000000L;
//        
//        String encrypt = encrypt(content, l + "");
//        System.out.println("密文：" + encrypt);
//        String decrypt = decrypt(encrypt, l + "");
//        System.out.println("解密后的内容：" + decrypt);
//        System.out.println("https://ijmdev.zywa.com.cn/privacy-dev/detection/threeparty/auth/auth?tk=" + encrypt + "&t=" + l);
//        System.out.println("https://demo1.privacy.ijiami.cn/detection/threeparty/auth/auth?tk=" + encrypt + "&t=" + l);
//        System.out.println("http://172.10.33.112:8080/detection/threeparty/auth/auth?tk=" + encrypt + "&t=" + l);
        
    }
}
