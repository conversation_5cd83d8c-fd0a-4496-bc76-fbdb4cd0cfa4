package cn.ijiami.detection.utils;

import java.awt.Graphics;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.tensorflow.SavedModelBundle;
import org.tensorflow.Session;
import org.tensorflow.Tensor;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.enums.CheckBoxStatusEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UIDistinguishUtils.java
 * @Description ui识别工具
 * @createTime 2022年02月22日 10:52:00
 */
@Slf4j
public class UIDistinguishUtils {

    public static final int CHECK_BOX_IMG_SIZE = 48;

    /**
     * 输入必须是RGB格式的48X48像素图片
     * @param image
     * @return
     */
    public static CheckBoxStatusEnum isCheckBoxChecked(BufferedImage image, String checkBoxPbFile, String checkedPbFile) {
        try {
            String system = System.getProperty("os.name");
            if (!StringUtils.contains(system, "windows")) {
                List<String> cpuInfo = EngineCommandUtil.runCmdResult("/bin/bash", new String[]{"cat /proc/cpuinfo"});
                log.info("cat cpu=" + cpuInfo);
                if (cpuInfo.stream().filter(StringUtils::isNoneBlank)
                        .noneMatch(line -> StringUtils.contains(line, "flags") && StringUtils.contains(line, "avx"))) {
                    log.info("CPU不支持AVX指令集，不进行CheckBox模型判断");
                    return CheckBoxStatusEnum.NONE;
                }
            }
            String cpu = System.getProperty("sun.cpu.isalist");
            // java的tensorflow库，暂时不支持arm的处理器
            log.info("cpu=" + cpu);
            if (!StringUtils.containsIgnoreCase(cpu, "amd64") && !StringUtils.containsIgnoreCase(cpu, "x86")) {
                return CheckBoxStatusEnum.NONE;
            }
            int type = isChecked(Tensor.create(getImgTensor(image, false)), checkedPbFile);
            if (type == CheckBoxStatusEnum.NONE.itemValue()) {
                return CheckBoxStatusEnum.NONE;
            } else if (type >= CheckBoxStatusEnum.CHECK.itemValue()) {
                return CheckBoxStatusEnum.CHECK;
            } else {
                return CheckBoxStatusEnum.UNCHECK;
            }
        } catch (Error e) {
            log.error("图片处理异常", e);
            return CheckBoxStatusEnum.NONE;
        }
    }

    private static boolean isCheckBox(Tensor<?> checkTensor, String tf2PbExportDir) {
        if (StringUtils.isBlank(tf2PbExportDir)) throw new IllegalArgumentException("tf2PbExportDir is empty");
        try (Session session = SavedModelBundle.load(tf2PbExportDir, "serve").session()) {
            int type = executeCheckBox(session, checkTensor);
            return type > 0;
        }
    }

    private static int isChecked(Tensor<?> checkTensor, String tf2PbExportDir) {
        if (StringUtils.isBlank(tf2PbExportDir)) throw new IllegalArgumentException("tf2PbExportDir is empty");
        try (Session session = SavedModelBundle.load(tf2PbExportDir, "serve").session()) {
            return executeChecked(session, checkTensor);
        }
    }

    private static int executeCheckBox(Session session, Tensor<?> inputTensor) {
        List<Tensor<?>> results = session.runner()
                .feed("serving_default_conv2d_input:0", inputTensor)
                .fetch("StatefulPartitionedCall:0")
                .run();
        float[][] result = new float[1][2];
        results.get(0).copyTo(result);
        log.info("execute " + Arrays.toString(result[0]));
        float score = 0;
        int type = CheckBoxStatusEnum.NONE.itemValue();
        // tensorflow返回的是[0,1]的区间，越接近1表示可能性最大。
        // 目前是2个分类，则数组数据长度为2，下标index从0-1分别代表：未选中，已选中
        // 示例数据：[0.2, 0.8] 表示未选中状态，[0.9, 0.02] 表示未识别出CheckBox
        for (int i=0; i<result[0].length; i++) {
            float value = result[0][i];
            // 找出最大的那个值，且这个值的可信度要高于0.99
            if (value > score && value > 0.70) {
                score = result[0][i];
                type = i;
            }
        }
        return type;
    }

    private static int executeChecked(Session session, Tensor<?> inputTensor) {
        List<Tensor<?>> results = session.runner()
                .feed("serving_default_conv2d_input:0", inputTensor)
                .fetch("StatefulPartitionedCall:0")
                .run();
        float[][] result = new float[1][3];
        results.get(0).copyTo(result);
        log.info("execute " + Arrays.toString(result[0]));
        float score = 0;
        int type = CheckBoxStatusEnum.NONE.itemValue();
        // tensorflow返回的是[0,1]的区间，越接近1表示可能性最大。
        // 目前是2个分类，则数组数据长度为2，下标index从0-1分别代表：未选中，已选中
        // 示例数据：[0.2, 0.8] 表示未选中状态，[0.9, 0.02] 表示未识别出CheckBox
        for (int i=0; i<result[0].length; i++) {
            float value = result[0][i];
            // 找出最大的那个值，且这个值的可信度要高于0.99
            if (value > score && value > 0.80) {
                score = result[0][i];
                type = i;
            }
        }
        return type;
    }

    private static float[][][][] getImgTensor(BufferedImage checkBoxImage, boolean gray) {
        if (checkBoxImage.getWidth() != CHECK_BOX_IMG_SIZE) {
            checkBoxImage = scaleCheckBoxImg(checkBoxImage);
        }
        File testDir = new File("D:/test/image/");
        try {
            if (testDir.exists()) {
                ImageIO.write(checkBoxImage, "png", new File(testDir, "newImage.png"));
            }
        } catch (IOException e) {
            e.getMessage();
        }
        int w = checkBoxImage.getWidth();
        int h = checkBoxImage.getHeight();
        float[][][][] imgTensor;
        if (gray) {
            imgTensor = new float[1][h][w][1];
        } else {
            imgTensor = new float[1][h][w][3];
        }
        for (int i = 0; i < h; i++) {
            for (int j = 0; j < w; j++) {
                if (gray) {
                    int pixel = checkBoxImage.getRGB(j, i); // 下面三行代码将一个数字转换为RGB数字，转为灰度图
                    int r = (pixel & 0xff0000) >> 16;
                    int g = (pixel & 0xff00) >> 8;
                    int b = (pixel & 0xff);
                    imgTensor[0][i][j][0] = (float) (0.299 * r + 0.587 * g + 0.114 * b) / 255f;
                } else {
                    int pixel = checkBoxImage.getRGB(j, i); // 下面三行代码将一个数字转换为RGB数字，同时归一化到[0,1]区间
                    imgTensor[0][i][j][0] = (float) ((pixel & 0xff0000) >> 16) / 255f;
                    imgTensor[0][i][j][1] = (float) ((pixel & 0xff00) >> 8) / 255f;
                    imgTensor[0][i][j][2] = (float) ((pixel & 0xff)) / 255f;
                }
            }
        }
        return imgTensor;
    }

    private static BufferedImage scaleCheckBoxImg(BufferedImage checkBoxImage) {
        Image img = checkBoxImage.getScaledInstance(CHECK_BOX_IMG_SIZE, CHECK_BOX_IMG_SIZE, Image.SCALE_SMOOTH);
        BufferedImage newImage = new BufferedImage(CHECK_BOX_IMG_SIZE, CHECK_BOX_IMG_SIZE, checkBoxImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(img, 0, 0, null);
        g.dispose();
        return newImage;
    }

    public static BufferedImage cropCheckBoxImage(String imagePath, RecognizeData.DataDTO dataDTO) throws IOException {
        List<List<Integer>> textBoxPosition = dataDTO.getTextBoxPosition();
        BufferedImage image = ImageIO.read(new File(imagePath));
        // 需要考虑横竖屏的关系, 取最小的作为屏幕宽度
        int width = Math.min(image.getWidth(), image.getHeight());
        // 需要截取的大小，以720分辨率屏幕为标准截取60个像素大小的图片
        float targetScale = width / 720f;
        // 如果是以O为开头，说明是圆形选中框被OCR识别为O，需要加上一些偏移量修正
        int offset = starsWithCheckbox(dataDTO) ? (int) (35 * targetScale) : 0;
        int cropSize = (int) (60 * targetScale);
        List<Integer> leftTop = textBoxPosition.get(0);
        List<Integer> leftBottom = textBoxPosition.get(3);
        int cropX = leftTop.get(0) - cropSize + offset;
        int cropY = leftTop.get(1) + (leftBottom.get(1) - leftTop.get(1)) / 2 - cropSize / 2;
        return image.getSubimage(Math.max(0, cropX), Math.max(0, cropY), cropSize, cropSize);
    }

    private static boolean starsWithCheckbox(RecognizeData.DataDTO dataDTO) {
        return StringUtils.startsWith(dataDTO.getText(), "O") || StringUtils.startsWith(dataDTO.getText(), "（）");
    }

    /**
     * 从截图中裁剪出48X48的checkbox图片
     * @param imagePath
     * @param screenWidth
     * @param screenHeight
     * @param uiComponents
     * @param beginIndex
     * @param privacyX
     * @param privacyY
     * @param privacyHeight
     * @return
     * @throws IOException
     */
    public static BufferedImage cropCheckBoxImage(String imagePath, TerminalTypeEnum terminalTypeEnum,
                                                  int screenWidth, int screenHeight, List<UIComponent> uiComponents,
                                                  int beginIndex, int privacyX, int privacyY, int privacyHeight) throws IOException, IjiamiApplicationException {
        int bound = 64;
        for (int i=beginIndex; i>=0; i--) {
            UIComponent uiComponent = uiComponents.get(i);
            // 只检测坐标在隐私文本附近且在前面的组件
            if (Math.abs(uiComponent.getY1() - privacyY) < bound && privacyX - uiComponent.getX1() > 0) {
                int height = uiComponent.getY2() - uiComponent.getY1();
                int width = uiComponent.getX2() - uiComponent.getX1();
                // 往前寻找CheckBox
                if (StringUtils.length(uiComponent.getText()) <= 1 && height < bound && width < bound) {
                    BufferedImage image = ImageIO.read(new File(imagePath));
                    return cropCheckBoxImage(uiComponent, screenWidth, screenHeight, image);
                }
            }
        }
        // 默认为0，如果都没找到那么就是所有元素都在同一行，0就是起始位置
        int textLineStartIndex = 0;
        for (int i=beginIndex; i>=0; i--) {
            UIComponent uiComponent = uiComponents.get(i);
            // 往前查找，如果元素的顶部坐标如果大于文本高度的三分之一，判断为不在同一行了
            if (Math.abs(uiComponent.getY1() - privacyY) > privacyHeight / 2) {
                // 后面一个元素就是行的起始
                textLineStartIndex = i + 1;
                break;
            }
        }
        UIComponent textLineStart = uiComponents.get(textLineStartIndex);
        // 如果找不到元素，有可能是元素没在xml文件中，那么就取这一行的文本起始位置去截图
        BufferedImage image = ImageIO.read(new File(imagePath));
        // 进行放大，把截图大小还原回屏幕大小一致
        return cropLeftImage(terminalTypeEnum, textLineStart.getX1(), textLineStart.getY1(), privacyHeight, screenWidth, screenHeight, image);
    }

    /**
     * 截图如果被压缩过，分辨率不是跟屏幕大小一致，需要把图片放大还原回去
     * @param image
     * @param screenWidth
     * @param screenHeight
     * @return
     */
    private static BufferedImage scaleScreenshotImage(BufferedImage image, int screenWidth, int screenHeight) {
        Image img = image.getScaledInstance(screenWidth, screenHeight, Image.SCALE_DEFAULT);
        BufferedImage newImage = new BufferedImage(screenWidth, screenHeight, image.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(img, 0, 0, null);
        g.dispose();
        return newImage;
    }

    private static BufferedImage cropCheckBoxImage(UIComponent uiComponent,
                                                   int screenWidth, int screenHeight, BufferedImage image) throws IjiamiApplicationException {
        // xml里面的数据尺寸可能和截图的尺寸不一致，需要进行缩放
        float screenScale;
        if (image.getWidth() != screenWidth) {
            screenScale = image.getWidth() / (float)screenWidth;
        } else {
            screenScale = 1.0f;
        }
        int cropSize = 60;
        int height = uiComponent.getY2() - uiComponent.getY1();
        int width = uiComponent.getX2() - uiComponent.getX1();
        // 找到这个组件的中心坐标点，需要以此为标准截取60X60像素大小的图片
        int centerX = uiComponent.getX1() + width / 2;
        int centerY = uiComponent.getY1() + height / 2;
        int cropX = (int) ((centerX - cropSize / 2) * screenScale);
        int cropY = (int) ((centerY - cropSize / 2) * screenScale);
        // cropSize是以xml里面的数据去计算的，要到截图去截取的时候，需要进行缩放
        int subImageSize = (int) (cropSize * screenScale);
        if (Math.max(0, cropY) + subImageSize > image.getHeight()) {
            throw new IjiamiApplicationException("error cropY=" + cropY + " subImageSize=" + subImageSize + " imageHeight" + image.getHeight());
        }
        if (Math.max(0, cropX) + subImageSize > image.getWidth()) {
            throw new IjiamiApplicationException("error cropX=" + cropY + " subImageSize=" + subImageSize + " imageWidth" + image.getWidth());
        }
        return image.getSubimage(Math.max(0, cropX), Math.max(0, cropY), subImageSize, subImageSize);
    }

    private static BufferedImage cropLeftImage(TerminalTypeEnum terminalTypeEnum, int privacyX, int privacyY,
                                               int privacyTextHeight, int screenWidth, int screenHeight, BufferedImage image) throws IjiamiApplicationException {
        // xml里面的数据尺寸可能和截图的尺寸不一致，需要进行缩放
        float screenScale;
        if (image.getWidth() != screenWidth) {
            screenScale = image.getWidth() / (float)screenWidth;
        } else {
            screenScale = 1.0f;
        }
        // 需要考虑横竖屏的关系, 取最小的作为屏幕宽度
        int width = Math.min(screenWidth, screenHeight);
        // 需要截取的大小，以720分辨率屏幕为标准截取84个像素大小的图片
        float targetScale = width / 720f;
        int cropSize = (int) (84 * targetScale);
        // android因为TextView与文字的显示存在必然的内边距，往右偏移一些，iOS则往左偏移一些
        int offset = (int) ((terminalTypeEnum == TerminalTypeEnum.ANDROID ? -10 : 6) * targetScale);
        // 文本组件的坐标边界会有一些偏移
        // 找到这个组件的左边的垂直居中点，需要以此为标准截取72X72像素大小的图片
        int cropX = (int) ((privacyX - offset - cropSize ) * screenScale);
        int cropY = (int) ((privacyY + privacyTextHeight / 2 - cropSize / 2) * screenScale);
        // cropSize是以xml里面的数据去计算的，要到截图去截取的时候，需要进行缩放
        int subImageSize = (int) (cropSize * screenScale);
        if (Math.max(0, cropY) + subImageSize > image.getHeight()) {
            // 裁切超出了下边界，进行重置y坐标
            cropY = image.getHeight() - subImageSize;
            if (cropY < 0) {
                throw new IjiamiApplicationException("error cropY=" + cropY + " subImageSize=" + subImageSize + " imageHeight" + image.getHeight());
            }
        }
        if (Math.max(0, cropX) + subImageSize > image.getWidth()) {
            // 裁切超出了右边界，进行重置x坐标
            cropX = image.getWidth() - subImageSize;
            if (cropX < 0) {
                throw new IjiamiApplicationException("error cropX=" + cropY + " subImageSize=" + subImageSize + " imageWidth" + image.getWidth());
            }
        }
        return image.getSubimage(Math.max(0, cropX), Math.max(0, cropY), subImageSize, subImageSize);
    }

    /**
     * 判断是否图片类型的checkbox
     * @param checkBoxView
     * @return
     */
    public static boolean isImageCheckBoxView(UIComponent checkBoxView) {
        boolean isButtonBound = checkBoxView.getX2() - checkBoxView.getX1() < 65 && checkBoxView.getY2() - checkBoxView.getY1() < 65;
        boolean isImage = StringUtils.isBlank(checkBoxView.getText()) || StringUtils.isBlank(checkBoxView.getContentDesc());
        boolean isCheckBox = StringUtils.containsIgnoreCase(checkBoxView.getResourceId(), "agree")
                || StringUtils.containsIgnoreCase(checkBoxView.getResourceId(), "check");
        return (checkBoxView.isClickable() || isCheckBox) && (isImage || isButtonBound);
    }
}
