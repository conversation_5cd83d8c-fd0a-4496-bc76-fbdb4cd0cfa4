package cn.ijiami.detection.utils;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据处理工具类
 *
 * <AUTHOR>
 * @date 2020-09-22 11:31
 */
public class DataHandleUtil {

    private DataHandleUtil() {
    }

    /**
     * 默认切割大小
     */
    private static final Integer DEFAULT_MAX_NUMBER = 10;

    /**
     * 计算切分次数
     */
    private static Integer countStep(Integer size) {
        return (size + DEFAULT_MAX_NUMBER - 1) / DEFAULT_MAX_NUMBER;
    }

    /**
     * 分割list集合，每个大小为：1000
     *
     * @param root 源数据
     * @return 非null集合
     */
    public static <T> List<List<T>> split(List<T> root) {
        return split(root, DEFAULT_MAX_NUMBER);
    }

    /**
     * 分割list集合
     *
     * @param root     源数据
     * @param splitNum 分割大小
     * @return 非null集合
     */
    public static <T> List<List<T>> split(List<T> root, int splitNum) {
        List<List<T>> target = new ArrayList<>();
        if (CollectionUtils.isEmpty(root)) {
            return target;
        }
        if (splitNum < 2 || root.size() < splitNum) {
            target.add(root);
            return target;
        }
        int limit = countStep(root.size());
        target = Stream.iterate(0, n -> n + 1).limit(limit).parallel()
                .map(a -> root.stream().skip(a * splitNum).limit(splitNum).parallel().collect(Collectors.toList())).collect(Collectors.toList());
        return target;
    }

    /**
     * 非有效json判断
     *
     * @param str
     * @return
     */
    public static boolean nonJSONValid(String str) {
        return !isJSONValid(str);
    }

    /**
     * 判断字符串是否位合法的json
     *
     * @param str
     * @return
     */
    public static boolean isJSONValid(String str) {
        try {
            JSONObject.parseObject(str);
        } catch (JSONException e1) {
            try {
                JSONObject.parseArray(str);
            } catch (JSONException e2) {
                return false;
            }
        }
        return true;
    }

    public static void main(String[] args) {
        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
        int limit = countStep(list.size());
        //方法一：使用流遍历操作
        List<List<Integer>> mglist = new ArrayList<>();
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
            mglist.add(list.stream().skip(i * DEFAULT_MAX_NUMBER).limit(DEFAULT_MAX_NUMBER).collect(Collectors.toList()));
        });
        System.out.println(mglist);
        //方法二：获取分割后的集合
        List<List<Integer>> splitList = Stream.iterate(0, n -> n + 1).limit(limit).parallel()
                .map(a -> list.stream().skip(a * DEFAULT_MAX_NUMBER).limit(DEFAULT_MAX_NUMBER).parallel().collect(Collectors.toList()))
                .collect(Collectors.toList());
        System.out.println(splitList);
    }
}
