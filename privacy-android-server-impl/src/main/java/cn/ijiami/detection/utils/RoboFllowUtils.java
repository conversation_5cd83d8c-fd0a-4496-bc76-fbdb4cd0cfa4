package cn.ijiami.detection.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class RoboFllowUtils {
	
	/**
	 * 上传本地图片(标注)到roboflow
	 * @param imageLocalPath 
	 * @param apiKey 密钥
	 * @param datasetName 数据集名称
	 * @return
	 */
	public static String uploadLocal(String imageLocalPath, String apiKey,String datasetName){
		// Get Image Path
		// String filePath = System.getProperty("user.dir") + System.getProperty("file.separator") + "YOUR_IMAGE.jpg";
        File file = new File(imageLocalPath);
        HttpURLConnection connection = null;
        try {
	        // Base 64 Encode
	        String encodedFile;
	        FileInputStream fileInputStreamReader = new FileInputStream(file);
	        byte[] bytes = new byte[(int) file.length()];
	        fileInputStreamReader.read(bytes);
	        encodedFile = new String(Base64.getEncoder().encode(bytes), StandardCharsets.US_ASCII);
	
	        apiKey = "zbaRWheq6cLVTUTHLybI"; // Your API Key
	        String dataSetName = "detection-t8z2u"; // Set Dataset Name (Found in Dataset URL)
	
	        // Construct the URL
	        String uploadUrl =
	                "https://api.roboflow.com/dataset/"+
	                		dataSetName + "/upload" +
	                        "?api_key=" + apiKey +
	                        "&name=" +file.getName()+
	                        "&split=train";
	        
	        //String uploadURL = "https://api.roboflow.com/dataset/detection-t8z2u/upload?api_key=zbaRWheq6cLVTUTHLybI&name=dddddd.png&split=pig";
            URL url = new URL(uploadUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type",
                    "application/x-www-form-urlencoded");

            connection.setRequestProperty("Content-Length",
                    Integer.toString(encodedFile.getBytes().length));
            connection.setRequestProperty("Content-Language", "en-US");
            connection.setUseCaches(false);
            connection.setDoOutput(true);
            // 设置连接主机服务器的超时时间：15000毫秒
 			connection.setConnectTimeout(15000);
 			// 设置读取远程返回的数据时间：60000毫秒
 			connection.setReadTimeout(60000);

            //Send request
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(encodedFile);
            wr.close();

            // Get Response
            InputStream stream = connection.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(stream));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
            reader.close();
            System.out.println("上传返回结果："+line);
            return line;
        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
		return "";
	}
	
	/**
	 * 上传本地推理图片
	 * @param imageLocalPath
	 * @param apiKey
	 * @param modelEndpoint 模型位置
	 * @return
	 */
	public String uploadInferenceLocal(String imageLocalPath, String apiKey,String modelEndpoint){
		// Get Image Path
        File file = new File(imageLocalPath);
        // Base 64 Encode
        String encodedFile;
        HttpURLConnection connection = null;
        try {
        	FileInputStream fileInputStreamReader = new FileInputStream(file);
        	byte[] bytes = new byte[(int) file.length()];
        	fileInputStreamReader.read(bytes);
        	encodedFile = new String(Base64.getEncoder().encode(bytes), StandardCharsets.US_ASCII);

//        	String API_KEY = apiKey; // Your API Key
//        	String modelEndPoint = modelEndpoint;//"dataset/v"; // model endpoint

        	// Construct the URL
        	String uploadUrl = "https://detect.roboflow.com/" + modelEndpoint + "?api_key=" + apiKey
                + "&name="+file.getName();

            // Configure connection to URL
            URL url = new URL(uploadUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

            connection.setRequestProperty("Content-Length", Integer.toString(encodedFile.getBytes().length));
            connection.setRequestProperty("Content-Language", "en-US");
            connection.setUseCaches(false);
            connection.setDoOutput(true);

            // Send request
            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.writeBytes(encodedFile);
            wr.close();

            // Get Response
            InputStream stream = connection.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(stream));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
            reader.close();
            return line;
        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return null;
	}
	
	/**
	 * 下载数据集
	 * @param workspace
	 * @param project
	 * @param version
	 * @param format
	 * @return
	 */
	///:workspace/:project/:version/:format
	//curl "https://api.roboflow.com/roboflow/chess-sample-4ckfl/1/yolov5pytorch?api_key=$ROBOFLOW_API_KEY"
	//https://app.roboflow.com/test-eykzb/detection-t8z2u/1/yolov5pytorch?api_key=zbaRWheq6cLVTUTHLybI
	public static String exportData(String workspace,String project,String version, String format,String apiKey){
		String url = "https://api.roboflow.com/%s/%s/%s/%s?api_key=%s";
		String urlGet = url.format(url, workspace,project,version,format,apiKey);
		return HttpClient.doGet(urlGet);
	}
	
//	public static void main1(String[] args) throws IOException {
//        // Get Image Path
//        String filePath = System.getProperty("user.dir") + System.getProperty("file.separator") + "YOUR_IMAGE.jpg";
//        filePath = "C:/Users/<USER>/Desktop/image/qq.png";
//        File file = new File(filePath);
//
//        // Base 64 Encode
//        String encodedFile;
//        FileInputStream fileInputStreamReader = new FileInputStream(file);
//        byte[] bytes = new byte[(int) file.length()];
//        fileInputStreamReader.read(bytes);
//        encodedFile = new String(Base64.getEncoder().encode(bytes), StandardCharsets.US_ASCII);
//
//        String apkKey = "zbaRWheq6cLVTUTHLybI"; // Your API Key
//        String DATASET_NAME = "detection-t8z2u"; // Set Dataset Name (Found in Dataset URL)
//
//        // Construct the URL
//        String uploadURL =
//                "https://api.roboflow.com/dataset/"+
//                        DATASET_NAME + "/upload" +
//                        "?api_key=" + apkKey +
//                        "&name=" +file.getName()+
//                        "&split=train";
//        
////        String uploadURL = "https://api.roboflow.com/dataset/detection-t8z2u/upload?api_key=zbaRWheq6cLVTUTHLybI&name=dddddd.png&split=pig";
//        // Http Request
//        HttpURLConnection connection = null;
//        try {
//            //Configure connection to URL
//            URL url = new URL(uploadURL);
//            connection = (HttpURLConnection) url.openConnection();
//            connection.setRequestMethod("POST");
//            connection.setRequestProperty("Content-Type",
//                    "application/x-www-form-urlencoded");
//
//            connection.setRequestProperty("Content-Length",
//                    Integer.toString(encodedFile.getBytes().length));
//            connection.setRequestProperty("Content-Language", "en-US");
//            connection.setUseCaches(false);
//            connection.setDoOutput(true);
//            // 设置连接主机服务器的超时时间：15000毫秒
// 			connection.setConnectTimeout(15000);
// 			// 设置读取远程返回的数据时间：60000毫秒
// 			connection.setReadTimeout(60000);
//
//            //Send request
//            DataOutputStream wr = new DataOutputStream(
//                    connection.getOutputStream());
//            wr.writeBytes(encodedFile);
//            wr.close();
//
//            // Get Response
//            InputStream stream = connection.getInputStream();
//            BufferedReader reader = new BufferedReader(new InputStreamReader(stream));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                System.out.println(line);
//            }
//            reader.close();
//            
//            System.out.println("上传成功");
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            if (connection != null) {
//                connection.disconnect();
//            }
//        }
//    }
//	
//	
//	public static void main(String[] args) throws IOException {
//		//https://app.roboflow.com/test-eykzb/detection-t8z2u/1/yolov5pytorch?api_key=zbaRWheq6cLVTUTHLybI
//		String workspace = "test-eykzb";
//		String project = "detection-t8z2u";
//		String version = "1";
//		String format = "yolov5pytorch";
//		String apiKey = "zbaRWheq6cLVTUTHLybI";
//		System.out.println(exportData(workspace, project, version, format, apiKey));
//	}
}
