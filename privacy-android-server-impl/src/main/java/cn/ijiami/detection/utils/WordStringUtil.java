package cn.ijiami.detection.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * word 字符串帮助类
 * 
 * <AUTHOR>
 *
 */
public class WordStringUtil {

	/**
	 * 转译特殊字符
	 * 
	 * @param str
	 * @return
	 */
	public static String converCharacters(String str) {
		if (str.contains("<") || str.contains(">") || str.contains("&")) {
			str = str.replaceAll("&", "&amp;");
			str = str.replaceAll("<", "&lt;");
			str = str.replaceAll(">", "&gt;");
		}
		return str;
	}
	
	
	public static String matchedItemVerification(String content, String matchedItem){
		content = content.replaceAll("\n", "");
		List<String> item = item(matchedItem);
		for (int i = 0; i < item.size(); i++) {
			String indexItem = item.get(i);
			content = content.replaceAll(indexItem, "【"+indexItem+"】");
		}
		return content;
	}
	
	private static List<String> item(String matchedItem){
		List<String> itemList = new ArrayList<String>();
		String item[] = matchedItem.split(",");
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < item.length; i++) {
			String indexItem = item[i];
			if(StringUtils.isBlank(indexItem)) {
				continue;
			}
			if(buffer.toString().contains(indexItem)) {
				continue;
			}
			buffer.append(indexItem);
			itemList.add(indexItem);
		}
		return itemList;
	}
	
	public static String delBlank(String str){
		if(StringUtils.isBlank(str)) {
			return "";
		}
	    return str.replaceAll("[\\s]*", "");
	}
	
	public static void main(String[] args) {
		String d = ".method public checkServerTrusted([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V .method public checkServerTrusted([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V     .registers 3     .param p1, certs    # [Ljava/security/cert/X509Certificate;     .param p2, authType    # Ljava/lang/String;      .prologue     .line 247     return-void .end method";
		System.out.println(delBlank(d));
	}
}
