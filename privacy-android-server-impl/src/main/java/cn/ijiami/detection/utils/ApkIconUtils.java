package cn.ijiami.detection.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import cn.ijiami.framework.base.utils.IOUtil;


public class ApkIconUtils {
	
	public static boolean extractFileFromApk(String apkPath, String fileName, String outputPath) {
        boolean success = false;
        InputStream is = null;
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        ZipFile zFile = null;
        try {
            zFile = new ZipFile(apkPath);
            ZipEntry entry = zFile.getEntry(fileName);
            if(entry == null){
                return false;
            }
            entry.getComment();
            entry.getCompressedSize();
            entry.getCrc();
            entry.isDirectory();
            entry.getSize();
            entry.getMethod();
            is = zFile.getInputStream(entry);
            if (null == is) {
                success = false;
            } else {
                File file = new File(outputPath);
                bos = new BufferedOutputStream(new FileOutputStream(file), 1024);
                byte[] b = new byte[1024];
                bis = new BufferedInputStream(is, 1024);
                while (bis.read(b) != -1) {
                    bos.write(b);
                }
                bos.flush();
                success = true;
            }
        } catch (Exception e) {
            success = false;
            e.getMessage();
        } finally {
            IOUtil.closeIO(is);
            IOUtil.closeIO(bis);
            IOUtil.closeIO(bos);
            IOUtil.closeIO(zFile);
        }
        return success;
    }
}
