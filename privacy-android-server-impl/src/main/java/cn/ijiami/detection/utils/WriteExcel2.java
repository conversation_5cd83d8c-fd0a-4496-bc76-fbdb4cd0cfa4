package cn.ijiami.detection.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class WriteExcel2 {
    private static final String EXCEL_XLS = "xls";
    private static final String EXCEL_XLSX = "xlsx";
    
    public static void main(String[] args) {
        
        Map<String, String> dataMap=new HashMap<String, String>();
        dataMap.put("BankName", "BankName");
        dataMap.put("Addr", "Addr");
        dataMap.put("Phone", "Phone");
        List<Map> list=new ArrayList<Map>();
        list.add(dataMap);
        writeExcel(list, 3, "F:/总数132_年月日时分秒1.xlsx");
        
    }

    public static void writeExcel(List<Map> dataList, int cloumnCount,String finalXlsxPath){
        OutputStream out = null;
        try {
            // 获取总列数
            int columnNumCount = cloumnCount;
            // 读取Excel文档
            File finalXlsxFile = new File(finalXlsxPath);
            Workbook workBook = getWorkbok(finalXlsxFile);
//            HSSFWorkbook workBook = new HSSFWorkbook();
            // sheet 对应一个工作页
            Sheet sheet = workBook.getSheetAt(0);
            /**
             * 删除原有数据，除了属性列
             */
//            int rowNumber = sheet.getLastRowNum();    // 第一行从0开始算
//            System.out.println("原始数据总行数，除属性列：" + rowNumber);
//            for (int i = 1; i <= rowNumber; i++) {
//                Row row = sheet.getRow(i);
//                sheet.removeRow(row);
//            }
//            // 创建文件输出流，输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
//            out =  new FileOutputStream(finalXlsxPath);
//            workBook.write(out);
            /**
             * 往Excel中写新数据
             */
            
            
            for (int j = 1; j < dataList.size(); j++) {
                // 创建一行：从第二行开始，跳过属性列
                Row row = sheet.createRow(j + 2);
                // 得到要插入的每一条记录
                Map dataMap = dataList.get(j);
                String name = dataMap.get("BankName").toString();
                String address = dataMap.get("Addr").toString();
                String phone = dataMap.get("Phone").toString();
                
//                Font font = workBook.createFont();
//                font.setFontHeightInPoints((short) 36);
//                font.setFontName("华文琥珀");
//                font.setColor(HSSFColor.RED.index);
                
//              //创建单元格样式
                CellStyle cellStyle = workBook.createCellStyle();
                //此处将HSSFCellStyle.SOLID_FOREGROUND改为FillPatternType.SOLID_FOREGROUND,网上搜索资料一堆错误用法
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND); 
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                cellStyle.setFillForegroundColor((short)9);
                //以下为API自带的颜色设置
//              cell_style.setFillForegroundColor(IndexedColors.BRIGHT_GREEN1.index);   
                 
                //自定义字体颜色, 同单元格样式
                Font font = workBook.createFont();
                font.setFontHeightInPoints((short) 9);
                //字体设置为Arial
                font.setFontName(HSSFFont.FONT_ARIAL);  
                //设置索引 10 为白色
//                palette.setColorAtIndex((short)10, (byte) 255, (byte) 255, (byte) 255);
                //将字体颜色设为白色
                font.setColor(IndexedColors.RED.index);
                cellStyle.setFont(font);

                
                for (int k = 0; k <= columnNumCount; k++) {
                	// 在一行内循环
                	Cell first = row.createCell(0);
//                	first.getCellStyle().setFont(font);
                	first.setCellValue(name);
                	first.setCellStyle(cellStyle);
                	
                	Cell second = row.createCell(1);
                	second.setCellValue(address);
        
                	Cell third = row.createCell(2);
                	third.setCellValue(phone);
                }
            }
            // 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
            out =  new FileOutputStream(finalXlsxPath);
            workBook.write(out);
            workBook.close();
        } catch (Exception e) {
            e.getMessage();
        } finally{
            try {
                if(out != null){
                    out.flush();
                    out.close();
                }
            } catch (IOException e) {
                e.getMessage();
            }
        }
        System.out.println("数据导出成功");
    }

    /**
     * 判断Excel的版本,获取Workbook
     * @param file
     * @return
     * @throws IOException
     */
    public static Workbook getWorkbok(File file) throws IOException{
        Workbook wb = null;
        FileInputStream in = new FileInputStream(file);
        if(file.getName().endsWith(EXCEL_XLS)){     //Excel&nbsp;2003
            wb = new HSSFWorkbook(in);
        }else if(file.getName().endsWith(EXCEL_XLSX)){    // Excel 2007/2010
            wb = new XSSFWorkbook(in);
        }
        wb.close();
        return wb;
    }
}
