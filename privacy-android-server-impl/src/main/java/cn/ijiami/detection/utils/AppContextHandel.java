package cn.ijiami.detection.utils;

import org.springframework.context.ApplicationContext;

/**
 * 获取applicationContext
 * 
 * <AUTHOR>
 *
 */
public class AppContextHandel {
	private static ApplicationContext appContetx;

	public static void setApplicationContext(ApplicationContext appContetx) {
		AppContextHandel.appContetx = appContetx;
	}

	public static ApplicationContext getApplicationContext() {
		return appContetx;
	}
}
