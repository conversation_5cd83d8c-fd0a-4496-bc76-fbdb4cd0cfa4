package cn.ijiami.detection.utils;

import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.enums.BooleanEnum;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SensitiveUtils.java
 * @Description 敏感词工具类
 * @createTime 2025年03月27日 16:09:00
 */
public class SensitiveUtils {

    /**
     * 是否明文传输
     * @param sensitive
     * @return
     */
    public static BooleanEnum isPlaintextTransmission(TSensitiveWord sensitive) {
        // 由正则表达式匹配出的传输个人信息就认为是明文传输
        return sensitive.getRiskLevel() && StringUtils.isNotBlank(sensitive.getRegex()) ? BooleanEnum.TRUE : BooleanEnum.FALSE;
    }


    public static boolean havePlaintextTransmission(List<TPrivacySensitiveWord> sensitiveWords) {
        return sensitiveWords
                .stream()
                .filter(Objects::nonNull)
                .anyMatch(s -> s.getPlaintextTransmission() == BooleanEnum.TRUE.value);
    }

}
