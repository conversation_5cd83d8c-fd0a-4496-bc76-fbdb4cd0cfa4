package cn.ijiami.detection.utils;

import cn.ijiami.detection.bean.WaverifyInfo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.net.URL;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class HtmlToText {

    /**
     * 解析请求的小程序更多信息数据
     * @param url
     */
    public static WaverifyInfo getBasicHtmlMessage(String url){
        WaverifyInfo info = new WaverifyInfo();
        try {
            Document document = Jsoup.parse(new URL(url),30000);
            Elements elements = document.getElementsByTag("script");
            for(Element element:elements){
                if(element == null){
                    continue;
                }
                //获取<script>标签所有信息
                String script = element.data().toString();
                if(script.contains("window.cgiData")){
                    log.info("开始解析html文件");
                    //获取标签头外的函数内容
                    script = element.childNode(0).toString();
                    //原始ID
                    String username = getStr(script,"","username:",",");
                    //该接口请求随便输入啥都能请求，以原始id有没有作为判断依据，没有则认为不是正经小程序，则返回错误
                    if(StringUtils.isEmpty(username)){
                        log.info("请求地址:{}调用微信公共接口获取信息有异常！",url);
                        return info;
                    }
                    info.setUsername(username);
                    //小程序名称
                    String nickname = getStr(script,"","nickname:",",");
                    info.setNickname(nickname);
                    //服务类目
                    String category_list = getStr(script,"","category_list:","}");
                    JSONObject json1 = JSONObject.parseObject(category_list);
                    if(json1 !=null){
                        JSONArray array = json1.getJSONArray("cate");
                        StringBuilder cateBuffer = new StringBuilder();
                        for(Object cata : array){
                            if(cata !=null && !"".equals(cata.toString())){
                                cateBuffer.append(cata.toString()).append("、");
                            }
                        }
                        if(!"".equals(cateBuffer.toString())){
                            String cateStr = cateBuffer.toString();
                            info.setCategoryList(cateStr.substring(0, cateStr.length()-1));
                        }
                    }
                    //账号主体,需要关键索引字段去精确截取,name前的空格不可以去掉
                    info.setName(getStr(script,"headimg_url","     name:",","));
                    //服务隐私及数据提示
                    String serviceAndData = "";
                    //游戏应用有单独的说明
                    String game_privacy_wording = getStr(script,"","game_privacy_wording:","&amp;");
                    if(game_privacy_wording != null && !"".equals(game_privacy_wording)){
                        serviceAndData = game_privacy_wording + "投诉。";
                    }else{
                        serviceAndData = "开发者严格按照 《"+ nickname +"小程序隐私保护指引》 处理你的个人信息，如你发现开发者不当处理你的个人信息，可进行投诉。";
                    }
                    info.setServiceAndData(serviceAndData);
                    //服务声明
                    String serviceStatement = "本服务由开发者向微信用户提供，开发者对本服务信息内容、数据资料及其运营行为等的真实性、合法性及有效性承担全部责任。 腾讯向开发者提供技术支持服务。";
                    info.setServiceStatement(serviceStatement);
                    //更新时间
                    String app_update_time = getStr(script,"","app_update_time:","*");
                    info.setUpdateTime(getUpdateTime(app_update_time));
                    //引用插件
                    String pluginstr = getStr(script,"","plugins:","],");
                    //处理插件数据
                    info.setPlugins(getPlugins(pluginstr));
                    info.setPluginAppIds(getPluginAppIds(pluginstr));
                    //授权服务商
                    String auth_3rd_list = getStr(script,"","auth_3rd_list:","],");
                    //处理授权服务商
                    String auth3reList = getauth3rdList(auth_3rd_list);
                    if(StringUtils.isNotEmpty(auth3reList)){
                        info.setAuth3reList(auth3reList);
                    }
                }
            }
            log.info("获取到的微信小程序更多信息内容为：{}", CommonUtil.beanToJson(info));
        } catch (IOException e) {
            e.getMessage();
            throw new RuntimeException("html解析异常！");
        }
        return info;
    }


    /**
     * 解析引用插件信息
     * @param url
     */
    public void getPluginsMessage(String url){

    }
    /**
     *
     * @param script 要查找的字符串
     * @param indexkey 关键索引位置（区别有些特殊索引同名不好截取）
     * @param key 查找开始的字符
     * @param f 查找结束的字符
     * @return
     */
    private static String getStr(String script,String indexkey,String key,String f){
        int index = 0;
        if(StringUtils.isNotEmpty(indexkey)){
            index = script.indexOf(indexkey);
        }
        //加入关键索引位置，避免有些索引字段重名不好找的问题
        int i = script.indexOf(key,index);
        int j = script.indexOf(f,i);
        if(i == -1 || j  == -1){
            return null;
        }
        if(f.contains("}") || f.contains("],")){
            j += 1;
        }
        if(key.contains("category_list:") || key.contains("auth_3rd_list:")){
            return script.substring(i+key.length(),j).trim();
        }
        return script.substring(i+key.length(),j).replaceAll("\"","").trim();
    }

    /**
     * 根据拿到的时间搓判断相差多久
     * @param time
     * @return
     */
    private static String getUpdateTime(String time){
        String diffDay = "";
        if(StringUtils.isEmpty(time)){
            return diffDay;
        }
        long startTimeStamp = Long.parseLong(time);
        if(time.length() ==10){
            startTimeStamp *=1000;
        }
        long endTimeStamp = Instant.now().toEpochMilli();
        LocalDate startTime = Instant.ofEpochMilli(startTimeStamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate endTime = Instant.ofEpochMilli(endTimeStamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        long years = ChronoUnit.YEARS.between(startTime, endTime);
        long months = ChronoUnit.MONTHS.between(startTime, endTime);
        long weeks = ChronoUnit.WEEKS.between(startTime, endTime);
        long days = ChronoUnit.DAYS.between(startTime, endTime);
        if(years != 0){
            diffDay = years + "年前（获取时间：" + startTime + "）";
        }else if(months != 0){
            diffDay = months + "月前（获取时间：" + startTime + "）";
        }else if(weeks != 0){
            diffDay = weeks + "周前（获取时间：" + startTime + "）";
        }else if(days <=7){
            diffDay = "1周内（获取时间：" + startTime + "）";
        }
        return diffDay;
    }


    /**
     * 获取更新时间日期
     * @param time
     * @return
     */
    private static String getUpdateDate(String time){
        if(StringUtils.isEmpty(time)){
            return null;
        }
        Long dateTime = Long.parseLong(time);
        if (time.length() == 10) {
            dateTime *= 1000;
        }
        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(dateTime), ZoneId.systemDefault()));
    }

    /**
     * 处理引用插件数据
     *
     * @param str
     * @return
     */
    private static String getPlugins(String str) {
        StringBuilder buffer = new StringBuilder();
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        String[] data = str.split("},");
        for (String s : data) {
            String appid = getStr(s, "", "appid:", ",");
            String nickname = getStr(s, "", "nickname:", ",");
            String realname = getStr(s, "", "realname:", ",");
            if (appid == null && nickname == null && realname == null) {
                continue;
            }
            buffer.append(nickname).append("（").append(realname).append("）").append("、");
        }
        if (!"".equals(buffer.toString())) {
            String plugins = buffer.toString();
            return plugins.substring(0, plugins.length() - 1);
        }
        return "";
    }

    /**
     * 处理引用插件数据
     *
     * @param str
     * @return
     */
    private static List<String> getPluginAppIds(String str) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isEmpty(str)) {
            return list;
        }
        String[] data = str.split("},");
        for (String s:data){
            String appid = getStr(s,"","appid:",",");
            String nickname = getStr(s,"","nickname:",",");
            String realname = getStr(s,"","realname:",",");
            if(appid == null && nickname == null && realname == null){
                continue;
            }
            list.add(appid);
        }
        return list;
    }

    /**
     * 处理第三方授权服务商
     * @param str
     * @return
     */
    private static String getauth3rdList(String str){
        String auth3rdList = "";
        if(StringUtils.isEmpty(str)) {
            return null;
        }
        JSONArray array = JSONObject.parseArray(str);
        if(array == null || array.size() == 0){
            return null;
        }
        StringBuilder sBuffer = new StringBuilder();
        for(Object list:array){
            if(list == null || StringUtils.isEmpty(list.toString())){
                continue;
            }
            sBuffer.append(list).append("、");
        }
        if(!"".equals(sBuffer.toString())){
            auth3rdList = sBuffer.toString();
            auth3rdList = auth3rdList.substring(0,auth3rdList.length()-1);
        }
        return auth3rdList;
    }
}
