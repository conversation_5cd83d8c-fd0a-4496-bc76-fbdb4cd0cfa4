package cn.ijiami.detection.utils;

import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;

/**
 * <AUTHOR>
 * @date 2020-03-26 12:01
 */
public class HtmlToPdf {

    /**
     * html转pdf
     *
     * @param wkPath   wkhtmltopdf绝对路径（Windows必需）
     * @param srcPath  html路径，可以是硬盘上的路径，也可以是网络路径
     * @param destPath pdf保存路径
     * @return 转换成功返回true
     */
    public static boolean convert(String wkPath, String srcPath, String destPath) {
        File file = new File(destPath);
        File parent = file.getParentFile();
        //如果pdf保存路径不存在，则创建路径
        if (!parent.exists()) {
            parent.mkdirs();
        }

        StringBuilder cmd = new StringBuilder();
        if (System.getProperty("os.name").indexOf("Windows") == -1) {
            cmd.append("wkhtmltopdf");
        } else {
            cmd.append(wkPath);
        }
        cmd.append(" ");
        // wkhtmltopdf, 0.12.6, Warning: Blocked access to file 原因：默认不允许访问本地文件，手动开启
        cmd.append(" --enable-local-file-access ");
        //设置页面上边距 (default 10mm)
        cmd.append(" --margin-top 0cm ");
        //设置页面上边距 (default 10mm)
        cmd.append(" --margin-bottom 0cm ");
        //设置页面上边距 (default 10mm)
        cmd.append(" --margin-left 0cm ");
        //设置页面上边距 (default 10mm)
        cmd.append(" --margin-right 0cm ");
        cmd.append(" ");
        cmd.append(srcPath);
        cmd.append(" ");
        cmd.append(destPath);

        boolean result = true;
        try {
            Process proc = Runtime.getRuntime().exec(cmd.toString());
            HtmlToPdfInterceptor error = new HtmlToPdfInterceptor(proc.getErrorStream());
            HtmlToPdfInterceptor output = new HtmlToPdfInterceptor(proc.getInputStream());
            error.start();
            output.start();
            proc.waitFor();
        } catch (Exception e) {
            result = false;
            e.getMessage();
        }

        return result;
    }

    /**
     * wkhtmltopdf 图片转换，解决ios图片问题
     *
     * @param source
     */
    public static void convertImg(String source) {
        if (StringUtils.isBlank(source) || !source.contains(".")) {
            return;
        }
        String formatName = source.substring(source.lastIndexOf(".") + 1);
        convertImg(source, formatName, source);
    }

    /**
     * wkhtmltopdf 图片转换
     *
     * @param source     源图片路径
     * @param formatName 将要转换的图片格式
     * @param result     目标图片路径
     */
    private static void convertImg(String source, String formatName, String result) {
        try {
            File f = new File(source);
            f.canRead();
            BufferedImage src = ImageIO.read(f);
            ImageIO.write(src, formatName, new File(result));
        } catch (Exception e) {
            e.getMessage();
        }
    }

    public static void main(String[] args) {
        //        HtmlToPdf.convert(null, "file:///Users/<USER>/ijiami/report/out/迅购邦_1.0.7_个人信息安全检测报告_全自动_20200322191937/index.html", "/Users/<USER>/Desktop/111.pdf");
        convertImg("C:\\Users\\<USER>\\Desktop\\20200624062398695icon.png");
    }
}
