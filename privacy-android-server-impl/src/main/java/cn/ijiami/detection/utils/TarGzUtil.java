package cn.ijiami.detection.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 解压tar.gz工具类
 */
@Slf4j
public class TarGzUtil {

    public static void testDeCompressTarGzip(Path source,Path target) throws IOException {

        if (Files.notExists(source)) {
            throw new IOException("您要解压的文件不存在");
        }

        //InputStream输入流，以下四个流将tar.gz读取到内存并操作
        //BufferedInputStream缓冲输入流
        //GzipCompressorInputStream解压输入流
        //TarArchiveInputStream解tar包输入流
        try (InputStream fi = Files.newInputStream(source);
             BufferedInputStream bi = new BufferedInputStream(fi);
             GzipCompressorInputStream gzi = new GzipCompressorInputStream(bi);
             TarArchiveInputStream ti = new TarArchiveInputStream(gzi)) {

            ArchiveEntry entry;
            while ((entry = ti.getNextEntry()) != null) {

                //获取解压文件目录，并判断文件是否损坏
                Path newPath = zipSlipProtect(entry, target);

                if (entry.isDirectory()) {
                    //创建解压文件目录
                    Files.createDirectories(newPath);
                } else {
                    //再次校验解压文件目录是否存在
                    Path parent = newPath.getParent();
                    if (parent != null) {
                        if (Files.notExists(parent)) {
                            Files.createDirectories(parent);
                        }
                    }
                    // 将解压文件输入到TarArchiveInputStream，输出到磁盘newPath目录
                    Files.copy(ti, newPath, StandardCopyOption.REPLACE_EXISTING);

                }
            }
        }

    }

    //判断压缩文件是否被损坏，并返回该文件的解压目录
    private static  Path zipSlipProtect(ArchiveEntry entry,Path targetDir)
            throws IOException {
        //过滤乱码跟不规则路径，改成_
        String pathName=CommonUtil.filterFileName(entry.getName());
        if(StringUtils.isEmpty(pathName)){
            throw new IOException("文件路径异常："+entry.getName());
        }
        Path targetDirResolved = targetDir.resolve(pathName);
        Path normalizePath = targetDirResolved.normalize();

        if (!normalizePath.startsWith(targetDir)) {
            throw new IOException("压缩文件已被损坏: " + entry.getName());
        }

        return normalizePath;
    }

    //解压tar.gz文件
    private static void decompressionGz(String sourceFile, String dest) throws IOException {
        TarArchiveInputStream fin = new TarArchiveInputStream(new GzipCompressorInputStream(new FileInputStream(sourceFile)));
        File extraceFolder = new File(dest);
        TarArchiveEntry entry;
        // 将 tar 文件解压到 extractPath 目录下
        while ((entry = fin.getNextTarEntry()) != null) {
            if (entry.isDirectory()) {
                continue;
            }
            File curfile = new File(extraceFolder, entry.getName().replace(":", "_"));
            File parent = curfile.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            // 将文件写出到解压的目录
            IOUtils.copy(fin, new FileOutputStream(curfile));
        }

    }

    public static void compressionGz(File folder, File tarFile) {
        try (FileOutputStream fOut = new FileOutputStream(tarFile);
             BufferedOutputStream bOut = new BufferedOutputStream(fOut);
             GzipCompressorOutputStream gzOut = new GzipCompressorOutputStream(bOut);
             TarArchiveOutputStream tOut = new TarArchiveOutputStream(gzOut)) {

            Files.walk(folder.toPath())
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        File file = path.toFile();
                        TarArchiveEntry tarEntry = new TarArchiveEntry(
                                folder.toPath().relativize(path).toFile());

                        // Set the size of the TarArchiveEntry
                        try {
                            tarEntry.setSize(file.length());
                            tOut.putArchiveEntry(tarEntry);

                            try (FileInputStream fis = new FileInputStream(file)) {
                                IOUtils.copy(fis, tOut);
                                tOut.closeArchiveEntry();
                            }
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            e.getMessage();
        }
    }


    public static void main(String[] args) throws IOException {
        //解压文件
        Path source = Paths.get("C:/Users/<USER>/Desktop/demo.tar.gz");
        //解压到哪
        Path target = Paths.get("C:/Users/<USER>/Desktop/demo2");

        //testDeCompressTarGzip(source,target);
        decompressionGz(source.toString(), target.toString());

    }
}
