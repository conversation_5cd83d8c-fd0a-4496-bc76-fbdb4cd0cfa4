package cn.ijiami.detection.utils;

import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.Base64;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import javax.imageio.ImageIO;

import cn.hutool.core.codec.Base64Encoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.detection.VO.RiskImageVo;

public class ImageUtils {

    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    /**
     * 图片是否合法
     *
     * @param imgPath
     * @return
     */
    public static boolean checkImagType(String imgPath) {
        String reg = ".+(.jpg|.webp|.jpeg|.png|.gif|.bmp|.JPG|.WEBP|.JPEG|.PNG|.GIF|.BMP)$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(imgPath);
        return matcher.find();
    }


    public static boolean checkTxtType(String path) {
        String reg = ".+(.txt|.TXT)$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(path);
        return matcher.find();
    }


    public static boolean checkZipType(String imgPath) {
        String reg = ".+(.zip|.ZIP)$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(imgPath);
        return matcher.find();
    }

    public static String readToString(String path) {
        StringBuilder content = new StringBuilder("");
        try {
            String code = resolveCode(path);
            File file = new File(path);
            InputStream is = new FileInputStream(file);
            InputStreamReader isr = new InputStreamReader(is, code);
            BufferedReader br = new BufferedReader(isr);
            String str = "";
            while (null != (str = br.readLine())) {
                content.append(str);
            }
            br.close();
        } catch (Exception e) {
            e.getMessage();
            System.err.println("读取文件:" + path + "失败!");
        }
        return content.toString();
    }


    /**
     * 判断是否是图片
     *
     * @param zipPath 包路径
     * @return
     * @throws IOException
     */
    @SuppressWarnings("finally")
    public static boolean checkIsImg(String zipPath) throws IOException {
        ZipFile zf = null;
        boolean isImg = false;
        log.info("图片包开始解压：{}", zipPath);
        try {
            int count = 0;
            zf = new ZipFile(zipPath, Charset.forName(resolveCode(zipPath)));
            Enumeration<?> e = zf.entries(); // 遍历Zip文件夹目录
            int size = zf.size();
            while (e.hasMoreElements()) {
                ZipEntry zentry = (ZipEntry) e.nextElement();
                String pathName = zentry.getName();
                if (!checkImagType(pathName)) {
                    count++;
                }
            }
            if (count >= 1) {
                isImg = false;
            }
            if (size == 0) {
                isImg = false;
            }
            log.info("图片包解压结束：isImg={},{}", count, zipPath);
        } catch (Exception e) {
            log.error(String.format("解压失败%s", e.getMessage()));
            e.getMessage();
        } finally {
            if (null != zf) {
                zf.close();
            }
        }
        return isImg;
    }

    @SuppressWarnings("finally")
    public static boolean checkIsNull(String zipPath) throws IOException {
        ZipFile zf = null;
        boolean isImg = false;
        try {
            zf = new ZipFile(zipPath); // 支持中文
            int size = zf.size();
            if (size == 0) {
                isImg = false;
            }
        } catch (Exception e) {
            log.error(String.format("解压失败%s", e.getMessage()));
            e.getMessage();
        } finally {
            if (null != zf) {
                zf.close();
            }
        }
        return isImg;
    }

    /**
     * zip解压
     *
     * @param srcFile zip源文件
     * @param destDirPath 解压后的目标文件夹
     * @throws RuntimeException 解压失败会抛出运行时异常
     */
    private static final int BUFFER_SIZE = 2 * 1024;

    public static void unZip(File srcFile, String destDirPath) throws RuntimeException {
        long start = System.currentTimeMillis();
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            throw new RuntimeException(srcFile.getPath() + "所指文件不存在");
        }
        // 开始解压
        ZipFile zipFile = null;
        System.out.println("开始解压：" + srcFile.getName());
        try {
//			zipFile = new ZipFile(srcFile);
            zipFile = new ZipFile(srcFile, Charset.forName(resolveCode(srcFile.getPath())));

            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                System.out.println("解压" + entry.getName());
                // 如果是文件夹，就创建个文件夹
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + "/" + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    // 如果是文件，就先创建一个文件，然后用io流把内容copy过去
                    File targetFile = new File(destDirPath + "/" + entry.getName());
                    // 保证这个文件的父文件夹必须要存在
                    if (!targetFile.getParentFile().exists()) {
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    // 将压缩文件内容写入到这个文件中
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[BUFFER_SIZE];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    fos.close();
                    is.close();
                }
            }
            long end = System.currentTimeMillis();
            System.out.println("解压完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            e.getMessage();
            throw new RuntimeException("unzip error from ZipUtils", e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.getMessage();
                }

            }
        }
    }


    //获取编码
    public static String resolveCode(String path) throws Exception {
        InputStream inputStream = new FileInputStream(path);
        byte[] head = new byte[3];
        inputStream.read(head);
        String code = "gb2312";
        if (head[0] == -1 && head[1] == -2){
            code = "UTF-16";
        } else if (head[0] == -2 && head[1] == -1){
            code = "Unicode";
    	} else if (head[0] == -17 && head[1] == -69 && head[2] == -65){
            code = "UTF-8";
		}
        inputStream.close();
        System.out.println(code);
        return code;
    }

    public static boolean checkZip(String filePath) throws RuntimeException {
        File srcFile = new File(filePath);
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            throw new RuntimeException(srcFile.getPath() + "所指文件不存在");
        }
        // 开始解压
        ZipFile zipFile = null;
        System.out.println("开始解压：" + srcFile.getName());
        try {
            zipFile = new ZipFile(srcFile, Charset.forName(resolveCode(srcFile.getPath())));
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                System.out.println("解压" + entry.getName());

                if (entry.isDirectory()) {
                    String dirPath = "/tmp/" + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    if (!checkImagType(entry.getName())) {
                        System.out.println("222");
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            e.getMessage();
            throw new RuntimeException("unzip error from ZipUtils", e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.getMessage();
                }

            }
        }
        return true;
    }

    public static void main(String[] args) {
//		String imgp= "Redocn_2012100818523401.rar";
//		System.out.println(checkZipType(imgp));
        try {
//			System.out.println(checkZip("C:\\Users\\<USER>\\Desktop\\ios\\1.zip"));

            System.out.println(checkImagType("1.jpg1"));

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.getMessage();
        }
    }

    public static RiskImageVo getRiskImage(String filePath) {
        java.io.File img = new java.io.File(filePath);

        if (!img.exists()) {
            return null;
        }
        try {
            BufferedImage bufferedImage = ImageIO.read(img);
            double widthPt = bufferedImage.getWidth() * 0.75;
            double heightPt = bufferedImage.getHeight() * 0.75;

            double finalWidthPt = 150;
            double finalHeightPt = heightPt * (finalWidthPt / widthPt);

            RiskImageVo riskImageVo = new RiskImageVo();
            riskImageVo.setHeight(finalHeightPt);
            riskImageVo.setWidth(finalWidthPt);
            return riskImageVo;
        } catch (Exception e) {
            e.getMessage();
            return null;
        }
    }

    public static String getImageBase64(String imagePath) {
        try {
            InputStream in = new FileInputStream(imagePath);
            byte[] data = new byte[in.available()];
            in.read(data);
            in.close();
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(data);
        } catch (Exception var5) {
            log.error("获取图表BASE64失败！", var5);
            return null;
        }
    }
}
