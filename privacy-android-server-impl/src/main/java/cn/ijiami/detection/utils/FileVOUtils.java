package cn.ijiami.detection.utils;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.github.pagehelper.util.StringUtil;

import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FileV0Utils.java
 * @Description 文件转换
 * @createTime 2022年01月13日 15:19:00
 */
@Slf4j
public class FileVOUtils {

    public static FileVO convertFileVOByFile(File saveFile) throws IOException {
        if (Objects.isNull(saveFile)) {
            throw new IjiamiRuntimeException("保存文件失败");
        }
        log.info("转换文件成功");
        FileVO fileVO = new FileVO();
        fileVO.setFilePath(saveFile.getAbsolutePath());
        fileVO.setFileSize(saveFile.length());
        fileVO.setFileName(saveFile.getName());
        fileVO.setFileExtName(findFileExtName(saveFile.getName()));
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) saveFile.length() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
        return fileVO;
    }

    public static String findFileExtName(String name) {
        if (!StringUtil.isEmpty(name)) {
            int index = name.lastIndexOf(".");
            if (index > 0) {
                return  name.substring(index + 1).toLowerCase();
            }
        }
        return "";
    }

}
