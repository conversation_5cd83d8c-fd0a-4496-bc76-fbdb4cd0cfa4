package cn.ijiami.detection.analyzer;

import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import cn.ijiami.detection.enums.BooleanEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.ijiami.detection.analyzer.bo.HostIpBO;
import cn.ijiami.detection.analyzer.parser.HostIpParser;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;

/**
 * host ip 业务处理
 *
 * <AUTHOR>
 * @date 2020-09-21 10:25
 */
@Component
public class HostIpaAction {

    private static Logger logger = LoggerFactory.getLogger(HostIpaAction.class);

    private final IpUtil ipUtil;

    private static final List<String> METHOD_LIST = Arrays.asList("GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT");
    private static final List<String> PROTOCOL = Arrays.asList("HTTP","HTTPS");

    public HostIpaAction(IpUtil ipUtil) {
        this.ipUtil = ipUtil;
    }

    /**
     * 解析通讯传输行为数据【host_ip】
     *
     * @param filePath     文件路径
     * @param stackDetails 18002 对应的堆栈详情数据
     * @return 解析通讯传输行为数据
     */
    public List<TPrivacyOutsideAddress> analyzeHostIp(String filePath, List<TPrivacyActionNougat> stackDetails) {
        // 获取hostIp中的数据
        IDetectDataParser parser = new HostIpParser();
        List<HostIpBO> hostIps = new ArrayList<>();
        if(new File(filePath).isDirectory()) {
        	List<String> filePaths = CommonUtil.listAllFilesInDir(filePath + File.separator, new String[]{".txt", ".TXT"}, true);
        	for (int i = 0; i < filePaths.size(); i++) {
        		hostIps.addAll(parser.parser(filePaths.get(i), null));
			}
        }else {
        	hostIps = parser.parser(filePath, null);
        }
        if (CollectionUtils.isEmpty(hostIps)) {
            logger.info("HostIpaAction - 通讯行为数据初步解析，无数据");
            return new ArrayList<>();
        }
        logger.info("HostIpaAction - 通讯行为数据初步解析：{} 条", hostIps.size());
        List<TPrivacyOutsideAddress> outsideAddresses = new ArrayList<>();
        // 解析通讯传输新为数据
        for (TPrivacyActionNougat stackDetail : stackDetails) {
            String detailsData = stackDetail.getDetailsData();
            if (StringUtils.isBlank(detailsData)) {
                continue;
            }
            // 获取满足过滤条件的host_ip数据
            List<HostIpBO> targetHostIpInfos = hostIps.stream().filter(buildHostIpFilter(detailsData)).collect(Collectors.toList());
            for (HostIpBO hostIp : targetHostIpInfos) {
                TPrivacyOutsideAddress outsideAddress = buildHostIpToOutsideAddress(stackDetail, hostIp);
                outsideAddresses.add(outsideAddress);
            }
        }
        logger.info("HostIpaAction - 通讯行为数据解析,获取到的符合要求的完整解析数据: {} 条", outsideAddresses.size());
        return outsideAddresses;
    }

    /**
     * 境内外数据过滤【host_ip】
     *
     * @param detailsData 抓包数据
     * @return 符合条件的数据
     */
    private Predicate<HostIpBO> buildHostIpFilter(String detailsData) {
        // 应产品要求，使用包含关系
        Predicate<HostIpBO> filter = (hostIp) -> {
            String compare = hostIp.getHost();
            if (StringUtils.isBlank(compare)) {
                return false;
            }
            // JZ.DO：优化部分
            return StringUtils.contains(detailsData, compare);
        };
        return filter;
    }

    /**
     * 构建境内外信息【host_ip】
     *
     * @param stackDetail 18002 对应的单条堆栈详情数据
     * @param hostIp      匹配到的hostIp实体
     * @return 境内外ip数据实体
     */
    private TPrivacyOutsideAddress buildHostIpToOutsideAddress(TPrivacyActionNougat stackDetail, HostIpBO hostIp) {
        //v2.5版本只要是string类型为空则按照--入库
        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        // 存放堆栈数据
        outsideAddress.setTaskId(stackDetail.getTaskId());
        outsideAddress.setBehaviorStage(stackDetail.getBehaviorStage());
        outsideAddress.setExecutorType(stackDetail.getExecutorType());
        outsideAddress.setExecutor(stackDetail.getExecutor());
        outsideAddress.setStackInfo(StringUtils.isEmpty(stackDetail.getStackInfo())?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(stackDetail.getStackInfo()));
        outsideAddress.setDetailsData(StringUtils.isEmpty(stackDetail.getDetailsData())? PinfoConstant.DETAILS_EMPTY: CommonUtil.filterPrintString(stackDetail.getDetailsData()));
        outsideAddress.setResponseData(DETAILS_EMPTY);
        outsideAddress.setStrTime(stackDetail.getActionTime());
        outsideAddress.setActionTime(stackDetail.getActionTime());
        // 存放系解析出的json数据
        outsideAddress.setSdkIds(stackDetail.getSdkIds());
        // 存放host_ip中的数据
        outsideAddress.setIp(StringUtils.isEmpty(hostIp.getIp())?PinfoConstant.DETAILS_EMPTY:hostIp.getIp());
        outsideAddress.setHost(StringUtils.isEmpty(hostIp.getHost())?PinfoConstant.DETAILS_EMPTY:hostIp.getHost());
        String address;
        if (StringUtils.isNotBlank(hostIp.getIp())) {
            address = ipUtil.getAddress(hostIp.getIp());
        } else {
            address = ipUtil.getAddress(hostIp.getHost());
        }
        outsideAddress.setAddress(StringUtils.isEmpty(address)?PinfoConstant.DETAILS_EMPTY:address);
        outsideAddress.setCounter(hostIp.getCounter());
        int isOutSide = IpUtil.isOutSide(address);
        outsideAddress.setOutside(isOutSide);
        if (StringUtils.isNotBlank(stackDetail.getDetailsData())) {
            outsideAddress.setRequestMethod(METHOD_LIST
                    .stream()
                    .filter(m -> stackDetail.getDetailsData().startsWith(m))
                    .findFirst()
                    .orElse(PinfoConstant.DETAILS_EMPTY)
            );

            outsideAddress.setProtocol(PROTOCOL
                    .stream()
                    .filter(m -> stackDetail.getDetailsData().toUpperCase().startsWith(m))
                    .findFirst()
                    .orElse(PinfoConstant.DETAILS_EMPTY)
            );

            //增加cookie标记以便前台展示
            if(stackDetail.getDetailsData().toUpperCase().contains("COOKIE")){
                outsideAddress.setCookieMark(BooleanEnum.TRUE.value);
                outsideAddress.setCookie(stackDetail.getDetailsData().substring(stackDetail.getDetailsData().toUpperCase().indexOf("COOKIE")));
            }

        } else {
            outsideAddress.setRequestMethod(PinfoConstant.DETAILS_EMPTY);
            outsideAddress.setCookie(PinfoConstant.DETAILS_EMPTY);
            outsideAddress.setCookieMark(BooleanEnum.FALSE.value);
        }
        outsideAddress.setUrl(StringUtils.isEmpty(hostIp.getUrl())?PinfoConstant.DETAILS_EMPTY:hostIp.getUrl());
        outsideAddress.setPackageName(stackDetail.getPackageName());
        //不含堆栈数据不会有端口号，按照--入库
        outsideAddress.setPort(PinfoConstant.DETAILS_EMPTY);
        return outsideAddress;
    }

}
