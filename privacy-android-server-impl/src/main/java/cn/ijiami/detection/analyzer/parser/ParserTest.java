package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.bo.HostIpBO;
import cn.ijiami.detection.analyzer.bo.SharedPrefBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-14 21:30
 */
public class ParserTest {

    public static void captureInfoParser() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\capture_info.txt";
        IDetectDataParser parser = new CaptureInfoParser();
        List<CaptureInfoBO> analysis = parser.parser(filePath, null);
        for (CaptureInfoBO captureInfoBO : analysis) {
            captureInfoBO.setOriginal(null);
            System.err.println(captureInfoBO.getUrl());
        }
    }

    public static void hostIpParser() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\hostip.txt";
        IDetectDataParser parser = new HostIpParser();
        List<HostIpBO> to = parser.parser(filePath, null);
        for (HostIpBO hostIpBO : to) {
            hostIpBO.setOriginal(null);
            System.err.println(hostIpBO);
        }
    }

    public static void behaviorInfoParser() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\behavior_info.json";
        IDetectDataParser parser = new BehaviorInfoParser();
        List<ActionStackBO> to = parser.parser(filePath, null);
        for (ActionStackBO hostIpBO : to) {
            System.err.println(hostIpBO);
        }
    }

    public static void sharePrefParser() {
        String filePath = "C:\\Users\\<USER>\\Desktop\\数据解析\\shared_prefs";
        IDetectDataParser parser = new SharedPrefParser();
        List<SharedPrefBO> to = parser.parser(filePath, null);
        for (SharedPrefBO sharedPref : to) {
            sharedPref.setContent(null);
            System.err.println(sharedPref);
        }
    }

    public static void main(String[] args) {
        //        captureInfoParser();
        //        hostIpParser();
        //        behaviorInfoParser();
        sharePrefParser();
    }
}
