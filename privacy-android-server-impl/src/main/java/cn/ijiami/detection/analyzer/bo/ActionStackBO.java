package cn.ijiami.detection.analyzer.bo;

import cn.ijiami.detection.entity.TPrivacyActionNougat;

import java.io.Serializable;
import java.util.Date;

/**
 * 行为堆栈数据
 * {"action_time":1600082701806,"type_id":28001,"type_name":"","details_data":"android.permission.REAL_GET_TASKS","stack_info":""}
 *
 * <AUTHOR>
 * @date 2020-09-09 11:04
 */
public class ActionStackBO implements Serializable {

    private static final long serialVersionUID = 2359214037325134641L;

    /**
     * 行为触发时间
     */
    private Date   actionTime;
    /**
     * 行为id
     */
    private Long   actionId;
    /**
     * 类型名称
     */
    private String typeName;
    /**
     * 详细数据
     */
    private String detailsData;
    /**
     * 函数调用栈数据
     */
    private String stackInfo;
    /**
     * jni函数调用栈数据
     */
    private String jniStackInfo;

    public ActionStackBO() {
    }

    public ActionStackBO(TPrivacyActionNougat privacyActionNougat) {
        this.detailsData = privacyActionNougat.getDetailsData();
        this.stackInfo = privacyActionNougat.getStackInfo();
        this.jniStackInfo = privacyActionNougat.getJniStackInfo();
        this.actionTime = privacyActionNougat.getActionTime();
        this.actionId = privacyActionNougat.getActionId();
    }

    public Date getActionTime() {
        return actionTime;
    }

    public void setActionTime(Date actionTime) {
        this.actionTime = actionTime;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDetailsData() {
        return detailsData;
    }

    public void setDetailsData(String detailsData) {
        this.detailsData = detailsData;
    }

    public String getStackInfo() {
        return stackInfo;
    }

    public void setStackInfo(String stackInfo) {
        this.stackInfo = stackInfo;
    }

    public String getJniStackInfo() {
        return jniStackInfo;
    }

    public void setJniStackInfo(String jniStackInfo) {
        this.jniStackInfo = jniStackInfo;
    }

    @Override
    public String toString() {
        return "ActionStackBO{" +
                "actionTime=" + actionTime +
                ", actionId=" + actionId +
                ", typeName='" + typeName + '\'' +
                ", detailsData='" + detailsData + '\'' +
                ", stackInfo='" + stackInfo + '\'' +
                ", jniStackInfo='" + jniStackInfo + '\'' +
                '}';
    }
}
