package cn.ijiami.detection.analyzer;

import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName XMLToMap.java
 * @Description xml内容转为键值对
 * @createTime 2024年05月21日 19:21:00
 */
@Slf4j
public class XMLToMap {

    public static Map<String, String> parseXMLToHashMap(String xmlContent)  {
        HashMap<String, String> resultMap = new HashMap<>();

        // Parse the XML content
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new java.io.ByteArrayInputStream(xmlContent.getBytes()));

            // Get the <map> element
            Element mapElement = document.getDocumentElement();

            // Get all child nodes of the <map> element
            NodeList nodeList = mapElement.getChildNodes();
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node node = nodeList.item(i);
                if (node.getNodeType() == Node.ELEMENT_NODE) {
                    Element element = (Element) node;
                    String name = element.getAttribute("name");
                    String value = element.getTagName().equals("string") ? element.getTextContent() : element.getAttribute("value");
                    resultMap.put(name, value);
                }
            }
            return resultMap;
        } catch (Exception e) {
            log.error("解析map失败", e);
            return Collections.emptyMap();
        }
    }
}
