package cn.ijiami.detection.analyzer.bo;

import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;

import java.io.Serializable;
import java.util.List;

/**
 * 检测数据集合
 *
 * <AUTHOR>
 * @date 2020-09-21 15:15
 */
public class DetectDataBO implements Serializable {

    private static final long serialVersionUID = 8087359762373641603L;

    /**
     * 抓包数据堆栈信息
     */
    private List<TPrivacyActionNougat>   privacyActionNougats;
    /**
     * 传输个人信息
     */
    private List<TPrivacySensitiveWord>  privacySensitiveWords;
    /**
     * 通讯行为分析
     */
    private List<TPrivacyOutsideAddress> privacyOutsideAddresses;
    /**
     * 存储个人信息
     */
    private List<TPrivacySharedPrefs>    privacySharedPrefs;

    private String appletVersion;
    private String appletLogoUrl;

    public List<TPrivacyActionNougat> getPrivacyActionNougats() {
        return privacyActionNougats;
    }

    public void setPrivacyActionNougats(List<TPrivacyActionNougat> privacyActionNougats) {
        this.privacyActionNougats = privacyActionNougats;
    }

    public List<TPrivacySensitiveWord> getPrivacySensitiveWords() {
        return privacySensitiveWords;
    }

    public void setPrivacySensitiveWords(List<TPrivacySensitiveWord> privacySensitiveWords) {
        this.privacySensitiveWords = privacySensitiveWords;
    }

    public List<TPrivacyOutsideAddress> getPrivacyOutsideAddresses() {
        return privacyOutsideAddresses;
    }

    public void setPrivacyOutsideAddresses(List<TPrivacyOutsideAddress> privacyOutsideAddresses) {
        this.privacyOutsideAddresses = privacyOutsideAddresses;
    }

    public List<TPrivacySharedPrefs> getPrivacySharedPrefs() {
        return privacySharedPrefs;
    }

    public void setPrivacySharedPrefs(List<TPrivacySharedPrefs> privacySharedPrefs) {
        this.privacySharedPrefs = privacySharedPrefs;
    }

    @Override
    public String toString() {
        return "FileAnalyzeBO{" + "privacyActionNougats=" + privacyActionNougats + ", privacySensitiveWords=" + privacySensitiveWords
                + ", privacyOutsideAddresses=" + privacyOutsideAddresses + ", privacySharedPrefs=" + privacySharedPrefs + '}';
    }

    public String getAppletVersion() {
        return appletVersion;
    }

    public void setAppletVersion(String appletVersion) {
        this.appletVersion = appletVersion;
    }

    public String getAppletLogoUrl() {
        return appletLogoUrl;
    }

    public void setAppletLogoUrl(String appletLogoUrl) {
        this.appletLogoUrl = appletLogoUrl;
    }
}
