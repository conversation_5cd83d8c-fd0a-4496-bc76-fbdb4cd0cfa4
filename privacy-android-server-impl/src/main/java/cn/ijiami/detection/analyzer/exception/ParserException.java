package cn.ijiami.detection.analyzer.exception;

/**
 * 解析异常
 *
 * <AUTHOR>
 * @date 2020/10/12 10:07
 **/
public class ParserException extends RuntimeException {

    private Integer code;

    private String message;

    public ParserException(IParserExceptionEnum parserExceptionEnum) {
        this.code = parserExceptionEnum.getCode();
        this.message = parserExceptionEnum.getMessage();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
