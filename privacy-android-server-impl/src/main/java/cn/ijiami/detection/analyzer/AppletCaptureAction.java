package cn.ijiami.detection.analyzer;

import cn.ijiami.detection.VO.detection.privacy.dto.AppletInputParamDTO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.ijiami.detection.utils.AppletActionUtils.*;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MiniProgramAction.java
 * @Description 小程序网络解析工具
 * @createTime 2023年04月03日 18:07:00
 */
@Component
public class AppletCaptureAction {

    @Autowired
    private IpUtil ipUtil;

    //分析传输个人信息
    public List<TPrivacySensitiveWord> analyzeCaptureInfo(Long taskId, BehaviorStageEnum behaviorStageEnum,
                                                          AppletActionBO data, List<TSensitiveWord> sensitiveWords){
        List<TPrivacySensitiveWord> result = new ArrayList<>();
        sensitiveWords.forEach(sensitiveWord -> {
            // 无名称，无正则视为无效
            if(StringUtils.isNotBlank(sensitiveWord.getRegex())){
                result.addAll(matchCaptureInfoByRegex(behaviorStageEnum, data, sensitiveWord, taskId));
            }else if(StringUtils.isNotBlank(sensitiveWord.getName())){
                result.addAll(matchCaptureInfoByWord(behaviorStageEnum, data, sensitiveWord, taskId));
            }
        });
        return result;
    }

    /**
     * 正则匹配传输个人信息数据
     *
     * @param data 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByRegex(BehaviorStageEnum behaviorStageEnum, AppletActionBO data,
                                                                TSensitiveWord sensitive, Long taskId) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (hasParams(data.getInputParam())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (hasCookie(data.getInputParam())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, true));
        }
        return sensitiveWordList;
    }

    /**
     * 关键词匹配传输个人信息数据
     *
     * @param data 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByWord(BehaviorStageEnum behaviorStageEnum, AppletActionBO data,
                                                               TSensitiveWord sensitive, Long taskId) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (hasParams(data.getInputParam())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (hasCookie(data.getInputParam())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, true));
        }
        return sensitiveWordList;
    }

    private boolean hasParams(AppletInputParamDTO inputParam) {
        if (Objects.isNull(inputParam)) {
            return false;
        }
        return !Objects.isNull(inputParam.getData());
    }

    private boolean hasCookie(AppletInputParamDTO inputParam) {
        if (Objects.isNull(inputParam)) {
            return false;
        }
        if (Objects.isNull(inputParam.getHeader())) {
            return false;
        }
        if (Objects.isNull(inputParam.getHeader().get("cookie"))) {
            return false;
        }
        return StringUtils.isNotBlank(inputParam.getHeader().get("cookie").toString());
    }

    private List<TPrivacySensitiveWord> matchByRegexFromCookieOrParams(BehaviorStageEnum behaviorStageEnum, AppletActionBO data,
                                                                       TSensitiveWord sensitive, Long taskId, boolean cookie) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        String from;
        if (cookie) {
            from = getCookie(data.getInputParam());
        } else {
            from = getParams(data.getInputParam());
        }
        Pattern pattern = Pattern.compile(sensitive.getRegex());
        Matcher matcher = pattern.matcher(from);
        // 获取正则匹配到的内容
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, from, sensitive, true);
            if (isInvalidInfo) {
                continue;
            }
            String sensitiveWord = matcher.group();
            if (StringUtils.isBlank(sensitiveWord)) {
                continue;
            }
            String code = SensitiveWordsHelper.getMatchString(matcher, from);
            String method = getMethod(data.getInputParam());
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(behaviorStageEnum, data, sensitive, code, method, sensitiveWord, taskId, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

    /**
     * 从capture info中组装敏感词
     *
     * @param data  网络抓包数据
     * @param sensitive    关键词
     * @param code 标记源
     * @param method       方法类型
     * @param keyword      抓取关键词
     * @return
     */
    private TPrivacySensitiveWord buildCaptureInfoToSensitiveWord(BehaviorStageEnum behaviorStageEnum, AppletActionBO data,
                                                                  TSensitiveWord sensitive, String code, String method, String keyword, Long taskId, boolean cookie) {
        //v2.5版本只要是string类型为空则按照--入库
        // 组装实体
        TPrivacySensitiveWord bean = new TPrivacySensitiveWord();
        // 存放堆栈数据
        bean.setStackInfo(StringUtils.isEmpty(data.getStackInfo())?PinfoConstant.DETAILS_EMPTY: CommonUtil.filterPrintString(data.getStackInfo()));
        bean.setDetailsData(getParams(data.getInputParam()));
        bean.setResponseData(getResponseData(data.getReturnData()));
        bean.setActionTime(new Date(data.getTimeStamp()));
        // 任务信息
        bean.setTaskId(taskId);
        bean.setBehaviorStage(behaviorStageEnum);
        bean.setExecutorType(ExecutorTypeEnum.APP.getValue());
        bean.setExecutor(data.getAppName());
        bean.setSdkName(StringUtils.EMPTY);
        // 存放系解析出的json数据
        bean.setSdkIds(StringUtils.EMPTY);
        // 存放敏感词数据
        bean.setTypeId(sensitive.getTypeId());
        bean.setName(StringUtils.isEmpty(sensitive.getName())?PinfoConstant.DETAILS_EMPTY:sensitive.getName());
        bean.setSensitiveWord(StringUtils.isEmpty(keyword)?PinfoConstant.DETAILS_EMPTY:keyword);
        bean.setRiskLevel(sensitive.getRiskLevel());
        bean.setSuggestion(sensitive.getSuggestion());
        // 存放解析出的数据
        bean.setMethod(StringUtils.isEmpty(method)?PinfoConstant.DETAILS_EMPTY:method);
        AppletInputParamDTO param = data.getInputParam();
        bean.setUrl(StringUtils.isEmpty(param.getUrl())?PinfoConstant.DETAILS_EMPTY:param.getUrl());
        try {
            URI url = new URI(param.getUrl());
            bean.setHost(StringUtils.isEmpty(url.getHost())?PinfoConstant.DETAILS_EMPTY:url.getHost());
            bean.setPort(url.getPort() < 0 ? PinfoConstant.DETAILS_EMPTY : String.valueOf(url.getPort()));
            bean.setAddress(StringUtils.isEmpty(url.getPath()) ? PinfoConstant.DETAILS_EMPTY : url.getPath());
            bean.setProtocol(StringUtils.isEmpty(url.getScheme()) ? PinfoConstant.DETAILS_EMPTY : url.getScheme());
            try {
                bean.setIp(InetAddress.getByName(url.getHost()).getHostAddress());
            } catch (UnknownHostException e) {
                bean.setIp(PinfoConstant.DETAILS_EMPTY);
            }
            String address = ipUtil.getAddress(url.getHost());
            bean.setAttributively(StringUtils.isEmpty(address) ? PinfoConstant.DETAILS_EMPTY : address);
        } catch (URISyntaxException e) {
            e.getMessage();
        }
        bean.setCode(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY : code);
        bean.setCookie(getCookie(param));
        //增加cookie标记，以便前端展示cookie标签
        bean.setCookieMark(StringUtils.isNotBlank(bean.getCookie())?1:null);
        bean.setCookieWord(cookie);
        bean.setPackageName(data.getAppId());
        bean.setPlaintextTransmission(isPlaintextTransmission(sensitive).value);
        return bean;
    }

    private List<TPrivacySensitiveWord> matchByWordFromCookieOrParams(BehaviorStageEnum behaviorStageEnum, AppletActionBO data,
                                                                      TSensitiveWord sensitive, Long taskId, boolean cookie) {
        String sensitiveWord = sensitive.getSensitiveWords();
        if (StringUtils.isBlank(sensitiveWord)) {
            return new ArrayList<>();
        }
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(cookie ? getCookie(data.getInputParam()) : getParams(data.getInputParam()),
                StringUtils.EMPTY, sensitive.getSensitiveWords(),false)) {
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(behaviorStageEnum, data, sensitive,
                    word.getCode(), getMethod(data.getInputParam()), word.getWord(), taskId, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

}
