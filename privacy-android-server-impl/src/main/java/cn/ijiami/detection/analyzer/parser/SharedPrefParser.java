package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.analyzer.XMLToMap;
import cn.ijiami.detection.analyzer.bo.SharedPrefBO;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.utils.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 捕获信息数据解析
 *
 * <AUTHOR>
 * @date 2020-05-15 9:42
 */
public class SharedPrefParser implements IDetectDataParser<SharedPrefBO> {

    @Override
    public List<SharedPrefBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 解析数据
     *
     * @param filePath 捕获信息数据存放的位置
     * @return list对象
     */
    private static List<SharedPrefBO> analysis(String filePath) {
        File sharedPrefsFile = new File(filePath);
        if (!sharedPrefsFile.isDirectory()) {
            return new ArrayList<>();
        }
        // 目录下的文件
        Collection<File> files = FileUtils.listFiles(new File(filePath), new String[] {"xml"}, true);
        if (CollectionUtils.isEmpty(files)) {
            return new ArrayList<>();
        }

        List<SharedPrefBO> sharedPrefs = new ArrayList<>();
        for (File file : files) {
            String absolutePath = file.getAbsolutePath();
            String content = CommonUtil.readFileToPrintString(absolutePath);
            if (StringUtils.isBlank(content)) {
                continue;
            }
            // 构建实体数据
            try {
                sharedPrefs.add(build(filePath, absolutePath, content));
            } catch (ParserException e) {
                e.getMessage();
            }
        }
        return sharedPrefs;
    }

    /**
     * 构建存储个人信息实体
     *
     * @param filePath     目录路径
     * @param absolutePath 文件绝对路径
     * @param content      文件对应内容
     * @return
     */
    private static SharedPrefBO build(String filePath, String absolutePath, String content) throws ParserException {
        SharedPrefBO bo = new SharedPrefBO();
        try {
            String path = absolutePath.replace(filePath, "");
            bo.setPath(path);
            bo.setContent(content);
            bo.setMap(XMLToMap.parseXMLToHashMap(content));
            bo.setAbsolutePath(absolutePath);
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.SHARED_PREFS);
        }
        return bo;
    }

}
