package cn.ijiami.detection.analyzer.helper;

import cn.ijiami.detection.utils.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class BehaviorActionConvertHelper {

    /**
     * Android设备sd卡公共目录路径匹配
     */
    private final static Pattern SDCARD_PATTERN = Pattern.compile("(" +
            "/storage/emulated/0/" +
    		"|/data/user/0/"+
            "|/sdcard/" +
            "|/data/user/0/"+
            "|/mnt/sdcard/" +
            "|/mnt/user/0/primary/" +
            "|/storage/self/primary/)(.*)");

    private final static Pattern IP_PATTERN = Pattern.compile("((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)");

    @Autowired
    private IpUtil ipUtil;

    public enum ConvertActionId {

        UNCHANGING(-1L), SDCARD_WRITE(1000000L), SDCARD_DELETE(1000001L), SDCARD_READ(1000002L), CPU(1000003L), LOCATION(1000008L),
        FOREIGN_IP(1000004L), IMEI(10002L), MEID(10012L), CLIPBOARD_1(33001L), CLIPBOARD_2(33002L), ICCID(10004L), ICCID_PHONE_NUMBER(1000401L);

        public final Long id;

        ConvertActionId(Long id) {
            this.id = id;
        }

    }

    static class Convert {

        public final Set<Long> convertIds;
        public final Function<ActionInfo, ConvertActionId> matcherFun;

        Convert(Set<Long> convertIds, Function<ActionInfo, ConvertActionId> matcherFun) {
            this.convertIds = convertIds;
            this.matcherFun = matcherFun;
        }
    }

    private final List<Convert> convertList = new ArrayList<>();

    private Set<Long> ids(Long ...a) {
        return new HashSet<>(Arrays.asList(a));
    }

    @PostConstruct
    private void init() {
        convertList.add(new Convert(ids(17001L, 17003L, 17006L), s -> {
            if (StringUtils.isNotBlank(s.details)) {
                // 是否sd卡目录
                Matcher sdcard = SDCARD_PATTERN.matcher(s.details);
                if (sdcard.find()) {
                    String path = sdcard.group(2);
                    if (path.contains(s.packageName)) {
                        // 操作app私有目录
                        return ConvertActionId.UNCHANGING;
                    } else {
                        // 写入公共存储区行为
                        return ConvertActionId.SDCARD_WRITE;
                    }
                }
            }
            return ConvertActionId.UNCHANGING;
        }));
        convertList.add(new Convert(ids(17002L), s -> {
            if (StringUtils.isNotBlank(s.details)) {
                Matcher matcher = SDCARD_PATTERN.matcher(s.details);
                if (matcher.find()) {
                    String path = matcher.group(2);
                    if (path.contains(s.packageName)) {
                        // 操作app私有目录
                        return ConvertActionId.UNCHANGING;
                    } else {
                        // 删除公共存储区行为
                        return ConvertActionId.SDCARD_DELETE;
                    }
                }
            }
            return ConvertActionId.UNCHANGING;
        }));
        convertList.add(new Convert(ids(17004L, 17005L), s -> {
            if (StringUtils.isNotBlank(s.details)) {
                Matcher matcher = SDCARD_PATTERN.matcher(s.details);
                if (matcher.find()) {
                    String path = matcher.group(2);
                    if (path.contains(s.packageName)) {
                        // 操作app私有目录
                        return ConvertActionId.UNCHANGING;
                    } else {
                        // 读取公共存储区行为
                        return ConvertActionId.SDCARD_READ;
                    }
                }
            }
            return ConvertActionId.UNCHANGING;
        }));
        convertList.add(new Convert(ids(18001L), s -> {
            if (IpUtil.isOutSide(ipUtil.getAddress(getIp(s.details))) > 0) {
                return ConvertActionId.FOREIGN_IP;
            } else {
                return ConvertActionId.UNCHANGING;
            }
        }));
        convertList.add(new Convert(ids(31001L), s -> {
            // 只有调用android.os.SystemProperties.get的行为才进行判断
            if (StringUtils.startsWith(s.stackInfo, "android.os.SystemProperties.get")) {
                if (StringUtils.containsIgnoreCase(s.details, ".imei")) {
                    // 转为获取imei行为
                    return ConvertActionId.IMEI;
                } else if (StringUtils.containsIgnoreCase(s.details, ".meid")) {
                    // 转为获取meid行为
                    return ConvertActionId.MEID;
                }
            }
            return ConvertActionId.UNCHANGING;
        }));
        convertList.add(new Convert(ids(10006L), s -> {
            // 根据参数转为监听地理位置行为
            if (StringUtils.contains(s.details, "PhoneStateListener.LISTEN_CELL_LOCATION")) {
                return ConvertActionId.LOCATION;
            } else {
                return ConvertActionId.UNCHANGING;
            }
        }));
        
        //android.content.ClipboardManager.getPrimaryClipDescription
        convertList.add(new Convert(ids(33002L), s -> {
            // 获取剪切板数据对象-->获取剪切板内容
            if (StringUtils.contains(s.stackInfo, "android.content.ClipboardManager.getPrimaryClipDescription")) {
                return ConvertActionId.CLIPBOARD_1;
            } else {
                return ConvertActionId.CLIPBOARD_2;
            }
        }));
        
        // 转换ICCID获取手机号情况
        convertList.add(new Convert(ids(10004L), s -> {
            // 判断是否为手机号码
            if (StringUtils.contains(s.stackInfo, "android.database.CursorWrapper.getString") && isPhoneNumber(s.details)) {
                return ConvertActionId.ICCID_PHONE_NUMBER;
            } else {
                return ConvertActionId.ICCID;
            }
        }));
    }
    
    /**
     * 判断是否为手机号
     * @param phoneNumber
     * @return
     */
    public boolean isPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return false;
        }
        // 正则表达式匹配中国大陆手机号码
        String phoneRegex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(phoneRegex);
    }
    
    public Long convertActionId(Long actionId, String details, String packageName) {
        return convertActionId(actionId, "", details, packageName);
    }

    public Long convertActionId(Long actionId, String stackInfo, String details, String packageName) {
        for (Convert matcher: convertList) {
            if (matcher.convertIds.contains(actionId)) {
                ConvertActionId convertActionId = matcher.matcherFun.apply(new ActionInfo(stackInfo, details, packageName));
                if (convertActionId != ConvertActionId.UNCHANGING) {
                    return convertActionId.id;
                }
            }
        }
        return actionId;
    }

    public static String getIp(String details) {
        if (StringUtils.isBlank(details)) {
            return "";
        }
        Matcher m = IP_PATTERN.matcher(details);
        if (m.find()) {
            return m.group();
        }
        return "";
    }

    static class ActionInfo {

        private final String stackInfo;
        private final String details;
        private final String packageName;

        ActionInfo(String stackInfo, String details, String packageName) {
            this.stackInfo = stackInfo;
            this.details = details;
            this.packageName = packageName;
        }
    }

}
