package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.bean.WaverifyInfo;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.detection.utils.SAXReaderFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.xml.sax.InputSource;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WechatAppletInfoParser.java
 * @Description 微信小程序信息解析
 * @createTime 2024年02月21日 17:44:00
 */
@Slf4j
public class WechatAppletInfoParser {

    public static TComplianceAppletPlugins parserPlugins(String filePath) {
        File file = new File(filePath);
        try {
            String encodeStr = "UTF-8";
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            String xmlContent = FileUtils.readFileToString(file, encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Map<String, String> infoMap = extractInfo(document.getRootElement(),
                    new CharSequence[]{"开发者", "AppID", "服务类目"});
            TComplianceAppletPlugins plugins = new TComplianceAppletPlugins();
            plugins.setPluginName(file.getName().replace(".xml", ""));
            plugins.setPluginAppid(getInfoDefault(infoMap, "AppID"));
            plugins.setDeveloper(getInfoDefault(infoMap, "开发者"));
            plugins.setServiceCategory(getInfoDefault(infoMap,"服务类目"));
            return plugins;
        } catch (Exception e) {
            log.error("微信小程序信息解析出错", e);
            return null;
        }
    }

    private static String getInfoDefault(Map<String, String> infoMap, String key) {
        String info = infoMap.get(key);
        return StringUtils.isBlank(info) ? PinfoConstant.DETAILS_EMPTY : info;
    }

    public static WaverifyInfo parserBasicInfo(String filePath) {
        File file = new File(filePath);
        try {
            String encodeStr = "UTF-8";
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            String xmlContent = FileUtils.readFileToString(file, encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Map<String, String> infoMap = extractInfo(document.getRootElement(),
                    new CharSequence[]{"账号原始ID", "AppID", "服务类目", "认证主体", "服务隐私及数据提示", "服务声明", "更新时间", "引用插件", "授权第三方服务"},
                    text -> StringUtils.equals(text, "授权第三方服务") ? "、" : StringUtils.EMPTY);
            WaverifyInfo waverifyInfo = new WaverifyInfo();
            waverifyInfo.setUsername(infoMap.get("账号原始ID"));
            waverifyInfo.setServiceAndData(infoMap.get("服务隐私及数据提示"));
            if (StringUtils.isNotBlank(waverifyInfo.getServiceAndData())) {
                Matcher matcher = Pattern.compile("《(.+)小程序隐私保护指引》").matcher(waverifyInfo.getServiceAndData());
                if (matcher.find()) {
                    waverifyInfo.setNickname(matcher.group(1));
                }
            }
            waverifyInfo.setAppId(infoMap.get("AppID"));
            waverifyInfo.setCategoryList(infoMap.get("服务类目"));
            waverifyInfo.setName(infoMap.get("认证主体"));
            waverifyInfo.setServiceStatement(infoMap.get("服务声明"));
            waverifyInfo.setUpdateTime(infoMap.get("更新时间") + "（获取时间：" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "）");
            waverifyInfo.setAuth3reList(infoMap.get("授权第三方服务"));
            return waverifyInfo;
        } catch (Exception e) {
            log.error("微信小程序信息解析出错", e);
            return null;
        }
    }

    private static Map<String, String> extractInfo(Element root, CharSequence[] extractKeys) {
        Map<String, String> infoMap = new HashMap<>();
        extractInfo(root, extractKeys, infoMap, null);
        return infoMap;
    }

    private static Map<String, String> extractInfo(Element root, CharSequence[] extractKeys, Function<String, String> delimiterFunc) {
        Map<String, String> infoMap = new HashMap<>();
        extractInfo(root, extractKeys, infoMap, delimiterFunc);
        return infoMap;
    }

    private static void extractInfo(Element root, CharSequence[] extractKeys, Map<String, String> infoMap, Function<String, String> delimiterFunc) {
        if (root.elements().isEmpty()) {
            return;
        }
        List<Element> elements = root.elements();
        for (int i = 0; i < elements.size(); i++) {
            Element element = elements.get(i);
            if (element.getName().equals("node")) {
                String text = element.attributeValue("text");
                if (StringUtils.equalsAnyIgnoreCase(text, extractKeys)) {
                    String delimiter = Objects.isNull(delimiterFunc) ? StringUtils.EMPTY : delimiterFunc.apply(text);
                    infoMap.put(text, findNextNodeText(elements, i + 1, i + 2, delimiter));
                }
            }
            extractInfo(element, extractKeys, infoMap, delimiterFunc);
        }
    }

    private static String findNextNodeText(List<Element> elements, int startIndex, int endIndex, String delimiter) {
        StringJoiner builder = new StringJoiner(delimiter);
        for (int i = startIndex; i < endIndex; i++) {
            Element element = elements.get(i);
            if (element.getName().equals("node")) {
                String text = element.attributeValue("text");
                if (StringUtils.isNotBlank(text)) {
                    builder.add(text);
                } else if (!element.elements().isEmpty()) {
                    builder.add(findNextNodeText(element.elements(), 0, element.elements().size(), delimiter));
                }
            }
        }
        return builder.toString();
    }

}
