package cn.ijiami.detection.analyzer;

import static cn.ijiami.detection.analyzer.bo.CaptureInfoBO.buildCaptureInfoBO;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;
import static cn.ijiami.detection.helper.SensitiveWordsHelper.enhancedWords;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

import java.io.File;
import java.io.FileInputStream;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.utils.HttpPacketFormatter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.parser.CaptureInfoParser;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.Text2Pic;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * capture info数据处理
 *
 * <AUTHOR>
 * @date 2020-09-21 10:25
 */
@Component
public class CaptureInfoAction {

    private static Logger                 logger = LoggerFactory.getLogger(CaptureInfoAction.class);
    private final  IjiamiCommonProperties commonProperties;
    private final  IBaseFileService       fileService;
    private final  IpUtil                 ipUtil;

    public CaptureInfoAction(IjiamiCommonProperties commonProperties, IBaseFileService fileService, IpUtil ipUtil) {
        this.commonProperties = commonProperties;
        this.fileService = fileService;
        this.ipUtil = ipUtil;
    }

    
    
    public List<CaptureInfoBO>  analyzeCaptureInfoBO(String filePath){
    	IDetectDataParser<CaptureInfoBO> parser = new CaptureInfoParser();
        List<CaptureInfoBO> captureInfos = new ArrayList<>();
        if(new File(filePath).isDirectory()) {
        	List<String> filePaths = CommonUtil.listAllFilesInDir(filePath + File.separator, new String[]{".txt", ".TXT"}, true);
        	for (int i = 0; i < filePaths.size(); i++) {
        		captureInfos.addAll(parser.parser(filePaths.get(i), null));
			}
        }else {
        	captureInfos = parser.parser(filePath, null);
        }
        if (CollectionUtils.isEmpty(captureInfos)) {
            logger.info("CaptureInfoAction - 传输个人信息数据初步解析，无数据");
            return new ArrayList<>();
        }
        logger.info("CaptureInfoAction - 传输个人信息数据初步解析：{} 条", captureInfos.size());
        return captureInfos;
    }
    
    /**
     * 解析传输个人信息数据【capture_info】
     *
     * @param filePath       文件路径
     * @param stackDetails   18002 对应的堆栈详情数据
     * @param sensitiveWords 关键词
     * @return
     */
    public List<TPrivacySensitiveWord> analyzeCaptureInfo(String filePath, List<TPrivacyActionNougat> stackDetails, List<TSensitiveWord> sensitiveWords) {
        IDetectDataParser<CaptureInfoBO> parser = new CaptureInfoParser();
        List<CaptureInfoBO> captureInfos = new ArrayList<>();
        if(new File(filePath).isDirectory()) {
        	List<String> filePaths = CommonUtil.listAllFilesInDir(filePath + File.separator, new String[]{".txt", ".TXT"}, true);
        	for (int i = 0; i < filePaths.size(); i++) {
        		captureInfos.addAll(parser.parser(filePaths.get(i), null));
			}
        }else {
        	captureInfos = parser.parser(filePath, null);
        }
        
//        IDetectDataParser<CaptureInfoBO> parser = new CaptureHostIpInfoParser();
//        List<CaptureInfoBO> result = parser.parser(filePath);
        
        if (CollectionUtils.isEmpty(captureInfos)) {
            logger.info("CaptureInfoAction - 传输个人信息数据初步解析，无数据");
            return new ArrayList<>();
        }
        logger.info("CaptureInfoAction - 传输个人信息数据初步解析：{} 条", captureInfos.size());
        List<TPrivacySensitiveWord> privacySensitiveWords = new ArrayList<>();
        // 解析通讯传输新为数据
        for (TPrivacyActionNougat stackDetail : stackDetails) {
            String detailsData = stackDetail.getDetailsData();
            // 无抓包数据视为无效数据
            if (StringUtils.isBlank(detailsData)) {
                continue;
            }
            // 获取满足过滤条件的host_ip数据
            List<CaptureInfoBO> targetCaptureInfos = captureInfos.stream().filter(buildCaptureInfoFilter(detailsData)).collect(Collectors.toList());
            // 获取解析后的完整传输数据
            List<TPrivacySensitiveWord> groupCapture = matchCaptureInfoData(targetCaptureInfos, stackDetail, sensitiveWords);
            if (CollectionUtils.isNotEmpty(groupCapture)) {
                privacySensitiveWords.addAll(groupCapture);
            }
        }
        logger.info("CaptureInfoAction - 传输个人信息数据解析,获取到的符合要求的完整解析数据: {} 条", captureInfos.size());
        return privacySensitiveWords;
    }

    public List<TPrivacySensitiveWord> analyzeEmptyDetailsWord(TPrivacyOutsideAddress outsideAddress, List<TPrivacyActionNougat> stackDetails,
                                                               List<TSensitiveWord> sensitiveWords) {
        List<TPrivacySensitiveWord> privacySensitiveWords = new ArrayList<>();
        CaptureInfoBO captureInfoBO = buildCaptureInfoBO(outsideAddress);
        // 解析通讯传输新为数据
        for (TPrivacyActionNougat stackDetail : stackDetails) {
            String detailsData = stackDetail.getDetailsData();
            // 无抓包数据视为无效数据
            if (StringUtils.isBlank(detailsData)) {
                continue;
            }
            if (!CommonUtil.isEmptyDetails(captureInfoBO.getUrl()) && StringUtils.contains(detailsData, captureInfoBO.getUrl())) {
                // 获取解析后的完整传输数据
                List<TPrivacySensitiveWord> groupCapture = matchCaptureInfoData(Collections.singletonList(captureInfoBO), stackDetail, sensitiveWords);
                if (CollectionUtils.isNotEmpty(groupCapture)) {
                    privacySensitiveWords.addAll(groupCapture);
                }
            }
        }
        return privacySensitiveWords;
    }

    /**
     * 传输个人信息数据过滤【capture_info】
     * <p>网络抓包数据敏感词</p>
     *
     * @param detailsData 抓包数据
     * @return 过滤器
     */
    public Predicate<CaptureInfoBO> buildCaptureInfoFilter(String detailsData) {
        Predicate<CaptureInfoBO> filter = (captureInfo) -> {
            // 应产品要求，使用包含关系
            String compare = captureInfo.getUrl();
            if (StringUtils.isBlank(compare)) {
                return false;
            }
            // JZ.DO：优化部分
            return StringUtils.contains(detailsData, compare);
        };
        return filter;
    }
    
    public List<TPrivacySensitiveWord> getPrivacySensitiveWordResult(List<TPrivacyActionNougat> stackDetailList, List<TSensitiveWord> sensitiveWords, Map<String,String> sensitiveWordMap) {
    	if (stackDetailList == null || stackDetailList.size()==0) {
    		logger.info("CaptureInfoAction - 传输个人信息数据初步解析，无数据");
    		return new ArrayList<>();
    	}
    	List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
    	for (TPrivacyActionNougat stackDetail : stackDetailList) {
    		
    		if(StringUtils.isBlank(stackDetail.getDetailsData())) {
    			continue;
    		}
    		String detailsData = stackDetail.getDetailsData();
			CaptureInfoBO  captureInfo = anysiCaptureInfoData(detailsData, stackDetail);
			String regexContent = captureInfo.getParams();
			if(StringUtils.isBlank(regexContent)) {
    			continue;
    		}
    		for (TSensitiveWord sensitiveWord : sensitiveWords) {
        		try {
    				// 无名称，无正则视为无效
    				if (StringUtils.isNotBlank(sensitiveWord.getRegex()) || StringUtils.isNotBlank(sensitiveWord.getName())) {
    					String key = stackDetail.getExecutor()+stackDetail.getPackageName()+stackDetail.getDetailsData()+ sensitiveWord.getSensitiveWords();
    					if(sensitiveWordMap.get(key) == null) {
    						
    						String regex = StringUtils.isBlank(sensitiveWord.getRegex())? enhancedWords(sensitiveWord.getSensitiveWords()) : sensitiveWord.getRegex();
    						
    						if(sensitiveWord.getTypeId() != null && sensitiveWord.getTypeId() == 11) {
    							regexContent = captureInfo.getOriginal();
    						}
    						
    						Pattern pattern = Pattern.compile(regex);
    				        Matcher matcher = pattern.matcher(regexContent);
    				        // 获取正则匹配到的内容
    				        while (matcher.find()) {
    				        	
    				            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, regexContent, sensitiveWord, true);
    				            if (isInvalidInfo) {
    				                continue;
    				            }

    				            String code = SensitiveWordsHelper.getMatchString(matcher, regexContent);
    				            String sensitiveWordMatcher = matcher.group();
    				            sensitiveWordMap.put(key, key);
    				            
    				            // 组装实体
    				            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(stackDetail, captureInfo, sensitiveWord,
                                        code, captureInfo.getMethod(), sensitiveWordMatcher, false);
    				            // 存放结果
    				            sensitiveWordList.add(bean);
    				        }
    					}
    				}
    			} catch (Exception e) {
    				e.getMessage();
    			}
        	}
    	}
    	return sensitiveWordList;
    }
    
    //根据详细信息解析数据
    private CaptureInfoBO anysiCaptureInfoData(String detailsData, TPrivacyActionNougat stackDetail){
    	if(!detailsData.startsWith("http")) {
    		return null;
    	}
        CaptureInfoBO captureInfo = new CaptureInfoBO();
		try {
            String str = detailsData;
            URI u = new URI(detailsData);
            String port = u.getPort() == -1 ? "80" : String.valueOf(u.getPort());
			if(u.getPort()==-1 && str.startsWith("https")){
				port = "443";
			}
			
			captureInfo.setDetailsData(detailsData);
			captureInfo.setDataType("3"); //HTTP 
            captureInfo.setExecutor(stackDetail.getExecutor()); //主体
            captureInfo.setExecutorType(stackDetail.getExecutorType());
            captureInfo.setHost(u.getHost());
            captureInfo.setIp(cn.ijiami.detection.utils.IpUtil.hostByIp(u.getHost()));
            captureInfo.setMethod("--");
            captureInfo.setOriginal(detailsData);
            captureInfo.setPackageName(stackDetail.getPackageName());
            captureInfo.setParams(u.getQuery());
            captureInfo.setPath(u.getPath());
            captureInfo.setPort(port);
            captureInfo.setProtocol(u.getScheme());
            captureInfo.setResponse("");
            captureInfo.setSdkIds(stackDetail.getSdkIds());
            captureInfo.setStackInfo(stackDetail.getStackInfo());
            captureInfo.setTimeStamp(stackDetail.getActionTimeStamp() == null ? null : String.valueOf(stackDetail.getActionTimeStamp()));
            captureInfo.setType(str.startsWith("https") ? "https" : "http");
            captureInfo.setUrl(detailsData);
            captureInfo.setOriginal(detailsData);
        } catch (Exception e) {
			e.getMessage();
		}
		return captureInfo;
    }

    /**
     * 匹配满足抓包数据的传输个人信息行为的数据
     *
     * @param captureInfos   从capture_info中解析出的数据且对应此条堆栈信息
     * @param stackDetail    对应的堆栈信息
     * @param sensitiveWords 关键字集合
     * @return 传输个人信息的行为数据
     */
    public List<TPrivacySensitiveWord> matchCaptureInfoData(List<CaptureInfoBO> captureInfos, TPrivacyActionNougat stackDetail,
                                                             List<TSensitiveWord> sensitiveWords) {
        // 用来存放目标数据
        List<TPrivacySensitiveWord> targetSensitiveWordInfos = new ArrayList<>();
        for (CaptureInfoBO captureInfo : captureInfos) {
            String original = captureInfo.getOriginal();
            // 不符合要求的数据，不包含host视作无效数据
            if (StringUtils.isBlank(original) || !original.contains("Host: ")) {
                continue;
            }
            // 匹配敏感词 再入库
            for (TSensitiveWord sensitive : sensitiveWords) {
                // 无名称，无正则视为无效
                if (StringUtils.isBlank(sensitive.getRegex()) && StringUtils.isBlank(sensitive.getName())) {
                    continue;
                }
                List<TPrivacySensitiveWord> sensitiveWordList;
                // 存在正则匹配正则，否则匹配关键字
                if (StringUtils.isNotBlank(sensitive.getRegex())) {
                    sensitiveWordList = matchCaptureInfoByRegex(stackDetail, captureInfo, sensitive);
                } else {
                    sensitiveWordList = matchCaptureInfoByWord(stackDetail, captureInfo, sensitive);
                }
                // 存在匹配到的数据
                if (CollectionUtils.isNotEmpty(sensitiveWordList)) {
                    targetSensitiveWordInfos.addAll(sensitiveWordList);
                }
            }
        }
        return targetSensitiveWordInfos;
    }

    /**
     * 关键词匹配传输个人信息数据
     *
     * @param stackDetail 堆栈信息
     * @param captureInfo 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByWord(TPrivacyActionNougat stackDetail, CaptureInfoBO captureInfo, TSensitiveWord sensitive) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
//        if (StringUtils.isNotBlank(captureInfo.getParams())) {
        if (StringUtils.isNotBlank(captureInfo.getOriginal())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(stackDetail, captureInfo, sensitive, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getCookie())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(stackDetail, captureInfo, sensitive, true));
        }
        return sensitiveWordList;
    }

    private List<TPrivacySensitiveWord> matchByWordFromCookieOrParams(TPrivacyActionNougat stackDetail, CaptureInfoBO captureInfo, TSensitiveWord sensitive,
                                                                      boolean cookie) {
        String sensitiveWord = sensitive.getSensitiveWords();
        if (StringUtils.isBlank(sensitiveWord)) {
            return new ArrayList<>();
        }
        String content = captureInfo.getParams();
        boolean isSpec = false;
        if(sensitive.getTypeId() != null && sensitive.getTypeId()==11) {
        	content = captureInfo.getOriginal();
        	isSpec = true;
        }
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
//        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(cookie ? captureInfo.getCookie() : captureInfo.getParams(),
        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(cookie ? captureInfo.getCookie() : content,
                StringUtils.EMPTY, sensitive.getSensitiveWords(), isSpec)) {
            sensitiveWordList.add(buildCaptureInfoToSensitiveWord(stackDetail, captureInfo, sensitive, word.getCode(), captureInfo.getMethod(), word.getWord(), cookie));
        }
        return sensitiveWordList;
    }

    /**
     * 从capture info中组装敏感词
     *
     * @param stackDetail  堆栈信息
     * @param captureInfo  网络抓包数据
     * @param sensitive    关键词
     * @param code 标记源
     * @param method       方法类型
     * @param keyword      抓取关键词
     * @return
     */
    private TPrivacySensitiveWord buildCaptureInfoToSensitiveWord(TPrivacyActionNougat stackDetail, CaptureInfoBO captureInfo, TSensitiveWord sensitive,
                                                                  String code, String method, String keyword, boolean cookie) {
        //v2.5版本只要是string类型为空则按照--入库
        // 组装实体
        TPrivacySensitiveWord bean = new TPrivacySensitiveWord();
        // 存放堆栈数据
        bean.setTaskId(stackDetail.getTaskId());
        bean.setExecutorType(stackDetail.getExecutorType());
        bean.setExecutor(stackDetail.getExecutor());
        bean.setStackInfo(StringUtils.isEmpty(stackDetail.getStackInfo())? PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(stackDetail.getStackInfo()));
        bean.setDetailsData(optDetails(CommonUtil.filterPrintString(captureInfo.getDetailsData())));
        bean.setResponseData(optDetails(CommonUtil.filterPrintString(captureInfo.getResponse())));
        bean.setBehaviorStage(stackDetail.getBehaviorStage());
        bean.setActionTime(stackDetail.getActionTime());
        bean.setSdkName(stackDetail.getExecutor());
        // 存放系解析出的json数据
        bean.setSdkIds(stackDetail.getSdkIds());
        // 存放敏感词数据
        bean.setTypeId(sensitive.getTypeId());
        bean.setName(StringUtils.isEmpty(sensitive.getName())?PinfoConstant.DETAILS_EMPTY:sensitive.getName());
        bean.setSensitiveWord(StringUtils.isEmpty(keyword)?PinfoConstant.DETAILS_EMPTY:keyword);
        bean.setRiskLevel(sensitive.getRiskLevel());
        bean.setSuggestion(sensitive.getSuggestion());
        // 存放解析出的数据
        bean.setMethod(StringUtils.isEmpty(method)?PinfoConstant.DETAILS_EMPTY:method);
        bean.setCookie(StringUtils.isEmpty(captureInfo.getCookie())?PinfoConstant.DETAILS_EMPTY:captureInfo.getCookie());
        //增加cookie标识以便前端进行cookie标签展示
        bean.setCookieMark(StringUtils.isNotBlank(captureInfo.getCookie())?1:null);
        bean.setCookieWord(cookie);
        bean.setUrl(StringUtils.isEmpty(captureInfo.getUrl())?PinfoConstant.DETAILS_EMPTY:captureInfo.getUrl());
        bean.setHost(StringUtils.isEmpty(captureInfo.getHost())?PinfoConstant.DETAILS_EMPTY:captureInfo.getHost());
        bean.setAddress(StringUtils.isEmpty(captureInfo.getPath())?PinfoConstant.DETAILS_EMPTY:captureInfo.getPath());
        bean.setCode(StringUtils.isEmpty(code) ? PinfoConstant.DETAILS_EMPTY : code);
        bean.setIp(StringUtils.isEmpty(captureInfo.getIp())?PinfoConstant.DETAILS_EMPTY:captureInfo.getIp());
        bean.setPackageName(stackDetail.getPackageName());
        String address = "";
        // 避免端口获取不到具体地址信息
        if(StringUtils.isNoneBlank(captureInfo.getIp())) {
        	address = ipUtil.getAddress(captureInfo.getIp());
        }else {
        	if (StringUtils.contains(captureInfo.getHost(), ":")) {
                address = ipUtil.getAddress(captureInfo.getHost().split(":")[0]);
            } else {
                address = ipUtil.getAddress(captureInfo.getHost());
            }
        }
        
        bean.setAttributively(StringUtils.isEmpty(address)?PinfoConstant.DETAILS_EMPTY:address);
        bean.setPort(StringUtils.isEmpty(captureInfo.getPort())?PinfoConstant.DETAILS_EMPTY:captureInfo.getPort());
        bean.setProtocol(StringUtils.isEmpty(captureInfo.getType())?PinfoConstant.DETAILS_EMPTY:captureInfo.getType());
        bean.setPlaintextTransmission(isPlaintextTransmission(sensitive).value);
        return bean;
    }

    /**
     * 正则匹配传输个人信息数据
     *
     * @param stackDetail 堆栈信息
     * @param captureInfo 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByRegex(TPrivacyActionNougat stackDetail, CaptureInfoBO captureInfo, TSensitiveWord sensitive) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getParams())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(stackDetail, captureInfo, sensitive, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getCookie())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(stackDetail, captureInfo, sensitive, true));
        }
        return sensitiveWordList;
    }

    private List<TPrivacySensitiveWord> matchByRegexFromCookieOrParams(TPrivacyActionNougat stackDetail, CaptureInfoBO captureInfo, TSensitiveWord sensitive,
                                                                       boolean cookie) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        String from;
        if (cookie) {
            from = captureInfo.getCookie();
        } else {
        	if(sensitive.getTypeId() != null && sensitive.getTypeId() == 11) {
        		from = captureInfo.getOriginal();
        	} else {
        		from = captureInfo.getParams();
        	}
        }
        Pattern pattern = Pattern.compile(sensitive.getRegex());
        Matcher matcher = pattern.matcher(from);
        // 获取正则匹配到的内容
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, from, sensitive, true);
            if (isInvalidInfo) {
                continue;
            }
            String code = SensitiveWordsHelper.getMatchString(matcher, from);
            captureInfo.setParams(code);
            String sensitiveWord = matcher.group();
            String method = captureInfo.getMethod();
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(stackDetail, captureInfo, sensitive, code, method, sensitiveWord, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }
    
    private String stringToImage(String request) {
        String fileKey = null;
        Text2Pic text2Pic = new Text2Pic(request);
        text2Pic.SetPicType(Text2Pic.PictureType.TP_JPG);
        File file = new File(commonProperties.getFilePath() + File.separator + "default" + File.separator + UUID.randomUUID() + ".jpg");
        text2Pic.buildImage(file);
        if (file.exists()) {
            try {
                FileVO fileVO = new FileVO();
                fileVO.setFileName(file.getName());
                fileVO.setFileKey(UuidUtil.uuid());
                fileVO.setInputStream(new FileInputStream(file));
                fileVO.setFileExtName(file.getName().substring(file.getName().lastIndexOf(".")));
                fileVO.setFilePath(file.getAbsolutePath());
                fileService.uploadFile(fileVO);
                fileKey = fileVO.getFileKey();
            } catch (Exception e) {
                fileKey = null;
            } finally {
                FileUtil.deleteFile(file);
            }
        }
        return fileKey;
    }
}
