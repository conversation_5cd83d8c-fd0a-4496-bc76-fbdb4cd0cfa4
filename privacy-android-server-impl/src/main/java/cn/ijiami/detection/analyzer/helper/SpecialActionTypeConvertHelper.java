package cn.ijiami.detection.analyzer.helper;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 行为数据转换
 * 针对行为20007、20008在行为日志输出数据中增加类型数据，类型对应的名称可参考以下数据
 * <AUTHOR>
 *
 */
public class SpecialActionTypeConvertHelper {
	
	public static final List<Long> ACTIONIDS = Arrays.asList(20001L,20002L,20003L,20004L,20005L,20006L,20007L,20008L);
	
	//过滤部分创建系统文件的行为
	public static final List<Long> WRITE_EXTERNAL_STORAGE_ACTIONIDS = Arrays.asList(1000000L,1000001L,1000002L);
	
	//过滤创建文件过滤
	public static final List<Long> FILTER_WRITE_CREATE_FILES_ACTIONIDS = Arrays.asList(17001L,17002L,17003L,17004L,17005L,17006L);
	
	//过滤系统目录
	public static final String WRITE_EXTERNAL_STORAGE_ACTIONIDS_REGEX = "(?i)android/media|(?i)android/data|(?i)android/obb|(?i)android/files|(?i)android/cache";
	//过滤沙箱系统预装包名
	public static final String WRITE_EXTERNAL_SYSTEM_APP_ACTIONIDS_REGEX = "jp.co.cyberagent.stf|io.appium.settings|com.example.testxposedactivity|io.appium.uiautomator2.server|io.appium.uiautomator2.server.test";

	//webview过滤
	//2023-11-23 增加过滤表情框架 org.chromium|
	public static final String FILTERPACKAGE = "org.chromium|android.webkit.WebView|com.google.android.gms|chromium-SystemWebView|androidx.emoji2.text|com.google.android";
	public static final String FILTERPACKAGE_DEMO1 = "com.google.android.gms|androidx.emoji2.text|com.google.android";
	
	//过滤多媒体发生变化误报数据 14017和14018中有针对识别uri为media/extemal/images/media/video/audio
	public static final List<Long> ACTIONID_14017_14018 = Arrays.asList(14017L, 14018L);
	public static final String FILTER_DETAIL_14017_14018_REGEX = "media|external|video|audio";
	
	//5.1-2025.3.5获取IP过滤socket
	public static final List<Long> ACTIONID_IP = Arrays.asList(24008L);
	public static final String FILTER_DETAIL_IP_REGEX = "java.net.SocksSocketImpl.connect";
	
	
	public static final String FILTER_31001_DETAIL = "(?i)getprop";
	
	public static final List<Long> ACTIONID_22002 = Arrays.asList(22002L);
	//安装列表
	public static final List<Long> ACTIONID_28006 = Arrays.asList(28006L);
	public static final List<Long> ACTIONID_28005 = Arrays.asList(28005L);
	//获取设备硬件序列号(过滤 runtime-->getprop)
	public static final List<Long> ACTIONID_31001 = Arrays.asList(31001L);
	
	//过滤运行中的进程
	public static final String FILTERACTIONSTACKINFO1 = "getRunningAppProcesses";
	public static final String FILTERACTIONSTACKINFO2 = "getRunningTasks";
	
	//动态注册广播监控
    public static final String DYNAMICREGISTRATIONBROADCAST = "android.intent.action.PACKAGE_ADDED|android.intent.action.PACKAGE_REMOVED";
    public static final List<Long> ACTIONID_32002 = Arrays.asList(32002L,32003L);
    
    //过滤安装列表-weibingtie.20240822
	public static final String FILTERACTIONSTACKINFO_28005 = "android.app.ApplicationPackageManager.getInstallerPackageName";


    //过滤ICCID
    public static final List<Long> ACTIONID_10004 = Arrays.asList(10004L);
    public static final String DYNAMICICCIDSTACKINFO = "android.database.CursorWrapper.getString";
    // 正则表达式，匹配ICCID或手机号
	public static final String ICCID_PHONE_NUMBER_PATTERN_10004 = "^(\\d{19,20})$|^1[3456789]\\d{9}$";

    public static final List<Long> ACTIONID_XIAOMI = Arrays.asList(51001L,51002L,51003L,51004L,51005L,51006L,51007L,51008L,51009L,51010L,51011L,51012L,51013L,51014L,51015L,51016L,51017L,51018L,51019L,51020L,51021L,51022L,51023L,51024L,51025L,51026L,51027L,51028L,51029L,51030L,51031L,51032L,51033L,50009L);

	public static final JSONObject ACTIONTYPE =JSONObject.parseObject(""
			+ "{\"-1\":\"android.sensor.all=获取了传感器列表\","
			+ "\"1\":\"android.sensor.accelerometer=加速度传感器\","
			+ "\"2\":\"android.sensor.magnetic_field=磁场传感器\","
			+ "\"3\":\"android.sensor.orientation=方向传感器\","
			+ "\"4\":\"android.sensor.gyroscope=陀螺仪传感器\","
			+ "\"5\":\"android.sensor.light=光传感器\","
			+ "\"6\":\"android.sensor.pressure=压力传感器\","
			+ "\"7\":\"android.sensor.temperature=温度传感器\","
			+ "\"8\":\"android.sensor.proximity=距离传感器\","
			+ "\"9\":\"android.sensor.gravity=重力传感器\","
			+ "\"10\":\"android.sensor.linear_acceleration=线性加速传感器\","
			+ "\"11\":\"android.sensor.rotation_vector=旋转矢量传感器\","
			+ "\"12\":\"android.sensor.relative_humidity=相对湿度传感器\","
			+ "\"13\":\"android.sensor.ambient_temperature=温度传感器\","
			+ "\"14\":\"android.sensor.magnetic_field_uncalibrated=磁场未校准传感器\","
			+ "\"15\":\"android.sensor.game_rotation_vector=游戏矢量旋转传感器\","
			+ "\"16\":\"android.sensor.gyroscope_uncalibrated=陀螺仪未校准传感器\","
			+ "\"17\":\"android.sensor.significant_motion=重要运动传感器\","
			+ "\"18\":\"android.sensor.step_detector=步进探测器传感器\","
			+ "\"19\":\"android.sensor.step_counter=步数传感器\","
			+ "\"20\":\"android.sensor.geomagnetic_rotation_vector=地磁旋转矢量传感器\","
			+ "\"21\":\"android.sensor.heart_rate=心跳速度传感器	\","
			+ "\"22\":\"android.sensor.tilt_detector=倾斜检测传感器\","
			+ "\"23\":\"android.sensor.wake_gesture=唤醒手势传感器\","
			+ "\"24\":\"android.sensor.glance_gesture=掠过手势传感器\","
			+ "\"25\":\"android.sensor.pick_up_gesture=拾起手势传感器\","
			+ "\"26\":\"android.sensor.wrist_tilt_gesture=手腕倾斜手势传感器\","
			+ "\"27\":\"android.sensor.device_orientation=设备方向传感器\","
			+ "\"28\":\"android.sensor.pose_6dof=姿势传感器\","
			+ "\"29\":\"android.sensor.stationary_detect=驻留检测传感器\","
			+ "\"30\":\"android.sensor.motion_detect=运动检测传感器\","
			+ "\"31\":\"android.sensor.heart_beat=心跳传感器\","
			+ "\"32\":\"android.sensor.dynamic_sensor_meta=传感器动态元\","
			+ "\"33\":\"android.sensor.additional_info=传感器附加信息\","
			+ "\"34\":\"android.sensor.low_latency_offbody_detect=低潜伏离体传感器\","
			+ "\"65536\":\"android.sensor.device_private_base=设备私有基础\","
			+ "\"35\":\"android.sensor.accelerometer_uncalibrated=加速度计未校准\"}");
	
	
	public boolean isSpecialActionId(Long actionId){
		return ACTIONIDS.contains(actionId);
	}
	
	public static String actionTypeConvert(String detailsData){
		if(StringUtils.isBlank(ACTIONTYPE.getString(detailsData))){
			return detailsData;
		}
		return ACTIONTYPE.getString(detailsData).split("=")[1];
	}
	
	 public static boolean isMatcherContent(String content, String regex) {
        try {
			if (StringUtils.isBlank(regex)) {
			    return true;
			}
			return Pattern.compile(regex, Pattern.CASE_INSENSITIVE)
			        .matcher(content).find();
		} catch (Exception e) {
			e.getMessage();
			return false;
		}
    }
	 
	public static boolean writeExternalStorageRegex(String content){
		if(StringUtils.isBlank(content)) {
			return false;
		}
		String regex[] = WRITE_EXTERNAL_STORAGE_ACTIONIDS_REGEX.split("\\|");
		for (String string : regex) {
			if(content.toLowerCase().endsWith(string)) {
				return true;
			}
		}
		return false;
	}
	
//	public static void main(String[] args) {
//		System.out.println(actionTypeConvert("-1"));
//
//		Long t = 20007L;
//		System.out.println(actionIds.contains(t));
//
//		String content = "/storage/emulated/0/Android/data/com.snssdk.api.embed/cache";
//		System.out.println(writeExternalStorageRegex(content));
//	}

	    //匹配ICCID或手机号
	    private static final String ICCID_PHONE_NUMBER_PATTERN = "^(\\d{19})$|^1[3456789]\\d{9}$";

	    public static boolean isIccidOrPhoneNumber(String str) {
	        return Pattern.matches(ICCID_PHONE_NUMBER_PATTERN, str);
	    }

	    public static void main(String[] args) {
	        String iccid1 = "1234567890123456789";  // 符合ICCID格式
	        String phoneNumber1 = "13912345678";   // 符合手机号格式
	        String invalidString = "abc123";        // 不符合任何格式

	        System.out.println(iccid1 + " 符合ICCID格式: " + isMatcherContent(iccid1,ICCID_PHONE_NUMBER_PATTERN_10004));
	        System.out.println(phoneNumber1 + " 符合手机号格式: " + isMatcherContent(phoneNumber1,ICCID_PHONE_NUMBER_PATTERN_10004));
	        System.out.println(invalidString + " 不符合任何格式: " + isMatcherContent(invalidString,ICCID_PHONE_NUMBER_PATTERN_10004));
	    }

}
