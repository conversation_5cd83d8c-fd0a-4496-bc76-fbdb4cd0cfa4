package cn.ijiami.detection.analyzer.json.processor;

import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.utils.CommonUtil;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EnumJsonValueProcessor.java
 * @Description JSON的枚举处理类
 * @createTime 2022年01月05日 16:35:00
 */
public class EnumJsonValueProcessor implements JsonValueProcessor {

    @Override
    public Object processArrayValue(Object o, JsonConfig jsonConfig) {
        return processValue(o);
    }

    @Override
    public Object processObjectValue(String s, Object o, JsonConfig jsonConfig) {
        return processValue(o);
    }

    private Object processValue(Object o) {
        if (o instanceof PrivacyStatusEnum) {
            return CommonUtil.beanToJson(o);
        }
        return Objects.isNull(o) ? null : o.toString();
    }

}
