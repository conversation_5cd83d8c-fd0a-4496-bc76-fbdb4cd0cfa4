package cn.ijiami.detection.analyzer.json.config;

import cn.ijiami.detection.analyzer.json.processor.EnumJsonValueProcessor;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import net.sf.json.JsonConfig;
import net.sf.json.util.CycleDetectionStrategy;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionJsonConfig.java
 * @Description JSON的默认配置
 * @createTime 2022年01月05日 16:37:00
 */
public class JsonConfigFactory {

    public static JsonConfig buildDefault() {
        JsonConfig config = new JsonConfig();
        config.setCycleDetectionStrategy(CycleDetectionStrategy.LENIENT);
        config.registerJsonValueProcessor(PrivacyStatusEnum.class, new EnumJsonValueProcessor());
        return config;
    }

}
