package cn.ijiami.detection.analyzer.exception;

/**
 * 解析异常枚举
 *
 * <AUTHOR>
 * @date 2020/10/12 10:09
 **/
public enum ParserExceptionEnum implements IParserExceptionEnum {
    /**
     * 堆栈数据解析
     */
    BEHAVIOR_INFO(10001, "堆栈数据解析异常"),
    /**
     * 传输
     */
    CAPTURE_INFO(10002, "传输个人信息数据解析异常"),
    /**
     * 通讯
     */
    HOST_IP(10003, "通讯行为数据解析异常"),
    /**
     * 存储
     */
    SHARED_PREFS(10004, "存储个人信息数据解析异常"),
    /**
     * 164号文日志记录
     */
    LOG_164(10005, "164号文相关判断日志数据解析异常");

    private Integer code;

    private String message;

    ParserExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
