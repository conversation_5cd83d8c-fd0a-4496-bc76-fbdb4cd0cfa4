package cn.ijiami.detection.analyzer.parser;

import cn.hutool.core.net.URLDecoder;
import cn.ijiami.detection.VO.detection.privacy.AlipayAppletAction;
import cn.ijiami.detection.VO.detection.privacy.WechatAppletAction;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.utils.CommonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static cn.ijiami.detection.utils.CommonUtil.readeFile;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletBehaviorInfoParser.java
 * @Description 小程序行为文件解析
 * @createTime 2023年04月19日 18:31:00
 */
@Slf4j
public class AlipayAppletBehaviorInfoParser extends AbstractAppletBehaviorInfoParser {

    public static AlipayAppletAction parserLogStr(String logText) {
        if (StringUtils.startsWith(logText, "%7B")) {
            logText = URLDecoder.decode(logText, StandardCharsets.UTF_8);
        }
        return CommonUtil.jsonToBean(logText, new TypeReference<AlipayAppletAction>() {});
    }

    @Override
    AppletActionBO parserData(String logText) {
        AlipayAppletAction action = parserLogStr(logText);
        return Objects.nonNull(action) ? action.getData() : null;
    }
}
