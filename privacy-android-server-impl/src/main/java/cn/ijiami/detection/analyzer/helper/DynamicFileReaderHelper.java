package cn.ijiami.detection.analyzer.helper;

import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 动态检测抓包数据读取工具
 *
 * <AUTHOR>
 * @date 2020-09-08 16:22
 */
public class DynamicFileReaderHelper {

    public static final int MAX_CONTENT_LENGTH = 5000;

    private DynamicFileReaderHelper() {
    }

    /**
     * 读取文件
     *
     * @param fileReader 文件内容处理函数
     * @param filePath   文件路径
     * @return
     */
    private static String readeFile(IFileReader fileReader, String filePath) {
        return readeFile(fileReader, new File(filePath));
    }

    /**
     * 读取文件
     *
     * @param fileReader 文件内容处理函数
     * @param file       文件内容
     * @return 解析后的字符串
     */
    private static String readeFile(IFileReader fileReader, File file) {
        StringBuilder stringbuilder = new StringBuilder();
        // 空或者是目录
        if (Objects.isNull(file) || file.isDirectory()) {
            return stringbuilder.toString();
        }
        // 处理文件流操作
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            stringbuilder = fileReader.fileReader(reader);
        } catch (IOException e) {
            e.getMessage();
        }
        return stringbuilder.toString();
    }

    /**
     * 读取文件内容
     *
     * @param filePath 文件绝对路径
     * @return
     */
    public static String readFileToString(String filePath) {
        String content = readeFile(reader -> {
            StringBuilder stringbuilder = new StringBuilder();
            String line;
            String contentType = "";
            String contentLength = "";
            while (null != (line = reader.readLine())) {
            	if (line.contains("Content-Type")) {
                    contentType = getHeaderValue(line);
                }
            	if (line.contains("Content-Length")) {
                    contentLength = getHeaderValue(line);
                }
                // 过滤下载包数据，流类型或者数据长度大于5000的判断为下载数据包
                if (contentType.equals("application/octet‑stream") || getContentLength(contentLength) > MAX_CONTENT_LENGTH) {
                    if (line.equals("\n")) {
                        break;
                    }
                    if (!isInvalidChars(line)) {
                        break;
                    }
                }
                stringbuilder.append(line).append("\n");
            }
            return stringbuilder;
        }, filePath);
        // 去掉16进制字符串
        return content.replaceAll("[^\\u0000-\\uFFFF]", "");
    }

    private static boolean isInvalidChars(String line) {
        if (StringUtils.isEmpty(line)) {
            return true;
        }
        char notInvalid = '�';
        int notInvalidCount = 0;
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            if (c == notInvalid) {
                notInvalidCount += 1;
            }
        }
        return notInvalidCount < line.length() * 0.25;
    }

    private static String getHeaderValue(String header) {
        String split = ": ";
        int startIndex = header.indexOf(split);
        if (startIndex > 0) {
            return header.substring(startIndex + split.length()).trim();
        }
        return "";
    }

    private static Long getContentLength(String contentLength) {
        try {
            return Long.parseLong(contentLength);
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 获取行为数据组
     *
     * @param actions 行为数据源
     * @return 分组后的行为数据(非null)
     */
    public static Map<String, List<ActionStackBO>> stackData(List<TPrivacyActionNougat> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return new HashMap<>(4);
        }
        // 类转换函数
        Function<TPrivacyActionNougat, ActionStackBO> format = t -> {
            ActionStackBO action = new ActionStackBO();
            action.setDetailsData(t.getDetailsData());
            action.setStackInfo(t.getStackInfo());
            action.setActionTime(t.getActionTime());
            action.setActionId(t.getActionId());
            return action;
        };
        // details对应的堆栈数据
        return actions.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getDetailsData, Collectors.mapping(format, Collectors.toList())));
    }

    /**
     * 获取详细行为数据组
     *
     * @param actions 行为数据源
     * @return 分组后的行为数据(非null)
     */
    public static Map<String, List<TPrivacyActionNougat>> stackDetailsData(List<TPrivacyActionNougat> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return new HashMap<>(4);
        }
        // details对应的堆栈数据
        return actions.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getDetailsData, Collectors.toList()));
    }

    /**
     * 提取完整的关键字
     *
     * @param keys   行为数据对应的详细数据
     * @param target 目标字符串
     * @return key or null
     */
    public static String extractKeywords(Set<String> keys, String target) {
        return extractKeywords(keys, target, null);
    }

    /**
     * 提取完整的关键字
     *
     * @param keys   行为数据对应的详细数据
     * @param target 目标字符串
     * @param expand 扩展
     * @return key or null
     */
    public static String extractKeywords(Set<String> keys, String target, String expand) {
        if (CollectionUtils.isEmpty(keys)) {
            return null;
        }

        List<String> targets;
        if (StringUtils.isEmpty(expand)) {
            targets = keys.stream().filter(str -> str.contains(target)).collect(Collectors.toList());
        } else {
            targets = keys.stream().filter(str -> str.contains(target) && str.contains(expand)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(targets)) {
            return null;
        }
        return targets.get(0);
    }
}
