package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import lombok.Data;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class CaptureHostIpInfoParser implements IDetectDataParser<CaptureInfoBO> {

    private static Logger logger = LoggerFactory.getLogger(CaptureHostIpInfoParser.class);

    private static final String MARK_MUST = "@ijiami_data_boundary\n";
    private static final String MARK_MUST_HOST = "Host: ";
    private static final String MARK_MUST_HOST_UPPER = "HOST: ";
    private static final String MARK_DATA_TYPE = "@ijiami_data_type:";
    private static final String MARK_ID = "@ijiami_id:";
    private static final String MARK_TIMESTAMP = "@ijiami_time:";
    private static final String MARK_COOKIE = "Cookie: ";
    private static final String MARK_STACK_START = "@jvm_stack_start\n";
    private static final String MARK_STACK_END = "\n@jvm_stack_end";
    private static final String MASK_IP = "@ijiami_ip:";
    private static final String MASK_TYPE = "@request_type:";
    private static final String MASK_HOST = "@host_name:";
    private static final String MARK_USER_AGENT = "User-Agent: ";
    private static final String MARK_CONNECTION = "Connection: ";
    private static final String MARK_ACCEPT_ENCODING = "Accept-Encoding: ";

    private static final String TYPE_HTTP_REQUEST = "1";
    private static final String TYPE_HTTPS_REQUEST = "2";
    private static final String TYPE_SSL_HANDSHAKES = "3";
    private static final String TYPE_TCP_CONNECT = "4";
    private static final String TYPE_TLS_HANDSHAKES = "5";

    private static final String DATA_TYPE_REQUEST = "1";
    private static final String DATA_TYPE_RESPONSE = "2";
    private static final String DATA_TYPE_HANDSHAKES = "3";

    /**
     * http2的头字段，记录目标url权限，相当于http1的Host头字段
     */
    private static final String MASK_AUTHORITY = ":authority:";

    /**
     * http2的头字段，不限于 "http" 和 "https" scheme 的 URI
     */
    private static final String MASK_SCHEME = ":scheme:";

    /**
     * http2的头字段，记录请求方法
     */
    private static final String MASK_METHOD = ":method:";

    /**
     * http2的请求头字段，目标 URI 的路径和查询部分。绝对路径和可选的 "?" 字符后的查询部分
     */
    private static final String MASK_PATH = ":path:";



    @Override
    public List<CaptureInfoBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 解析数据
     *
     * @param filePath 捕获信息数据存放的位置
     * @return list对象
     */
    private static List<CaptureInfoBO> analysis(String filePath) {
        File dirFile = new File(filePath);
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            return new ArrayList<>();
        }
        File[] files = dirFile.listFiles();
        if (Objects.isNull(files)) {
            return new ArrayList<>();
        }
        Map<String, HttpInfo> httpInfoMap = new ConcurrentHashMap<>();
        List<CaptureInfoBO> result = new ArrayList<>();
        List<CaptureInfoBO> allList = Arrays.stream(files)
                .parallel()
                .flatMap(captureFile -> Arrays
                        .stream(DynamicFileReaderHelper.readFileToString(captureFile.getAbsolutePath()).split(MARK_MUST))
                        .parallel()
                )
                .map(request -> RegExUtils.replaceAll(request, "(?m)^\\s*$(\\n|\\r\\n)", "").trim())
                .filter(StringUtils::isNotBlank)
                .map(request -> {
                    try {
                        return build(request);
                    } catch (ParserException e) {
                        e.getMessage();
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        for (CaptureInfoBO captureInfoBO : allList) {
            // 时间戳不为空或者非握手数据
            if (StringUtils.equalsAny(captureInfoBO.getType(), TYPE_HTTP_REQUEST, TYPE_HTTPS_REQUEST)
                    && StringUtils.isNotBlank(captureInfoBO.getTimeStamp())) {
                HttpInfo info = httpInfoMap.get(captureInfoBO.getId());
                if (info == null) {
                    info = new HttpInfo();
                    httpInfoMap.put(captureInfoBO.getId(), info);
                }
                if (DATA_TYPE_REQUEST.equals(captureInfoBO.getDataType())) {
                    info.setRequest(captureInfoBO);
                } else if (DATA_TYPE_RESPONSE.equals(captureInfoBO.getDataType())) {
                    info.setResponse(captureInfoBO);
                }
            } else if (StringUtils.equalsAny(captureInfoBO.getType(), TYPE_TCP_CONNECT)) {
                result.add(captureInfoBO);
            }
        }
        for (HttpInfo info : httpInfoMap.values()) {
            if (Objects.nonNull(info.getRequest())) {
                CaptureInfoBO request = info.getRequest();
                if (Objects.nonNull(info.getResponse())) {
                    request.setResponse(info.getResponse().getDetailsData());
                }
                result.add(request);
            }
        }
        return result;
    }

    @Data
    static class HttpInfo {
        public CaptureInfoBO request;
        public CaptureInfoBO response;
    }

    /**
     * 构建 捕获信息数据实体
     *
     * @param root
     * @return
     */
    protected static CaptureInfoBO build(String root) throws ParserException {
        CaptureInfoBO info = new CaptureInfoBO();
        try {
            // 保留一份原始数据
            info.setOriginal(root);

            if (StringUtils.contains(root, MARK_STACK_START) && StringUtils.contains(root, MARK_STACK_END)
                    && root.indexOf(MARK_STACK_END) > ((root.indexOf(MARK_STACK_START) + MARK_STACK_START.length()))) {
                info.setStackInfo(root.substring(root.indexOf(MARK_STACK_START) + MARK_STACK_START.length(), root.indexOf(MARK_STACK_END)));
            }
            String[] lines = root.split("\n");
            for (String line : lines) {
                if (line.contains(MARK_TIMESTAMP)) {
                    info.setTimeStamp(CaptureInfoParser.extractFromMark(line, MARK_TIMESTAMP));
                    continue;
                }
                if (line.contains(MARK_MUST_HOST)) {
                    info.setHost(CaptureInfoParser.extractFromMark(line, MARK_MUST_HOST));
                    continue;
                }
                if (line.contains(MASK_AUTHORITY)) {
                    info.setHost(CaptureInfoParser.extractFromMark(line, MASK_AUTHORITY));
                    continue;
                }
                if (line.contains(MARK_MUST_HOST_UPPER)) {
                    info.setHost(CaptureInfoParser.extractFromMark(line, MARK_MUST_HOST_UPPER));
                    continue;
                }
                if (line.contains(MASK_IP)) {
                    String ip = CaptureInfoParser.extractFromMark(line, MASK_IP);
                    String port = "";
                    // 兼容ip返回带端口数据
                    if (StringUtils.contains(ip, ":")) {
                        String[] infoArray = ip.split(":");
                        ip = infoArray[0].trim();
                        port = infoArray[1].trim();
                    }
                    info.setIp(ip);
                    info.setPort(port);
                    continue;
                }
                if (line.contains(MARK_COOKIE)) {
                    info.setCookie(CaptureInfoParser.extractFromMark(line, MARK_COOKIE));
                    continue;
                }

                if (line.contains(MARK_USER_AGENT)) {
                    info.setUserAgent(CaptureInfoParser.extractFromMark(line, MARK_USER_AGENT));
                    continue;
                }

                if (line.contains(MARK_CONNECTION)) {
                    info.setConnection(CaptureInfoParser.extractFromMark(line, MARK_CONNECTION));
                    continue;
                }

                if (line.contains(MARK_ACCEPT_ENCODING)) {
                    info.setAcceptEncoding(CaptureInfoParser.extractFromMark(line, MARK_ACCEPT_ENCODING));
                    continue;
                }

                if (line.contains(MASK_TYPE)) {
                    info.setType(CaptureInfoParser.extractFromMark(line, MASK_TYPE));
                    continue;
                }

                if (line.contains(MASK_HOST)) {
                    info.setHost(CaptureInfoParser.extractFromMark(line, MASK_HOST));
                    continue;
                }
                if (line.contains(MARK_ID)) {
                    info.setId(CaptureInfoParser.extractFromMark(line, MARK_ID));
                    continue;
                }
                if (line.contains(MARK_DATA_TYPE)) {
                    info.setDataType(CaptureInfoParser.extractFromMark(line, MARK_DATA_TYPE));
                    continue;
                }
                if (line.contains(MASK_METHOD)) {
                    info.setMethod(CaptureInfoParser.extractFromMark(line, MASK_METHOD));
                    continue;
                }
                if (line.contains(MASK_PATH)) {
                    info.setPath(CaptureInfoParser.extractFromMark(line, MASK_PATH));
                    continue;
                }
                // 判断是否是请求方法部分
                if (CaptureInfoParser.isContainHttpMethod(line)) {
                    String[] headers = line.split(" ");
                    info.setMethod(headers[0]);
                    info.setPath(headers[1]);
                    if (line.lastIndexOf(" ") + 1 == line.length()) {
                        continue;
                    }
                }
                if (!line.equals("0")) {
                    info.setParams(line);
                }
            }
            // http请求
            if (StringUtils.equals(TYPE_HTTP_REQUEST, info.getType())) {
                info.setUrl("http://" + (StringUtils.isBlank(info.getHost()) ? info.getIp() : info.getHost()) + info.getPath());
                info.setProtocol("HTTP");
            } else if (StringUtils.equals(TYPE_HTTPS_REQUEST, info.getType())) {
                // https请求
                info.setUrl("https://" + info.getHost());
                info.setPath(info.getPath());
                info.setProtocol("HTTPS");
            } else if (StringUtils.equals(TYPE_TCP_CONNECT, info.getType())) {
                // TCP连接
                info.setUrl("tcp://" + (StringUtils.isBlank(info.getHost()) ? info.getIp() : info.getHost()));
                info.setProtocol("TCP");
            }

            // 截取抓包数据
            String detailsData = "";
            if (StringUtils.contains(root, MASK_HOST)) {
                detailsData = root.substring(root.indexOf(MASK_HOST));
                if (detailsData.contains("\n")) {
                    detailsData = detailsData.substring(detailsData.indexOf("\n") + 1);
                } else {
                    detailsData = "";
                }
            } else if (StringUtils.contains(root, MASK_TYPE)) {
                detailsData = root.substring(root.indexOf(MASK_TYPE));
                if (detailsData.contains("\n")) {
                    detailsData = detailsData.substring(detailsData.indexOf("\n") + 1);
                } else {
                    detailsData = "";
                }
            }
            info.setDetailsData(detailsData);
        } catch (Exception e) {
            logger.error("CaptureHostIpInfoParser fail error", e);
            throw new ParserException(ParserExceptionEnum.CAPTURE_INFO);
        }
        return info;
    }

    public static void main(String args[]) {
        List<CaptureInfoBO> list = analysis("D:\\test\\capture_info_03");
        System.out.println("xxxx:" + list.size());
    }
}
