package cn.ijiami.detection.analyzer;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.analyzer.bo.SharedPrefBO;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.analyzer.parser.SharedPrefParser;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.utils.Text2Pic;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * capture info数据处理
 *
 * <AUTHOR>
 * @date 2020-09-21 10:25
 */
@Component
public class SharedPrefAction {

    private static Logger                 logger = LoggerFactory.getLogger(SharedPrefAction.class);
    private final  IjiamiCommonProperties commonProperties;
    private final  IBaseFileService       fileService;

    public SharedPrefAction(IjiamiCommonProperties commonProperties, IBaseFileService fileService) {
        this.commonProperties = commonProperties;
        this.fileService = fileService;
    }

    /**
     * 解析存储个人信息数据【Shared Pref】
     *
     * @param filePath       文件路径
     * @param stackDetails   17006 对应的堆栈详情数据
     * @param sensitiveWords 关键词
     * @return
     */
    public List<TPrivacySharedPrefs> analyzeSharedPref(String filePath, List<TPrivacyActionNougat> stackDetails, List<TSensitiveWord> sensitiveWords) {
        IDetectDataParser<SharedPrefBO> parser = new SharedPrefParser();
        List<SharedPrefBO> sharedPrefs = parser.parser(filePath, null);
        if (CollectionUtils.isEmpty(sharedPrefs)) {
            logger.info("SharedPrefAction - 存储个人信息数据初步解析，无数据");
            return new ArrayList<>();
        }
        logger.info("SharedPrefAction - 存储个人信息数据初步解析：{} 条", sharedPrefs.size());

        // 获取所有存储个人信息的数据
        Map<String, List<TPrivacySharedPrefs>> groupSharedPref = matchSharedPrefData(sharedPrefs, sensitiveWords);
        if (groupSharedPref == null || groupSharedPref.size() == 0) {
            logger.debug("SharedPrefAction - 存储个人信息数据，匹配关键词数据，无满足条件数据");
            return new ArrayList<>();
        }
        // 关键词匹配出的解析数据的文件地址
        Set<String> sharedPrefFilePaths = groupSharedPref.keySet();
        List<TPrivacySharedPrefs> privacySharedPrefs = new ArrayList<>();
        // 解析通讯存储新为数据
        for (TPrivacyActionNougat stackDetail : stackDetails) {
            String detailsData = stackDetail.getDetailsData();
            // 无抓包数据视为无效数据
            if (StringUtils.isBlank(detailsData)) {
                continue;
            }
            // 获取满足过滤条件的文件路径
            List<String> targetSharePrefPaths = sharedPrefFilePaths.stream().filter(buildSharePrefFilter(detailsData)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(targetSharePrefPaths)) {
                continue;
            }
            // 组装堆栈数据
            for (String targetSharePrefPath : targetSharePrefPaths) {
                List<TPrivacySharedPrefs> root = groupSharedPref.get(targetSharePrefPath);
                List<TPrivacySharedPrefs> target = putStackDetailToPrivacy(root, stackDetail);
                if (CollectionUtils.isNotEmpty(target)) {
                    privacySharedPrefs.addAll(target);
                }
            }
        }
        logger.info("SharedPrefAction - 存储个人信息数据解析,获取到的符合要求的完整解析数据: {} 条", privacySharedPrefs.size());
        return privacySharedPrefs;
    }

    /**
     * 将堆栈数据存入到存储个人信息数据中
     *
     * @param root
     * @param stackDetail
     * @return
     */
    private List<TPrivacySharedPrefs> putStackDetailToPrivacy(List<TPrivacySharedPrefs> root, TPrivacyActionNougat stackDetail) {
        if (CollectionUtils.isEmpty(root)) {
            return new ArrayList<>();
        }
        List<TPrivacySharedPrefs> target = new ArrayList<>(root.size());
        for (TPrivacySharedPrefs privacySharedPrefs : root) {
            TPrivacySharedPrefs bean = new TPrivacySharedPrefs();
            BeanUtils.copyProperties(privacySharedPrefs, bean);
            // 存放堆栈信息
            bean.setTaskId(stackDetail.getTaskId());
            bean.setExecutorType(stackDetail.getExecutorType());
            bean.setExecutor(stackDetail.getExecutor());
            bean.setBehaviorStage(stackDetail.getBehaviorStage());
            //v2.5版本只要是string类型为空则按照--入库
            bean.setStackInfo(StringUtils.isEmpty(stackDetail.getStackInfo())?PinfoConstant.DETAILS_EMPTY:stackDetail.getStackInfo());
            bean.setActionTime(stackDetail.getActionTime());
            // 存放系解析出的json数据
            bean.setSdkIds(stackDetail.getSdkIds());
            bean.setPackageName(stackDetail.getPackageName());
            target.add(bean);
        }
        return target;
    }

    /**
     * 存储个人信息数据过滤【capture_info】
     * <p>存储个人信息</p>
     *
     * @param detailsData 抓包数据
     * @return 过滤器
     */
    private Predicate<String> buildSharePrefFilter(String detailsData) {
        Predicate<String> filter = (path) -> {
            if (StringUtils.isBlank(path)) {
                return false;
            }
            // windows上测试需要打开，由于与linux路径表示不同
            // path = path.replace("\\", "/");
            return StringUtils.contains(detailsData, path);
        };
        return filter;
    }

    /**
     * 匹配解析出的存储个人信息数据，无堆栈
     *
     * @param sharedPrefs    从shared Prefs中获取的文件数据
     * @param sensitiveWords 关键字集合
     * @return 存储个人信息数据
     */
    private Map<String, List<TPrivacySharedPrefs>> matchSharedPrefData(List<SharedPrefBO> sharedPrefs, List<TSensitiveWord> sensitiveWords) {
        // 用来存放目标数据
        Map<String, List<TPrivacySharedPrefs>> targetSharedPrefMap = new HashMap<>(64);
        for (SharedPrefBO sharedPref : sharedPrefs) {
            String content = sharedPref.getContent();
            // 不符合要求的数据
            if (StringUtils.isBlank(content) || StringUtils.isBlank(sharedPref.getPath())) {
                continue;
            }
            List<TPrivacySharedPrefs> targetSharedPrefs = new ArrayList<>(sensitiveWords.size());
            // 匹配敏感词 再入库
            for (TSensitiveWord sensitive : sensitiveWords) {
                // 无名称，无正则视为无效
                if (StringUtils.isBlank(sensitive.getRegex()) && StringUtils.isBlank(sensitive.getName())) {
                    continue;
                }
                List<TPrivacySharedPrefs> privacySharedPrefs;
                // 存在正则匹配正则，否则匹配关键字
                if (StringUtils.isNotBlank(sensitive.getRegex())) {
                    privacySharedPrefs = matchSharedPrefByRegex(sharedPref, sensitive);
                } else {
                    privacySharedPrefs = matchSharedPrefByWord(sharedPref, sensitive);
                }
                // 存在匹配到的数据
                if (CollectionUtils.isNotEmpty(privacySharedPrefs)) {
                    targetSharedPrefs.addAll(privacySharedPrefs);
                }
            }
            if (CollectionUtils.isNotEmpty(targetSharedPrefs)) {
                targetSharedPrefMap.put(sharedPref.getPath(), targetSharedPrefs);
            }
        }
        return targetSharedPrefMap;
    }

    private List<TPrivacySharedPrefs> matchSharedPrefByWord(SharedPrefBO sharedPref, TSensitiveWord sensitive) {
        List<TPrivacySharedPrefs> privacySharedPrefs = new ArrayList<>();
        for (SensitiveWordsHelper.SensitiveInfo info : SensitiveWordsHelper.findXmlName(sharedPref.getContent(), sensitive.getSensitiveWords())) {
            TPrivacySharedPrefs bean = buildSharedPrefToPrivacy(sharedPref, sensitive, info.getCode(), info.getMarkOriginal(), info.getWord());

            privacySharedPrefs.add(bean);
        }
        return privacySharedPrefs;
    }

    private List<TPrivacySharedPrefs> matchSharedPrefByRegex(SharedPrefBO sharedPref, TSensitiveWord sensitive) {
        List<TPrivacySharedPrefs> privacySharedPrefs = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        String content = sharedPref.getContent();
        Pattern pattern = Pattern.compile(sensitive.getRegex());
        Matcher matcher = pattern.matcher(content);
        // 获取正则匹配到的内容
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, content, sensitive, true);
            if (isInvalidInfo) {
                continue;
            }
            String code = SensitiveWordsHelper.getMatchString(matcher, content);
            String keyword = matcher.group();
            String markOriginal = code.replace(keyword, "<em>" + keyword + "</em>");
            TPrivacySharedPrefs bean = buildSharedPrefToPrivacy(sharedPref, sensitive, code, markOriginal, keyword);
            privacySharedPrefs.add(bean);
        }
        return privacySharedPrefs;
    }

    /**
     * 构建存储个人信息实体
     *
     * @param sharedPref   存储个人信息
     * @param sensitive    关键词
     * @param code         存储个人信息的代码片段
     * @param markOriginal 标记源
     * @param keyword      抓取关键词
     * @return
     */
    private TPrivacySharedPrefs buildSharedPrefToPrivacy(SharedPrefBO sharedPref, TSensitiveWord sensitive, String code, String markOriginal, String keyword) {
        //v2.5版本只要string类型为空则按照--入库
        TPrivacySharedPrefs bean = new TPrivacySharedPrefs();
        // 存放敏感词信息
        bean.setTypeId(sensitive.getTypeId());
        bean.setName(StringUtils.isEmpty(sensitive.getName())? PinfoConstant.DETAILS_EMPTY:sensitive.getName());
        bean.setSensitiveId(sensitive.getId());
        // 存放存储个人信息
        bean.setSensitiveWord(StringUtils.isEmpty(keyword)?PinfoConstant.DETAILS_EMPTY:keyword);
        bean.setPath(sharedPref.getPath());
        bean.setContent(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY:code);
        // JZ.DO：优化部分
        // 存放文件转换成的图片
        //        bean.setFileKey(stringToImage(markOriginal));
        return bean;
    }

    private String stringToImage(String request) {
        String fileKey = null;
        Text2Pic text2Pic = new Text2Pic(request);
        text2Pic.SetPicType(Text2Pic.PictureType.TP_JPG);
        File file = new File(commonProperties.getFilePath() + File.separator + "default" + File.separator + UUID.randomUUID() + ".jpg");
        text2Pic.buildImage(file);
        if (file.exists()) {
            try {
                FileVO fileVO = new FileVO();
                fileVO.setFileName(file.getName());
                fileVO.setFileKey(UuidUtil.uuid());
                fileVO.setInputStream(new FileInputStream(file));
                fileVO.setFileExtName(file.getName().substring(file.getName().lastIndexOf(".")));
                fileVO.setFilePath(file.getAbsolutePath());
                fileService.uploadFile(fileVO);
                fileKey = fileVO.getFileKey();
            } catch (Exception e) {
                fileKey = null;
            } finally {
                FileUtil.deleteFile(file);
            }
        }
        return fileKey;
    }
}
