package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSensitiveWord;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonySharedPrefAction.java
 * @Description 存储行为解析
 * @createTime 2024年06月24日 18:02:00
 */
@Component
public class HarmonySharedPrefAction {


    public List<TPrivacySharedPrefs> analyzeSharedPref(String filePath, List<TSensitiveWord> sensitiveWords) {
        return Collections.emptyList();
    }

}
