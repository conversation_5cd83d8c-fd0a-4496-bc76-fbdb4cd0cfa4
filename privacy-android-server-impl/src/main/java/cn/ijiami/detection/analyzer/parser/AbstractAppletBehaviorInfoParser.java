package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.VO.detection.privacy.AlipayAppletAction;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static cn.ijiami.detection.utils.CommonUtil.readeFile;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AbstractAppletBehaviorInfoParser.java
 * @Description 小程序行为文件解析
 * @createTime 2023年09月13日 12:06:00
 */
public abstract class AbstractAppletBehaviorInfoParser implements IDetectDataParser<AppletActionBO> {

    @Override
    public List<AppletActionBO> parser(String filePath, String appName) {
        List<String> lines = readeFile(new File(filePath));
        List<AppletActionBO> appletActionBOList = new ArrayList<>(lines.size());
        for (String logText:lines) {
            AppletActionBO data = parserData(logText);
            if (data != null) {
                appletActionBOList.add(data);
            }
        }
        return appletActionBOList;
    }

    abstract AppletActionBO parserData(String logText);

}
