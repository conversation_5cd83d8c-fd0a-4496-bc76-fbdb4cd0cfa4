package cn.ijiami.detection.analyzer.bo;

import java.io.Serializable;

/**
 * host ip 文件实体对应类
 *
 * <AUTHOR>
 * @date 2020-05-14 20:40
 */
public class HostIpBO implements Serializable {
    private static final long    serialVersionUID = -3044736254544642667L;
    /**
     * 原始数据
     */
    private              String  original;
    /**
     * IP 地址
     */
    private              String  ip;
    /**
     * host 地址
     */
    private              String  host;
    /**
     * 调用 次数
     */
    private              Integer counter;
    /**
     * 地址
     */
    private              String  url;

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getCounter() {
        return counter;
    }

    public void setCounter(Integer counter) {
        this.counter = counter;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "HostIpBO{" + "original='" + original + '\'' + ", ip='" + ip + '\'' + ", host='" + host + '\'' + ", counter=" + counter + ", url='" + url + '\''
                + '}';
    }
}
