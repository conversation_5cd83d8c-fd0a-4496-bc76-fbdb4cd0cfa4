package cn.ijiami.detection.analyzer.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonyCaptureInfoBO.java
 * @Description 鸿蒙网络日志
 * @createTime 2025年03月03日 18:30:00
 */
@Data
public class HarmonyCaptureInfoBO {

    private String cookie;
    private String method;
    private String url;
    private String host;
    private String content;
    private String header;
    private Date actionTime;
    private String appName;
    private String appPackageName;

    private String port;
    private String address;
    private String protocol;
    private String ip;
}
