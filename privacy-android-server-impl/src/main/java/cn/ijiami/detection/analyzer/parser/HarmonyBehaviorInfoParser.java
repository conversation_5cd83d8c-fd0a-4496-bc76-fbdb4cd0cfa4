package cn.ijiami.detection.analyzer.parser;
import cn.ijiami.detection.VO.detection.privacy.dto.HarmonyActionBO;
import cn.ijiami.detection.utils.CommonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static cn.ijiami.detection.utils.CommonUtil.readeFile;

@Slf4j
public class HarmonyBehaviorInfoParser implements IDetectDataParser<HarmonyActionBO> {

    @Override
    public List<HarmonyActionBO> parser(String filePath, String appName) {
        List<String> lines = readeFile(new File(filePath));
        List<HarmonyActionBO> appletActionBOList = new ArrayList<>(lines.size());
        for (String logText:lines) {
            HarmonyActionBO data = CommonUtil.jsonToBean(logText, new TypeReference<HarmonyActionBO>() {});
            if (data != null) {
                appletActionBOList.add(data);
            }
        }
        return appletActionBOList;
    }
}