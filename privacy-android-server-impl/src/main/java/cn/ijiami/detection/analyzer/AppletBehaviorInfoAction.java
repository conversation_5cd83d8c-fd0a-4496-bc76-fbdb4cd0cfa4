package cn.ijiami.detection.analyzer;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletInputParamDTO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.parser.AbstractAppletBehaviorInfoParser;
import cn.ijiami.detection.analyzer.parser.AlipayAppletBehaviorInfoParser;
import cn.ijiami.detection.analyzer.parser.WechatAppletBehaviorInfoParser;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.TTaskExtendMapper;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.utils.AppletActionUtils;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.net.*;
import java.util.*;

import static cn.ijiami.detection.utils.AppletActionUtils.getCookie;
import static cn.ijiami.detection.utils.CommonUtil.cookieMark;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletBehaviorInfoAction.java
 * @Description 小程序行为解析
 * @createTime 2023年04月19日 18:19:00
 */
@Slf4j
@Component
public class AppletBehaviorInfoAction {

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private AppletCaptureAction captureAction;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Autowired
    private CacheService cacheService;

    /**
     * 分析新为数据，入库堆栈信息【behave_info】
     *
     * @param filePath      文件路径
     * @param taskDetailVO  任务详情
     * @param behaviorStage 行为阶段
     * @param actionNougatList 权限数据
     * @return 行为数据
     */
    public DetectDataBO analyzeBehaviorInfo(String filePath, TaskDetailVO taskDetailVO, BehaviorStageEnum behaviorStage,
                                            List<TActionNougat> actionNougatList, List<TSensitiveWord> sensitiveWords) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(taskDetailVO.getTerminal_type());
        // 获取文件中的堆栈数据
        DetectDataBO data = new DetectDataBO();
        AbstractAppletBehaviorInfoParser parser;
        if (terminalType == TerminalTypeEnum.WECHAT_APPLET) {
            parser = new WechatAppletBehaviorInfoParser();
        } else if (terminalType == TerminalTypeEnum.ALIPAY_APPLET) {
            parser = new AlipayAppletBehaviorInfoParser();
        } else {
            throw new IjiamiRuntimeException("not found AppletBehaviorInfoParser");
        }
        List<AppletActionBO> actionList = parser.parser(filePath, taskDetailVO.getApk_name());
        if (CollectionUtils.isEmpty(actionList)) {
            log.info("BehaviorInfoAction - 行为数据初步解析，无数据");
            data.setPrivacyActionNougats(Collections.emptyList());
            data.setPrivacySensitiveWords(Collections.emptyList());
            data.setPrivacySharedPrefs(Collections.emptyList());
            data.setPrivacyOutsideAddresses(Collections.emptyList());
            return data;
        }
        boolean onlySavePersonalBehavior = isOnlySavePersonalBehavior(taskDetailVO.getTaskId());
        log.info("BehaviorInfoAction -  行为数据初步解析: {} 条 onlySavePersonalBehavior: {}", actionList.size(), onlySavePersonalBehavior);
        List<TActionFilterGroupRegex> actionFilterGroupRegexList = actionFilterGroupDao.findActionFilterGroupRegexList(
                taskDetailVO.getTaskId(), TerminalTypeEnum.getItem(taskDetailVO.getTerminal_type()));
        // 组装需求的数据
        List<TPrivacyActionNougat> actionNougats = new ArrayList<>();
        List<TPrivacyOutsideAddress> privacyOutsideAddress = new ArrayList<>();
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        for (AppletActionBO actionBO : actionList) {
            Optional<TActionNougat> actionNougat = actionNougatList
                    .stream()
                    .filter(nougat -> StringUtils.containsIgnoreCase(nougat.getActionApi(), actionBO.getActionApi()))
                    .findFirst();
            if (!actionNougat.isPresent()) {
                continue;
            }
            actionBO.setAppName(taskDetailVO.getApk_name());
            if (actionBO.getActionType() == AppletActionTypeEnum.NET.itemValue()) {
                TPrivacyOutsideAddress address = buildNetwork(taskDetailVO.getTaskId(), actionBO, behaviorStage, ipUtil);
                if (Objects.nonNull(address)) {
                    privacyOutsideAddress.add(address);
                }
                //分析传输个人信息
                sensitiveWordList.addAll(captureAction.analyzeCaptureInfo(taskDetailVO.getTaskId(), behaviorStage, actionBO, sensitiveWords));
                if (StringUtils.isNotBlank(actionBO.getAppVersion())) {
                    data.setAppletVersion(actionBO.getAppVersion());
                }
                if (StringUtils.isNotBlank(actionBO.getLogoPicUrl())) {
                    data.setAppletLogoUrl(actionBO.getLogoPicUrl());
                }
            } else if (actionBO.getActionType() == AppletActionTypeEnum.ACTION.itemValue()) {
                TPrivacyActionNougat action = buildBehavioral(taskDetailVO.getTaskId(), actionBO, behaviorStage, actionNougat.get());
                // 过滤行为
                if (BehaviorInfoAction.filterActionGroupRegex(actionFilterGroupRegexList, action.getActionId(),
                        action.getStackInfo(), action.getDetailsData())) {
                    continue;
                }
                // 是否只保存个人信息行为
                if (onlySavePersonalBehavior && actionNougat.get().getPersonal() != PrivacyStatusEnum.YES.getValue()) {
                    continue;
                }
                actionNougats.add(action);
            } else if (actionBO.getActionType() == AppletActionTypeEnum.STORAGE.itemValue()) {
                log.info("小程序储存行为");
            }
        }
        log.info("BehaviorInfoAction - 行为数据解析，获取到的符合要求的完整解析数据: actionNougats={} 条, privacyOutsideAddress={} 条",
                actionNougats.size(), privacyOutsideAddress.size());
        data.setPrivacyActionNougats(actionNougats);
        data.setPrivacySensitiveWords(sensitiveWordList);
        data.setPrivacySharedPrefs(Collections.emptyList());
        data.setPrivacyOutsideAddresses(privacyOutsideAddress);
        return data;
    }

    protected boolean isOnlySavePersonalBehavior(Long taskId) {
        Example example = new Example(TTaskExtend.class);
        example.createCriteria().andEqualTo("taskId", taskId);
        TTaskExtend taskExtend = taskExtendMapper.selectOneByExample(example);
        if (Objects.isNull(taskExtend)) {
            return false;
        } else if (Objects.isNull(taskExtend.getOnlySavePersonalBehavior())) {
            return false;
        } else {
            return taskExtend.getOnlySavePersonalBehavior();
        }
    }

    public static TPrivacyActionNougat buildBehavioral(Long taskId, AppletActionBO data, BehaviorStageEnum behaviorStage, TActionNougat actionInfo) {
        TPrivacyActionNougat actionNougat = new TPrivacyActionNougat();
        actionNougat.setTaskId(taskId);
        // 默认主体类型为APP（本应用）
        actionNougat.setExecutorType(ExecutorTypeEnum.APP.getValue());
        actionNougat.setExecutor(data.getAppName());
        actionNougat.setPackageName(data.getAppId());
        // 组装堆栈数据
        actionNougat.setActionId(actionInfo.getActionId());
        actionNougat.setStackInfo(data.getStackInfo());
        actionNougat.setDetailsData(CommonUtil.beanToJson(data.getReturnData()));
        actionNougat.setBehaviorStage(behaviorStage);
        actionNougat.setActionTime(new Date(data.getTimeStamp()));
        actionNougat.setActionTimeStamp(data.getTimeStamp());
        // 是否是使用APP前触发
        boolean isTriggeredBeforeUseApp = actionNougat.getBehaviorStage() == BehaviorStageEnum.BEHAVIOR_GRANT;
        actionNougat.setType(isTriggeredBeforeUseApp);
        return actionNougat;
    }

    public static TPrivacyOutsideAddress buildNetwork(Long taskId, AppletActionBO data, BehaviorStageEnum behaviorStage, IpUtil ipUtil) {
        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        outsideAddress.setTaskId(taskId);
        outsideAddress.setBehaviorStage(behaviorStage);
        outsideAddress.setExecutorType(ExecutorTypeEnum.APP.getValue());
        outsideAddress.setExecutor(data.getAppName());
        outsideAddress.setPackageName(data.getAppId());
        // 存放json格式的数据
        outsideAddress.setStackInfo(data.getStackInfo());
        if (Objects.isNull(data.getInputParam())) {
            return null;
        }
        AppletInputParamDTO param = data.getInputParam();
        //没有url就不分析
        if(StringUtils.isBlank(param.getUrl())){
            return null;
        }
        outsideAddress.setDetailsData(AppletActionUtils.getParams(param));
        outsideAddress.setResponseData(AppletActionUtils.getResponseData(data.getReturnData()));
        outsideAddress.setUrl(param.getUrl());
        outsideAddress.setActionTime(new Date(data.getTimeStamp()));
        try {
            URI url = new URI(param.getUrl());
            outsideAddress.setHost(url.getHost());
            outsideAddress.setProtocol(url.getScheme());
            //组装cookie
            outsideAddress.setCookie(getCookie(param));
            outsideAddress.setCookieMark(cookieMark(outsideAddress));
            if (url.getScheme().equalsIgnoreCase("https")) {
                outsideAddress.setPort("443");
            } else if (url.getPort() > -1) {
                outsideAddress.setPort(String.valueOf(url.getPort()));
            } else {
                outsideAddress.setPort("80");
            }
            try {
                if (StringUtils.isNotBlank(url.getHost())) {
                    outsideAddress.setIp(InetAddress.getByName(url.getHost()).getHostAddress());
                } else {
                    outsideAddress.setIp(PinfoConstant.DETAILS_EMPTY);
                }
            } catch (Exception e) {
                log.info("获取ip失败 host={} message={}", url.getHost(), e.getMessage());
                outsideAddress.setIp(PinfoConstant.DETAILS_EMPTY);
            }
            //归属地
            String address = ipUtil.getAddress(url.getHost());
            outsideAddress.setAddress(address);
            outsideAddress.setOutside(IpUtil.isOutSide(address));
        } catch (URISyntaxException e) {
            log.info("url解析失败 host={} message={}", param.getUrl(), e.getMessage());
        }
        outsideAddress.setCounter(1);
        return outsideAddress;
    }

}