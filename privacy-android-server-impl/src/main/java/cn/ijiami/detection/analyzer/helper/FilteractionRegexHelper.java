package cn.ijiami.detection.analyzer.helper;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cn.ijiami.detection.entity.TActionFilterRegex;
import cn.ijiami.detection.enums.ActionFilterFieldEnum;
import cn.ijiami.detection.enums.ActionFilterModeEnum;

public class FilteractionRegexHelper {
	
	public static boolean filteractionRegexMethod(long actionId , String stackInfo, String detail, List<TActionFilterRegex> filterRegexList){
		for (TActionFilterRegex tActionFilterRegex : filterRegexList) {
			
			Long regexActionId = tActionFilterRegex.getActionId();
			if(regexActionId != actionId) {
				continue;
			}
			
			//如果为空, 说明不需要过滤直接放过
			if(StringUtils.isBlank(tActionFilterRegex.getActionFilterRegex())) {
				return true;
			}
			
			//1堆栈方式判定 
			if(tActionFilterRegex.getActionFilterField() == ActionFilterFieldEnum.stackInfo) {
				return strRegex(stackInfo, tActionFilterRegex);
			}
			
			//2数据详情方式
			if(tActionFilterRegex.getActionFilterField() == ActionFilterFieldEnum.detailsData) {
				return strRegex(detail, tActionFilterRegex);
			}
		}
		return false;
	}
	
	
	private static boolean strRegex(String content, TActionFilterRegex tActionFilterRegex){
		if(tActionFilterRegex.getActionFilterMode() == ActionFilterModeEnum.contains) {
			return SpecialActionTypeConvertHelper.isMatcherContent(content, tActionFilterRegex.getActionFilterRegex());
		}
		
		if(tActionFilterRegex.getActionFilterMode() == ActionFilterModeEnum.equals) {
			return content.equals(tActionFilterRegex.getActionFilterRegex());
		}
		return false;
	}
	
}
