package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.analyzer.bo.HostIpBO;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * host ip 数据解析
 *
 * <AUTHOR>
 * @date 2020-05-14 20:23
 */
public class HostIpParser implements IDetectDataParser<HostIpBO> {

    @Override
    public List<HostIpBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 解析数据
     *
     * @param filePath 抓包工具获取到的数据的文件路径
     * @return list对象
     */
    private static List<HostIpBO> analysis(String filePath) {
        String readStr = DynamicFileReaderHelper.readFileToString(filePath);
        if (StringUtils.isBlank(readStr)) {
            return new ArrayList<>();
        }

        List<HostIpBO> hostIps = new ArrayList<>();
        // 截取\n源于readFileToPrintString方法
        String[] lines = readStr.split("\n");
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            // 构建源数据
            try {
                hostIps.addAll(build(line));
            } catch (ParserException e) {
                e.getMessage();
            }
        }
        return hostIps;
    }

    /**
     * 多IP分析，拆分为多条数据
     *
     * @param root 源数据
     * @return
     * @throws Exception
     */
    private static List<HostIpBO> build(String root) throws ParserException {
        List<HostIpBO> hostIps = new ArrayList<>();
        try {
            String line = root.substring(1, root.length() - 1);
            String[] split = line.split("]\\[");
            String ips = split[0];
            String counter = split[1];
            String host = split[2];
            String url = host;
            // 检测过程中存在没有url的数据，进行特殊处理
            if (split.length == 4) {
                url = split[3];
            }
            if (ips.contains(",")) {
                for (String ip : ips.split(", ")) {
                    hostIps.add(build(ip, host, counter, url, root));
                }
            } else {
                hostIps.add(build(ips, host, counter, url, root));
            }
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.HOST_IP);
        }
        return hostIps;
    }

    /**
     * 构建host IP文件对应实体
     * <p>v1:[************][1][pages.tmall.com]
     * <p>v2:[**************(1), **************(2)][14][alapi.idianyou.cn][http://alapi.idianyou.cn/dianyou_centerapi/renovate/infograpSoCheckUpdate.do,http://alapi.idianyou.cn/dianyou_centerapi/renovate/myappCheckUpdate.do,http://alapi.idianyou.cn/dianyou_centerapi/active/init.do]
     *
     * @param ip       ip地址，格式不唯一
     * @param host     host地址
     * @param count    统计次数
     * @param url      完整地址
     * @param original 原始数据
     * @return
     */
    private static HostIpBO build(String ip, String host, String count, String url, String original) {
        String suffix = ")";
        HostIpBO to = new HostIpBO();
        to.setOriginal(original);
        to.setHost(host);
        to.setUrl(url);
        // ip数据格式变更兼容之前版本
        if (ip.endsWith(suffix)) {
            to.setIp(ip.substring(0, ip.indexOf("(")));
            to.setCounter(getIpCount(ip));
        } else {
            to.setIp(ip);
            to.setCounter(Integer.parseInt(count));
        }
        return to;
    }

    /**
     * 根据格式获取ip使用次数
     *
     * @param ip **************(6)
     * @return 6
     */
    private static int getIpCount(String ip) {
        String count = ip.substring(ip.indexOf("(") + 1, ip.indexOf(")"));
        if (StringUtils.isEmpty(count)) {
            return 0;
        }
        return Integer.parseInt(count);
    }
}
