package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.analyzer.helper.SpecialActionTypeConvertHelper;
import cn.ijiami.detection.utils.DataHandleUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.utils.DataHandleUtil;

/**
 * 行为数据读取器
 *
 * <AUTHOR>
 * @date 2020-09-14 21:44
 */
public class BehaviorInfoParser implements IDetectDataParser<ActionStackBO> {

    public static final String MARK_ACTION_TIME  = "action_time";
    public static final String MARK_TYPE_ID      = "type_id";
    public static final String MARK_TYPE_NAME    = "type_name";
    public static final String MARK_DETAILS_DATA = "details_data";
    public static final String MARK_STACK_INFO = "stack_info";
    public static final String MARK_JNI_STACK_INFO = "jni_stack_info";

    //爱加密加固包名
    public static final String FILTER_IJIAMI_STACK = "comijm.dataencryption|com.ijm.dataencryption|s.h.e.l.l|com.ijiami.residconfusion|ccc.rrr.hhh.s|com.shell.SdkManager|com.shell.SdkManagerApplication|lte.NCall|com.ijiami.whitebox|com.ijiami|com.appsafekb.crypto|com.ijm.detect.drisk|com.wxgzs.sdk";

    @Override
    public List<ActionStackBO> parser(String filePath, String appName) {
        return analysis(filePath);
    }

    /**
     * 分析文件
     *
     * @param filePath
     * @return
     */
    private static List<ActionStackBO> analysis(String filePath) {
        if (!new File(filePath).exists()) {
            return new ArrayList<>();
        }
        String readStr = DynamicFileReaderHelper.readFileToString(filePath);
        if (StringUtils.isBlank(readStr)) {
            return new ArrayList<>();
        }
        // 截取\n源于readFileToPrintString方法
        return Arrays.stream(readStr.split("\n"))
                .parallel()
                .filter(json -> !DataHandleUtil.nonJSONValid(json))
                .map(json -> {
                    // 构建实体数据
                    try {
                        return build(json);
                    } catch (ParserException e) {
                        e.getMessage();
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 构建行为堆栈数据
     *
     * @param json
     * @return
     */
    private static ActionStackBO build(String json) throws ParserException {
        ActionStackBO stack = new ActionStackBO();
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject.containsKey(MARK_ACTION_TIME)) {
                stack.setActionTime(jsonObject.getDate(MARK_ACTION_TIME));
            }
            if (jsonObject.containsKey(MARK_TYPE_ID)) {
                stack.setActionId(jsonObject.getLong(MARK_TYPE_ID));
            }
            if (jsonObject.containsKey(MARK_TYPE_NAME)) {
                stack.setTypeName(jsonObject.getString(MARK_TYPE_NAME));
            }
            if (jsonObject.containsKey(MARK_DETAILS_DATA)) {
                stack.setDetailsData(jsonObject.getString(MARK_DETAILS_DATA));
            }
            if (jsonObject.containsKey(MARK_STACK_INFO)) {
                stack.setStackInfo(jsonObject.getString(MARK_STACK_INFO));
            }
            if (jsonObject.containsKey(MARK_JNI_STACK_INFO)) {
                stack.setJniStackInfo(jsonObject.getString(MARK_JNI_STACK_INFO));
            }
            //判断是否存在爱加密的加固包，如果存在就过滤掉单行代码
            boolean isMatch = isMatcherContent(stack.getStackInfo(), FILTER_IJIAMI_STACK);
            if(isMatch) {
            	stack.setStackInfo(filterIJMStackInfo(stack.getStackInfo()));
            }
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.BEHAVIOR_INFO);
        }
        return stack;
    }

    public static boolean isMatcherContent(String content, String regex) {
        try {
        	if (StringUtils.isBlank(content)) {
			    return false;
			}
			return Pattern.compile(regex, Pattern.CASE_INSENSITIVE)
			        .matcher(content).find();
		} catch (Exception e) {
			e.getMessage();
			return false;
		}
    }

    public static String filterIJMStackInfo(String stackInfo) {
        List<String> stack = Arrays.asList(stackInfo.split("<---"));
        StringBuilder sf = new StringBuilder();
        int size = stack.size();

        for (int i = 0; i < size; i++) {
            String str = stack.get(i);
            boolean isMatch = isMatcherContent(str, FILTER_IJIAMI_STACK);
            if (!isMatch) {
                sf.append(str);
                if (i < size - 1) {
                    sf.append("<---");
                }
            }
        }
        return sf.toString();
    }

}
