package cn.ijiami.detection.analyzer.bo;

import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.entity.interfaces.RawExecutor;

import java.io.Serializable;

/**
 * 传输个人信息数据 对应数据实体
 *
 * <AUTHOR>
 * @date 2020-05-16 10:12
 */
public class CaptureInfoBO implements Serializable, RawExecutor {

    private static final long    serialVersionUID = -184336235873414734L;
    /**
     * 原始数据
     */
    private              String  original;
    /**
     * 请求时间
     */
    private              String  timeStamp;
    /**
     * 请求方法
     */
    private              String  method;
    /**
     * 完整地址
     */
    private              String  url;
    /**
     * 请求路径
     */
    private              String  path;
    /**
     * 请求协议
     */
    private              String  protocol;
    /**
     * 内容长度
     */
    private              String  contentLength;
    /**
     * 请求类型
     */
    private              String  contentType;
    /**
     * host 地址
     */
    private              String  host;
    /**
     * port 地址
     */
    private              String  port;
    /**
     * 链接类型
     */
    private              String  connection;
    /**
     * 用户代理
     */
    private              String  userAgent;
    /**
     * 预期
     */
    private              String  expect;
    /**
     * 访问编码
     */
    private              String  acceptEncoding;
    /**
     * 请求参数
     */
    private              String  params;
    /**
     * cookie信息
     */
    private              String  cookie;
    /**
     * ip地址
     */
    private              String  ip;
    /**
     * 堆栈信息
     */
    private              String  stackInfo;
    /**
     * 主体包名
     */
    private              String  packageName;
    /**
     * 主体
     */
    private              String  executor;
    /**
     * 主体类型 1.APP 2.SDK
     */
    private              Integer executorType;
    /**
     * 请求类型1http请求 2https请求
     */
    private              String  type;
    /**
     * 详细抓包数据
     */
    private              String  detailsData;
    /**
     * 当主体类型为SDK时，存储sdkId，采用json格式
     */
    private              String  sdkIds;

    /**
     * 抓包id，用来关联请求数据和响应数据
     */
    private              String  id;

    /**
     * 数据类型 1表示请求数据，2表示响应数据，3表示https握手数据
     */
    private              String dataType;

    private              String response;

    public String getSdkIds() {
        return sdkIds;
    }

    public void setSdkIds(String sdkIds) {
        this.sdkIds = sdkIds;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getContentLength() {
        return contentLength;
    }

    public void setContentLength(String contentLength) {
        this.contentLength = contentLength;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getConnection() {
        return connection;
    }

    public void setConnection(String connection) {
        this.connection = connection;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getExpect() {
        return expect;
    }

    public void setExpect(String expect) {
        this.expect = expect;
    }

    public String getAcceptEncoding() {
        return acceptEncoding;
    }

    public void setAcceptEncoding(String acceptEncoding) {
        this.acceptEncoding = acceptEncoding;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCookie() {
        return cookie;
    }

    public void setCookie(String cookie) {
        this.cookie = cookie;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getStackInfo() {
        return stackInfo;
    }

    public void setStackInfo(String stackInfo) {
        this.stackInfo = stackInfo;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public Integer getExecutorType() {
        return executorType;
    }

    public void setExecutorType(Integer executorType) {
        this.executorType = executorType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getDetailsData() {
        return detailsData;
    }

    public void setDetailsData(String detailsData) {
        this.detailsData = detailsData;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "CaptureInfoBO{" +
                "original='" + original + '\'' +
                ", timeStamp='" + timeStamp + '\'' +
                ", method='" + method + '\'' +
                ", url='" + url + '\'' +
                ", path='" + path + '\'' +
                ", protocol='" + protocol + '\'' +
                ", contentLength='" + contentLength + '\'' +
                ", contentType='" + contentType + '\'' +
                ", host='" + host + '\'' +
                ", port='" + port + '\'' +
                ", connection='" + connection + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", expect='" + expect + '\'' +
                ", acceptEncoding='" + acceptEncoding + '\'' +
                ", params='" + params + '\'' +
                ", cookie='" + cookie + '\'' +
                ", ip='" + ip + '\'' +
                ", stackInfo='" + stackInfo + '\'' +
                ", packageName='" + packageName + '\'' +
                ", executor='" + executor + '\'' +
                ", executorType=" + executorType +
                ", type='" + type + '\'' +
                ", detailsData='" + detailsData + '\'' +
                ", sdkIds='" + sdkIds + '\'' +
                ", id='" + id + '\'' +
                ", dataType='" + dataType + '\'' +
                ", response='" + response + '\'' +
                '}';
    }

    public static CaptureInfoBO buildCaptureInfoBO(TPrivacyOutsideAddress outsideAddress) {
        CaptureInfoBO captureInfo = new CaptureInfoBO();
        captureInfo.setExecutorType(outsideAddress.getExecutorType());
        captureInfo.setExecutor(outsideAddress.getExecutor());
        captureInfo.setSdkIds(outsideAddress.getSdkIds());
        captureInfo.setStackInfo(outsideAddress.getStackInfo());
        captureInfo.setDetailsData(outsideAddress.getDetailsData());
        captureInfo.setResponse(outsideAddress.getResponseData());
        captureInfo.setTimeStamp(String.valueOf(outsideAddress.getActionTime().getTime()));
        captureInfo.setIp(outsideAddress.getIp());
        captureInfo.setProtocol(outsideAddress.getProtocol());
        captureInfo.setCookie(outsideAddress.getCookie());
        captureInfo.setPort(outsideAddress.getPort());
        captureInfo.setHost(outsideAddress.getHost());
        captureInfo.setMethod(outsideAddress.getRequestMethod());
        captureInfo.setUrl(outsideAddress.getUrl());
        captureInfo.setPackageName(outsideAddress.getPackageName());
        captureInfo.setOriginal(outsideAddress.getDetailsData());
        return captureInfo;
    }
}
