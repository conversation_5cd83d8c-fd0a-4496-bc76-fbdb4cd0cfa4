package cn.ijiami.detection.analyzer.bo;

import java.io.Serializable;
import java.util.Map;

/**
 * SharedPref 中文件的内容
 *
 * <AUTHOR>
 * @date 2020-09-21 10:53
 */
public class SharedPrefBO implements Serializable {

    private static final long serialVersionUID = -4297620390959423001L;

    /**
     * 文件绝对路径
     */
    private String absolutePath;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 文件中的内容
     */
    private String content;

    /**
     * 文件内容键值对
     */
    private Map<String, String> map;

    public String getAbsolutePath() {
        return absolutePath;
    }

    public void setAbsolutePath(String absolutePath) {
        this.absolutePath = absolutePath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "SharedPrefBO{" + "absolutePath='" + absolutePath + '\'' + ", path='" + path + '\'' + ", content='" + content + '\'' + '}';
    }

    public Map<String, String> getMap() {
        return map;
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }
}
