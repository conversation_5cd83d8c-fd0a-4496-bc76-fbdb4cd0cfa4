package cn.ijiami.detection.thread;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.enums.PackerStatusEnum;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.device.util.CommandUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;


/**
 * IOS重签类
 */
public class IOSResignThread implements Runnable {
	
	private static final Logger LOG = LoggerFactory.getLogger(IOSResignThread.class);
	private TAssets assets;
	private IjiamiCommonProperties commonProperties;
    private SingleFastDfsFileService singleFastDfsFileService;
    private TAssetsMapper assetsMapper;
    private String fastDFSIntranetIp;
	
	public IOSResignThread(TAssets assets,IjiamiCommonProperties commonProperties,
			SingleFastDfsFileService singleFastDfsFileService,TAssetsMapper assetsMapper,String fastDFSIntranetIp){
		this.assets = assets;
		this.commonProperties = commonProperties;
		this.singleFastDfsFileService = singleFastDfsFileService;
		this.assetsMapper = assetsMapper;
		this.fastDFSIntranetIp = fastDFSIntranetIp;
	}

	@Override
	public void run() {
		long time1 = System.currentTimeMillis();
		LOG.info("Linux.IOS重签:md5={},name={}",assets.getMd5(), assets.getName());
		//下载IPA
		File file = new File(commonProperties.getFilePath() + File.separator + "default" + File.separator + UuidUtil.uuid() + ".ipa");
		File outFile = new File(commonProperties.getFilePath() + File.separator + "default" + File.separator + UuidUtil.uuid() + ".ipa");
        try {
        	String path = assets.getAppUrl(fastDFSIntranetIp);
			FileUtils.copyURLToFile(new URL(path), file);
		} catch (MalformedURLException e) {
			e.getMessage();
		} catch (IOException e) {
			e.getMessage();
		}
        
        if(!file.exists()) {
        	LOG.info("assetsId={},下载文件文件不存在={}", assets.getId(), file.getAbsoluteFile());
        	return;
        }
		
		//执行重签
        //./zsign -k ijiami.p12 -p 123456 -m ijiami.mobileprovision -o out.ipa  orig.ipa
		String macToolPath = commonProperties.getProperty("ios.resign.tool.path");
//		String cmd = "cd %s && %s/zsign -k %s/ijiami.p12 -p 123456 -m %s/ijiami.mobileprovision -o %s  %s";
		//python3 sign.py  orig.ipa out.ipa
		String cmd = "python %s/sign.sh %s %s %s ";  
		String origPath = file.getPath();
		String outPath = outFile.getPath();
//		cmd = String.format(cmd, macToolPath, macToolPath, macToolPath, macToolPath, outPath , origPath);
		cmd = String.format(cmd, macToolPath, outPath , origPath, macToolPath);
		LOG.info("cmd={}", cmd);
		String result = CommandUtil.execCmd(cmd);
		LOG.info("assetsId={},重签结果={}", assets.getId(), result);
		if(!outFile.exists()) { 
			LOG.info("assetsId={},重签输出文件不存在={}", assets.getId(), outPath);
			return;
		}
		//上传文件
		String signPath = uploadDataFileToFastDfs(outPath);
		if(StringUtils.isBlank(signPath)) {
			LOG.info("assetsId={},上传文件失败={}", assets.getId(), outPath);
			file.delete();
	        outFile.delete();
			return;
		}
		
		//修改状态
		int isHavePacker = PackerStatusEnum.RESIGN.getValue();
        assetsMapper.updateDumpZipUrlById(signPath, isHavePacker, assets.getId());
        long time2 = System.currentTimeMillis();
        LOG.info("assetsId={},重签完成,耗时={}毫秒", assets.getId(), (time2-time1));
        
        file.delete();
        outFile.delete();
	}
	
	private String uploadDataFileToFastDfs(String dataPath) {
        try {
            FileVO uploadFile = FileVOUtils.convertFileVOByFile(new File(dataPath));
            FileVO fastDfsFile = singleFastDfsFileService.instance().storeFile(uploadFile);
            String shellIpaPath = fastDfsFile.getFilePath();
            if (org.apache.commons.lang3.StringUtils.isBlank(shellIpaPath)) {
            	LOG.error("IOSResignThread检测数据上传文件失败 shellIpaPath为空");
            }
            return shellIpaPath;
        } catch (IOException | IjiamiApplicationException e) {
        	LOG.error("IOSResignThread检测数据上传文件失败", e);
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }
}
