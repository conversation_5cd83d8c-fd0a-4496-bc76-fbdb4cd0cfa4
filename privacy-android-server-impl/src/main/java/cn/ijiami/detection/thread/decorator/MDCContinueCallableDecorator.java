package cn.ijiami.detection.thread.decorator;

import cn.ijiami.detection.utils.CommonUtil;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Callable;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MDCContinueCallableDecorator.java
 * @Description 自动切换mdc的Callable，如果没有traceId会自动切换
 * @createTime 2021年12月01日 16:09:00
 */
public class MDCContinueCallableDecorator<T> implements Callable<T> {

    private final Callable<T> delegate;

    protected Map<String, String> logContextMap;

    public MDCContinueCallableDecorator(Callable<T> callable) {
        this.delegate = callable;
        this.logContextMap = MDC.getCopyOfContextMap();
        if (Objects.isNull(logContextMap)) {
            this.logContextMap = new HashMap<>();
        }
        if (Objects.isNull(logContextMap.get(TRACE_ID))) {
            logContextMap.put(TRACE_ID, CommonUtil.genTraceId());
        }
    }

    @Override
    public T call() throws Exception {
        MDC.setContextMap(this.logContextMap);
        try {
            return this.delegate.call();
        } finally {
            MDC.clear();
        }
    }
}
