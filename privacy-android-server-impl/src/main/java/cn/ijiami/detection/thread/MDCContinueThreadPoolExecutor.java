package cn.ijiami.detection.thread;

import cn.ijiami.detection.thread.decorator.MDCContinueCallableDecorator;
import cn.ijiami.detection.thread.decorator.MDCContinueRunnableDecorator;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MDCContinueThreadPoolExecutor.java
 * @Description 自动切换MDC的线程池
 * @createTime 2023年09月07日 17:38:00
 */
public class MDCContinueThreadPoolExecutor extends ThreadPoolExecutor {

    public MDCContinueThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public MDCContinueThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public MDCContinueThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public MDCContinueThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable task) {
        super.execute(new MDCContinueRunnableDecorator(task));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(new MDCContinueRunnableDecorator(task));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(new MDCContinueCallableDecorator<>(task));
    }
}
