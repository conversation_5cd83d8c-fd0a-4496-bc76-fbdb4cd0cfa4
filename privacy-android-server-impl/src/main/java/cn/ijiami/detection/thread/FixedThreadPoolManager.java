package cn.ijiami.detection.thread;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * 对FixedThreadPool对象进行统一管理 大小
 * 
 */
public class FixedThreadPoolManager {

	private static FixedThreadPoolManager manager = new FixedThreadPoolManager();
//	private static ExecutorService fixedthreadPool;
	private static  ThreadPoolExecutor fixedthreadPool;
	
	public static int poolSize=10;
	private FixedThreadPoolManager() {
		fixedthreadPool = (ThreadPoolExecutor) new ThreadPoolExecutor(8, 16,
				60L, TimeUnit.SECONDS,
	            new LinkedBlockingQueue<>(200), // 队列长度限制为1000
	            new ThreadFactoryBuilder().setNameFormat(FixedThreadPoolManager.class.getSimpleName() + "-pool-%d").build(),
				new ThreadPoolExecutor.CallerRunsPolicy() {
					@Override
					public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
						super.rejectedExecution(r, e);
					}
				});
		fixedthreadPool.setKeepAliveTime(120, TimeUnit.SECONDS);
		fixedthreadPool.allowCoreThreadTimeOut(true);
	}
	

	public static FixedThreadPoolManager getInstance() {
		return manager;
	}

	public void execute(Runnable command) {
		printInfo();
		fixedthreadPool.execute(command);
	}

	public <T> Future<T> submit(Callable<T> task) {
		printInfo();
		return fixedthreadPool.submit(task);
	}

	public Future<?> submit(Runnable task) {
		printInfo();
		return fixedthreadPool.submit(task);
	}

	public <T> Future<T> submit(Runnable task, T result) {
		return fixedthreadPool.submit(task, result);
	}

	public int getActiveCount() {
		if (fixedthreadPool instanceof ThreadPoolExecutor) {
			ThreadPoolExecutor executor = (ThreadPoolExecutor) fixedthreadPool;
			System.out.println("活动线程数：" + executor.getActiveCount());
			System.out.println("线程池大小：" + executor.getPoolSize());
			return executor.getActiveCount();
		}
		return 0;
	}
	
	public void printInfo() {
		if (fixedthreadPool instanceof ThreadPoolExecutor) {
			ThreadPoolExecutor executor = (ThreadPoolExecutor) fixedthreadPool;
			System.out.println("活动线程数：" + executor.getActiveCount());
			System.out.println("线程池大小：" + executor.getPoolSize());
			BlockingQueue<Runnable> queu = executor.getQueue();
			System.out.println("等候线程数：" + queu.size());
		}
	}

}
