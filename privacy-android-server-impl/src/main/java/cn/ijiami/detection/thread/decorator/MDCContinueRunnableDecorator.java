package cn.ijiami.detection.thread.decorator;

import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

@Slf4j
public class MDCContinueRunnableDecorator implements Runnable {

    private final Runnable delegate;

    protected Map<String, String> logContextMap;

    public MDCContinueRunnableDecorator(Runnable runnable) {
        this.delegate = runnable;
        this.logContextMap = MDC.getCopyOfContextMap();
        if (Objects.isNull(logContextMap)) {
            this.logContextMap = new HashMap<>();
        }
        if (Objects.isNull(logContextMap.get(TRACE_ID))) {
            logContextMap.put(TRACE_ID, CommonUtil.genTraceId());
        }
    }

    @Override
    public void run() {
        MDC.setContextMap(this.logContextMap);
        try {
            this.delegate.run();
        } catch (Throwable throwable) {
            log.error("线程出现异常", throwable);
        } finally {
            MDC.clear();
        }
    }
}
