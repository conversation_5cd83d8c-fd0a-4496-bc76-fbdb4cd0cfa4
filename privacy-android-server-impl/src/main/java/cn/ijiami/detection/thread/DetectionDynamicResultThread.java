package cn.ijiami.detection.thread;

import cn.ijiami.detection.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.support.Acknowledgment;

import cn.ijiami.detection.job.XxlDetectMessageServer;

import java.util.UUID;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;


public class DetectionDynamicResultThread implements Runnable {
	
	private static final Logger LOG = LoggerFactory.getLogger(DetectionDynamicResultThread.class);
	private XxlDetectMessageServer xxlDetectMessageServer;
	private String message;
	private Acknowledgment acknowledgment;
	public DetectionDynamicResultThread(String message,XxlDetectMessageServer xxlDetectMessageServer,Acknowledgment acknowledgment){
		this.xxlDetectMessageServer = xxlDetectMessageServer;
		this.message = message;
        this.acknowledgment = acknowledgment;
	}

	@Override
	public void run() {
		MDC.put(TRACE_ID, CommonUtil.genTraceId());
		try {
			LOG.info(message);
			xxlDetectMessageServer.analysisDynamicDetectionMessage(message,acknowledgment);
		} finally {
			MDC.remove(TRACE_ID);
		}
	}
}
