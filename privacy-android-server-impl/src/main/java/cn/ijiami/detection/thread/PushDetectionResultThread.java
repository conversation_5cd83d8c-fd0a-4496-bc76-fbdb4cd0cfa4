package cn.ijiami.detection.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.enums.PushStatusEnum;
import cn.ijiami.detection.service.api.ITaskExtendService;
import cn.ijiami.detection.utils.PlatformConstant;



public class PushDetectionResultThread implements Runnable {
	
	private static final Logger LOG = LoggerFactory.getLogger(PushDetectionResultThread.class);
	private ITaskExtendService taskExtendService;
	private String message;
	private Long taskId;
	public PushDetectionResultThread(Long taskId, String message,ITaskExtendService taskExtendService){
		this.taskExtendService = taskExtendService;
		this.message = message;
		this.taskId = taskId;
	}

	@Override
	public void run() {
		LOG.info("{}开始推送数据taskId={}",message,taskId);
		
		TTaskExtendVO vo = taskExtendService.findTaskByTaskId(taskId);
		if(vo==null) {
			taskExtendService.updatePushStatus(taskId, PushStatusEnum.PUSH_NOSTART);
			return;
		}
//		0待推送  1推送成功  2推送失败
		if(vo.getPushStatus()==null) {
			taskExtendService.updatePushStatus(taskId, PushStatusEnum.PUSH_NOSTART);
			return;
		}
		if(vo.getPushStatus() == 1){
			LOG.info("任务已经推送过...taskId={}",taskId);
			return;
		}
		taskExtendService.pushData(taskId);
		if(PlatformConstant.map.get(taskId)!= null) {
			PlatformConstant.map.remove(taskId);
		}
	}
}
