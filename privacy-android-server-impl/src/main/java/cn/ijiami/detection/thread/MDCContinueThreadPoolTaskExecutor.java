package cn.ijiami.detection.thread;

import cn.ijiami.detection.thread.decorator.MDCContinueCallableDecorator;
import cn.ijiami.detection.thread.decorator.MDCContinueRunnableDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MDCContinueThreadPoolTaskExecutor.java
 * @Description 自动切换MDC的线程池
 * @createTime 2021年12月01日 16:07:00
 */
public class MDCContinueThreadPoolTaskExecutor extends ThreadPoolTaskExecutor {


    @Override
    public void execute(Runnable task) {
        super.execute(new MDCContinueRunnableDecorator(task));
    }

    @Override
    public void execute(Runnable task, long startTimeout) {
        super.execute(new MDCContinueRunnableDecorator(task), startTimeout);
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(new MDCContinueRunnableDecorator(task));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(new MDCContinueCallableDecorator<>(task));
    }


}
