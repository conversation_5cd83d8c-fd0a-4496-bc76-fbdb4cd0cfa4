package cn.ijiami.detection.fastdfs;

import org.csource.common.NameValuePair;
import org.csource.fastdfs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class BigFileFastDFSClientUtil {

    private static final Logger LOG = LoggerFactory.getLogger(BigFileFastDFSClientUtil.class);

    private TrackerClient trackerClient = null;

    private StorageServer storageServer = null;

    private void init() throws Exception {
        trackerClient = new TrackerClient();
    }


    /**
     * 使用conf文件进行初始化
     *
     * @param conf
     * @throws Exception
     */
    public BigFileFastDFSClientUtil(String conf) throws Exception {
        ClientGlobal.init(conf);
        init();
    }

    /**
     * 使用properties文件进行初始化
     *
     * @param properties
     * @throws Exception
     */
    public BigFileFastDFSClientUtil(Properties properties) throws Exception {
        ClientGlobal.initByProperties(properties);
        init();
    }

    /**
     * 上传文件
     *
     * @param fileName
     * @param extName
     * @param metas
     * @return
     */
    public String uploadFile(String fileName, String extName, NameValuePair[] metas) {
        String result = null;
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            result = storageClient.upload_file1(fileName, extName, metas);
        } catch (Exception e) {
            LOG.error("上传文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }
        return result;
    }


    public String uploadFile(String fileName) {
        return uploadFile(fileName, null, null);
    }

    /**
     * 上传文件
     *
     * @param fileName
     * @param extName
     * @return
     */
    public String uploadFile(String fileName, String extName) {
        return uploadFile(fileName, extName, null);
    }

    /**
     * 上传文件
     *
     * @param filePath 文件的内容，字节数组
     * @param extName     文件扩展名
     * @return
     * @throws Exception
     */
    public String uploadBigFile(String filePath, String extName) {
        String result = null;
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            int size = 1024 * 1024 * 10;
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            String[] parts = storageClient.upload_file(filePath, extName ,null);
            result = parts != null ? parts[0] + "/" + parts[1] : null;
        } catch (Exception e) {
            LOG.error("上传文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }
        return result;
    }


    /**
     * 上传文件
     *
     * @param fileContent 文件的内容，字节数组
     * @param extName     文件扩展名
     * @param metas       文件扩展信息
     * @return
     * @throws Exception
     */
    public String uploadFile(byte[] fileContent, String extName, NameValuePair[] metas) {
        String result = null;
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            result = storageClient.upload_file1(fileContent, extName, metas);
        } catch (Exception e) {
            LOG.error("上传文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }
        return result;
    }

    /**
     * 上传文件
     *
     * @param fileContent 文件的字节数组
     * @param extName     文件的扩展名 如 txt  jpg png 等
     * @return null为失败
     */
    public String uploadFile(byte[] fileContent, String extName) {
        return uploadFile(fileContent, extName, null);
    }

    /**
     * 上传文件
     *
     * @param fileContent 文件的字节数组
     * @return null为失败
     * @throws Exception
     */
    public String uploadFile(byte[] fileContent) {
        return uploadFile(fileContent, null, null);
    }

    /**
     * 下载
     *
     * @param path
     * @return
     */
    public byte[] downloadBytes(String path) {
        byte[] b = null;
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            b = storageClient.download_file1(path);
        } catch (Exception e) {
            LOG.error("下载文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }

        return b;
    }

    /**
     * 删除文件
     *
     * @param storagePath
     * @return
     */
    public Integer deleteFile(String storagePath) {
        int result = -1;
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            result = storageClient.delete_file1(storagePath);
        } catch (Exception e) {
            LOG.error("删除文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }
        return result;
    }

    /**
     * 获取文件信息
     *
     * @param remoteFileName
     * @return
     */
    public FileInfo getFileInfo(String remoteFileName) {
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageClient1 storageClient = new StorageClient1(trackerServer, storageServer);
            return storageClient.get_file_info1(remoteFileName);
        } catch (Exception e) {
            LOG.error("获取文件失败", e);
        } finally {
            try {
                trackerServer.close();
            } catch (Exception e) {
                LOG.error("关闭trackerServer失败", e);
            }

        }
        return null;
    }
}
