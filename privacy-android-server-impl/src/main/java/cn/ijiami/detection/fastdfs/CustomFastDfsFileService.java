package cn.ijiami.detection.fastdfs;

import java.io.InputStream;
import java.util.Properties;

//import com.ijiami.framework.utils.FileUtil;
//import com.ijiami.framework.utils.UuidUtil;
import org.apache.commons.lang3.StringUtils;
import org.csource.fastdfs.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import com.github.pagehelper.util.StringUtil;

import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.context.FileContext;
import cn.ijiami.framework.file.service.impl.AbstractFileService;
import cn.ijiami.framework.file.service.impl.DefaultFastDfsFileService;
//import cn.ijiami.framework.file.utils.FastDFSClientUtil;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 自定义FastDFS文件服务类
 *
 * <AUTHOR>
 * @date 2020-07-11 14:36
 */
public class CustomFastDfsFileService extends AbstractFileService {

    private static final Logger LOG = LoggerFactory.getLogger(DefaultFastDfsFileService.class);

    private BigFileFastDFSClientUtil fastDfsClientUtil = null;

    public CustomFastDfsFileService(FileContext fileContext) {
        super(fileContext);
        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties("fastdfs-client.properties");
            fastDfsClientUtil = new BigFileFastDFSClientUtil(properties);
        } catch (Exception e) {
            LOG.error("初始化fastDFS失败:" + e);
        }
    }

    /**
     * 删除fast dfs 文件
     * @param storagePath
     * @return
     */
    public boolean deleteFile(String storagePath) {
        int result = fastDfsClientUtil.deleteFile(storagePath);
        return result != -1;
    }

    @Override
    public FileVO storeFile(FileVO fileVO) throws IjiamiApplicationException {
        String fileName = fileVO.getFileName();
        // 文件扩展名
        String fileExtName = "";
        if (!StringUtil.isEmpty(fileName)) {
            fileExtName = fileName.substring(fileName.lastIndexOf(".") + 1).toUpperCase();
        }

        try {
            String filePath;
            if (StringUtils.isBlank(fileVO.getFilePath())) {
                filePath = fastDfsClientUtil.uploadFile(FileUtil.inputStream2byte(fileVO.getInputStream()), fileExtName);
            } else {
                filePath = fastDfsClientUtil.uploadBigFile(fileVO.getFilePath(), fileExtName);
            }
            // 只返回文件扩展名和FileKey
            fileVO.setFilePath(filePath);
            fileVO.setFileUrl(filePath);
            fileVO.setFileKey(UuidUtil.uuid());
            fileVO.setRelativePath(filePath);
            fileVO.setFileExtName(fileExtName);

        } catch (Exception e) {
            LOG.error("保存到fastdfs文件系统错误:", e);
            throw new IjiamiApplicationException(e.getMessage());
        } finally {

        }
        return fileVO;
    }

    @Override
    public FileVO findFile(String filePath) {
        InputStream inputStream = null;
        FileVO fileVO = new FileVO();
        if (StringUtil.isEmpty(filePath)) {
            LOG.warn("文件路径不能为空");
        }
        FileInfo fileInfo = fastDfsClientUtil.getFileInfo(filePath);
        if (fileInfo == null) {
            return null;
        }
        inputStream = getFileStream(filePath);
        fileVO.setFileUrl(filePath);
        fileVO.setInputStream(inputStream);
        fileVO.setFileSize(fileInfo.getFileSize());
        fileVO.setFileName(null);
        fileVO.setFileExtName(null);
        fileVO.setFileKey(UuidUtil.randomNumber());

        return fileVO;
    }

    @Override
    public boolean checkFileExist(String filePath) {
        boolean result = false;
        try {
            FileInfo fileInfo = fastDfsClientUtil.getFileInfo(filePath);
            if (fileInfo != null) {
                result = true;
            }
        } catch (Exception e) {
            LOG.error("fastdfs文件系统异常，", e);
        } finally {
        }
        return result;
    }

    @Override
    public InputStream getFileStream(String filePath) {
        InputStream inputStream = null;
        try {
            byte[] bytes = fastDfsClientUtil.downloadBytes(filePath);
            inputStream = FileUtil.byte2InputStream(bytes);
        } catch (Exception e) {
            LOG.error("文件读取异常", e);
        } finally {
        }
        return inputStream;
    }

    public FileVO convertToPdf(String source, String target) {
        return null;
    }

    public void downloadFile() {

    }
}

