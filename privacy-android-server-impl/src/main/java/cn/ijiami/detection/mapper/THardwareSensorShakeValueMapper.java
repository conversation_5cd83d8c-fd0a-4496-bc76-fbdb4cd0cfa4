package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TDeepShakeValue;
import cn.ijiami.detection.entity.THardwareSensorShakeValue;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface THardwareSensorShakeValueMapper extends IjiamiMapper<THardwareSensorShakeValue> {

    List<THardwareSensorShakeValue> getShakeValueByTaskId(@Param("taskId") Long taskId);

    void deleteAllValueByTaskId(@Param("taskId") Long taskId);
}
