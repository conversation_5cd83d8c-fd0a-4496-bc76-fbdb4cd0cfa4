package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TPrivacyLawsBasis;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TPrivacyLawsBasisMapper extends IjiamiMapper<TPrivacyLawsBasis> {

    /**
     * 查询所有检测项及关联关键词信息
     *
     * @return
     */
    List<TPrivacyLawsBasis> selectItemNoInfo();

    List<TPrivacyLawsBasis> selectItemNoInfoByLawId(@Param("lawId") Integer lawId);
    
   /**
    * 根据行为ID获取数据
    * @param itemNo
    * @return
    */
    List<TPrivacyLawsBasis> findByItemNo(@Param("itemNo") String itemNo);

}