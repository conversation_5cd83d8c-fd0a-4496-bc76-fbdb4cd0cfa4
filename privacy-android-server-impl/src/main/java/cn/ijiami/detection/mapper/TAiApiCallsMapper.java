package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TAiApiCalls;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAiApiCallsMapper.java
 * @Description ai调用纪录
 * @createTime 2025年03月24日 11:33:00
 */
public interface TAiApiCallsMapper extends IjiamiMapper<TAiApiCalls> {
    List<TAiApiCalls> findByAuthCodeId(@Param("authCodeId") String authCodeId);

    int deleteByAuthCodeId(@Param("authCodeId") String authCodeId);
}
