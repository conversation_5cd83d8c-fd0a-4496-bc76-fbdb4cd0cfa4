package cn.ijiami.detection.mapper;

import java.util.List;

import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @Date 2020/8/19
 **/
public interface TPrivacyPolicyItemMapper extends IjiamiMapper<TPrivacyPolicyItem> {

    List<TPrivacyPolicyItem> findAll();

    List<TPrivacyPolicyItem> findByTerminalType(@Param("terminalType") int terminalType);
}
