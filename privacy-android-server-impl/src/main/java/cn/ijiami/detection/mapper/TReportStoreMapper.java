package cn.ijiami.detection.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import cn.ijiami.detection.entity.TReportStore;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 报告存储mapper
 *
 * <AUTHOR>
 * @since 2019年2月25日
 */
public interface TReportStoreMapper extends IjiamiMapper<TReportStore> {

    /**
     * 根据documentId查询报告信息
     *
     * @param documentId 文档id
     * @return 报告信息
     */
    @Select("select * from t_report_store where document_id = #{documentId} and type=#{type} limit 1")
    TReportStore findByDocumentIdAndType(@Param("documentId") String documentId, @Param("type") Integer type);

    @Select("select * from t_report_store where bussiness_id = #{bussinessId} and type=#{type} order by create_time desc limit 1")
    TReportStore findByBussinessIdAndType(@Param("bussinessId") String bussinessId, @Param("type") Integer type);
    
    @Select("select * from t_report_store where bussiness_id = #{bussinessId} and type!=10 and param_md5=#{paramMd5} order by create_time desc limit 1")
    TReportStore findByBussinessIdAndParamMd5(@Param("bussinessId") String bussinessId, @Param("paramMd5") String paramMd5);
    
    @Select("select * from t_report_store where type=10 and bussiness_id = #{bussinessId} and param_md5=#{paramMd5} order by create_time desc limit 1")
    TReportStore getCreateReportStatus(@Param("bussinessId") String bussinessId, @Param("paramMd5") String paramMd5);
}
