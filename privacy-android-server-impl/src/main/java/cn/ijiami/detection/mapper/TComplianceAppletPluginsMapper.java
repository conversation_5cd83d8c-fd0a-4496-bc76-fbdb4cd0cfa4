package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TComplianceAppletPluginsMapper extends IjiamiMapper<TComplianceAppletPlugins> {

    List<TComplianceAppletPlugins> selectAllPluginsByTaskId(@Param(value = "taskId")Long taskId);
}
