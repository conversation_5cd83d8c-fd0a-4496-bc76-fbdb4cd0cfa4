package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyVO;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.entity.TPrivacyPolicy;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @description
 * @date 2019-03-25 10:35
 */
public interface TPrivacyPolicyMapper extends IjiamiMapper<TPrivacyPolicy> {

    PrivacyPolicyVO findByPrivacyIdAndTaskId(@Param("privacyId") Long privacyId, @Param("taskId") Long taskId);

    List<PrivacyCheckVO> selectPrivacyPolicy(@Param("taskId") Long taskId, @Param("type") Integer type, @Param("terminalType") Integer terminalType);

    List<Integer> distinctTypeByTaskId(@Param("taskId") Long taskId);

    List<TPrivacyPolicyType> countLawByTaskId(@Param("taskId") Long taskId,@Param("terminalType") Integer terminalType);

    TPrivacyPolicyType countLawByTaskIdAndType(@Param("taskId") Long taskId, @Param("type") Integer type,@Param("terminalType") Integer terminalType);

    /**
     * 根据文档ID查询法律法规的检测数据
     *
     * @param documentId
     * @return
     */
    List<TPrivacyPolicyType> countLawByDocumentId(@Param("documentId") String documentId);

    List<TPrivacyCheck> findByTypeVo(@Param("type") Integer type,@Param("terminalType") Integer terminalType);

    TPrivacyPolicyType findByPrivacyByType(@Param("lawType") Integer lawType);

    int deleteByTaskIdAndType(@Param("taskId") Long taskId, @Param("type") Integer type);

    /**
     * 查询检测点
     *
     * @param terminalType 终端类型
     * @param lawType 法律法规
     * @return
     */
    List<PrivacyCheckVO> selectPrivacyCheckByTerminalAndLawType(@Param("terminalType") Integer terminalType,@Param("lawType") Integer lawType);

}