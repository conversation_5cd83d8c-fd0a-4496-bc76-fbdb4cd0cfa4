package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.statistics.SdkUsageResult;
import cn.ijiami.detection.entity.TSdkDectDetail;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TSdkDectDetailMapper.java
 * @Description sdk使用统计记录
 * @createTime 2022年07月05日 15:26:00
 */
public interface TSdkDectDetailMapper extends IjiamiMapper<TSdkDectDetail> {

    List<SdkUsageResult> findSdkUsageStatistics(@Param("userId") Long userId,
                                                @Param("limit") Integer limit,
                                                @Param("terminalType") Integer terminalType,
                                                @Param("startDate") Date startDate,
                                                @Param("endDate") Date endDate,
                                                @Param("assetsName") String assetsName);

    void deleteByTaskId(@Param("taskId") Long taskId);

    void deleteByAssetsId(@Param("assetsId") Long assetsId);
}