package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TCategorySensitiveWord;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019/11/7 16:08
 */
public interface TCategorySensitiveWordMapper extends IjiamiMapper<TCategorySensitiveWord> {

    List<Long> findSensitiveIdsByCategoryIds(@Param("categoryIds") List<Long> categoryIds);
}
