package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TDeepShakeValue;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TDeepShakeValueMapper extends IjiamiMapper<TDeepShakeValue> {

    List<TDeepShakeValue> getShakeValueByTaskId(@Param("taskId") Long taskId);

    int updateShakeStatusById(@Param("id") Long id,@Param("flag") Integer flag);
    int updateViolationById(@Param("id") Long id,@Param("isViolation") Integer isViolation);
    int updateDescribeById(@Param("id") Long id,@Param("description") String description);

    void deleteAllValueByTaskId(@Param("taskId") Long taskId);
}
