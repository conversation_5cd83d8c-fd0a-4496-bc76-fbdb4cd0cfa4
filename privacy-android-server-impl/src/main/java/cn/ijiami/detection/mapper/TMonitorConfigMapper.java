package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TMonitorConfigEntity;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-28 15:19
 */
@Mapper
public interface TMonitorConfigMapper extends IjiamiMapper<TMonitorConfigEntity> {


    List<TMonitorConfigEntity> selectCleanShow();

    @Select("select id,code,name,value from t_monitor_config where group_id=2 and is_show=1")
    List<TMonitorConfigEntity> selectAlertShow();

    @Select("select * from t_monitor_config where group_id=#{groupId}")
    List<TMonitorConfigEntity> selectAllByGroup(int groupId);

    @Update(value = "update t_monitor_config set value = now() where code = #{code}")
    void updateTime(String code);

    @Update(value = "update t_monitor_config set value = #{value} where code = #{code}")
    void updateValue(@Param("code") String code,@Param("value") String value);
}
