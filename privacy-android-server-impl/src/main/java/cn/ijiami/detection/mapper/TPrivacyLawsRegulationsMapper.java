package cn.ijiami.detection.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.VO.PrivacyLawsVO;
import cn.ijiami.detection.entity.TPrivacyLawsRegulations;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TPrivacyLawsRegulationsMapper extends IjiamiMapper<TPrivacyLawsRegulations> {

    /**
     * 查询有效的法规条款
     *
     * @return
     */
    List<LawDetectResultVO> selectValidTerms(@Param("lawId") Long lawId);

    /**
     * 查询所有法规
     *
     * @return
     */
    List<Map<String, Object>> selectAllLaw(@Param("taskId") Long taskId);

    /**
     * 根据任务ID查询法规数据
     *
     * @param taskId
     * @return
     */
    List<TPrivacyPolicyType> selectLawByTaskId(@Param("taskId") Long taskId);

    /**
     * 查询有效的法规条款
     *
     * @return
     */
    List<LawDetectResultVO> selectByTerminalType(@Param("terminalType") Integer terminalType);

    List<TPrivacyLawsRegulations> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询可检测的法规类型
     * @param terminalType
     * @return
     */
    List<PrivacyLawsVO> selectPrivacyLawsParentName(@Param("terminalType") Integer terminalType);


    String selectLawNameByLawId(@Param("terminalType") Integer terminalType, @Param("lawId") Long lawId);
    
}