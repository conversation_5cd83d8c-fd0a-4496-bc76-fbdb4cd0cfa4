package cn.ijiami.detection.mapper;

import java.util.List;
import java.util.Map;

import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.query.LawQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-05-27 09:55
 */
public interface TPrivacyPolicyTypeMapper extends IjiamiMapper<TPrivacyPolicyType> {

    List<PrivacyCheckVO> findByTaskIdAndType(@Param("taskId") Long taskId, @Param("type") Integer type, @Param("terminalType") Integer terminalType);

    @Select("select t2.id,t2.type,t2.law_name from t_privacy_check t1 join t_privacy_policy_type t2 on t1.type_id = t2.id where t2.status=2 and t2.is_del=0 and t1.is_show=1 and t1.status=2 and t1.is_del=0 and t1.terminal_type=#{terminalType} group by t2.type")
    List<TPrivacyPolicyType> findLaw(@Param("terminalType") Integer terminalType);

    /**
     * 统计法规数量
     *
     * @return
     */
    int countLawNumber(@Param("terminalType") Integer terminalType);

    List<PrivacyPolicyTypeVO> selectLaws(LawQuery lawQuery);
    /**
     * Description:获取同一终端类型的最大法规类别
     *
     * @Author:lyl
     * @Date:2023/11/30 11:46
     */
    int selectMaxType(Integer terminalType);

    TPrivacyPolicyType selectPolicyByIdAndTerminalType(@Param("id") Long id,@Param("terminalType") Integer terminalType);

    void deleteById(@Param("id") Long id,@Param("userId") Long userId);

    TPrivacyPolicyType selectByLawName(@Param("lawName") String lawName,@Param("terminalType") Integer terminalType);
}
