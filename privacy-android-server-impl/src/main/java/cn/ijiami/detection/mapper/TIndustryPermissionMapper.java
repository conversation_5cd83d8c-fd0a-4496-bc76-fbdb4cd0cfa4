package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TIndustryPermission;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @description
 * @date 2019-03-23 16:12
 */
public interface TIndustryPermissionMapper extends IjiamiMapper<TIndustryPermission> {

    List<TIndustryPermission> findByCategoryIds(@Param("categoryIds") List<Long> categoryIds);
}
