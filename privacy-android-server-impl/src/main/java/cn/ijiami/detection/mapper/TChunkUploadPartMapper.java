package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TChunkUploadPart;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TChunkUploadPartMapper extends IjiamiMapper<TChunkUploadPart> {

    @Select("select * from t_chunk_upload_part where file_id=#{fileId} order by chunk_num")
    List<TChunkUploadPart> selectByFileId(@Param("fileId") Long fileId);

    @Delete("delete from t_chunk_upload_part where file_id=#{fileId}")
    Integer delByFileId(@Param("fileId") Long fileId);
}
