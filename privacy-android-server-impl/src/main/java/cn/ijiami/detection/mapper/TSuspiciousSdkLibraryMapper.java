package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TSuspiciousSdkLibrary;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSuspiciousSdkLibraryMapper extends IjiamiMapper<TSuspiciousSdkLibrary> {

    List<TSuspiciousSdkLibrary> findInPackageName(@Param("packageNameList") List<String> packageNameList);

    List<TSuspiciousSdkLibrary> findInId(@Param("idList") List<Long> idList);
}
