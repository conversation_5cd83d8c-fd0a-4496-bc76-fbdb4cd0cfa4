package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.statistics.RiskResult;
import cn.ijiami.detection.entity.TAppLawsRiskCollect;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TAppLawsRiskCollectMapper extends IjiamiMapper<TAppLawsRiskCollect> {

    List<RiskResult> findRiskAssets(@Param("userId") Long userId,
                                    @Param("lawId") Integer lawId,
                                    @Param("startDate") Date startDate,
                                    @Param("endDate") Date endDate,
                                    @Param("assetsName") String assetsName,
                                    @Param("limit") Integer limit);

    List<RiskResult> findRiskAssetsInName(@Param("userId") Long userId,
                                          @Param("lawId") Integer lawId,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate,
                                          @Param("nameList") List<String> nameList);

    List<RiskResult> findRiskLawItem(@Param("userId") Long userId,
                                     @Param("name") String name,
                                     @Param("version") String version,
                                     @Param("lawId") Integer lawId,
                                     @Param("startDate") Date startDate,
                                     @Param("endDate") Date endDate);

    Integer detectionCount(@Param("userId") Long userId, @Param("name") String name, @Param("version") String version, @Param("lawId") Integer lawId);

    Integer detectionTotalCount(@Param("userId") Long userId,
                                @Param("lawId") Integer lawId,
                                @Param("startDate") Date startDate,
                                @Param("endDate") Date endDate,
                                @Param("assetsName") String assetsName,
                                @Param("packageNameList") List<String> packageNameList);

    void deleteByTaskId(@Param("taskId") Long taskId);

    void deleteByAssetsId(@Param("assetsId") Long assetsId);
}
