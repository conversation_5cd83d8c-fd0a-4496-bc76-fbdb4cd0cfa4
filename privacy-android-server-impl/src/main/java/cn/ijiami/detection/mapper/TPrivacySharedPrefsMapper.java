package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.CountSharedPrefsNameVO;
import cn.ijiami.detection.VO.CountSharedPrefsTypeVO;
import cn.ijiami.detection.VO.SharedPrefsExcelReportVO;
import cn.ijiami.detection.VO.StoragePersonalMessageAndType;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2020/1/2 15:35
 */
public interface TPrivacySharedPrefsMapper extends IjiamiMapper<TPrivacySharedPrefs> {

    List<CountSharedPrefsTypeVO> countSharedPrefsTypeByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<CountSharedPrefsNameVO> countSharedPrefsNameByTaskId(@Param("taskId") Long taskId, @Param("typeId") Long typeId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySharedPrefs> findByTaskIdAndTypeIdAndName(@Param("taskId") Long taskId, @Param("typeId") Long typeId, @Param("name") String name, @Param("behaviorStage") Integer behaviorStage);

    void deleteByTaskId(@Param("taskId") Long taskId);

    List<TPrivacySharedPrefs> findByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySharedPrefs> findByTaskIdAndBehaviorStageAndQuerys(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage, @Param("executors") String executors, @Param("executorType") String executorType,
                                                                    @Param("typeName") String typeName, @Param("personalName") String personalName, @Param("sortType") Integer sortType, @Param("sortOrder") Integer sortOrder,
                                                                    @Param("terminalType") Integer terminalType);

    List<String> getBehaviorStorage(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<String> getBehaviorStorageMessageType(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<String> getBehaviorStoragePersonalMessage(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<StoragePersonalMessageAndType> getStoragePersonalMessageList(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<StoragePersonalMessageAndType> getStorageMessageTypeList(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);
    
    List<SharedPrefsExcelReportVO> findMisjudgmentData(@Param("createUserId") Long createUserId,@Param("startTime") String startTime,@Param("endTime") String endTime);

    TPrivacySharedPrefs selectDataById(@Param("id") Long id);
}