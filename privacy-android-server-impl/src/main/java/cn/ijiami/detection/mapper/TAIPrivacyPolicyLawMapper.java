package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TAIChatMessage;
import cn.ijiami.detection.entity.TAIPrivacyPolicyLaw;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAIPrivacyPolicyLawMapper.java
 * @Description ai助手移动应用隐私相关的法规
 * @createTime 2024年09月24日 18:53:00
 */
public interface TAIPrivacyPolicyLawMapper extends IjiamiMapper<TAIPrivacyPolicyLaw> {
}
