package cn.ijiami.detection.mapper;

import java.util.Collection;
import java.util.List;

import cn.ijiami.detection.VO.PersonalActionVO;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019/11/12 15:34
 */
public interface TActionNougatMapper extends IjiamiMapper<TActionNougat> {

   TActionNougat  findByTerminalTypeAndActionId(@Param("actionId")String actionId,@Param("TerminalType")Integer terminalType);

   List<TActionNougat> findByTerminalType(@Param("TerminalType")Integer terminalType);

   List<TActionNougat> findInActionId(@Param("actionIdList") Collection<String> actionIdList);

   List<TActionNougat> findInTerminalTypeAndActionId(@Param("actionIdList") List<String> actionIdList,@Param("terminalType") TerminalTypeEnum terminalType);

   List<Long> findActionIdByNames(@Param("actionNameList") Collection<String> actionNameList, @Param("terminalType") TerminalTypeEnum terminalType);

   List<PersonalActionVO> selectPersonalAction(@Param("terminalType") Integer terminalType);
}
