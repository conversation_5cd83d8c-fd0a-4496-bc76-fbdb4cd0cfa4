package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TTaskQueue;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface TTaskQueueMapper extends IjiamiMapper<TTaskQueue> {

	/**
	 * 待解析的数据
	 * @return
	 */
	List<TTaskQueue> waitQueueList(@Param("dataSources") String dataSources,@Param("size") Integer size, @Param("strategy") Integer strategy);
	
	TTaskQueue findQueueByBussinessId(@Param("bussinessId") String bussinessId);

	@Select("select count(1) from t_task_queue where create_time>CURDATE() and queue_status=1")
	int getWaitNum();

	@Select("select count(1) from t_task_queue where create_time>CURDATE() and queue_status=3")
	int getFailNum();

}