package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.entity.TPrivacyLawsDetail;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TPrivacyLawsDetailMapper extends IjiamiMapper<TPrivacyLawsDetail> {

    /**
     * 查询除行为信息以外数据
     *
     * @param taskId
     * @param itemNo
     * @return
     */
    List<TPrivacyLawsDetail> selectOtherDetailByDataType(@Param("taskId") Long taskId, @Param("itemNo") String itemNo);

    /**
     * 查询行为数据信息
     *
     * @param taskId
     * @param itemNo
     * @return
     */
    List<LawActionDetailVO> selectActionDetailByTaskIdAndItemNo(@Param("taskId") Long taskId,
                                                                @Param("itemNo") String itemNo,
                                                                @Param("dataType") Integer dataType,
                                                                @Param("actionPermissionAliases") List<String> actionPermissionAliases,
                                                                @Param("actionIds") List<String> actionIds,
                                                                @Param("executors") List<String> executors);

    /**
     * 根绝任务id删除
     *
     * @param taskId
     */
    void deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 获取所有检测点数据信息
     *
     * @param taskId 任务ID
     * @return
     */
    List<LawActionDetailVO> selectAllItemDetailByTaskId(@Param("taskId") Long taskId, @Param("lawId") Integer lawId, @Param("dataType") Integer dataType);

    /**
     * 查询除所有行为信息以外数据
     *
     * @param taskId
     * @return
     */
    List<TPrivacyLawsDetail> selectAllItemOtherDetailByDataType(@Param("taskId") Long taskId, @Param("lawId") Integer lawId);
}