package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TPrivacyCategory;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-08-26 14:34
 */
public interface TPrivacyCategoryMapper extends IjiamiMapper<TPrivacyCategory> {

    List<String> findCategoryByTaskId(@Param("taskId") Long taskId);

    List<TPrivacyCategory> findByTaskId(@Param("taskId") Long taskId);

    void deleteByTaskId(@Param("taskId") Long taskId);
}
