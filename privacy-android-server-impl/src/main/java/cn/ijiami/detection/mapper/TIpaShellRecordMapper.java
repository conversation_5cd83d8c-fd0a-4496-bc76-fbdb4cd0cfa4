package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import cn.ijiami.detection.entity.TIpaShellRecord;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 砸壳服务记录
 *
 * @author:LXD
 * @description
 * @date: 2020/6/5
 */
public interface TIpaShellRecordMapper extends IjiamiMapper<TIpaShellRecord> {

    /**
     * 根据id更新砸壳记录
     *
     * @param taskId
     * @param descp
     * @param status
     * @param resultJson
     * @param updateTime
     */
    @Update(value = "update t_ipa_shell_record set descp = #{descp},status = #{status}, update_time = #{updateTime},result_json = #{resultJson} where task_id  = #{taskId} AND status != 4")
    void updateByTaskId(@Param("taskId") Long taskId, @Param("descp") String descp, @Param("status") int status, @Param("resultJson") String resultJson,
                        @Param("updateTime") Date updateTime);

    /**
     * 根据资产ID查询砸壳记录
     *
     * @param assetId
     * @return
     */
    TIpaShellRecord selectOneByAssetId(@Param("assetId") Long assetId);

    /**
     * 根据资产ID查询砸壳记录
     *
     * @param taskId
     * @return
     */
    TIpaShellRecord selectOneByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据状态查询 砸壳记录
     *
     * @param status
     * @return
     */
    List<Long> selectByStatus(@Param("status") Integer status, @Param("maxShellCount") Integer maxShellCount);

    /**
     * 更新脱壳的状态
     *
     * @param shellId
     * @param oldStatus
     * @param newStatus
     * @return
     */
    int updateStatusById(@Param("shellId") Long shellId, @Param("oldStatus") Integer oldStatus, @Param("newStatus") Integer newStatus);

    /**
     * 根据状态查询应用
     *
     * @param status
     * @return
     */
    List<Long> selectAssetByStatus(@Param("status") Integer status, @Param("maxShellCount") Integer maxShellCount);

    /**
     * 根据状态查询任务
     *
     * @param status
     * @return
     */
    List<Long> selectTaskByStatus(@Param("status") Integer status, @Param("maxShellCount") Integer maxShellCount);

    /**
     * 查询失效的脱壳记录
     *
     * @param second 秒
     * @return
     */
    List<TIpaShellRecord> selectFailRecordByTime(@Param("second") int second);

    List<TIpaShellRecord> selectWaitRecordOverShellCount(@Param("maxShellCount") Integer maxShellCount);

    /**
     * 根据状态查询砸壳记录列表
     *
     * @param status
     * @return
     */
    List<TIpaShellRecord> selectShellRecordByStatus(@Param("status") Integer status);

    /**
     * 查询状态为等待砸壳和砸壳中的列表记录
     * @return
     */
    List<TIpaShellRecord> selectAllShellRecordByStatus();

    int updateTimeByTaskId(@Param("taskId") Long taskId);

    @Update(value = "update t_ipa_shell_record set is_need_remind=1 where task_id=#{taskId}")
    int updateNeedRemind(@Param("taskId") Long taskId);
    
    /**
     * 砸壳重签超过30分钟设置为超时
     * @return
     */
    List<TIpaShellRecord> ipaShellRecordTimeOut();
}
