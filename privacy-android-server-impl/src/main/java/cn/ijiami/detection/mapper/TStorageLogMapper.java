package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import cn.ijiami.detection.enums.StorageFileType;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TStorageLog;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 文件储存记录DAO
 *
 * <AUTHOR>
 * @date 2020/7/11 11:08
 **/
public interface TStorageLogMapper extends IjiamiMapper<TStorageLog> {

    /**
     * 根据文件地址更新
     *
     * @param storageAddress
     * @param assetsId
     * @param gmtModified
     */
    void updateByStorageAddress(@Param("storageAddress") String storageAddress,
                                @Param("assetsId") Long assetsId,
                                @Param("gmtModified") Date gmtModified,
                                @Param("createUserId") Long createUserId);

    /**
     * 根据文件地址更新
     *
     * @param gmtModified
     */
    void updateByAssociationKey(@Param("associationKey") String associationKey,
                                @Param("gmtModified") Date gmtModified,
                                @Param("createUserId") Long createUserId);

    /**
     * 查询未使用的存储记录
     *
     * @return
     */
    List<TStorageLog> selectUnusedStorage();


    /**
     * 查询未删除存储记录
     *
     * @return
     */
    List<TStorageLog> findNullAssetsIdByAddress(@Param("storageAddress") String storageAddress, @Param("createUserId") Long createUserId);


    /**
     * 查询未删除存储记录
     *
     * @return
     */
    TStorageLog findOneByAssociationKey(@Param("associationKey") String associationKey,
                                        @Param("fileTypeList") List<StorageFileType> fileTypeList,
                                        @Param("createUserId") Long createUserId);

    /**
     * 查询包括已删除的存储记录
     *
     * @return
     */
    List<TStorageLog> findAllByAddress(@Param("storageAddress") String storageAddress);


    /**
     * 查询包括已删除的存储记录
     *
     * @return
     */
    Long countAssetsIdNotNullByAddress(@Param("storageAddress") String storageAddress);

    /**
     * 查询任务文件的存储记录
     *
     * @return
     */
    Long countTaskFileByTaskId(@Param("associationKey") String associationKey, @Param("storageType") Integer storageType);


    /**
     * 移除文件的资源记录，每天2点定时器会去清除这些文件，然后再把状态更新为已删除
     *
     * @param storageAddress
     * @param assetsId
     * @param gmtModified
     */
    void markWaitDeleteByStorageAddress(@Param("storageAddress") String storageAddress, @Param("assetsId") Long assetsId, @Param("gmtModified") Date gmtModified);

    /**
     * 移除文件的资源记录，每天2点定时器会去清除这些文件，然后再把状态更新为已删除
     *
     * @param associationKey
     */
    void markWaitDeleteByAssociationKey(@Param("associationKey") String associationKey, @Param("storageType") Integer storageType, @Param("gmtModified") Date gmtModified);


    /**
     * 更新文件状态为已删除
     *
     * @param storageAddress
     * @param assetsId
     * @param gmtModified
     */
    void updateDeleteByStorageAddress(@Param("storageAddress") String storageAddress, @Param("assetsId") Long assetsId, @Param("gmtModified") Date gmtModified);
}