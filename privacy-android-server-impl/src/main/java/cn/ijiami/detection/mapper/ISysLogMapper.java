package cn.ijiami.detection.mapper;

import java.util.List;

import cn.ijiami.detection.query.SysLogQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import cn.ijiami.manager.syslog.entity.TsysOperateLog;

/**
 * 个人信息检测审计日志Mapper
 */
public interface ISysLogMapper extends IjiamiMapper<TsysOperateLog>{

    /**
     * 通过实体条件查询系统日志
     *
     * @param query
     *            (日志条件对象)
     * @return List<User> (日志集合)
     */
    List<TsysOperateLog> selectSysLogByQuery(SysLogQuery query);
}
