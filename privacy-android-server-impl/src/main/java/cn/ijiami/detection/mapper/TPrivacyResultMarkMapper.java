package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TPrivacyResultMark;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TPrivacyResultMarkMapper extends IjiamiMapper<TPrivacyResultMark> {

    String findLatestMarkDesc(@Param("bId") Long bId);

    List<TPrivacyResultMark> findByResultType(@Param("bId") Long bId, @Param("resultType") LawResultTypeEnum resultType);


    TPrivacyResultMark findLatestMarkByResultType(@Param("bId") Long bId, @Param("resultType") LawResultTypeEnum resultType);

}