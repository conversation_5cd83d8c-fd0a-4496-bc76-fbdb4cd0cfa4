package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import cn.ijiami.detection.VO.statistics.*;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.LawConclusionVO;
import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.VO.PrivacyLawsMisjudgmentVO;
import cn.ijiami.detection.entity.TPrivacyLawsResult;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 法规检测条款结果
 *
 * <AUTHOR>
 * @date 2020/12/26 15:39
 **/
public interface TPrivacyLawsResultMapper extends IjiamiMapper<TPrivacyLawsResult> {

    /**
     * @param taskId
     * @return
     */
    List<LawDetectResultVO> selectResultByTaskId(@Param("taskId") Long taskId, @Param("itemResultStatus") Integer itemResultStatus, @Param("lawId") Long lawId);

    /**
     * @param taskId
     * @return
     */
    List<LawDetectResultVO> selectActionByTaskId(@Param("taskId") Long taskId, @Param("itemResultStatus") Integer itemResultStatus);

    LawDetectResultVO selectItemResult(@Param("taskId") Long taskId, @Param("itemNo") String itemNo);

    /**
     * @param taskId
     */
    void deleteByTaskId(@Param("taskId") Long taskId);

    /**
     * 查询误判的数据
     * @param createUserId
     * @param startTime
     * @param endTime
     * @return
     */
    List<PrivacyLawsMisjudgmentVO> findMisjudgmentData(@Param("createUserId") Long createUserId,@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("lawId") Integer lawId);

    List<TPrivacyLawsResult> findResultStatusInTaskId(@Param("taskIds") List<Long> taskIds);
    
    List<LawConclusionVO> selectLawConclusionByTaskId(Long taskId);

    LawConclusionVO selectLawConclusionByTaskIdAndItemNo(Long taskId, String itemNo);

    List<DetectFalsePositivesApp> findDetectFalsePositivesAssets(@Param("userId") Long userId,
                                                                 @Param("lawId") Integer lawId,
                                                                 @Param("startDate") Date startDate,
                                                                 @Param("endDate") Date endDate,
                                                                 @Param("assetsName") String assetsName,
                                                                 @Param("limit") Integer limit);


    List<DetectFalsePositivesLaw> findDetectFalsePositivesLawItem(@Param("userId") Long userId,
                                                                  @Param("lawId") Integer lawId,
                                                                  @Param("startDate") Date startDate,
                                                                  @Param("endDate") Date endDate,
                                                                  @Param("assetsName") String assetsName,
                                                                  @Param("limit") Integer limit);

    List<DetectFalsePositivesAppLaw> findDetectFalsePositivesAssetsInName(@Param("userId") Long userId,
                                                                          @Param("lawId") Integer lawId,
                                                                          @Param("startDate") Date startDate,
                                                                          @Param("endDate") Date endDate,
                                                                          @Param("nameList") List<String> nameList);

    List<DetectFalsePositivesApp> findDetectFalsePositivesAssetsByPage(@Param("userId") Long userId,
                                                                       @Param("lawId") Integer lawId,
                                                                       @Param("startDate") Date startDate,
                                                                       @Param("endDate") Date endDate,
                                                                       @Param("assetsName") String assetsName);

    List<RiskResult> findDetectFalsePositivesLawItemDetail(@Param("userId") Long userId,
                                                           @Param("name") String name,
                                                           @Param("version") String version,
                                                           @Param("lawId") Integer lawId,
                                                           @Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate);

    List<DetectFalsePositivesReportAssetsInfo> findDetectFalsePositivesReport(@Param("userId") Long userId,
                                                                              @Param("assetsName") String assetsName,
                                                                              @Param("lawIds") List<Integer> lawIds,
                                                                              @Param("startDate") Date startDate,
                                                                              @Param("endDate") Date endDate);

    List<RiskResult> findDetectFalsePositivesAssetsByLawItemId(@Param("userId") Long userId,
                                                               @Param("lawsItemId") Long lawsItemId,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate,
                                                               @Param("assetsName") String assetsName);

    Integer detectFalsePositivesTotalCountByLawItem(@Param("userId") Long userId, @Param("name") String name,
                                                    @Param("version") String version,
                                                    @Param("terminalType") Integer terminalType,
                                                    @Param("lawId") Integer lawId,
                                                    @Param("lawsItemId") Long lawsItemId);

    Integer detectFalsePositivesTotalCount(@Param("userId") Long userId,
                                           @Param("lawId") Integer lawId,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("assetsName") String assetsName,
                                           @Param("packageNameList") List<String> packageNameList);
}