package cn.ijiami.detection.mapper;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TReportDesign;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TReportDesignMapper extends IjiamiMapper<TReportDesign> {

    TReportDesign findByUserIdAndReportObject(@Param("userId") Long userId, @Param("reportObject") Integer reportObject,@Param("terminalType") Integer terminalType);
}