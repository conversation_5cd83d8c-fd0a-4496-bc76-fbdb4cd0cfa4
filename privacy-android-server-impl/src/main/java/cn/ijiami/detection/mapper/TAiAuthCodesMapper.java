package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TAiAuthCodes;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAiAuthCodes.java
 * @Description ai授权码
 * @createTime 2025年03月24日 11:33:00
 */
public interface TAiAuthCodesMapper extends IjiamiMapper<TAiAuthCodes> {
    List<TAiAuthCodes> findByClientId(@Param("clientId") Long clientId);

    TAiAuthCodes findByAuthCodeId(@Param("authCodeId") String authCodeId);

    int updateRemainingQuota(@Param("authCodeId") String authCodeId, @Param("remainingQuota") Integer remainingQuota);

    int deleteByAuthCodeId(@Param("authCodeId") String authCodeId);
}