package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TIosDeviceConnection;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface TIosDeviceConnectionMapper extends IjiamiMapper<TIosDeviceConnection> {

        TIosDeviceConnection findByConnectionId(@Param("connectionId") String connectionId);

        TIosDeviceConnection findByTaskId(@Param("taskId") Long taskId);

        List<TIosDeviceConnection> findByTaskIds(@Param("taskIdList") Collection<Long> taskIdList);

        void freeTimeoutFrpcPort(@Param("qrCodeTimeout") Long qrCodeTimeout, @Param("taskTimeout") Long taskTimeout);

        List<String> getTimeoutFrpcPort(@Param("qrCodeTimeout") Long qrCodeTimeout, @Param("taskTimeout") Long taskTimeout);

        List<String> findUsedFrpcPort(@Param("frpcUrl") String frpcUrl);
}
