package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UserAppletDeviceStatusEnum;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TUserUseDevice;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TUserUseDeviceMapper extends IjiamiMapper<TUserUseDevice> {


    /**
     * 用户使用中的设备数
     * @param userId
     * @param terminalType
     * @return
     */
    Long findUsingCount(@Param("userId") Long userId, @Param("terminalType") Integer terminalType);

    /**
     * 用户占用的设备数，占用数=预占数+使用数
     * @param userId
     * @param terminalType
     * @param preemptTimeout
     * @param startTimeout
     * @return
     */
    Long findPreemptCount(@Param("userId") Long userId,
                          @Param("terminalType") Integer terminalType,
                          @Param("preemptTimeout") Date preemptTimeout,
                          @Param("startTimeout") Date startTimeout);


    /**
     * 找到正在使用和预占的设备
     * @param userId
     * @param businessId
     * @param preemptTimeout
     * @param startTimeout
     * @return
     */
    List<TUserUseDevice> findUserDevices(@Param("userId") Long userId,
                                         @Param("businessId") Long businessId,
                                         @Param("preemptTimeout") Date preemptTimeout,
                                         @Param("startTimeout") Date startTimeout);
    
    
    /**
     * 获取小程序使用状态(超过1个小时候的数据)
     * @param terminalType
     * @param appletStatus
     * @return
     */
    List<TUserUseDevice> findAppletUseDevicesTimeOut(@Param("terminalType") Integer terminalType, 
    										  @Param("appletStatusList") List<UserAppletDeviceStatusEnum> appletStatusList, @Param("interval") Integer interval);
    
    
    /**
     * 获取小程序使用状态(1个小时候内的数据)
     * @param terminalTypeList
     * @param appletStatusList
     * @return
     */
    List<TUserUseDevice> findAppletUseDevices(@Param("terminalTypeList") List<TerminalTypeEnum> terminalTypeList,
                                              @Param("appletStatusList") List<UserAppletDeviceStatusEnum> appletStatusList, @Param("interval") Integer interval);
    
    /**
     * 获取某个用户最后一次使用小程序的设备
     * @param terminalType
     * @param appletStatusList
     * @param userId
     * @return
     */
    TUserUseDevice findAppletUseDevicesByUserId(@Param("terminalType") Integer terminalType, 
    											@Param("appletStatusList") List<UserAppletDeviceStatusEnum> appletStatusList,
    											@Param("userId") Long userId);

    /**
     * 获取设备id最后一次使用记录
     * @param terminalType
     * @param businessId
     * @param deviceSerial
     * @return
     */
    TUserUseDevice findLastUseDeviceByDeviceSerial(@Param("terminalType") Integer terminalType,
                                                @Param("businessId") Long businessId,
                                                @Param("deviceSerial") String deviceSerial);

}