package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TPrivacyLawsConclusionAction;
import cn.ijiami.detection.entity.TPrivacyLawsPrivacyPolicyActionKeyWords;
import cn.ijiami.detection.entity.TPrivacyLawsPrivacyPolicyKeyWords;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TPrivacyLawsPrivacyPolicyKeyWordsMapper extends IjiamiMapper<TPrivacyLawsPrivacyPolicyKeyWords> {

    List<TPrivacyLawsPrivacyPolicyActionKeyWords> findKeyWordsInActionId(@Param("actionIds") List<Long> actionIds);


    List<TPrivacyLawsPrivacyPolicyActionKeyWords> findPersonalActionKeyWords();


    List<TPrivacyLawsPrivacyPolicyActionKeyWords> findAllActionKeyWords();


    TPrivacyLawsPrivacyPolicyActionKeyWords findKeyWordsByActionId(@Param("actionId") Long actionId);


    TPrivacyLawsPrivacyPolicyActionKeyWords findKeyWordsByItemNo(@Param("itemNo") String itemNo);

}
