package cn.ijiami.detection.mapper;

import java.util.List;

import cn.ijiami.detection.VO.DetectionTemplateTypeVO;
import cn.ijiami.detection.entity.TDetectionTemplate;
import cn.ijiami.detection.query.DetectionTemplateQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 检测模版映射类
 *
 * <AUTHOR>
 */
public interface TDetectionTemplateMapper extends IjiamiMapper<TDetectionTemplate> {

    /**
     * 根据检测模版对象查询检测模版集合
     *
     * @param detectionTemplate
     * @return
     */
    List<TDetectionTemplate> selectDetectionTemplateList(DetectionTemplateQuery detectionTemplateQuery);

    List<DetectionTemplateTypeVO> selectDetectionTemplateList1(DetectionTemplateQuery detectionTemplateQuery);
}