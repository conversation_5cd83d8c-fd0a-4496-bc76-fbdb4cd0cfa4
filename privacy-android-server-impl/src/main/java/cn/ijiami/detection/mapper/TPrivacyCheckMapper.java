package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-05-27 09:56
 */
public interface TPrivacyCheckMapper extends IjiamiMapper<TPrivacyCheck> {

    List<TPrivacyCheck> findByIds(@Param("ids") List<Long> ids);

    /**
     * Description:根据条件查询检测项具体明细，如果存在相同的数据则展示最新一条
     * 同一个typeId存在多条数据只展示最新一条
     *
     * @Author:lyl
     * @Date:2023/11/30 9:54
     */
    List<TPrivacyCheck> selectPrivacyByTypeId(@Param("terminalType") Integer terminalType,@Param("typeId") Long typeId);

    void updatePrivacyCheckById(@Param("id") Long id, @Param("status") Integer status, @Param("updateTime") Date date, @Param("updateUserId") Long updateUserId);

    /**
     * Description:根据法规id查询出所有检测项
     *
     * @Author:lyl
     * @Date:2023/12/4 10:08
     */
    List<TPrivacyCheck> selectByTypeIdAndTerminalType(@Param("typeId") Long typeId,@Param("terminalType") Integer teminalType);

    int deleteById(@Param("id") Long id,@Param("userId") Long userId);

    Map<String,Integer> selectMaxSort(@Param("typeName") String typeName, @Param("typeId") Long typeId);

    List<TPrivacyCheck> findAllCachePrivacyByTypeId(@Param("typeId") Long type,@Param("terminalType") Integer terminalType);

    /**
     * 查找相同类型的检测项
     * @param typeId
     * @param typeName
     * @return
     */
    List<TPrivacyCheck> findCheckByTypeName(@Param("typeId") Long typeId,@Param("typeName") String typeName);

    List<TPrivacyCheck> findAllNoCachePrivacyByTypeId(@Param("typeId") Long typeId,@Param("terminalType") Integer teminalType);
}
