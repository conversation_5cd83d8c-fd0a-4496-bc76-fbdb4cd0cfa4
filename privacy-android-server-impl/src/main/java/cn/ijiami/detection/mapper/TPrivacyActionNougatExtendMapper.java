package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.ActionNameAndPermission;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.PrivacyActionNougatVO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/12 15:34
 */
public interface TPrivacyActionNougatExtendMapper extends IjiamiMapper<TPrivacyActionNougatExtend> {

    void deleteByTaskId(@Param("taskId") Long taskId);

}
