package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.StaticFunctionBehaviorVO;
import cn.ijiami.detection.VO.StaticFunctionDetailVO;
import cn.ijiami.detection.entity.TStaticFunctions;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TStaticFunctionsMapper extends IjiamiMapper<TStaticFunctions> {

    List<TStaticFunctions> findByTaskId(@Param("taskId") long taskId);


    List<StaticFunctionBehaviorVO> findBehaviorByTaskId(@Param("taskId") long taskId, @Param("privacyList") List<PrivacyStatusEnum> privacyList);


    List<StaticFunctionDetailVO> findDetailByTaskIdAndActionId(@Param("taskId") long taskId, @Param("actionId") long actionId);
}
