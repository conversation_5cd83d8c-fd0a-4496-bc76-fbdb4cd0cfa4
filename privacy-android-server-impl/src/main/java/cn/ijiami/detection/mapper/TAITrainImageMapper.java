package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TAITrainImage;
import cn.ijiami.detection.enums.AITrainImageStatusEnum;
import cn.ijiami.detection.query.AITrainImageVo;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TAITrainImageMapper extends IjiamiMapper<TAITrainImage> {


    List<AITrainImageVo> findImage(@Param("statusList")List<AITrainImageStatusEnum> statusList);

}
