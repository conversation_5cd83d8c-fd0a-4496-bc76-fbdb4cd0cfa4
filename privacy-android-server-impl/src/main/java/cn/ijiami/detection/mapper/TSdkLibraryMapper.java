package cn.ijiami.detection.mapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.sdk.IosPrivacySdk;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.query.CustomSdkQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSdkLibraryMapper extends IjiamiMapper<TSdkLibrary> {

    SdkVO findByPackageNameAndTaskId(@Param("packagesName") String packagesName, @Param("taskId") Long taskId);


    SdkVO findByNameAndTaskId(@Param("sdkName") String sdkName, @Param("taskId") Long taskId);

    Set<Long> findByTypeName(@Param("typeName") String typeName);

    void updatePermissionCodesById(@Param("permissionCodes") String permissionCodes, @Param("id") Long id);

    List<TSdkLibrary> findAll();

    List<TSdkLibrary> findByTerminalType(@Param("terminalType") Integer terminalType);


    List<TSdkLibrary> findFirstPartyByTerminalType(@Param("terminalType") Integer terminalType);

    TSdkLibrary findByNameAndVersion(@Param("sdkName") String sdkName, @Param("version") String version);

    List<TSdkLibrary> findInSdkName(@Param("sdkNameList") Collection<String> sdkNameList);

    List<TSdkLibrary> findInPackageName(@Param("packagesNameList") Collection<String> packagesNameList);

    /**
     * 根据包名列表 SDK来源查询数据
     * @param packagesNameList 包名列表
     * @param source 数据来源 2自研sdk 3全国管理平台SDK
     */
    List<TSdkLibrary> findInPackageNameSource(@Param("packagesNameList") Collection<String> packagesNameList, @Param("source") Integer source, @Param("terminalType") Integer terminalType);

    List<TSdkLibrary> findInSdkNameSource(@Param("sdkNameList") Collection<String> sdkNameList, @Param("source") Integer source, @Param("terminalType") Integer terminalType);

    List<TSdkLibrary> findBaseSdkInPackage(@Param("packageList") Collection<String> packageList);
    
    List<TSdkLibrary> findInMd5(@Param("md5List") Collection<String> md5List);

    TSdkLibrary findByMd5AndVersion(@Param("md5") String md5, @Param("version") String version);

    List<TSdkLibrary> findCustomSdkByPage(@Param("query") CustomSdkQuery query);

    List<TSdkLibrary> findHaveAliasByTerminalType(@Param("terminalType") Integer terminalType);

    Long countByNameAndVersion(@Param("sdkName") String sdkName,
                               @Param("version") String version,
                               @Param("terminalType") Integer terminalType,
                               @Param("packagesNameList") Collection<String> packagesNameList);
    
    /**
     * 获取隐私清单和签名的SDK
     * @return
     */
   List<IosPrivacySdk> getIosPrivacySdkList();
}