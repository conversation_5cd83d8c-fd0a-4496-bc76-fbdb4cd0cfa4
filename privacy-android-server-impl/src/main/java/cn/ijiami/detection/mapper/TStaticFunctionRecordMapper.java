package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TStaticFunctionRecord;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TStaticFunctionRecordMapper extends IjiamiMapper<TStaticFunctionRecord> {

    void updateProgress(@Param("id") long id, @Param("progress") int progress, @Param("status") int status);

    void updateStatus(@Param("id") long id, @Param("status") int status);

    void incrementShellCount(@Param("id") long id, @Param("updateTime") Date updateTime);

    TStaticFunctionRecord findByTaskId(@Param("taskId") long taskId);

    TStaticFunctionRecord findSuccessByAssetsId(@Param("assetsId") long assetsId);

    TStaticFunctionRecord findByRequestId(@Param("requestId") String requestId);

    List<TStaticFunctionRecord> findByTimeout(@Param("timeoutMinute") long timeoutMinute);
}
