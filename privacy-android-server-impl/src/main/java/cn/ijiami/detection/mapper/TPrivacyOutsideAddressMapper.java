package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.CountOutsideTypeVO;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019/12/3 14:51
 */
public interface TPrivacyOutsideAddressMapper extends IjiamiMapper<TPrivacyOutsideAddress> {

    List<TPrivacyOutsideAddress> findByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacyOutsideAddress> findByTaskIdAndOutside(@Param("taskId") Long taskId, @Param("outside") Integer outside, @Param("behaviorStage") Integer behaviorStage);

    List<CountOutsideTypeVO> countByOutside(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    void deleteByTaskId(@Param("taskId") Long taskId);

    List<TPrivacyOutsideAddress> findByTaskIdAndOutsideIos(@Param("taskId") Long taskId, @Param("outside") Integer outside, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacyOutsideAddress> findByTaskIdAndOutsideStackInfo(@Param("taskId") Long taskId,@Param("ip") String ip ,@Param("host") String host, @Param("behaviorStage") Integer behaviorStage);

    List<String> getOutSideAddressExecutor(@Param("taskId") Long taskId);

    List<TPrivacyOutsideAddress> findOutsideDataByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage, @Param("executors") String executors, @Param("executorTypeString") String executorTypeString,
                                                         @Param("outsideString") String outsideString, @Param("protocol") String protocol, @Param("sortType") Integer sortType, @Param("sortOrder") Integer sortOrder,
                                                         @Param("cookieMark") Integer cookieMark, @Param("terminalType") Integer terminalType);

    List<String> getBehaviorNoun(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);
    List<String> getBehaviorNounOfProtocol(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);
    
    List<TPrivacyOutsideAddress> findByTaskIdAndOutsideAndSdkId(@Param("taskId") Long taskId, @Param("outside") Integer outside, @Param("sdkId") String sdkId);
    
    List<TPrivacyOutsideAddress> findByTaskIdAndOutsideAndSdkName(@Param("taskId") Long taskId, @Param("outside") Integer outside, @Param("sdkName") String sdkName);

    TPrivacyOutsideAddress selectDetailDataByOutsideId(@Param("id") Long id);
}
