package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.SensitiveTypeVO;
import cn.ijiami.detection.entity.TSensitiveType;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSensitiveTypeMapper extends IjiamiMapper<TSensitiveType> {

	List<TSensitiveType> selectSensitiveTypeList(TSensitiveType sensitiveType);

	List<SensitiveTypeVO> findByTaskId(@Param("taskId") Long taskId);
}