package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TSdkLibraryClass;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TSdkLibraryClassMapper extends IjiamiMapper<TSdkLibraryClass> {

    List<TSdkLibraryClass> findBySdkId(@Param("sdkId") Long sdkId);

}
