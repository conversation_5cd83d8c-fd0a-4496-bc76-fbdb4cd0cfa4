package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import cn.ijiami.detection.entity.TTemplateDetectionItem;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 用户检测项中间表映射类
 * 
 * <AUTHOR>
 *
 */
public interface TTemplateDetectionItemMapper extends IjiamiMapper<TTemplateDetectionItem> {

	/**
	 * 批量新增用户检测项中间表
	 * 
	 * @param templateId
	 * @param detectionItemIds
	 */
	int insertTemplateDetectionItem(List<TTemplateDetectionItem> templateDetectionItems);

	/**
	 * 根据模版ID 删除 中间表
	 * 
	 * @param templateId
	 * @return
	 */
	int deleteDetectionItemByTemplateId(@Param("templateId") Long templateId);

	@Select("select detection_item_id from t_template_detection_item where template_id = #{templateId}")
	List<Long> selectDetectionItemId(@Param("templateId") Long templateId);
}