package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TAiApiCalls;
import cn.ijiami.detection.entity.TAiQuotaUpdates;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TAiQuotaUpdatesMapper extends IjiamiMapper<TAiQuotaUpdates> {

    List<TAiQuotaUpdates> findByAuthCodeId(@Param("authCodeId") String authCodeId);

    int deleteByAuthCodeId(@Param("authCodeId") String authCodeId);

}