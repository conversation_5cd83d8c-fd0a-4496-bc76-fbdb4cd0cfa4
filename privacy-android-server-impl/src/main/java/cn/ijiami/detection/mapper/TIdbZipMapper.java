package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TIdbZip;
import cn.ijiami.framework.mybatis.IjiamiMapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TIdbZipMapper extends IjiamiMapper<TIdbZip> {

    @Select(value = "select * from t_idb_zip where type_id=#{typeId} limit 1")
    TIdbZip findOnlyOne(Long typeId);
    
    @Delete(value = "delete from t_task where task_id = #{id} ")
    void deleteById(@Param("id") Long id);
    
    void insertIdb(@Param("fileName")String fileName,@Param("fileUrl")String fileUrl);

    int updateIdb(@Param("id")Integer id,@Param("fileName")String fileName,@Param("fileUrl")String fileUrl);
}
