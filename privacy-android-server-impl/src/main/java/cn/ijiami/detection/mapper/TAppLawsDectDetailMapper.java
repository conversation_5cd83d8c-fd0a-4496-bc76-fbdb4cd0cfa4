package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.statistics.RiskResult;
import cn.ijiami.detection.entity.TAppLawsDectDetail;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAppLawsDectDetailMapper.java
 * @Description 统计风险详情表（只存有风险的条文）
 * @createTime 2022年07月05日 15:24:00
 */
public interface TAppLawsDectDetailMapper extends IjiamiMapper<TAppLawsDectDetail> {

    List<RiskResult> findRiskAssetsByPage(@Param("userId") Long userId,
                                          @Param("lawId") Integer lawId,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate,
                                          @Param("assetsName") String assetsName);

    List<RiskResult> findRiskLaw(@Param("userId") Long userId,
                                 @Param("lawId") Integer lawId,
                                 @Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate,
                                 @Param("assetsName") String assetsName,
                                 @Param("limit") Integer limit);

    List<RiskResult> findRiskAssetsByLawItemId(@Param("userId") Long userId,
                                               @Param("lawsItemId") Long lawsItemId,
                                               @Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate,
                                               @Param("assetsName") String assetsName);

    Integer nonComplianceTotalCountByAssetsName(@Param("userId") Long userId, @Param("name") String name,
                                                @Param("version") String version,
                                                @Param("lawId") Integer lawId,
                                                @Param("lawsItemId") Long lawsItemId);


    Integer nonComplianceTotalCount(@Param("userId") Long userId,
                                    @Param("lawId") Integer lawId,
                                    @Param("startDate") Date startDate,
                                    @Param("endDate") Date endDate,
                                    @Param("assetsName") String assetsName,
                                    @Param("terminalType") TerminalTypeEnum terminalType,
                                    @Param("packageNameList") List<String> packageNameList);

    void deleteByTaskId(@Param("taskId") Long taskId);

    void deleteByAssetsId(@Param("assetsId") Long assetsId);
}
