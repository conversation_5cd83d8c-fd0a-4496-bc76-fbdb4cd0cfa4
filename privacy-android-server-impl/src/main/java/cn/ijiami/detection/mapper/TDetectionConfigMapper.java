package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.DetectionConfigItem;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.entity.TDetectionConfig;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TRoleDetectionConfigurationMapper.java
 * @Description 用户检测功能配置
 * @createTime 2023年06月21日 12:01:00
 */
public interface TDetectionConfigMapper extends IjiamiMapper<TDetectionConfig> {

    List<DetectionConfigItem> findList(@Param("name") String name, @Param("sortType") Integer sortType, @Param("sortOrder") Integer sortOrder);

    List<TDetectionConfigVO> findJobIdConfig();
    
    /**
     * 查询所有指定设备的用户，除当前用户外
     * @param createUserId
     * @return
     */
    List<String> getAllUserAppointDevice(@Param("createUserId") Long createUserId);
}
