package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.SystemUpdateDetailVO;
import cn.ijiami.detection.VO.SystemUpdateItemVO;
import cn.ijiami.detection.entity.TSystemUpdate;
import cn.ijiami.detection.enums.SystemUpdateStatusEnum;
import cn.ijiami.detection.enums.SystemUpdateTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TSystemUpdateMapper extends IjiamiMapper<TSystemUpdate> {

    List<SystemUpdateItemVO> findItemByPage();

    List<SystemUpdateDetailVO> findDetailByPage(@Param("name") String name, @Param("type") SystemUpdateTypeEnum type);

    void updateFinishById(@Param("id") Long id,
                            @Param("updateStatus") SystemUpdateStatusEnum updateStatus,
                            @Param("previousVersionId") Long previousVersionId,
                            @Param("updateUserId") Long updateUserId);

    void updateStatusById(@Param("id") Long id,
                          @Param("updateStatus") SystemUpdateStatusEnum updateStatus,
                          @Param("updateUserId") Long updateUserId);

    void deleteUpdate(@Param("id") Long id, @Param("updateUserId") Long updateUserId);

    List<String> getNameList();
}