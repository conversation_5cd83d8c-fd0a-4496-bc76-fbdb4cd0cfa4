package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.UserSdkAliasVO;
import cn.ijiami.detection.entity.TUserSdkAlias;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TUserSdkAliasMapper  extends IjiamiMapper<TUserSdkAlias> {

    List<UserSdkAliasVO> findLawSdkAlias(@Param("userId") Long userId);

    List<UserSdkAliasVO> findUserSdkAlias(@Param("userIdList") List<Long> userIdList, @Param("name") String name, @Param("packageName") String packageName);

}
