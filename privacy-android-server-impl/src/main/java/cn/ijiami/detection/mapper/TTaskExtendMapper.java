package cn.ijiami.detection.mapper;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskExtend;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TTaskExtendMapper extends IjiamiMapper<TTaskExtend> {


	/**
	 * 根据任务ID，获取业务扩展信息
	 *
	 * @param taskId
	 * @return
	 */
	TTaskExtendVO findTaskByTaskId(@Param("taskId") Long taskId);

	/**
	 * 根据任务ID，获取业务扩展信息
	 *
	 * @param taskId
	 * @return
	 */
	List<TTaskExtendVO> findTaskByTaskIds(@Param("taskIdList") Collection<Long> taskId);


//	TTaskExtendVO findTaskExtendByBusinessId(@Param("bussinessId") String bussinessId);
	/**
	 * 待推送的数据
	 * @return
	 */
	List<TTaskExtendVO> waitPushList();
	
	/**
	 * 根据第三方业务ID获取任务信息
	 *
	 * @param bussinessId 第三方业务ID
	 * @return
	 */
	TTask findTaskByBussinessId(@Param("bussinessId") String bussinessId);
	
	TTask findTaskByBussinessIdDynamicDesc(@Param("bussinessId") String bussinessId);
	
	/**
	 * 根据用户ID 获取到用户法规权限信息
	 * @param userId
	 * @return
	 */
	List<String> findPushlawRole(@Param("userId") Long userId);
}