package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.CustomLawsGroupVO;
import cn.ijiami.detection.entity.TCustomLawsGroup;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TCustomLawsGroupMapper extends IjiamiMapper<TCustomLawsGroup> {


    List<CustomLawsGroupVO> findGroupList(@Param("terminalType") TerminalTypeEnum terminalType, @Param("name") String name);

    Integer countGroup(@Param("groupId") Long groupId,
                       @Param("terminalType") TerminalTypeEnum terminalType);

}
