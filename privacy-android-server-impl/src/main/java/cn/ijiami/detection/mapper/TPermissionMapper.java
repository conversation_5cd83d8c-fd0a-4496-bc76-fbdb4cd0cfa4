package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.UserPermissionVO;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TPermissionMapper extends IjiamiMapper<TPermission> {

	/**
	 * 根据敏感权限对象查询用户铭感权限VO
	 *
	 * @param permission
	 * @return
	 */
	List<UserPermissionVO> selectPermissionByPage(TPermission permission);

	List<UserPermissionVO> selectAllPermission(TPermission permission);

	List<PermissionVO> findPermissionByPermissionCodes(@Param("permissionCodes") String permissionCodes);

	List<PermissionVO> findByPermissionCodeList(@Param("permissionCodes") List<String> permissionCodes);
	
	List<PermissionVO> findPermissionByPermissionCodesSdk(@Param("permissionCodes") String permissionCodes, @Param("taskId") Long taskId,  @Param("sdkId") Long sdkId);

	List<PermissionVO> findAll(@Param("terminalType")Integer terminalType);

	List<TPermission> findByTerminalType(@Param("name")String name,@Param("terminalType")Integer terminalType);

	List<String> getPermissionCodes(@Param("actionIdList") List<Long> actionIdList);

	List<TPermission> getPermissionByPermissionNames(@Param("nameList") List<String> permissionNames,@Param("terminalType")Integer terminalType);
}