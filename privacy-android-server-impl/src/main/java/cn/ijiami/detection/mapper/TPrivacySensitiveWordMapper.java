package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.CountSensitiveNameVO;
import cn.ijiami.detection.VO.CountSensitiveTypeVO;
import cn.ijiami.detection.VO.PersonalMessageAndType;
import cn.ijiami.detection.VO.SensitiveWordExcelReportVO;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-05-29 10:14
 */
public interface TPrivacySensitiveWordMapper extends IjiamiMapper<TPrivacySensitiveWord> {

    List<TPrivacySensitiveWord> findByTypeIdAndTaskId(@Param("typeId") Long typeId, @Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySensitiveWord> findByTaskIdAndMethod(@Param("taskId") Long taskId, @Param("method") String method, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySensitiveWord> findByTaskIdAndSdkId(@Param("taskId") Long taskId, @Param("sdkId") Long sdkId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySensitiveWord> findByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySensitiveWord> findBySdk();

    List<CountSensitiveTypeVO> countSensitiveTypeByTaskId(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<CountSensitiveNameVO> countSensitiveNameByTaskId(@Param("taskId") Long taskId, @Param("typeId") Long typeId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacySensitiveWord> findByTaskIdAndTypeIdAndName(@Param("taskId") Long taskId, @Param("typeId") Long typeId, @Param("name") String name, @Param("behaviorStage") Integer behaviorStage);

    void deleteByTaskId(@Param("taskId") Long taskId);

    List<TPrivacySensitiveWord> findByTaskIdAndAllQuerys(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage,
                                                         @Param("executors") String executors, @Param("executorType") String executorType,
                                                         @Param("typeName") String typeName, @Param("personalName") String personalName,
                                                         @Param("sortType") Integer sortType, @Param("sortOrder") Integer sortOrder,
                                                         @Param("cookieMark") Integer cookieMark, @Param("terminalType") Integer terminalType,
                                                         @Param("plaintextTransmission") Integer plaintextTransmission);

    List<String> getBehaviorTransmission(@Param("taskId")Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<String> getBehaviorBehaviorMessageType(@Param("taskId")Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<String> getBehaviorPersonalMessage(@Param("taskId")Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<PersonalMessageAndType> getBehaviorPersonalList(@Param("taskId")Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<PersonalMessageAndType> getBehaviorMessageTypeList(@Param("taskId")Long taskId,@Param("behaviorStage") Integer behaviorStage);
    
    List<SensitiveWordExcelReportVO> findMisjudgmentData(@Param("createUserId") Long createUserId,@Param("startTime") String startTime,@Param("endTime") String endTime);

    TPrivacySensitiveWord selectDataById(@Param("id") Long id);
}
