package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.ijiami.detection.entity.TTaskTypeCount;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import org.apache.ibatis.annotations.*;

import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.Privacy164ResultVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

@Mapper
public interface TTaskMapper extends IjiamiMapper<TTask> {

    /**
     * 分页查询任务
     *
     * @return
     */
    public List<TTask> findTaskByPage(TaskQuery taskQuery);

    /**
     * 根据文档id查询任务状态   deng
     *
     * @param documentId
     * @return
     */
    public Map<String, Object> queryTaskMessageByDocumentId(String documentId);

    /**
     * 逻辑删除
     *
     * @param id 主键
     */
//    @Update(value = "update t_task set is_delete=1 where task_id = #{id} ")
//    void deleteById(@Param("id") Long id);
    @Delete(value = "delete from t_task where task_id = #{id} ")
    void deleteById(@Param("id") Long id);

    @Update(value = "update t_task set static_task_sort = static_task_sort - 1 where static_task_sort > #{staticTaskSort} and task_tatus = 1 ")
    void updateStaticTaskSort(@Param("staticTaskSort") Integer staticTaskSort);

    @Update(value = "update t_task set dynamic_task_sort = dynamic_task_sort - 1 where dynamic_task_sort > #{dynamicTaskSort} and dynamic_status = 1")
    void updateDynamicTaskSort(@Param("dynamicTaskSort") Integer dynamicTaskSort);

    /**
     * 逻辑删除
     *
     * @param id 主键
     */
//    @Update(value = "update t_task set is_delete=1 where task_id = #{id} and create_user_id=#{userId} ")
//    void deleteByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
    
    @Delete(value = "delete from t_task where task_id = #{id} and create_user_id=#{userId} ")
    void deleteByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 根据documentId删除
     *
     * @param ids
     */
    void deleteByApkDetectionDetailId(@Param("ids") List<String> ids);

    /**
     * 根据documentId获取task_id
     *
     * @param documentId
     * @return
     */
    @Select("select task_id from t_task where apk_detection_detail_id = #{documentId} ")
    Long findTaskIdByDocumentId(@Param("documentId") String documentId);

    List<BigDataVO> findDetectedMd5();

    TTask findByMd5(@Param("md5") String md5);

    TTask findByDeviceSerial(@Param("deviceSerial") String deviceSerial);

    TTask findByDeviceHardwareSerial(@Param("deviceHardwareSerial") String deviceHardwareSerial);

    TTask findByTaskIdForUpdate(@Param("taskId") Long taskId);

    TTask findByDocumentId(@Param("documentId") String documentId);

    /**
     * 查询静态检测超时
     *
     * @return
     */
    List<TTask> findStaticDataTimeOut(@Param("terminalType") Integer terminalType);

    /**
     * 查询检测完成的检测任务
     *
     * @param begin
     * @param end
     * @return
     */
    List<String> selectDocumentIdByTime(@Param("begin") Date begin, @Param("end") Date end);

    /**
     * 查询检测中的动态检测任务
     *
     * @param userId
     * @return
     */
    Integer getDynamicTaskCount(@Param("userId") Long userId, @Param("terminalTypeList") List<TerminalTypeEnum> terminalTypeList, @Param("detectionType") Integer detectionType);

    /**
     * 查询任务管理器中正在动态检测或者法规检测的任务
     *
     * @param userId
     * @param terminalType
     * @return
     */
    List<TTask> getDynamicTaskList(@Param("userId") Long userId, @Param("terminalType") Integer terminalType, @Param("detectionType") Integer detectionType);

    Integer selectDynamicTaskByUserId(@Param("userId") Long userId, @Param("terminalType") Integer terminalType, @Param("detectionType") Integer detectionType);

    List<TTaskTypeCount> taskTypeCount(TaskQuery taskQuery);

    List<TTask> findNeedToCheckDynamicTask(@Param("terminalType") Integer terminalType);

    List<TTask> findDynamicDataTimeOut(@Param("terminalType") Integer terminalType, @Param("timeout") Date timeout);

    List<TTask> findDynamicDataWaitTimeOut(@Param("terminalType") Integer terminalType, @Param("timeout") Date timeout);

    List<TTask> findLawDataTimeOut(@Param("terminalType") Integer terminalType);

    List<TTask> findReviewDataTimeOut(@Param("terminalTypeList") List<TerminalTypeEnum> terminalTypeList);

    Integer getMinStaticWaitSort(@Param("terminalType") Integer terminalType);

    Integer getMinDynamicWaitSort(@Param("terminalType") Integer terminalType);

    Integer getMaxStaticWaitSort(@Param("terminalType") Integer terminalType);

    Integer getMaxDynamicWaitSort(@Param("terminalType") Integer terminalType);

    List<TTask> getDynamicWaitSortList(@Param("terminalType") Integer terminalType, @Param("userId") Long userId);

    List<TTask> getStaticWaitSortList(@Param("terminalType") Integer terminalType, @Param("userId") Long userId);

    List<TTask> getAiDetectWaitingForLogin(@Param("terminalType") Integer terminalType, @Param("aiDetectLoginStatus") Integer aiDetectLoginStatus,
                                      @Param("userId") Long userId);

    Integer countUserSandyBoxTask(@Param("userId") Long userId);

    Integer countActiveTask(@Param("userId") Long userId,
                            @Param("detectionType") Integer detectionType,
                            @Param("terminalType") Integer terminalType);

    Integer countStaticTask(@Param("userId") Long userId,
                            @Param("detectionType") Integer detectionType,
                            @Param("terminalType") Integer terminalType);


    Integer getDynamicTaskDeviceCount(@Param("userId") Long userId,
                                      @Param("terminalTypeList") List<TerminalTypeEnum> terminalTypeList, @Param("dynamicDeviceType") Integer dynamicDeviceType);

    List<TTask> getStaticWaitTask();

    List<TTask> getAndroidDynamicWaitTask();

    List<TTask> getDynamicWaitTask(@Param("terminalType") Integer terminalType);

    List<TTask> getIosDynamicWaitTask();

    List<TTask> getAppletWaitTask();

    Integer getStaticCheckTask(@Param("terminalTypeList") List<Integer> terminalTypeList);

    Integer getSpeedCheckTask(@Param("terminalTypeList") List<Integer> terminalTypeList);

    List<TTask> getFastDynamicTask(@Param("terminalType") Integer terminalType);

    TTask findDetectionCompleteByMd5(@Param("md5") String md5);
    
    /**
     * 查询164号文结果数据
     * @param taskIds
     * @param resultStatus
     * @return
     */
    List<Privacy164ResultVO> find164DetectionResultList(@Param("taskIds") List<Long> taskIds, @Param("resultStatus") Integer resultStatus);

    /**
     * 查询164号文结果数据
     * @param taskIds
     * @param resultStatus
     * @return
     */
    List<Privacy164ResultVO> find164DetectionResultListNew(@Param("taskIds") List<Long> taskIds, @Param("resultStatus") Integer resultStatus);

    Long countWaitingOrDetectionInByAssetsId(@Param("assetsId") Long assetsId, @Param("createUserId") Long createUserId);

    Long countWaitingOrDetectionInByMd5(@Param("createUserId") Long createUserId, @Param("md5") String md5);

    Long findAssetsLastTaskDynamicDetectDuration(@Param("assetsId") Long assetsId);

    Long findAssetsLastTaskStaticDetectDuration(@Param("assetsId") Long assetsId);

    Long findAssetsStaticDetectDuration(@Param("assetsId") Long assetsId);

    @Select("select task_id from t_task where assets_id = #{assetsId} ")
    List<Long> getTaskByAssetsId(@Param("assetsId") Long assetsId);

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1")
    int getTodayNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1 and dynamic_status=6")
    int getTodayFinishNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1 and dynamic_status=1")
    int getTodayWaitNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1 and dynamic_status in (4,7,8,9,10) and data_path is null")
    int getTodayDetectingNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1 and dynamic_status =5")
    int getTodayFailNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and detection_type=1 and dynamic_status =4 and data_path is not null")
    int getTodayWarehousingNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and TIMESTAMPDIFF(MINUTE, task_starttime, task_endtime)<30 and detection_type=1 and dynamic_status =6")
    int getTodayDetectTimeLowNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and TIMESTAMPDIFF(MINUTE, task_starttime, task_endtime) between 30 and 45 and detection_type=1 and dynamic_status =6")
    int getTodayDetectTimeMediumNum();

    @Select("select count(1) from t_task where task_starttime>CURDATE() and TIMESTAMPDIFF(MINUTE, task_starttime, task_endtime) > 45 and detection_type=1 and dynamic_status =6")
    int getTodayDetectTimeHighNum();

    @Select("select count(1) from t_task where create_user_id=#{createUserId} and task_tatus=2")
    int getWaitStaticDetectionCountByUserId(@Param("createUserId") Long createUserId);
}