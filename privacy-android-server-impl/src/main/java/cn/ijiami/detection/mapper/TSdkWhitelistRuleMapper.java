package cn.ijiami.detection.mapper;

import java.util.List;

import org.springframework.data.repository.query.Param;

import cn.ijiami.detection.entity.TSdkWhitelistRule;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSdkWhitelistRuleMapper extends IjiamiMapper<TSdkWhitelistRule> {

    public List<String> findPackageNameByType(@Param("type") Integer type);

    public List<String> findPatternByType(@Param("type") Integer type);
}
