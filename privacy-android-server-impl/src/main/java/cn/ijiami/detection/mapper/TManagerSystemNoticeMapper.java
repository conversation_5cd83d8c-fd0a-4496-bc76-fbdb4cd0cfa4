package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.ManagerSystemNoticeVO;
import cn.ijiami.detection.entity.TManagerSystemNotice;
import cn.ijiami.detection.enums.SystemNoticeSendStatusEnum;
import cn.ijiami.detection.enums.SystemNoticeTypeEnum;
import cn.ijiami.detection.enums.SystemNoticeValidityPeriodEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TManagerSystemNoticeMapper extends IjiamiMapper<TManagerSystemNotice> {

    List<ManagerSystemNoticeVO> findByPage(@Param("type") SystemNoticeTypeEnum type,
                                           @Param("templateId") Long templateId,
                                           @Param("validityPeriod") SystemNoticeValidityPeriodEnum validityPeriod,
                                           @Param("sendStatus") SystemNoticeSendStatusEnum sendStatus,
                                           @Param("sortOrder") Integer sortOrder);

}
