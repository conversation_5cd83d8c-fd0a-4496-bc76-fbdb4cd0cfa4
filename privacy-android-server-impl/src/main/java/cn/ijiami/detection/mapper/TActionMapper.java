package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.ActionVO;
import cn.ijiami.detection.entity.TAction;
import cn.ijiami.detection.query.ActionQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TActionMapper extends IjiamiMapper<TAction> {

	/**
	 * 查询行为函数列表
	 * 
	 * @return List<ActionVO>
	 */
	public List<ActionVO> findActionList(ActionQuery actionQuery);

	/**
	 * 根据函数名查询行为信息
	 */
	public ActionVO findActionByFunction(@Param("function") String function);
}