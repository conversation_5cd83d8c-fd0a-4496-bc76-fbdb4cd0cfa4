package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import cn.ijiami.detection.entity.TPrivacyPolicyImg;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-06-03 10:25
 */
public interface TPrivacyPolicyImgMapper extends IjiamiMapper<TPrivacyPolicyImg> {

    /**
     * 根据任务ID及法律法规检测点id查询数据
     * @param taskId 任务id
     * @param policyId 法律法规检测点
     * @return
     */
    @Select("SELECT a.file_key AS fileKey FROM t_privacy_policy_img a WHERE a.task_id=#{taskId} AND a.policy_id=#{policyId} ")
    List<String> selectByTaskIdAndPolicyId(@Param("taskId") Long taskId,@Param("policyId") Long policyId);

    /**
     * 根据任务ID获取所有截图Key值
     *
     * @param taskId
     * @return
     */
    @Select("SELECT a.file_key AS fileKey,a.policy_id AS policyId FROM t_privacy_policy_img a WHERE a.task_id=#{taskId}")
    List<TPrivacyPolicyImg> selectImgByTaskId(@Param("taskId") Long taskId);
}
