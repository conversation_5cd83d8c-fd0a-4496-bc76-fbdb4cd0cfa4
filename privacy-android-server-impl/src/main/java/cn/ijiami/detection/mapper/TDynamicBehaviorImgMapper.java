package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TDynamicBehaviorImg;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TDynamicBehaviorImgMapper extends IjiamiMapper<TDynamicBehaviorImg> {

    List<TDynamicBehaviorImg> findByActionNougatId(@Param("taskId") Long taskId,@Param("actionId") Long actionId,@Param("businessId") Long businessId,@Param("businessType") Integer businessType);
}
