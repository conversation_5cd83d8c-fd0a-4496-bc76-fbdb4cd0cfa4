package cn.ijiami.detection.mapper;

import cn.ijiami.detection.entity.TChannelNotification;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TChannelNotificationMapper extends IjiamiMapper<TChannelNotification> {

    @Delete("delete from t_channel_notification")
    int deleteAllData();
    List<TChannelNotification> findChannelByQuery(@Param("name")String name, @Param("ascList") List<String> ascList, @Param("descList") List<String> descList,
                                @Param("channelTypeList")List<Integer> channelTypeList,@Param("problemList") List<String> problemList,@Param("bulletinOrganList")List<String> bulletinOrganList);
}
