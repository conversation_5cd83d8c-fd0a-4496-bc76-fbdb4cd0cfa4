package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
*
* <AUTHOR>
* @Date 2020/8/19
**/
public interface TPrivacyPolicyResultMapper extends IjiamiMapper<TPrivacyPolicyResult> {

    List<TPrivacyPolicyResult> findByTaskId(@Param("taskId")long taskId);

    List<TPrivacyPolicyResult> findAppStoreByTaskId(@Param("taskId")long taskId);

    void deleteByTaskId(@Param("taskId") Long taskId);
    
    @Select("select * from t_privacy_policy_result where policy_item_id=2 and task_id = #{taskId} limit 1")
    TPrivacyPolicyResult findTaskIdByPolicyResult(@Param("taskId") Long taskId);
}
