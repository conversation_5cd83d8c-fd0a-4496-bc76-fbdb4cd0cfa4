package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TBigdataFunctional;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * @data 2021/6/11
 */
public interface TBigdataFunctionalMapper extends IjiamiMapper<TBigdataFunctional> {

    List<TBigdataFunctional> getBigDataFunctionalById(@Param("ids") List<Long> ids);
}
