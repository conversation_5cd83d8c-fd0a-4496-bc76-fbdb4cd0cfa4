package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.SuspiciousSdkDataVO;
import cn.ijiami.detection.entity.TSuspiciousSdk;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSuspiciousSdkMapper extends IjiamiMapper<TSuspiciousSdk> {

    List<SuspiciousSdkBehaviorVO> findBehaviorByTaskId(@Param("taskId") Long taskId);

    void deleteByTaskId(@Param("taskId") Long taskId);

    List<SuspiciousSdkBehaviorVO> findAndroidBehaviorByTaskDistinctId(@Param("taskId") Long taskId);
    
    List<SuspiciousSdkBehaviorVO> findIosBehaviorByTaskDistinctId(@Param("taskId") Long taskId);
    
    List<SuspiciousSdkDataVO> findSuspiciousSdkData(@Param("createUserId") Long createUserId,@Param("startTime") String startTime,@Param("endTime") String endTime);

}
