package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TClientUpdate;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019-08-27 10:09
 */
public interface TClientUpdateMapper extends IjiamiMapper<TClientUpdate> {

    /**
     * 查询最新版本信息
     * @param currentVersion 版本号 如：2300 实际是 2.3
     * @return 版本信息
     */
    List<TClientUpdate> findNewVersion(@Param("currentVersion") Integer currentVersion);
}
