package cn.ijiami.detection.mapper;

import java.util.List;

import cn.ijiami.detection.VO.AppStorePrivacyApiVO;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.ActionNameAndPermission;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.PrivacyActionNougatVO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * <AUTHOR>
 * @date 2019/11/12 15:34
 */
public interface TPrivacyActionNougatMapper extends IjiamiMapper<TPrivacyActionNougat> {

    /**
     * 根据taskId统计每个（非应用前）行为触发的次数
     *
     * @param taskId 任务id
     * @return 行为列表
     */
    List<TPrivacyActionNougat> countByTaskId(@Param("taskId") Long taskId);

    List<TPrivacyActionNougat> countActionByTaskId(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);

    /**
     * 根据任务id查询行为详细信息
     *
     * @param taskId   任务id
     * @return 行为列表
     */
    List<TPrivacyActionNougat> findByTaskId(@Param("taskId") Long taskId, @Param("actionIdList") List<Long> actionIdList,
                                            @Param("isPersonalList") List<PrivacyStatusEnum> isPersonalList,
                                            @Param("behaviorStageList")List<BehaviorStageEnum> behaviorStageList);

    /**
     * 根据id查询行为详细信息
     *
     * @param id  行为id
     * @return 行为列表
     */
    TPrivacyActionNougat findById(@Param("id") Long id);

    /**
     * 根据任务id和行为id查询行为详细信息
     *
     * @param taskId   任务id
     * @param actionId 行为id
     * @return 行为列表
     */
    List<TPrivacyActionNougat> findByTaskIdAndActionId(@Param("taskId") Long taskId, @Param("actionId") Long actionId);

    List<PermissionVO> countActionPermission(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    /**
     * 根据actionPermission和executorType分组统计行为使用的权限
     *
     * @param taskId 任务id
     * @return 权限列表
     */
    List<PermissionVO> countActionPermissionByTaskId(@Param("taskId") Long taskId);

    List<PermissionVO> countSdkPermission(@Param("taskId") Long taskId, @Param("packageName") String packageName);

    void deleteByTaskId(@Param("taskId") Long taskId);

    void deleteNotPersonalByTaskId(@Param("taskId") Long taskId);

    List<PrivacyActionNougatVO> countActionNougatByTaskId(@Param("taskId") Long taskId,@Param("behaviorStage") Integer behaviorStage);

    List<TPrivacyActionNougat> findByTaskIdExcel(Long taskId);

    Integer countBehaviorsCategoryByTaskId(@Param("taskId") Long taskId);

    List<TPrivacyActionNougat> countBehaviorsByTaskIdAndStage(@Param("taskId") Long taskId, @Param("behaviorStage") int behaviorStage);

    List<TPrivacyActionNougat> countBehaviorsByTaskId(@Param("taskId") Long taskId);

    int countBehaviorsByTaskIdAndExecutorAndStage(@Param("taskId") Long taskId, @Param("executorType") int executorType, @Param("behaviorStage") int behaviorStage);

    List<TPrivacyActionNougat> findByTaskIdAndBehaviorStage(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);
    
    List<TPrivacyActionNougat> findByTaskIdAndBehaviorStageNew(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage, @Param("packageName") String packageName);
    
    List<TPrivacyActionNougat> findByTaskIdAndActionIdAndExecutors(@Param("taskId") Long taskId, @Param("actionId") Long actionId, @Param("executors") String executors, @Param("behaviorStage") Integer behaviorStage);
    
    List<TPrivacyActionNougat> findByTaskIdAndActionIdAndExecutorsNew(@Param("taskId") Long taskId, @Param("actionId") Long actionId, @Param("executors") String executors, @Param("behaviorStage") Integer behaviorStage,@Param("packageName") String packageName);

    List<String> getBehaviorSdk(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<String> getBehaviorSdkPkg(@Param("taskId") Long taskId);

    List<String> getBehaviorPermission(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<AppStorePrivacyApiVO> getAppStorePrivacyApiList(@Param("taskId") Long taskId);

    List<ActionNameAndPermission> getBehaviorApplyName(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage,@Param("packageName") String packageName);

    List<ActionNameAndPermission> getLawDetailBehaviorApplyName(@Param("taskId") Long taskId, @Param("dataType") Integer dataType, @Param("itemNo") String itemNo);

    List<ActionNameAndPermission> getLawDetailBehaviorPermissionList(@Param("taskId") Long taskId, @Param("dataType") Integer dataType, @Param("itemNo") String itemNo);

    List<ActionNameAndPermission> getLawDetailBehaviorActionNameList(@Param("taskId") Long taskId, @Param("dataType") Integer dataType,@Param("itemNo") String itemNo);

    List<String> getLawDetailBehaviorSdk(@Param("taskId") Long taskId, @Param("dataType") Integer dataType,@Param("itemNo") String itemNo);

    List<String> getLawDetailBehaviorPermission(@Param("taskId") Long taskId, @Param("dataType") Integer dataType,@Param("itemNo") String itemNo);

    List<TPrivacyActionNougat> findByTaskIdAndActionIdAndQuerys(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage, @Param("packageName") String packageName,
                                                                @Param("executors") String executors,
                                                                @Param("actionPermissionAliases") String actionPermissionAliases,
                                                                @Param("actionIds") String actionIds,
                                                                @Param("executorTypeString") String executorTypeString,
                                                                @Param("isPersonal") String isPersonal,
                                                                @Param("sortType") Integer sortType,
                                                                @Param("sortOrder") Integer sortOrder,
                                                                @Param("terminalType") Integer terminalType);

    List<ActionNameAndPermission> getBehaviorActionNameList(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage,@Param("packageName") String packageName);

    List<ActionNameAndPermission> getBehaviorPermissionList(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage,@Param("packageName") String packageName);

    TPrivacyActionNougat getResultByIdAndTaskId(@Param("id") Long id);

    List<TPrivacyActionNougat> selectAllActionData(@Param("taskId") Long taskId, @Param("terminalType") Integer terminalType, @Param("isPersonal") Integer isPersonal);

    TPrivacyActionNougat selectDataById(@Param("id") Long id, @Param("packageName") String packageName);

    List<TPrivacyActionNougat> getByTaskIdAndActionId(@Param("taskId") Long taskId, @Param("actionId") Long actionId, @Param("executors") String executors, @Param("behaviorStage") Integer behaviorStage);

    Long countActionNougatByTaskIdAndStage(@Param("taskId") Long taskId, @Param("behaviorStage") Integer behaviorStage);

    List<TPrivacyActionNougat> getActionBehaviorByTaskId(@Param("taskId") Long taskId);

    void updateCycleTrigger(@Param("id") Long id, @Param("numberAction") Integer numberAction, @Param("triggerCycleTime") Long triggerCycleTime);
}
