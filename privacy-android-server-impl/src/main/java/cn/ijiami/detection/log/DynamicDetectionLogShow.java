package cn.ijiami.detection.log;

import java.util.Optional;

import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.helper.IosActionLogConvertHelper;
import org.apache.commons.lang.StringUtils;

//import com.ijiami.framework.utils.FileUtil;
import com.ijm.ios.RuntimeDetection.ctrl.IfsShow;
import com.ijm.ios.RuntimeDetection.data.InfoAct;
import com.ijm.ios.RuntimeDetection.data.InfoConfig;

import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.analyzer.json.config.JsonConfigFactory;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.service.api.ISendMessageService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

import static cn.ijiami.detection.utils.CommonUtil.isEmptyDetails;

/**
 * <AUTHOR>
 * @date 2020/6/11
 */
@Slf4j
public class DynamicDetectionLogShow implements IfsShow {

    private static final int MAX_INFO = 10000;
    private ISendMessageService messageService;
    private DynamicTaskContext taskContext;
    private DetectionDataService detectionDataService;
    private IosActionLogConvertHelper iosActionLogConvertHelper;

    public DynamicDetectionLogShow(ISendMessageService messageService, DynamicTaskContext taskContext,
                                   DetectionDataService detectionDataService, IosActionLogConvertHelper iosActionLogConvertHelper) {
        this.messageService = messageService;
        this.taskContext = taskContext;
        this.detectionDataService = detectionDataService;
        this.iosActionLogConvertHelper = iosActionLogConvertHelper;
    }

    @Override
    public void show(InfoAct infoAct) {
        try {
            log.info("行为id：desc={} stage={}", infoAct.getStrDesc(), infoAct.getStage());
            if (infoAct.getStrInfo().length() > MAX_INFO) {
                infoAct.setStrInfo(infoAct.getStrInfo().substring(MAX_INFO));
            }
            if (infoAct.getStrTrigger().length() > MAX_INFO) {
                infoAct.setStrTrigger(infoAct.getStrTrigger().substring(MAX_INFO));
            }
            if (infoAct.getStrType().equals(InfoConfig.STR_TYPE_NET)) {
                TPrivacyOutsideAddress outsideAddress = iosActionLogConvertHelper.buildPrivacyOutsideAddress(infoAct, taskContext);
                if (isEmptyDetails(outsideAddress.getProtocol()) && (isEmptyDetails(outsideAddress.getIp()) || isEmptyDetails(outsideAddress.getHost()))) {
                    log.info("网络数据错误");
                    return;
                }
                // 保存数据
                detectionDataService.insertIosNetAction(outsideAddress, taskContext);
                // 放到保存数据之后，需要获取主键
                RealTimeNetLog netLog = iosActionLogConvertHelper.buildNetLog(outsideAddress);
                // 清空详情，前端需要详情则去调用另外的详情接口
                netLog.setRequestData(StringUtils.EMPTY);
                netLog.setResponseData(StringUtils.EMPTY);
                // 日志实时展示
                messageService.sendTaskNetLogMessage(JSONObject.fromObject(netLog, JsonConfigFactory.buildDefault()), taskContext);
            } else if (infoAct.getStrType().equals(InfoConfig.STR_TYPE_FILE)) {
                // 文件行为不做处理
            } else {
                Optional<TActionNougat> nougatOptional = Optional.ofNullable(taskContext.getActionNougatMap().get(Long.parseLong(infoAct.getStrDesc().trim())));
                if (nougatOptional.isPresent()) {
                    TPrivacyActionNougat actionNougat = iosActionLogConvertHelper.buildPrivacyActionNougat(infoAct, nougatOptional.get(), taskContext);
                    // 保存
                    detectionDataService.insertDynamicAction(actionNougat, taskContext);
                    // 个人信息相关才推送给前端
                    if (PrivacyStatusEnum.getItem(nougatOptional.get().getPersonal()) == PrivacyStatusEnum.YES) {
                        // 过滤行为
                        if (BehaviorInfoAction.filterActionGroupRegex(taskContext.getActionFilterGroupRegexList(), nougatOptional.get().getActionId(),
                                actionNougat.getStackInfo(), actionNougat.getDetailsData())) {
                            return;
                        }
                        // 放到保存之后，需要获取主键id
                        IOSRealTimeLog actionLog = iosActionLogConvertHelper.buildRealTimeLog(actionNougat, taskContext.getAppName());
                        // 清空详情，前端需要详情则去调用另外的详情接口
                        actionLog.setStrTrigger(StringUtils.EMPTY);
                        actionLog.setStrInfo(StringUtils.EMPTY);
                        // 日志实时展示
                        messageService.sendTaskDynamicLogMessage(
                                JSONObject.fromObject(actionLog, JsonConfigFactory.buildDefault()), taskContext);
                    }
                }
            }
        } catch (Exception e) {
            log.error("日志实时展示出错", e);
        }
    }

}
