package cn.ijiami.detection.message;

import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.job.ApiPushProgressServer;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 用于处理 kafka\redis的消息发送服务处理
 *
 * <AUTHOR>
 * @date 2020/10/28 8:54
 **/
@Slf4j
@Component
public class MessageSendKit {

    @Autowired
    private IMessageHelper messageHelper;
    @Autowired
    private ApiPushProgressServer apiPushProgressServer;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('DetectionAppletStopProgress')}")
    private String detectionAppletStopProgress;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('DetectionStopProgress')}")
    private String detectionStopProgress;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('ReleaseDetectionDevice')}")
    private String releaseDetectionDevice;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('StartAIDetection')}")
    private String startAIDetection;

    /**
     * 停止xxl的检测任务
     *
     * @param task
     */
    public void stopXxlDetectionTask(TTask task) {
        // 支付宝小程序静态任务也需要发中断消息
        if (isAppletStaticTaskRunning(task) || (isAutoTask(task) && isDynamicTaskRunning(task))) {
            sendXxlStopMessage(task);
        } else {
            log.info("不用发送中断消息到xxl terminalType={} taskTatus={} dynamicStatus={}",
                    task.getTerminalType(), task.getTaskTatus(), task.getDynamicStatus());
        }
    }

    /**
     * 停止静态检测
     *
     * @param task
     */
    public void stopXxlDynamicDetectionTask(TTask task) {
        if ((isAutoTask(task) && isDynamicTaskRunning(task))) {
            sendXxlStopMessage(task);
        } else {
            log.info("不用发送动态任务中断消息到xxl terminalType={} taskTatus={} dynamicStatus={}",
                    task.getTerminalType(), task.getTaskTatus(), task.getDynamicStatus());
        }
    }

    /**
     * 停止静态检测
     *
     * @param task
     */
    public void stopXxlStaticDetectionTask(TTask task) {
        // 支付宝小程序静态任务也需要发中断消息
        if (isAppletStaticTaskRunning(task)) {
            sendXxlStopMessage(task);
        } else {
            log.info("不用发送静态任务中断消息到xxl terminalType={} taskTatus={} dynamicStatus={}",
                    task.getTerminalType(), task.getTaskTatus(), task.getDynamicStatus());
        }
    }

    private void sendXxlStopMessage(TTask task) {
        JSONObject msgObj = sendStopDynamicTaskMessage(task);
        msgObj.put("status", Collections.singletonList(ThirdPartyMessageTypeEnum.DETECTION_ERROR.getValue()));
        //推送中断消息给第三方,不区分静态还是动态
        apiPushProgressServer.pushProgress(task, msgObj, null);
    }

    private boolean isAutoTask(TTask task) {
        return task.getDetectionType() == null || TaskDetectionTypeEnum.isAuto(task.getDetectionType());
    }

    private boolean isAppletStaticTaskRunning(TTask task) {
        return task.getTerminalType().isApplet()
                && task.getTaskTatus() == DetectionStatusEnum.DETECTION_IN;
    }

    private boolean isDynamicTaskRunning(TTask task) {
        return task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
                || task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA;
    }

    public JSONObject sendStopDynamicTaskMessage(TTask task) {
        JSONObject msgObj = new JSONObject();
        msgObj.put("taskId", task.getTaskId());
        if (task.getTerminalType().isApplet()) {
            log.info("DetectionAppletStopProgress {}", msgObj);
            messageHelper.send(detectionAppletStopProgress, msgObj.toString());
        } else {
            log.info("DetectionStopProgress {}", msgObj);
            messageHelper.send(detectionStopProgress, msgObj.toString());
        }
        return msgObj;
    }

    /**
     * 释放手机设备
     *
     * @param task
     */
    public void sendReleaseDeviceMsgToXxlDetection(TTask task) {
        JSONObject msgObj = new JSONObject();
        msgObj.put("taskId", task.getTaskId());
        messageHelper.send(releaseDetectionDevice, msgObj.toString());
    }

    /**
     * 手机开始AI检测
     *
     * @param task
     */
    public void sendStartAIDetectionMsgToXxlDetection(TTask task) {
        JSONObject msgObj = new JSONObject();
        msgObj.put("taskId", task.getTaskId());
        messageHelper.send(startAIDetection, msgObj.toString());
    }

}
