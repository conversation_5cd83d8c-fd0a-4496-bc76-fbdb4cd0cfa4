package cn.ijiami.detection.message;

import cn.ijiami.detection.message.param.SendBroadcastMessageParam;
import cn.ijiami.detection.message.param.SendHiddenMessageToUserParam;
import cn.ijiami.detection.message.param.SendNoticeToUserParam;
import cn.ijiami.detection.message.param.AnalysisProgressParam;
import cn.ijiami.detection.message.param.DeviceOperateLogParam;
import cn.ijiami.detection.message.param.UploadMessageParam;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 消息通知发送工具类
 * 用于替代MessageServiceApi和UserServiceApi的HTTP调用，改为使用kafka消息队列
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
public class MessageNotificationSendKit {

    @Autowired
    private IMessageHelper messageHelper;

    // 消息相关topic
    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SendUploadMessage')}")
    private String sendUploadMessageTopic;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SendAnalysisProgress')}")
    private String sendAnalysisProgressTopic;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SendNoticeToUser')}")
    private String sendNoticeToUserTopic;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SendBroadcastMessage')}")
    private String sendBroadcastMessageTopic;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SendHiddenMessageToUser')}")
    private String sendHiddenMessageToUserTopic;

    // 用户操作相关topic
    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('SaveDeviceOperateLog')}")
    private String saveDeviceOperateLogTopic;

    /**
     * 发送上传消息
     *
     * @param param 上传消息参数
     */
    public void sendUploadMessage(UploadMessageParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "sendUploadMessage");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送上传消息到kafka: {}", msgObj);
            messageHelper.send(sendUploadMessageTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送上传消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送分析进度消息
     *
     * @param param 分析进度参数
     */
    public void sendAnalysisProgress(AnalysisProgressParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "sendAnalysisProgress");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送分析进度消息到kafka: {}", msgObj);
            messageHelper.send(sendAnalysisProgressTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送分析进度消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送通知给用户
     *
     * @param param 用户通知参数
     */
    public void sendNoticeToUser(SendNoticeToUserParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "sendNoticeToUser");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送用户通知消息到kafka: {}", msgObj);
            messageHelper.send(sendNoticeToUserTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送用户通知消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送广播消息
     *
     * @param param 广播消息参数
     */
    public void sendBroadcastMessage(SendBroadcastMessageParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "sendBroadcastMessage");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送广播消息到kafka: {}", msgObj);
            messageHelper.send(sendBroadcastMessageTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送广播消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送隐藏消息给用户
     *
     * @param param 隐藏消息参数
     */
    public void sendHiddenMessageToUser(SendHiddenMessageToUserParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "sendHiddenMessageToUser");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送隐藏消息到kafka: {}", msgObj);
            messageHelper.send(sendHiddenMessageToUserTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送隐藏消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存设备操作日志
     *
     * @param param 设备操作日志参数
     */
    public void saveDeviceOperateLog(DeviceOperateLogParam param) {
        try {
            JSONObject msgObj = new JSONObject();
            msgObj.put("type", "saveDeviceOperateLog");
            msgObj.put("data", JSON.toJSONString(param));
            
            log.info("发送设备操作日志到kafka: {}", msgObj);
            messageHelper.send(saveDeviceOperateLogTopic, msgObj.toString());
        } catch (Exception e) {
            log.error("发送设备操作日志失败: {}", e.getMessage(), e);
        }
    }
}