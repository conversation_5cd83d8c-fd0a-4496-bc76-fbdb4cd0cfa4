package cn.ijiami.detection.message;

/**
 * 消息处理
 *
 * <AUTHOR>
 * @date 2020-10-21 14:12
 */
public interface IMessageHelper {

    /**
     * 消息发送
     *
     * @param topic
     * @param content
     */
    void send(String topic, String content);

    /**
     * 消息发送
     *
     * @param topic
     * @param key
     * @param content
     */
    void send(String topic, String key, String content);
}
