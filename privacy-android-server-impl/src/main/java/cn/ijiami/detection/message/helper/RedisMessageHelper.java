package cn.ijiami.detection.message.helper;

import cn.ijiami.detection.message.IMessageHelper;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * redis处理消息助手
 *
 * <AUTHOR>
 * @date 2020/10/21 12:32
 **/
public class RedisMessageHelper implements IMessageHelper {

    private RedisTemplate redisTemplate;

    public RedisMessageHelper(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void send(String topic, String content) {
        redisTemplate.convertAndSend(topic, content);
    }

    @Override
    public void send(String topic, String key, String content) {
        redisTemplate.convertAndSend(topic, content);
    }
}
