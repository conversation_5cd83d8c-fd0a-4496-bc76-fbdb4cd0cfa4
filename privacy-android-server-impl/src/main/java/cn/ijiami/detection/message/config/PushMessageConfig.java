package cn.ijiami.detection.message.config;

import cn.ijiami.detection.message.IMessageHelper;
import cn.ijiami.detection.message.helper.KafkaMessageHelper;
import cn.ijiami.detection.message.helper.RedisMessageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * 推送消息配置，用于注册xxl消息处理服务
 *
 * <AUTHOR>
 * @date 2020-10-21 14:19
 */
@Configuration
public class PushMessageConfig {

    private static Logger logger = LoggerFactory.getLogger(PushMessageConfig.class);

    @Bean
    @ConditionalOnExpression("#{'redis'.equals(environment['message.server.switch'])}")
    public IMessageHelper redisMessageHelper(RedisTemplate redisTemplate) {
        // 解决redis发送中文乱码问题
        RedisSerializer<String> stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(stringSerializer);
        logger.info("------------------------- redis消息发送服务注册 -------------------------");
        return new RedisMessageHelper(redisTemplate);
    }

    @Bean
    @ConditionalOnExpression("#{'kafka'.equals(environment['message.server.switch'])}")
    public IMessageHelper kafkaMessageHelper(KafkaTemplate kafkaTemplate) {
        logger.info("------------------------- kafka消息发送服务注册 -------------------------");
        return new KafkaMessageHelper(kafkaTemplate);
    }

}
