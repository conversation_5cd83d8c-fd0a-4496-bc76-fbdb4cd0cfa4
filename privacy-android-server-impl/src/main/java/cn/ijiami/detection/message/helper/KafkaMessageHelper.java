package cn.ijiami.detection.message.helper;

import cn.ijiami.detection.message.IMessageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * kafka处理消息方式
 *
 * <AUTHOR>
 * @date 2020/10/21 12:32
 **/
public class KafkaMessageHelper implements IMessageHelper {

    private static Logger logger = LoggerFactory.getLogger(KafkaMessageHelper.class);

    private KafkaTemplate<String, String> kafkaTemplate;

    public KafkaMessageHelper(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void send(String topic, String key, String message) {
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, key, message);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onSuccess(Object o) {
                logger.info("sendKafkaMessage topic:{} message:{} send success", topic, message);
            }

            @Override
            public void onFailure(Throwable throwable) {
                logger.info("sendKafkaMessage topic:{} message:{} send error:{}", topic, message, throwable.getMessage());
            }
        });
    }

    @Override
    public void send(String topic, String message) {
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, message);
        future.addCallback(new ListenableFutureCallback() {
            @Override
            public void onSuccess(Object o) {
                logger.info("sendKafkaMessage topic:{} message:{} send success", topic, message);
            }

            @Override
            public void onFailure(Throwable throwable) {
                logger.info("sendKafkaMessage topic:{} message:{} send error:{}", topic, message, throwable.getMessage());
            }
        });
    }
}
