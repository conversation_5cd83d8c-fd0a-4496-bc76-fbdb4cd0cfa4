package cn.ijiami.detection.kafka;

import java.util.Optional;
import java.util.UUID;

import cn.ijiami.detection.utils.CommonUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import cn.ijiami.base.common.config.IjiamiCommonProperties;
import cn.ijiami.detection.job.XxlDetectMessageServer;
import cn.ijiami.detection.message.IMessageHelper;
import cn.ijiami.detection.thread.DetectionDynamicResultThread;
import cn.ijiami.detection.thread.FixedThreadPoolManager;
import net.sf.json.JSONObject;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Component
@ConditionalOnExpression("#{'kafka'.equals(environment['message.server.switch'])}")
public class MessageKafkaReceiver {
    @Autowired
    private XxlDetectMessageServer xxlDetectMessageServer;
    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;
    @Autowired
    private IMessageHelper messageHelper;
    private static Logger  logger = LoggerFactory.getLogger(MessageKafkaReceiver.class);

    @KafkaListener(topics = "#{new String[]{'${ijiami.detection.topic.prefix:}'.concat('PinfoDetectionProgress')}}")
    public void onMessage(ConsumerRecord<Object, Object> consumerRecord,Acknowledgment acknowledgment) {
    	//logger.info("静态检测收到更新检测进度消息：{}", consumerRecord);
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        try {
            Optional<Object> kafkaMassage = Optional.ofNullable(consumerRecord.value());
            if(kafkaMassage.isPresent()){
                Object o = kafkaMassage.get();
                String msgContent = o.toString();
                logger.info("------------------------- kafka静态检测，监听到一条消息，偏移量:{},主题：{}，内容：{}",consumerRecord.offset() , consumerRecord.topic(), msgContent);
                xxlDetectMessageServer.analysisStaticDetectionMessage(msgContent);
                acknowledgment.acknowledge();
            }
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    @KafkaListener(topics = "#{new String[]{'${ijiami.detection.topic.prefix:}'.concat('PinfoDynamicDetectionProgress')}}")
    public void onDynamicProgressMessage1(ConsumerRecord consumerRecord,Acknowledgment acknowledgment) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        try {
            Optional<Object> kafkaMassage = Optional.ofNullable(consumerRecord.value());
            if(kafkaMassage.isPresent()){
                Object o = kafkaMassage.get();
                String msgContent = o.toString();
                JSONObject msgJsonObj = JSONObject.fromObject(msgContent);
                int status = msgJsonObj.getInt("status");
                if(status==0) {
                    FixedThreadPoolManager fixed = FixedThreadPoolManager.getInstance();
                    //如果活动线程数等于线程池大小，则不做操作
                    if(fixed.getActiveCount() == FixedThreadPoolManager.poolSize){
                        logger.info("------------------------- kafka动态检测，监听到一条消息，偏移量:{},主题：{}，内容：{}", consumerRecord.offset(), consumerRecord.topic(), msgContent);
                        logger.info("------------------------- 线程池线程已满，不做处理！！！！！");
                        return;
                    }else{
                        logger.info("------------------------- kafka动态检测，监听到一条消息，偏移量:{},主题：{}，内容：{}", consumerRecord.offset() ,consumerRecord.topic(), msgContent);
                        DetectionDynamicResultThread imageThread = new DetectionDynamicResultThread(msgContent,xxlDetectMessageServer,acknowledgment);
                        fixed.execute(imageThread);
                    }
                }else {
                    logger.info("------------------------- kafka动态检测，监听到一条消息，偏移量:{},主题：{}，内容：{}", consumerRecord.offset(), consumerRecord.topic(), msgContent);
                    xxlDetectMessageServer.analysisDynamicDetectionMessage(msgContent,acknowledgment);
                }
            }
        } finally {
            MDC.remove(TRACE_ID);
        }
    }
    
}
