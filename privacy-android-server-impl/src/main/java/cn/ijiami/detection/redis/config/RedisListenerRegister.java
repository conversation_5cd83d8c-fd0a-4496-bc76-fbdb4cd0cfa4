package cn.ijiami.detection.redis.config;

import cn.ijiami.detection.job.XxlDetectMessageServer;
import cn.ijiami.detection.redis.listener.RedisMessageListener;
import cn.ijiami.detection.redis.pull.RedisMessageReceiver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Map;

/**
 * 自动注册redis 消息监听服务
 *
 * <AUTHOR>
 * @date 2020-10-20 18:03
 */
@Configuration
@ConditionalOnExpression("#{'redis'.equals(environment['message.server.switch'])}")
public class RedisListenerRegister {
    private static final Logger logger    = LoggerFactory.getLogger(RedisListenerRegister.class);
    /**
     * redis自定义配置 分隔符
     */
    private static final String DELIMITER = ",";

    @Value("${ijiami.detection.taskSort:true}")
    private Boolean taskExecutor;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('PinfoDetectionProgress')}")
    private String staticProgressTopics;

    @Value("#{'${ijiami.detection.topic.prefix:}'.concat('PinfoDynamicDetectionProgress')}")
    private String dynamicProgressTopics;

    /**
     * 解决reids消息 中文乱码问题
     *
     * @param redisTemplate
     * @return
     */
    @Bean
    public RedisTemplate stringSerializerRedisTemplate(RedisTemplate redisTemplate) {
        RedisSerializer<String> stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(stringSerializer);
        return redisTemplate;
    }

    /**
     * 初始化redis消息监听器
     *
     * @param redisTemplate
     * @return
     */
    @Bean
    RedisMessageReceiver redisMessageReceiver(RedisTemplate redisTemplate, XxlDetectMessageServer xxlDetectMessageServer) {
        RedisMessageReceiver redisMessageReceiver = new RedisMessageReceiver();
        redisMessageReceiver.setXxlDetectMessageServer(xxlDetectMessageServer);
        redisMessageReceiver.initListener(new String[]{staticProgressTopics}, new String[]{dynamicProgressTopics}, redisTemplate);
        return redisMessageReceiver;
    }

    /**
     * 注册redis消息监听器
     *
     * @param connectionFactory
     * @param redisMessageReceiver
     * @return
     */
    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory, RedisMessageReceiver redisMessageReceiver) {
        Map<String, RedisMessageListener> redisMessageListenerMap = redisMessageReceiver.getRedisMessageListenerMap();
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        if (taskExecutor) {
            // 订阅了一个叫channelName的通道
            for (Map.Entry<String, RedisMessageListener> entry : redisMessageListenerMap.entrySet()) {
                logger.info("------------------------- redis消息服务监听注册，监听主题：{} -------------------------", entry.getKey());
                container.addMessageListener(entry.getValue(), new PatternTopic(entry.getKey()));
            }
        }
        return container;
    }
}
