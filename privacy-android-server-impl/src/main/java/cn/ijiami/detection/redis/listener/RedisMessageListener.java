package cn.ijiami.detection.redis.listener;

import cn.ijiami.detection.redis.callback.RedisMessageCallback;
import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.UUID;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * 消息监听器 模型
 *
 * <AUTHOR>
 * @date 2020/10/20 18:26
 **/
@Slf4j
public class RedisMessageListener implements MessageListener {
    /**
     * redis 模板
     */
    private RedisTemplate        redisTemplate;
    /**
     * 消息 回调
     */
    private RedisMessageCallback callback;

    public RedisMessageListener(RedisTemplate redisTemplate, RedisMessageCallback callback) {
        this.redisTemplate = redisTemplate;
        this.callback = callback;
    }

    /**
     * 处理订阅消息
     *
     * @param message 消息
     * @param bytes
     */
    @Override
    public void onMessage(Message message, byte[] bytes) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        try {
            byte[] body = message.getBody();
            byte[] channel = message.getChannel();
            String msgContent = (String) redisTemplate.getValueSerializer().deserialize(body);
            String topic = (String) redisTemplate.getStringSerializer().deserialize(channel);
            log.info("RedisMessageListener 处理开始");
            callback.callback(topic, msgContent);
            log.info("RedisMessageListener 处理完成");
        } catch (Exception e) {
            log.info("RedisMessageListener 处理失败 message={}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID);
        }
    }
}
