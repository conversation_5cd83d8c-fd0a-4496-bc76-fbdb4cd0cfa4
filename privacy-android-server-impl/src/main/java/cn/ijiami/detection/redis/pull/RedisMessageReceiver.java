package cn.ijiami.detection.redis.pull;

import cn.ijiami.detection.job.XxlDetectMessageServer;
import cn.ijiami.detection.redis.callback.RedisMessageCallback;
import cn.ijiami.detection.redis.listener.RedisMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

/**
 * redis 消息监听器
 *
 * <AUTHOR>
 * @date 2020-10-21 16:08
 */
public class RedisMessageReceiver {

    private static final Logger logger = LoggerFactory.getLogger(RedisMessageReceiver.class);
    private XxlDetectMessageServer xxlDetectMessageServer;

    private Map<String, RedisMessageListener> redisMessageListenerMap = new HashMap<>(8);

    public Map<String, RedisMessageListener> getRedisMessageListenerMap() {
        return redisMessageListenerMap;
    }

    public void setXxlDetectMessageServer(XxlDetectMessageServer xxlDetectMessageServer) {
        this.xxlDetectMessageServer = xxlDetectMessageServer;
    }

    public void initListener(String[] staticProgressTopics, String[] dynamicProgressTopics, RedisTemplate redisTemplate) {
        for (String topic:staticProgressTopics) {
            redisMessageListenerMap.put(topic, new RedisMessageListener(redisTemplate, onStaticProgressMessage()));
        }
        for (String topic:dynamicProgressTopics) {
            redisMessageListenerMap.put(topic, new RedisMessageListener(redisTemplate, onDynamicProgressMessage()));
        }
    }

    private RedisMessageCallback onStaticProgressMessage() {
        return (topic, msgContent) -> {
            logger.info("------------------------- 静态检测，监听到一条消息，主题：{}，内容：{}", topic, msgContent);
            xxlDetectMessageServer.analysisStaticDetectionMessage(msgContent);
        };
    }

    private RedisMessageCallback onDynamicProgressMessage() {
        return (topic, msgContent) -> {
            logger.info("------------------------- 动态检测，监听到一条消息，主题：{}，内容：{}", topic, msgContent);
            xxlDetectMessageServer.analysisDynamicDetectionMessage(msgContent, null);
        };
    }
}
