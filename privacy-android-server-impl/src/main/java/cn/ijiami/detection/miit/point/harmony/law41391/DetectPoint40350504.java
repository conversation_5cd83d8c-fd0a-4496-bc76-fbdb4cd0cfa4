package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350504;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350504.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 4. App是否提供单次授权选项
 * 判断规则：
 * e)如操作系统支持，申请相机、位置、麦克风等可收集个人信息权限应向用户提供单次授权的选项；
 * 发现风险：
 * 建议APP申请相机、位置、麦克风等可收集个人信息权限应向用户提供单次授权的选项
 */
@EnableDetectPoint
public class DetectPoint40350504 extends DetectPoint350504 {

}
