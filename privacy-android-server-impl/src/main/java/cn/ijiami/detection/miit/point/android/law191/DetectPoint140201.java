package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.lang3.StringUtils;

/**
 * 因用户不同意收集非必要个人信息或打开非必要权限，拒绝提供业务功能；
 *
 * 发现风险：
 * 1、拒绝权限之后App退出
 */
@EnableDetectPoint
public class DetectPoint140201 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 遍历界面
        if (isAppQuitAfterDisagreePermission(commonDetectInfo, detectResult)) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText("用户不同意打开非必要权限，拒绝提供业务功能"));
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        }
        return detectResult;
    }
}
