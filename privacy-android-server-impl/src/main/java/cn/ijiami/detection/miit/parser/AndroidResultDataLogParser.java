package cn.ijiami.detection.miit.parser;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.utils.SAXReaderFactory;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.xml.sax.InputSource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.ijiami.detection.analyzer.exception.ParserException;
import cn.ijiami.detection.analyzer.exception.ParserExceptionEnum;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitUIClassEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.DataHandleUtil;

/**
 * 行为数据读取器
 *
 * <AUTHOR>
 * @date 2020-09-14 21:44
 */
@Component
public class AndroidResultDataLogParser implements IDetectDataParser<ResultDataLogBO> {

    private static Logger logger = LoggerFactory.getLogger(AndroidResultDataLogParser.class);

    public static final String MARK_TIME = "time";
    public static final String MARK_TYPE = "type";
    public static final String MARK_DATA_TAG = "dataTag";
    public static final String MARK_XML_PATH = "xmlpath";
    public static final String MARK_IMG_PATH = "imgpath";
    public static final String MARK_RUN_STATUS = "runstatus";
    public static final String RESULTS_DIR_NAME = "resultFiles";
    public static final String DETAILS = "details";
    //隐私政策页面过滤关键词
    public static final String PRIVACY_REGULAR_FILTERING_APP = "微博|微信|QQ";
    public static final String PRIVACY_REGULAR_FILTERING_WORDS = "微博服务使用协议|微博使用服务协议|微信隐私保护指引摘要|微信隐私保护指引|微博个人信息保护政策|QQ隐私保护指引";
    public static final int AUTHOR_PERMISSION_DESC_LENGTH = 60;
    
    //过滤用户协议等隐私政策页面
    public static final String PRIVACY_DETAIL_FILTER_WORDS = "用户协议|服务协议|使用协议";
    

    @Override
    public List<ResultDataLogBO> parser(String filePath, String appName) {
        return analysis(filePath, appName);
    }

    /**
     * 分析文件
     *
     * @param filePath
     * @return
     */
    private static List<ResultDataLogBO> analysis(String filePath, String appName) {
        String readStr = DynamicFileReaderHelper.readFileToString(filePath);
        if (StringUtils.isBlank(readStr)) {
            return new ArrayList<>();
        }
        String parentDir = new File(filePath).getParent();
        List<ResultDataLogBO> logs = new ArrayList<>();
        // 截取\n源于readFileToPrintString方法
        String[] jsons = readStr.split("\n");
        for (String json : jsons) {
            // 清理掉无效的json数据
            if (DataHandleUtil.nonJSONValid(json)) {
                continue;
            }
            // 构建实体数据
            try {
                ResultDataLogBO log = build(json);
                logs.add(log);
            } catch (ParserException e) {
                e.getMessage();
            }
        }
        for (int i = 0; i < logs.size(); i++) {
            ResultDataLogBO log = logs.get(i);
            ResultDataLogBO nextLog = i + 1 < logs.size() ? logs.get(i + 1) : null;
            analyzerUIDumpXmlResult(log, nextLog, parentDir, appName);
        }
        return logs;
    }

    /**
     * 构建164号文数据
     *
     * @param json
     * @return
     */
    private static ResultDataLogBO build(String json) throws ParserException {
        ResultDataLogBO log = new ResultDataLogBO();
        log.setTerminalType(TerminalTypeEnum.ANDROID);
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject.containsKey(MARK_TIME)) {
                log.setTime(jsonObject.getLong(MARK_TIME));
            }
            if (jsonObject.containsKey(MARK_TYPE)) {
                log.setType(jsonObject.getInteger(MARK_TYPE));
            }
            if (jsonObject.containsKey(MARK_DATA_TAG)) {
                log.setDataTag(jsonObject.getInteger(MARK_DATA_TAG));
            }
            if (jsonObject.containsKey(MARK_XML_PATH)) {
                log.setXmlPath(getRelativePath(jsonObject.getString(MARK_XML_PATH)));
            }
            if (jsonObject.containsKey(MARK_IMG_PATH)) {
                log.setImgPath(getRelativePath(jsonObject.getString(MARK_IMG_PATH)));
            }
            if (jsonObject.containsKey(MARK_RUN_STATUS)) {
                log.setRunStatus(jsonObject.getString(MARK_RUN_STATUS));
            }
            if (jsonObject.containsKey(DETAILS)) {
                log.setDetails(jsonObject.getString(DETAILS));
            }
        } catch (Exception e) {
            throw new ParserException(ParserExceptionEnum.LOG_164);
        }
        return log;
    }

    private static String getRelativePath(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        if (StringUtils.contains(path, RESULTS_DIR_NAME)) {
            return path.substring(path.indexOf(RESULTS_DIR_NAME)).replaceAll("\\\\", "/");
        }
        return path;
    }

    /**
     * 解析xml内容
     *
     * @param resultDataLogBO
     * @return
     */
    private static void analyzerUIDumpXmlResult(ResultDataLogBO resultDataLogBO, ResultDataLogBO nextResultDataLogBO, String fileDir, String appName) {
        if (StringUtils.isBlank(resultDataLogBO.getXmlPath())) {
            return;
        }
        String filePath = fileDir + File.separator + resultDataLogBO.getXmlPath();
        File file = new File(filePath);
        if (!file.exists() || file.isDirectory()) {
            return;
        }
        List<UIComponent> uiComponents = new ArrayList<>();
        String xmlContent = null;
        try {
            StringBuilder stringBuilder = new StringBuilder();
            String encodeStr = getEncodeString(filePath);
            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
            xmlContent = FileUtils.readFileToString(file, encodeStr);
            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
            source.setEncoding(encodeStr);
            Document document = reader.read(source);
            Element root = document.getRootElement();
            List<Element> elements;
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue() && xmlContent.contains("<" + PinfoConstant.ORIGINAL_WEB_VIEW_CLASS_NAME)) {
                elements = getTargetViewRootElement(root, PinfoConstant.ORIGINAL_WEB_VIEW_CLASS_NAME, false);
            } else {
                elements = getRootElement(root);
            }
            if(!CollectionUtils.isEmpty(elements)) {
            	for (int i = 0; i < elements.size(); i++) {
                	Element element = elements.get(i);
                    UIComponent uiComponent = new UIComponent();
                    uiComponent.setXmlTag(element.getName());
                    uiComponent.setText(element.attributeValue("text"));
                    uiComponent.setClassName(element.attributeValue("class"));
                    uiComponent.setPackageName(element.attributeValue("package"));
                    uiComponent.setChecked(Boolean.parseBoolean(element.attributeValue("checked")));
                    // 父控件可点击子控件可点击
                    Element parentElement = element.getParent();
                    String parentClickAbleStr = parentElement.attributeValue("clickable");
                    if (StringUtils.isNotBlank(parentClickAbleStr) && Boolean.parseBoolean(parentClickAbleStr)) {
                        uiComponent.setClickable(true);
                    } else {
                    	uiComponent.setClickable(isClickableElement(element));
                    }
                    uiComponent.setContentDesc(element.attributeValue("content-desc"));
                    uiComponent.setSelected(Boolean.parseBoolean(element.attributeValue("selected")));
                    if (element.attribute("resource-id") == null) {
                        uiComponent.setHasResourceId(true);
                    } else {
                        uiComponent.setHasResourceId(false);
                        uiComponent.setResourceId(element.attributeValue("resource-id"));
                    }
                    // 坐标信息
                    String boundsStr = element.attributeValue("bounds");
                    if (StringUtils.isNotBlank(boundsStr)) {
                        boundsStr = boundsStr.replace("][", ",");
                        boundsStr = boundsStr.replace("[", "");
                        boundsStr = boundsStr.replace("]", "");
                        String[] boundArray = boundsStr.split(",");
                        if (boundArray.length == 4) {
                            uiComponent.setX1(Integer.parseInt(boundArray[0]));
                            uiComponent.setY1(Integer.parseInt(boundArray[1]));
                            uiComponent.setX2(Integer.parseInt(boundArray[2]));
                            uiComponent.setY2(Integer.parseInt(boundArray[3]));
                        }
                    }
                    uiComponents.add(uiComponent);
                    if (StringUtils.isNotBlank(uiComponent.getText())) {
                    	if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                    		stringBuilder.append(uiComponent.getText()+"\n");
                    	}else {
                    		stringBuilder.append(uiComponent.getText());
                    	}
                    }
                    if (StringUtils.isNotBlank(uiComponent.getContentDesc()) && isNotPdfDesc(uiComponent)) {
                    	if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                    		 stringBuilder.append(uiComponent.getContentDesc()+"\n");
                    	}else {
                    		 stringBuilder.append(uiComponent.getContentDesc());
                    	}
                       
                        if (!StringUtils.endsWith(uiComponent.getContentDesc(), "。")) {
                            stringBuilder.append("。");
                        }
                    }
                }
            }

            //dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
            UIDumpResult result = new UIDumpResult();
            Element window = findWindowElement(root);
            result.setScreenHeight(getWindowSize(window, "height", PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_HEIGHT));
            result.setScreenWidth(getWindowSize(window, "width", PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_WIDTH));
            result.setUiComponentList(uiComponents);
            // 过滤表情符号
            result.setFullText(MiitWordKit.filterEmoji(stringBuilder.toString()));
            if (isInstallFailureUi(result)) {
                return;
            }
            //过滤权限授权页面
        	if (result.getFullText() != null && result.getFullText().contains("向上导航") && result.getFullText().contains("应用权限")) {
        		return;
        	}
        	
        	if(isPrivacyFilterWord(result.getFullText(), appName)){
        		return;
        	}
        	
            // 隐私信息界面
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                result.setUiType(MiitUITypeEnum.APPLY_POLICY.getValue());
                if (checkIsPolicyDetail(result)) {
                    result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                } else if (checkIsBeforeAuthorPopup(result)) {
                    resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue());
                    result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
                }
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue()) {
                result.setUiType(MiitUITypeEnum.APPLY_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_AGREE.getValue()) {
                result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_DENIED.getValue()) {
                result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue()) {
                result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PERSONAL.getValue()) {
                result.setUiType(MiitUITypeEnum.OTHER.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_BEFORE_SHAKE_THE_PHONE.getValue()) {
                result.setUiType(MiitUITypeEnum.BEFORE_SHAKE_THE_PHONE.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AFTER_SHAKE_THE_PHONE.getValue()) {
                result.setUiType(MiitUITypeEnum.AFTER_SHAKE_THE_PHONE.getValue());
            } else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP2.getValue()) {
            	//过滤权限授权页面
            	if (result.getFullText() != null && result.getFullText().contains("向上导航") && result.getFullText().contains("应用权限")) {
            		return;
            	}
                if (isClickText(nextResultDataLogBO, "允许")) {
                    result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
                } else if (isClickText(nextResultDataLogBO, "拒绝")) {
                    result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
                } else {
                    result.setUiType(MiitUITypeEnum.SETTING_PERMISSION.getValue());
                }
            }

            String fullText = result.getFullText();

            String privacyHtmlRegex = cn.ijiami.detection.utils.SeleniumUtils.privacyHtmlRegex;
            boolean isPrivacy = cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper.isPrivacyDetail(fullText, privacyHtmlRegex);
            if (StringUtils.isNoneBlank(fullText)
                    && fullText.length() > cn.ijiami.detection.utils.ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH
                    && isPrivacy
                    && resultDataLogBO.getType() != ResultDataLogBoTypeEnum.SHAKE.type) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue());
            }

            resultDataLogBO.setUiDumpResult(result);
        } catch (Exception e) {
            logger.error("resultDataLogBO analyzerUIDumpXmlResult path:{} error", filePath, e);
            //出现异常后看看页面里面是否存在OCR内容
            setUiDumpResult(xmlContent, uiComponents, resultDataLogBO);
        }
    }
    
    /**
     * 过滤隐私政策/隐私政策页面截图，精准匹配下识别标题如：《微博使用服务协议》、《微信隐私保护指引摘要》《微信隐私保护指引》《微博个人信息保护政策》、《QQ隐私保护指引》，则不取用获取到的内容。
     * @param fullText
     * @param appName
     * @return
     */
    private static boolean isPrivacyFilterWord(String fullText ,String appName){

    	if(StringUtils.isBlank(fullText) || StringUtils.isBlank(appName)) {
    		return false;
    	}
    	
        // 提取前100个字符的内容
        String fullTextStart = fullText != null ? fullText.substring(0, Math.min(fullText.length(), 100)) : "";

        // 正则表达式模式对象
        Pattern appPattern = Pattern.compile(PRIVACY_REGULAR_FILTERING_APP);
        Pattern wordPattern = Pattern.compile(PRIVACY_REGULAR_FILTERING_WORDS);

        // 检查appName是否包含在PRIVACY_REGULAR_FILTERING_APP中
        boolean isFilteredApp = appPattern.matcher(appName).find();

        // 检查fullTextStart是否包含PRIVACY_REGULAR_FILTERING_WORDS中的任意一个词
        boolean containsPrivacyWord = wordPattern.matcher(fullTextStart).find();

        // 如果内容的前100个字包含PRIVACY_REGULAR_FILTERING_WORDS并且appName不包含PRIVACY_REGULAR_FILTERING_APP，则return
        if (!isFilteredApp && containsPrivacyWord) {
            return true;
        }
        return false;
    }

    private static boolean isClickText(ResultDataLogBO resultDataLogBO, String text) {
        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
            });
            return StringUtils.equals(detailsBO.getText(), text);
        }
        return false;
    }


    private static void setUiDumpResult(String xmlContent, List<UIComponent> uiComponents, ResultDataLogBO resultDataLogBO) {
        try {
            if (StringUtils.isBlank(xmlContent)) {
                return;
            }
            List<String> listXmlContent = ocrResultOnlp(xmlContent);
            if (listXmlContent == null || listXmlContent.size() == 0) {
                return;
			}
			
			StringBuffer sbff = new StringBuffer();
			for (String string : listXmlContent) {
				UIComponent uiComponent = new UIComponent();
				uiComponent.setText(string);
				uiComponents.add(uiComponent);
				if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
					sbff.append(uiComponent.getText()+"\n");
				}else {
					sbff.append(uiComponent.getText());
				}
			}
			
			//dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
			UIDumpResult result = new UIDumpResult();
            result.setScreenHeight(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_HEIGHT);
            result.setScreenWidth(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_WIDTH);
			result.setUiComponentList(uiComponents);
			result.setFullText(sbff.toString());
			// 隐私信息界面
			if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
			    result.setUiType(MiitUITypeEnum.APPLY_POLICY.getValue());
			    if (checkIsPolicyDetail(result)) {
			        result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
			    } else if (checkIsBeforeAuthorPopup(result)) {
			        resultDataLogBO.setDataTag(MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue());
			        result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
			    }
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP.getValue()) {
			    result.setUiType(MiitUITypeEnum.APPLY_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_AGREE.getValue()) {
			    result.setUiType(MiitUITypeEnum.AGREE_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_DENIED.getValue()) {
			    result.setUiType(MiitUITypeEnum.DISAGREE_PERMISSION.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_REGISTER_LOGIN.getValue()) {
			    result.setUiType(MiitUITypeEnum.LOGIN_RIGISTER.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PERSONAL.getValue()) {
			    result.setUiType(MiitUITypeEnum.OTHER.getValue());
			} else if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_BEFORE_AUTHOR_POPUP.getValue()) {
			    result.setUiType(MiitUITypeEnum.BEFORE_AUTHOR_POPUP.getValue());
			}
			resultDataLogBO.setUiDumpResult(result);
		} catch (Exception e) {
			logger.error("setUiDumpResult analyzerUIDumpXmlResult error={}", e);
		}
    }
    
    public static List<String> ocrResultOnlp(String result){
		List<String> matchStrs = new ArrayList<String>();
		String reg = "text\\': '(.*?)\\'";
        Pattern patten = Pattern.compile(reg);//编译正则表达式
        Matcher matcher = patten.matcher(result);// 指定要匹配的字符串
        while (matcher.find()) { //此处find（）每次被调用后，会偏移到下一个匹配
        	String res = matcher.group().replaceAll("text': '", "").replace("'", "");
        	if(StringUtils.isBlank(res)) {
        		continue;
        	}
        	matchStrs.add(res);
        }
        
        reg = "text=\"(.*?)\\\"";
        patten = Pattern.compile(reg);//编译正则表达式
        matcher = patten.matcher(result);// 指定要匹配的字符串
        while (matcher.find()) { //此处find（）每次被调用后，会偏移到下一个匹配
        	String res = matcher.group().replaceAll("text=\"", "").replace("\"", "");
        	if(StringUtils.isBlank(res)) {
        		continue;
        	}
        	matchStrs.add(res);
        }
        return matchStrs;
	}

    private static int getWindowSize(Element window, String attrName, int defaultValue) {
        if (window != null) {
            if (StringUtils.isNotBlank(window.attributeValue(attrName))) {
                try {
                    int width = Integer.parseInt(window.attributeValue(attrName));
                    // 屏幕是否在合理的范围内
                    if (width > 0 && width < 10000) {
                        return width;
                    }
                } catch (Exception e) {
                    e.getMessage();
                }
            }
        }
        return defaultValue;
    }

    /**
     * 非pdf的描述文本
     * @param component
     * @return
     */
    private static boolean isNotPdfDesc(UIComponent component) {
        /**
         * PDF的xml结构
         * <node class="android.widget.Image" text="" content-desc="5254000300B2OVC7Z000006680954" resource-id=""
         * visible-to-user="false" click-able="false" scroll-able="false" in-screen="false" center-x="540" center-y="2598"
         * width="996" height="-1357" />
         *
         */
        return !"android.widget.Image".equals(component.getClassName()) && !CommonUtil.isPrint(component.getContentDesc());
    }

    private static boolean isInstallFailureUi(UIDumpResult result) {
        boolean containsInstallFailureText = StringUtils.contains(result.getFullText(), "已禁止")
                && StringUtils.contains(result.getFullText(), "安装来自此来源的未知应用");
        boolean hasSettingButton = result.getUiComponentList().stream().anyMatch(component ->
                StringUtils.equals(component.getClassName(), MiitUIClassEnum.BUTTON_VIEW.getValue()) && "设置".equals(component.getText()));
        return containsInstallFailureText && hasSettingButton;
    }

    public static String getEncodeString(String filePath) throws Exception {
        BufferedInputStream bin = new BufferedInputStream(Files.newInputStream(Paths.get(filePath)));
        int p = (bin.read() << 8) + bin.read();
        bin.close();
        String code = null;

        switch (p) {
            case 0xefbb:
            case 0x3c3f:
                code = "UTF-8";
                break;
            case 0xfffe:
                code = "Unicode";
                break;
            case 0xfeff:
                code = "UTF-16BE";
                break;
            default:
                code = "GBK";
        }
        return code;
    }

    /**
     * 判断是否是检测详情
     *
     * @param result
     * @return
     */
    private static boolean checkIsPolicyDetail(UIDumpResult result) {
        // 根据resourceId判断
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if (StringUtils.isBlank(uiComponent.getResourceId()) && StringUtils.isBlank(uiComponent.getText()) && StringUtils
                    .isNotBlank(uiComponent.getContentDesc()) && StringUtils.equalsIgnoreCase(uiComponent.getXmlTag(), "node")
                    && StringUtils.length(result.getFullText()) > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                return true;
            }

            boolean uiType = MiitUIClassEnum.TEXT_VIEW.getValue().equals(uiComponent.getClassName());
            String textContent = uiComponent.getText();
            // 隐私政策再弹窗中
            boolean isContainsPirvacy = cn.ijiami.detection.miit.kit.MiitWordKit.checkIsContainsPrivacy(textContent);
            if (uiType && StringUtils.isNotBlank(textContent) && isContainsPirvacy && textContent.length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
                result.setUiType(MiitUITypeEnum.POLICY_DETAIL.getValue());
                return true;
            }
        }

        int length = result.getFullText().length();
        // 隐私政策跳转连接
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if (StringUtils.contains(uiComponent.getText(), "隐私") && uiComponent.getText().length() < 10 && uiComponent.isClickable() && length<ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH) {
                return false;
            }
        }

        // 判断有无同意按钮
        for (UIComponent uiComponent : result.getUiComponentList()) {
            if ((StringUtils.contains(uiComponent.getText(), "同意") || StringUtils.contains(uiComponent.getText(), "拒绝") || StringUtils.contains(uiComponent.getText(), "确定"))
                    && uiComponent.getText().length() < 10
                    && uiComponent.isClickable()
                    && StringUtils.isNotBlank(uiComponent.getResourceId())) {
                return false;
            }
        }
        if (result.getFullText().length() > ConstantsUtils.PARSER_PRIVACY_DETAIL_TEXT_LENGTH) {
            return true;
        }
        return false;
    }

    private static boolean checkIsBeforeAuthorPopup(UIDumpResult result) {
        if (CollectionUtils.isEmpty(result.getUiComponentList())) {
            return false;
        }
        if (result.getUiComponentList().stream().anyMatch(AndroidResultDataLogParser::hasButton)) {
            long count = 0;
            int index = 0;
            while(index < result.getFullText().length() && (index = result.getFullText().indexOf("权限", index)) > 0) {
                index++;
                count++;
            }
            return result.getFullText().length() < count * AUTHOR_PERMISSION_DESC_LENGTH;
        }
        return false;
    }

    private static boolean hasButton(UIComponent uiComponent) {
        return StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.BUTTON_VIEW.getValue())
                || StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IMAGE_BUTTON_VIEW.getValue());
    }

    /**
     * 获取根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getRootElement(Element root) {
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getRootElement(element));
            } else {
                result.add(element);
            }
        }
        return result;
    }

    /**
     * 获取指定class的根节点
     *
     * @param root
     * @return
     */
    public static List<Element> getTargetViewRootElement(Element root, String targetViewClassName, boolean rootIsTheTargetView) {
        Attribute classAttribute = root.attribute("class");
        boolean isTargetView = false;
        if (classAttribute != null) {
            String value = classAttribute.getValue();
            isTargetView = targetViewClassName.equals(value);
        }
        List<Element> result = new ArrayList<>();
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements() != null && element.elements().size() > 0) {
                result.addAll(getTargetViewRootElement(element, targetViewClassName, rootIsTheTargetView || isTargetView));
            } else if (rootIsTheTargetView || isTargetView) {
                result.add(element);
            } else if (isClickableElement(element)) {
                result.add(element);
            }
        }
        return result;
    }

    private static boolean isClickableElement(Element element) {
        return Boolean.parseBoolean(element.attributeValue("clickable"))
                || Boolean.parseBoolean(element.attributeValue("click-able"));
    }
    
//    private static void analyzerUIDumpXmlResultTest(String filePath) {
//        try {
//            StringBuilder stringBuilder = new StringBuilder();
//            List<UIComponent> uiComponents = new ArrayList<>();
//            String encodeStr = getEncodeString(filePath);
//            SAXReader reader = SAXReaderFactory.createSafeSaxReader();
//            String xmlContent = FileUtils.readFileToString(new File(filePath), encodeStr);
//            InputSource source = new InputSource(new ByteArrayInputStream(xmlContent.getBytes(encodeStr)));
//            source.setEncoding(encodeStr);
//            Document document = reader.read(source);
//            Element root = document.getRootElement();
//            List<Element> elements = getRootElement(root);
////            for (Element element : elements) {
//            if(elements != null && elements.size()>0) {
//            	for (int i = 0; i < elements.size(); i++) {
//                	Element element = elements.get(i);
//                    UIComponent uiComponent = new UIComponent();
//                    uiComponent.setXmlTag(element.getName());
//                    uiComponent.setText(element.attributeValue("text"));
//                    uiComponent.setClassName(element.attributeValue("class"));
//                    uiComponent.setChecked(Boolean.parseBoolean(element.attributeValue("checked")));
//                    // 父控件可点击子控件可点击
//                    Element parentElement = element.getParent();
//                   
//                    String is = element.attributeValue("clickable");
//                    String parentClickAbleStr = parentElement.attributeValue("clickable");
//                    if (StringUtils.isNotBlank(parentClickAbleStr) && Boolean.parseBoolean(parentClickAbleStr)) {
//                        uiComponent.setClickable(true);
//                    } else {
//                    	uiComponent.setClickable(Boolean.parseBoolean(element.attributeValue("clickable")));
////                    	if(StringUtils.isNotBlank(parentClickAbleStr) && i>0) {
////                    		for (int j = i+1; j > i; j--) {
////                    			//查看父节点是否能点击
////                        		Element parentElement1 = elements.get(j).getParent();
////                        		String parentClickAbleStr1 = parentElement1.attributeValue("clickable");
////                                if (StringUtils.isNotBlank(parentClickAbleStr1) && Boolean.parseBoolean(parentClickAbleStr1)) {
////                                   uiComponent.setClickable(true);
////                                }
////							}
////                    	}
//                    	
//                    }
//                    uiComponent.setContentDesc(element.attributeValue("content-desc"));
//                    uiComponent.setSelected(Boolean.parseBoolean(element.attributeValue("selected")));
//                    if (element.attribute("resource-id") == null) {
//                        uiComponent.setHasResourceId(true);
//                    } else {
//                        uiComponent.setHasResourceId(false);
//                        uiComponent.setResourceId(element.attributeValue("resource-id"));
//                    }
//                    // 坐标信息
//                    String boundsStr = element.attributeValue("bounds");
//                    if (StringUtils.isNotBlank(boundsStr)) {
//                        boundsStr = boundsStr.replace("][", ",");
//                        boundsStr = boundsStr.replace("[", "");
//                        boundsStr = boundsStr.replace("]", "");
//                        String[] boundArray = boundsStr.split(",");
//                        if (boundArray.length == 4) {
//                            uiComponent.setX1(Integer.parseInt(boundArray[0]));
//                            uiComponent.setY1(Integer.parseInt(boundArray[1]));
//                            uiComponent.setX2(Integer.parseInt(boundArray[2]));
//                            uiComponent.setY2(Integer.parseInt(boundArray[3]));
//                        }
//                    }
//                    uiComponents.add(uiComponent);
//                    
//                }
//            }
//            
//            //dataTag(数据标签) ： 1(隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面）| 6（个性化定推）
//            UIDumpResult result = new UIDumpResult();
//            result.setUiComponentList(uiComponents);
//            // 过滤表情符号
//            result.setFullText(MiitWordKit.filterEmoji(stringBuilder.toString()));
//            
//        } catch (Exception e) {
//            logger.error("resultDataLogBO analyzerUIDumpXmlResult path:{} error", filePath, e);
//        }
//    }

    private static Element findWindowElement(Element root) {
        if (MiitUIClassEnum.HIERARCHY.getValue().equals(root.getName())) {
            return root;
        }
        Iterator<Element> iterator = root.elementIterator();
        while (iterator.hasNext()) {
            Element nextElement = iterator.next();
            if (MiitUIClassEnum.HIERARCHY.getValue().equals(nextElement.getName())) {
                return nextElement;
            }
            if (nextElement.elements().size() == 1) {
                return findWindowElement(nextElement);
            }
        }
        return null;
    }
    

    public static void main(String[] args) {
//    	String filePath = "E:/zywa/ijiami/dynamic_file/069e62325dff87f197ba246d6d19d29c_7520_AUTO/resultFiles/1619142978634/uidump.xml";
//    	analyzerUIDumpXmlResultTest(filePath);
    	
    	String fullText = "关闭微博登录微博服务使用协议 1.特别提示1.1微博运营方同意按照本协议的规定及其不时发布的操作规则向用户提供微博服务。为获得微博服务，微博服务使用人(以下称)需在认真阅读及独立思考的基础上认可、同意本协议的全部条款(特别是以加粗方式提示用户注意的条款)并按照页面上的提示完成全部的注册程序。用户在进行注册过程中点击按钮(或实际使用微博服务)即表示用户完全接受本服务协议及新浪网络服务使用协议、微博社区公约、微博商业行为规范办法、微博举报投诉操作细则及微博运营方公示的各项规则、规范。如用户对本服务协议或协议的任何部分存有任何异议，应终止注册程序(或停止使用微博服务)。未成年用户应在监护人的陪同下阅读本协议，并在监护人同意后接受本协议、使用微博服务。1.2";
    	String appName = "微博"; 
    	System.out.println(isPrivacyFilterWord(fullText, appName));
	}
    
    public static void main1(String[] args) {
        AndroidResultDataLogParser parser = new AndroidResultDataLogParser();
        List<ResultDataLogBO> resultDataLogBOList = parser.parser("C:\\Users\\<USER>\\Downloads\\淘券吧_20210114091246\\59f865d6dc612123d8d5ecd06505819b_6612_AUTO\\resultDataLog", null);

        boolean hasCheckBox = false;
        List<ResultDataLogBO> applyResultDataLogBOList = resultDataLogBOList.stream().filter(resultDataLogBO ->
                resultDataLogBO.getUiDumpResult() != null
                        && StringUtils.contains(resultDataLogBO.getUiDumpResult().getFullText(), "登录")
                        && (StringUtils.contains(resultDataLogBO.getUiDumpResult().getFullText(), "隐私")
                        || StringUtils.contains(resultDataLogBO.getUiDumpResult().getFullText(), "个人信息保护"))
                        && (resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.APPLY_POLICY.getValue()
                        || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()
                        || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue())).collect(Collectors.toList());
        // 遍历界面
        for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
            // 申请隐私政策页面
            List<UIComponent> uiComponents = resultDataLogBO.getUiDumpResult().getUiComponentList();
            for (int index = 0; index < uiComponents.size(); index++) {
                UIComponent uiComponent = uiComponents.get(index);
                // 包含checkbox
                if (StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.CHECK_BOX.getValue())
                        || (StringUtils.contains(uiComponent.getText(), "勾选") && uiComponent.getText().length() < 20)) {
                    hasCheckBox = true;
                    //  默认勾选 不合规
                    if (uiComponent.isChecked()) {
                        System.out.println("哈哈哈哈1：" + resultDataLogBO.getImgPath());
                    }
                    // 默认不勾选直接返回安全
                    else {
                        System.out.println("哈哈哈哈2：" + resultDataLogBO.getImgPath());
                    }
                    break;
                }
                // 图片类型checkbox
                if (index >= 1
                        && ((StringUtils.contains(uiComponent.getText(), "同意") && uiComponent.getText().length() < 50)
                        || (StringUtils.contains(uiComponent.getContentDesc(), "同意") && uiComponent.getContentDesc().length() < 50))) {
                    UIComponent checkBoxView = uiComponents.get(index - 1);
                    // 可点击
                    if (checkBoxView.isClickable() && StringUtils.isBlank(checkBoxView.getText())
                            && Math.abs(checkBoxView.getY1() - uiComponent.getY1()) < 10 && Math.abs(checkBoxView.getY2() - uiComponent.getY2()) < 10) {
                        hasCheckBox = true;
                        if (checkBoxView.isSelected()) {
                            System.out.println("哈哈哈哈3：" + resultDataLogBO.getImgPath());
                        } else {
                            System.out.println("哈哈哈哈4：" + resultDataLogBO.getImgPath());
                        }
                        break;
                    }

                }
            }
            if (hasCheckBox) {
                break;
            }
        }

        // 不包含弹出框
        if (!hasCheckBox) {
            // 判断语义内容
            for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
                for (UIComponent uiComponent : resultDataLogBO.getUiDumpResult().getUiComponentList()) {
                    String fullText = uiComponent.getText() + uiComponent.getContentDesc();
                    if (fullText.length() > 50) {
                        continue;
                    }
                    if (StringUtils.contains(fullText, "即同意")
                            || StringUtils.contains(fullText, "表示同意")
                            || StringUtils.contains(fullText, "即视为")
                            || StringUtils.contains(fullText, "阅读并同意")
                            || (StringUtils.contains(fullText, "同意") && StringUtils.contains(fullText, "意味着"))
                            || (StringUtils.contains(fullText, "同意") && StringUtils.contains(fullText, "并注册"))
                            || (StringUtils.contains(fullText, "表示") && StringUtils.contains(fullText, "同意"))
                            || (StringUtils.contains(fullText, "即为") && StringUtils.contains(fullText, "同意"))
                            || (StringUtils.contains(fullText, "代表") && StringUtils.contains(fullText, "同意"))) {
                        System.out.println("哈哈哈哈5：" + resultDataLogBO.getImgPath());
                        break;
                    }
                }
            }
        }


        // 不涉及 添加图片登录注册/申请权限截图
        for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
            System.out.println("哈哈哈哈6：" + resultDataLogBO.getImgPath());
        }


    }
    
}
