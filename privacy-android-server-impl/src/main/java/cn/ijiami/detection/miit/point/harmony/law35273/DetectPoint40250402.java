package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250402;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.4.a
 * 收集个人敏感信息前，应征得个人信息主体的明示同意，并应确保个人信息主体的明示同意是其在完全知情的基础上自主给出的、具体的、清晰明确的意愿表示；
 *
 * 判断规则
 * 发现风险
 * 1 用户首次注册、登陆时，未提供勾选框或默认勾选。
 * 2 隐私政策难于阅读，隐私政策文本小于等于设定值，行距小于等于设定值。
 * 3 申请个人信息相关权限时未同步告知其使用目的。
 * 4 未提供隐私政策。
 */
@EnableDetectPoint
public class DetectPoint40250402 extends AppletDetectPoint250402 {

}
