package cn.ijiami.detection.miit.point.alipay.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350701;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350604.java
 * @Description
 * 其他要求
 * 1. 是否告知并取得用户授权同意后进行剪切板或公共存储区的读取行为
 * 判断规则：
 * b)如存在读取剪切板或公共存储区的行为，应告知用户读取的信息范围和使用目的，不应在未取得用户同意的情况下，收集剪切板中包含的个人信息和公共存储区中的个人信息.
 * 发现风险：
 * APP未告知并取得用户授权同意后进行剪切板或公共存储区的读取行为
 */
@EnableDetectPoint
public class DetectPoint30350701 extends DetectPoint350701 {


}
