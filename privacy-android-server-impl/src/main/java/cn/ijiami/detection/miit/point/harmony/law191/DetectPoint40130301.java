package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint130301;

/**
 * 用户明确表示不同意后，仍收集个人信息或打开可收集个人信息的权限，或频繁征求用户同意、干扰用户正常使用；
 *
 * 未发现风险：
 * 1、拒绝权限，能继续正常遍历，并未发现再次授权弹窗
 * 发现风险：
 * 1、拒绝权限，App退出，再次打开应用再弹出权限授权窗口
 * 2、拒绝权限，之再次弹出权限
 */
@EnableDetectPoint
public class DetectPoint40130301 extends AppletDetectPoint130301 {

}
