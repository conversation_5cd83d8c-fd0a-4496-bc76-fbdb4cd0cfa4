package cn.ijiami.detection.miit.callback;

import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;

/**
 * 检测结果回调
 *
 * <AUTHOR>
 * @date 2020/12/18 16:20
 **/
public interface IDetectCallback {

    /**
     * 检测开始
     *
     * @param commonParam      公共参数
     * @param customDetectInfo 自定义检测信息
     */
    void startDetection(CommonDetectInfo commonParam, CustomDetectInfo customDetectInfo);

    /**
     * 检测结束
     *
     * @param commonParam      公共参数
     * @param customDetectInfo 自定义检测信息
     * @param detectResult     检测结果
     */
    void endDetection(CommonDetectInfo commonParam, CustomDetectInfo customDetectInfo, DetectResult detectResult);

    /**
     * 获取检测结果
     *
     * @return
     */
    DetectResult getResult();
}
