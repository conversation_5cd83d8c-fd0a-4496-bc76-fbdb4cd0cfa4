package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10202;

/**
 * APP在运行时，未见向用户告知且未经用户同意，存在固定频率读取位置信息，非服务所必需且无合理应用场景，超出实现产品或服务的业务功能所必需的最低频率。
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（地理位置行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 * 1、隐私政策截图
 * 2、共XXX次（地理位置行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断无隐私政策
 * 2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则不涉及
 * 二、有隐私政策
 * 1、判断有隐私政策
 * 2、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则不涉及
 *
 * 涉及行为：12001L, 12002L, 12003L, 12004L, 12005L, 12006L
 * </pre></blockquote>
 *
 *
 *
 *
 *20210408
 *分两种情况：
 *一、无隐私政策(删除)
 *     1、判断无隐私政策
 *     2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则合规
 *二、有隐私政策(删除)
 *     1、判断有隐私政策
 *     2、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则合规
 *
 *新增判断：
 *一、无隐私政策
 *     1、判断无隐私政策
 *     2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *       触发超过3次以上【授权前行为】、【前台行为】、【后台行为】
 *
 *二、有隐私政策
 *    场景一：
 *     1、判断有隐私政策
 *     2、隐私政策中有行为关键词
 *     3、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *       触发超过3次以上【授权前行为】、【后台行为】
 *    场景二：
 *     1、判断有隐私政策
 *     2、隐私政策中没有行为关键词
 *    3、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *      触发超过3次以上【授权前行为】【前台行为】【后台行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4010202 extends DetectPoint10202 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
