package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.d
 * 个人信息主体不同意使用、关闭或退出特定业务功能的，不应频繁征求个人信息主体的同意。
 *
 * 判断规则
 * 发现风险：
 * 1、拒绝权限，App退出，再次打开应用相同功能界面再弹出权限授权窗口（主动点击除外）
 * 2、拒绝权限，之后再次弹出权限
 */
@EnableDetectPoint
public class DetectPoint250303 extends PrivacyUiDetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult restartApplyResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        // 拒绝权限授权弹窗后重新启动应用，应用是否仍弹出重复窗口
        checkRestartApplyPermission(commonDetectInfo, customDetectInfo, restartApplyResult, Collections.singletonList(PermissionNameHelper.FILTERWORD));
        if (restartApplyResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            conclusionList.add(
                    String.format(
                            "%s在用户明确拒绝%s等权限申请后，重新运行时，仍向用户弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。",
                            executor(commonDetectInfo), String.join("、", restartApplyResult.getRequestPermissionNames())));
            suggestionList.add("保留用户拒绝授权记录，不能向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。");
        }
        DetectResult repeatedApplyResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        // 是否频繁申请权限
        checkRepeatedlyApplyPermission(commonDetectInfo, repeatedApplyResult);
        if (repeatedApplyResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            conclusionList.add(
                    String.format(
                            "%s运行时，在用户明确拒绝%s等权限申请后，仍向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。",
                            executor(commonDetectInfo), String.join("、", repeatedApplyResult.getRequestPermissionNames())));
            suggestionList.add("APP在运行相应服务场景时，用户拒绝授权后，与授权不相关的服务应该不受影响。");
        }
        // 不涉及 添加图片
        if (restartApplyResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED
                && repeatedApplyResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            savePermissionImage(commonDetectInfo, restartApplyResult);
            restartApplyResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            return restartApplyResult;
        } else {
            DetectResult detectResult = this.buildNonCompliance(commonDetectInfo, customDetectInfo);
            detectResult.setScreenshots(new HashSet<>());
            detectResult.setScreenshotMd5s(new HashSet<>());
            detectResult.setRequestPermissionNames(new HashSet<>());
            // 合并2个检测的数据
            mergeResult(detectResult, restartApplyResult);
            mergeResult(detectResult, repeatedApplyResult);
            // 生成结论和建议
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            return detectResult;
        }
    }

    private void mergeResult(DetectResult detectResult, DetectResult otherResult) {
        if (CollectionUtils.isNotEmpty(otherResult.getScreenshots())) {
            for (String absolutePath: otherResult.getScreenshots()) {
                detectResult.getScreenshots().add(absolutePath);
            }
        }
        if (CollectionUtils.isNotEmpty(otherResult.getScreenshotMd5s())) {
            for (String md5: otherResult.getScreenshotMd5s()) {
                detectResult.getScreenshotMd5s().add(md5);
            }
        }
        if (CollectionUtils.isNotEmpty(otherResult.getRequestPermissionNames())) {
            for (String name: otherResult.getRequestPermissionNames()) {
                detectResult.getRequestPermissionNames().add(name);
            }
        }
    }

}
