package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import com.google.common.collect.Sets;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.5.b
 *个人信息保护政策所告知的信息应真实、准确、完整；
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 APP收集的个人信息与隐私政策不一致
 * 2 SDK收集的个人信息与隐私政策不一致
 * 3 APP实际申请的个人信息权限与隐私政策不一致
 * 【无隐私政策】
 * 有个人信息行为或者权限就存在风险
 */
@EnableDetectPoint
public class DetectPoint250502 extends Law35273DetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        List<ActionAnalyse> actionResultList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            suggestionList.add(nonePrivacyDetailSuggestion(commonDetectInfo));
        }
        // APP和SDK行为检测
        CheckNonePrivacyActionResult result = checkAllStageNonePrivacyAction(commonDetectInfo, customDetectInfo);
        detectResult.setPrivacyPolicyFragment(joinFragmentText(commonDetectInfo, result));
        if (!result.isNonInvolved()) {
            conclusionList.addAll(result.getConclusionList());
            suggestionList.addAll(result.getSuggestionList());
            actionResultList.addAll(result.getAnalysisResult());
        }
        List<String>  collectActionList = notInPrivacyPolicyPermission(commonDetectInfo, detectResult);
        if (!collectActionList.isEmpty()) {
            conclusionList.add(String.join("、", collectActionList) + "收集个人信息权限未在隐私政策中声明。");
            suggestionList.add("请在隐私政策中声明收集个人信息权限及使用目的。");
        }
        if (conclusionList.isEmpty()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setAnalysisResult(actionResultList);
        }
        return detectResult;
    }
}