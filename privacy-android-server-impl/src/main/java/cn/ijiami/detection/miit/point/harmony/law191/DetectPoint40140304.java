package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140304;

import java.util.ArrayList;
import java.util.List;

/**
 * 要求用户一次性同意打开多个可收集个人信息的权限，用户不同意则无法使用。
 * <p>
 * 发现风险：
 * 1、target<23
 * 2、启动应用之后，连续弹出权限授权窗口；拒绝权限后，App退出
 */
@EnableDetectPoint
public class DetectPoint40140304 extends DetectPoint140304 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
