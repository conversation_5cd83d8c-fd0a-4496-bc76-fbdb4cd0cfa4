package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20105;

/**
 * APP运行时，在用户明确拒绝通讯录/定位/短信/录音/相机/XXX等权限申请后，仍向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用
 * <p>
 * <blockquote><pre>
 * 截图
 * 检测结果会返回判断标识和截图，判断拒绝权限授权弹窗，是否仍弹出重复窗口，如果是，则违规，否则合规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4020105 extends DetectPoint20105 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
