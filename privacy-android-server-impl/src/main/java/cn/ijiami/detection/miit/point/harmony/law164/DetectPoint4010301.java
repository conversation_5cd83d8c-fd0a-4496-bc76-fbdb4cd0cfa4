package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10301;

/**
 * APP未见向用户告知且未经用户同意，存在将IMEI/设备MAC地址/软件安装列表等个人信息发送给友盟/极光/个推等第三方SDK的行为。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（IMEI/设备MAC地址/软件安装行为）
 * 触发时间 （推送）SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 （推送） 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、隐私政策截图
 * 3、共XXX次（IMEI/设备MAC地址/软件安装行为）
 * 触发时间 （推送）SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 （推送） 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 涉及行为：
 * </pre></blockquote>
 *
 *
 * 场景一：新增
 * 1、有隐私政策
 * 2、隐私政策中没有关键词
 * 3、（推送）SDK 触发了行为
 * 【前台行为】
 *
 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、（推送）SDK 触发了行为
 【授权前行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4010301 extends DetectPoint10301 {

	@Override
	public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
		return super.doDetect(commonDetectInfo, customDetectInfo);
	}

}