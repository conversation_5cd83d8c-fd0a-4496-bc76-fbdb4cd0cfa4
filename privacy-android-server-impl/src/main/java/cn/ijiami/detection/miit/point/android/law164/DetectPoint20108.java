package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.miit.enums.MiitUITypeEnum.SETTING_PERMISSION;
import static cn.ijiami.detection.utils.ResultDataLogUtils.isUiAuthor;

/**
 * APP 未见提供相关业务功能或服务，不应申请通讯录、定位、短信、录音、相机、日历等权限。
 * 检测结论：APP运行时未曾触发【行为权限】弹窗，在AndroidManifest 声明或者代码中调用，属于过度申请权限。
 * 整改建议：
 * 建议根据APP实际情况删减不必要的权限，相关业务功能有使用【权限】等权限需在隐私政策中清晰明示APP收集的目的、方式、范围。
 * 注意:自动化测试过程中未曾触发以上权限弹窗进行权限申请，如APP无需申请以上权限，请不要在AndroidManifest 声明或者代码中调用，建议删除涉及到的代码。
 **/
@Slf4j
@EnableDetectPoint
public class DetectPoint20108 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 运行时的权限申请弹窗
        List<PermissionVO> xmlPermissionList = commonDetectInfo.getPrivacyDetectionService()
                .getXMLPermission(commonDetectInfo.getApkDetectionDetailId(), null);
        if (!CollectionUtils.isEmpty(xmlPermissionList)) {
            // 运行时的申请的权限名
            List<ResultDataLogBO> runtimeApplayPermissionList = getRuntimeApplyPermissionList(commonDetectInfo);
            List<String> applyNameList = getRuntimeApplyNameList(commonDetectInfo, runtimeApplayPermissionList);
            if (!runtimeApplayPermissionList.isEmpty()) {
                // xml中声明的权限
                List<String> excessPermissionNameList = getXmlApplyNameList(commonDetectInfo, xmlPermissionList)
                        .stream()
                        .filter(applyName -> !applyNameList.contains(applyName)) // xml中声明的权限没在运行时的权限申请弹窗出现，判断违规
                        .collect(Collectors.toList());
                runtimeApplayPermissionList.forEach(resultLog -> {
                    addNoInvolvedImage(commonDetectInfo, detectResult, resultLog);
                });
                if (!excessPermissionNameList.isEmpty()) {
                    log.info("权限过渡申请 {}", excessPermissionNameList);
                    excessPermissionNameList.forEach(name -> addRequestPermissionName(detectResult, name));
                    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
                }
            } else {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        findPermissionSetting(commonDetectInfo.getResultDataLogs())
                .ifPresent(resultDataLogBO -> addScreenshot(commonDetectInfo, detectResult, resultDataLogBO));
        return detectResult;
    }

    private List<ResultDataLogBO> getRuntimeApplyPermissionList(CommonDetectInfo commonDetectInfo) {
        return commonDetectInfo.getResultDataLogs()
                .stream()
                .filter(resultLog -> resultLog.getType() != ResultDataLogBoTypeEnum.DISCOVER_POLICY_PAUSE.type)
                .collect(Collectors.groupingBy(ResultDataLogBO::getType))
                .values()
                .stream()
                .map(this::checkAppAuthor)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<String> getRuntimeApplyNameList(CommonDetectInfo commonDetectInfo, List<ResultDataLogBO> runtimeApplayPermissionList) {
        return runtimeApplayPermissionList
                .stream()
                .filter(resultLog -> Objects.nonNull(resultLog.getUiDumpResult()))
                .map(resultLog -> findApplyPermissionRegex(commonDetectInfo, resultLog.getUiDumpResult()).getApplyName())
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> getXmlApplyNameList(CommonDetectInfo commonDetectInfo, List<PermissionVO> xmlPermissionList) {
        return xmlPermissionList.stream()
                .map(permissionVO -> findApplyPermissionRegexByText(commonDetectInfo, permissionVO.getHarm()))
                .filter(p -> notInPrivacyPolicy(commonDetectInfo, p.getRegex()))
                .map(TApplyPermission::getApplyName)
                .filter(StringUtils::isNoneBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    protected boolean notInPrivacyPolicy(CommonDetectInfo commonDetectInfo, String regex) {
        if (StringUtils.isBlank(regex)) {
            return false;
        }
        if (StringUtils.isBlank(commonDetectInfo.getPrivacyPolicyContent())) {
            return true;
        }
        return !Pattern.compile(regex).matcher(commonDetectInfo.getPrivacyPolicyContent()).find();
    }

    protected Optional<ResultDataLogBO> findPermissionSetting(List<ResultDataLogBO> resultDataLogBOList) {
        return resultDataLogBOList
                .stream()
                .filter(resultLog -> resultLog.getType() == ResultDataLogBoTypeEnum.PERMISSION_SETTING.type)
                .filter(resultLog -> resultLog.getDataTag() == MiitDataTypeEnum.UI_AUTHOR_POPUP2.getValue())
                .peek(resultLog -> {
                    if (Objects.isNull(resultLog.getUiDumpResult())) {
                        UIDumpResult result = new UIDumpResult();
                        result.setFullText(SETTING_PERMISSION.getName());
                        result.setUiType(SETTING_PERMISSION.getValue());
                        result.setUiComponentList(Collections.emptyList());
                        result.setScreenHeight(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_HEIGHT);
                        result.setScreenWidth(PinfoConstant.DEFAULT_ANDROID_DEVICE_SCREEN_WIDTH);
                        resultLog.setUiDumpResult(result);
                    }
                })
                .findFirst();
    }

    protected List<ResultDataLogBO> checkAppAuthor(List<ResultDataLogBO> allLogList) {
        List<ResultDataLogBO> noComplianceLogList = new ArrayList<>();
        // 过滤出普通遍历阶段数据
        for (ResultDataLogBO resultLog:allLogList) {
            if (isUiAuthor(resultLog)) {
                noComplianceLogList.add(resultLog);
            }
        }
        return noComplianceLogList;
    }
}
