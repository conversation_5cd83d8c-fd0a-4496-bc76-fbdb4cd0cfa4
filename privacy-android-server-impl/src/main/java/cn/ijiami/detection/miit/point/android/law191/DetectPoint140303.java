package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

import java.util.*;

/**
 * 仅以改善服务质量、提升用户体验、定向推送信息、研发新产品等为由，强制要求用户同意收集个人信息；
 *
 * 发现风险：
 * 1、隐私政策内存在【改善服务、提升使用体验、研发新产品】关键词
 * 【无隐私政策属于未发现风险】
 */
@EnableDetectPoint
public class DetectPoint140303 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            Set<String> keyWordList = new HashSet<>(Arrays.asList("改善服务", "提升使用体验", "研发新产品"));
            if (keyWordList.stream().anyMatch( keyWord -> commonDetectInfo.getPrivacyPolicyContent().contains(keyWord))) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
            detectResult.setPrivacyPolicyFragment(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), keyWordList));
        }
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(getLawName(commonDetectInfo));
        } else {
            detectResult.setConclusion("该检测项未发现风险");
        }
        return detectResult;
    }
}
