package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250501;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.5.a
 * a)应制定个人信息保护政策，内容应包括但不限于：
 * 1)个人信息控制者的基本情况，包括主体身份、联系方式；
 * 2)收集、使用个人信息的业务功能，以及各业务功能分别收集的个人信息类型。涉及个人敏感信息的，需明确标识或突出显示；
 * 3)个人信息收集方式、存储期限、涉及数据出境情况等个人信息处理规则； 4)对外共享、转让、公开披露个人信息的目的、涉及的个人信息类型、接收个人信息的第三方类型，以及各自的安全和法律责任；
 * 5)个人信息主体的权利和实现机制，如查询方法、更正方法、删除方法、注销账户的方法、撤回同意的方法、获取个人信息副本的方法、对信息系统自动决策结果进行申诉的方法等；
 * 6)提供个人信息后可能存在的安全风险，及不提供个人信息可能产生的影响；
 * 7)遵循的个人信息安全基本原则，具备的数据安全能力，以及采取的个人信息安全保护措施，必要时可公开数据安全和个人信息保护相关的合规证明；
 * 8)处理个人信息主体询问、投诉的渠道和机制，以及外部纠纷解决机构及联络方式。
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 隐私政策不是独立成文
 * 2 APP收集的个人信息与隐私政策不一致
 * 3 SDK收集的个人信息与隐私政策不一致
 * 4 隐私政策没有对【删除、更正（修改）、查询（访问）、注销个人信息】
 * 【没有隐私政策】
 * 没有隐私政策即有风险
 */
@EnableDetectPoint
public class DetectPoint40250501 extends AppletDetectPoint250501 {

}
