package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10105;

/**
 * APP向用户明示SDK的收集使用规则，未经用户同意，SDK存在收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为。
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中关于「IMEI、设备MAC地址和软件安装列表、通讯录和短信」等相关语句
 * 3、共XXX次（IMEI、设备MAC地址和软件安装列表、通讯录和短信）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 * 1、判断有隐私政策
 * 2、静默安装状态下，判断是否有触发收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为，且触发主体是SDK，
 * 10106 涉及此项（如果有其中任何一个行为触发，再通过关键字匹配隐私政策中，是否包含该行为的说明，如果无说明，则判断违规，有说明，则判断合规）
 *
 * 涉及行为：10002L,24009L,28005L,14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L,14001L,14002L,14003L,14004L
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020-12-18 15:31
 */
@EnableDetectPoint
public class DetectPoint4010105 extends DetectPoint10105 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
