package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350604.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 6. 是否存在热更新机制
 * 判断规则：
 * f)如第三方SDK存在热更新机制，App应要求SDK:
 * 1)在热更新推送前向App运营者告知本次热更新的具体内容及可能造成的影响；
 * 2)如热更新内容涉及个人信息处理目的、方式和范围的变更，应通过邮件、电话等逐一送达方式告知App运营者。
 * 发现风险：
 * 存在热更新机制
 */
@EnableDetectPoint
public class DetectPoint350604 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult  = getBaseDetectResult(commonDetectInfo, customDetectInfo);;
		try {
			List<String> hotFixSdkList = commonDetectInfo.getPrivacyDetectionService().getHotfixSdk(commonDetectInfo.getTaskId());
			if (!commonDetectInfo.getTerminalTypeEnum().isApplet() && !hotFixSdkList.isEmpty()) {
			    detectResult.setConclusion("存在热更新机制");
			    detectResult.setSuggestion("建议第三方SDK在热更新推送前向App运营者告知本次热更新的具体内容及可能造成的影响，如热更新内容涉及个人信息处理目的、方式和范围的变更，应通过邮件、电话等逐一送达方式告知App运营者。");
			    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
			} else {
			    detectResult.setConclusion("该检测项未发现风险");
			    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
			}
		} catch (Exception e) {
			e.getMessage();
			 detectResult.setConclusion("该检测项未发现风险");
			   detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
		}
        return detectResult;
    }

}
