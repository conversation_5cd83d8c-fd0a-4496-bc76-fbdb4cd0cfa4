package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10101;
import cn.ijiami.detection.miit.point.android.law35273.Law35273DetectPoint;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350101.java
 * @Description
 * App收集的个人信息是否满足最小必要性
 * 2. 收集的个人信息是否具有明确、合理、具体的个人信息处理目的
 * 判断规则
 * a)收集的个人信息应具有明确、合理、具体的个人信息处理目的；
 * 发现风险
 * 无隐私政策：
 * 1、在APP中通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示隐私政策。
 * 2、请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。
 * 有隐私政策：
 * 建议根据APP实际情况增加隐私政策，并在隐私政策中清晰明示【取表：行为-关键词中个人信息列的名称，多项时，用“、”隔开】等APP收集的个人信息具有明确、合理、具体的个人信息处理目的。
 */
@EnableDetectPoint
public class DetectPoint350101 extends DetectPoint10101 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = super.doDetect(commonDetectInfo, customDetectInfo);
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            List<String> suggestionList = Arrays.asList(
                    nonePrivacyDetailSuggestion(commonDetectInfo),
                    "请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。");
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            return detectResult;
        } else if (CollectionUtils.isNotEmpty(detectResult.getAnalysisResult())) {
            String actionTypeNames = String.join("、", getActionTypeNames(customDetectInfo, detectResult.getAnalysisResult()));
            String executorName = executor(commonDetectInfo);
            detectResult.setConclusion(buildSequenceText(
                    String.format("%s未见向用户明示收集的个人信息具有明确、合理、具体的个人信息处理目的，未经用户同意，存在收集%s的行为。",
                            executorName, actionTypeNames)
            ));
            detectResult.setSuggestion(buildSequenceText(
                    String.format("建议根据%s实际情况增加隐私政策，并在隐私政策中清晰明示%s等%s收集的个人信息具有明确、合理、具体的个人信息处理目的。",
                            executorName, actionTypeNames, executorName)));
            return detectResult;
        }
        return detectResult;
    }

}
