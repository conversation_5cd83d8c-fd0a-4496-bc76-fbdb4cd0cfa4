package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.helper.DetectPoint20107Helper;
import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20107;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.ijiami.detection.helper.DetectPointCommonHelper.addNoComplianceImage;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350501.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 1. App是否提前申请与业务功能无关的个人信息权限
 * 判断规则：
 * b)在用户未使用相关业务功能时，不应提前申请与当前业务功能无关的权限；
 * 1)单个场景在用户拒绝授权后，48h内弹窗提示用户打开权限的次数超过1次；
 * 2)每当用户重新打开App或使用无关的业务功能时，都会再次向用户索要授权或提示用户缺少相关授权。
 * 发现风险：
 * 建议APP在运行相应服务场景时，再向用户申请必要的授权【权限名称，多项时，用“、”隔开】等相关权限。
 */
@EnableDetectPoint
public class DetectPoint350501 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<ResultDataLogBO> noComplianceLogList = DetectPoint20107Helper.findNoComplianceLog(commonDetectInfo);
        if (!noComplianceLogList.isEmpty()) {
            List<String> permissionNameList = DetectPoint20107Helper.getPermissionNameList(commonDetectInfo, noComplianceLogList);
            // 判断是否只包含存储权限，如果是只是存在存储就不违规
            if (!permissionNameList.stream().allMatch(PermissionNameHelper.FILTERWORD::equalsIgnoreCase)) {
                noComplianceLogList.forEach(resultLog -> {
                    addNoComplianceImage(commonDetectInfo, detectResult, resultLog);
                });
                detectResult.setConclusion(buildSequenceTextFormat("%s首次打开（或其他时机），未见使用权限对应的相关产品或服务时，提前向用户弹窗申请开启%s等权限。",
                        executor(commonDetectInfo), String.join("、", permissionNameList)));
                detectResult.setSuggestion(buildSequenceTextFormat("建议%s在运行相应服务场景时，再向用户申请必要的授权%s等相关权限。",
                        executor(commonDetectInfo), String.join("、", permissionNameList)));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        }
        // 不涉及 添加图片
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            savePermissionImage(commonDetectInfo, detectResult);
        }
        return detectResult;
    }

}
