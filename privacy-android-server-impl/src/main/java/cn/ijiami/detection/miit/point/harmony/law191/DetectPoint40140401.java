package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;

/**
 * 收集个人信息的频度等超出业务功能实际需要；
 *
 * 发现风险：
 * 1、前台持续、固定频率，同一个主体，触发同一个人信息行为
 * 2、后台、App退出阶段触发个人信息行为（除了定位行为）
 */
@EnableDetectPoint
public class DetectPoint40140401 extends DetectPoint140401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}