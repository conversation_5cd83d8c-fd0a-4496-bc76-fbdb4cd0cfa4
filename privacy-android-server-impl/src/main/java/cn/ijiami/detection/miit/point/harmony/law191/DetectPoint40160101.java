package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint160101;

/**
 * 未提供有效的更正、删除个人信息及注销用户账号功能；
 * <p>
 * 未发现风险：
 * 1、隐私政策对【删除、更正（修改）、查询（访问）、注销个人信息】描述
 */
@EnableDetectPoint
public class DetectPoint40160101 extends DetectPoint160101 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
