package cn.ijiami.detection.miit.point.alipay.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint130801;

/**
 * 以欺诈、诱骗等不正当方式误导用户同意收集个人信息或打开可收集个人信息的权限，如故意欺瞒、掩饰收集使用个人信息的真实目的；
 *
 * 检测规则
 * 发现风险：
 * 1、检测出现【抽奖、红包】页面，点击，弹出权限申请窗口；
 * 2、通讯抓包数据代码片度内容与通讯录内容完全一直（预设通讯录内容）
 *
 * 数据展现
 * 发现风险：
 * 【无关隐私政策】
 * 1、检测出现【抽奖、红包】页面，点击，弹出权限申请窗口；（遍历流程中相关的截图【抽奖、红包】页面、权限截图）
 * 2、通讯抓包数据代码片度内容与通讯录内容完全一直（展示抓包数据：行为阶段、触发时间、IP、域名、端口号、地理位置、代码片段）
 *
 * 检测结论
 * 发现风险：
 * 1、App存在以欺诈、诱骗等不正当方式误导用户打开可收集个人信息的权限行为
 *
 * 未发现风险：
 * 1、该检测项未发现风险
 *
 */
@EnableDetectPoint
public class DetectPoint30130801 extends AppletDetectPoint130801 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
