package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 收集的个人信息类型或打开的可收集个人信息权限与现有业务功能无关；
 * 检测规则
 * 发现风险：
 * 1、启动应用之后，连续弹出权限授权窗口（除了存储、电话）
 *
 * 数据展现
 * 发现风险：
 * 【无关隐私政策】
 * 1、启动应用之后，连续弹出权限授权窗口（除了存储、电话）（遍历流程截图，仅权限图片）
 *
 * 检测结论
 * 发现风险：
 * 1、打开的可收集个人信息权限与现有业务功能无关
 * 未发现风险：
 * 1、该检测项未发现风险
 */
@EnableDetectPoint
public class DetectPoint140101 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);

        List<String> actionNameList = new ArrayList<>();
        // 把界面数据过滤出来，避免非界面数据干扰开始阶段的判断
        checkAllBehaviorStageContinuousApplyPermission(commonDetectInfo, actionNameList, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceText("打开的可收集个人信息权限与现有业务功能无关"));
            detectResult.setSuggestion(buildSequenceText("建议调整与无关业务功能的收集的个人信息类型或打开的可收集个人信息权限规则。"));
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        }
        return detectResult;
    }
}
