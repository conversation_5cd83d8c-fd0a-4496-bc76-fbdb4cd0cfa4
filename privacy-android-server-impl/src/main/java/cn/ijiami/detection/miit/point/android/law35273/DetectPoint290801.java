package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.8.a
 * 在中华人民共和国境内运营中收集和产生的个人信息向境外提供的，个人信息控制者应符合国家相关规定和相关标准的要求。
 * 规则
 * 发现风险
 * APP存在跨境传输行为，隐私政策中未说明。
 */
@EnableDetectPoint
public class DetectPoint290801 extends Law35273DetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<TPrivacyOutsideAddress> outsideAddresses = new ArrayList<>();
        outsideAddresses.addAll(getOutsideAddresses(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        outsideAddresses.addAll(getOutsideAddresses(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        outsideAddresses.addAll(getOutsideAddresses(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        outsideAddresses.addAll(getOutsideAddresses(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        List<TPrivacyOutsideAddress> addressList = outsideAddresses.stream().filter(address -> address.getOutside() == PrivacyStatusEnum.YES.getValue()).collect(Collectors.toList());
        if (!addressList.isEmpty()) {
            detectResult.setConclusion(buildSequenceTextFormat("%s存在跨境传输行为，未在隐私政策中声明。", executor(commonDetectInfo)));
            detectResult.setSuggestion("请在隐私政策中说明个人信息存储地域和跨境传输情况。");
            detectResult.setOutsideAddressResult(getActionOutsideList(addressList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }
}
