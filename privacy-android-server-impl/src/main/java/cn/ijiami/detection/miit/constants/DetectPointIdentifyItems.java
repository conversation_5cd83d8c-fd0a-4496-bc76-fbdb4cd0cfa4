package cn.ijiami.detection.miit.constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum DetectPointIdentifyItems {

    POINT_120101(
            new DetectPoint("业务功能在说明收集的个人信息时，是否无使用“等、例如”等方式概括说明", "")),
    POINT_PERSONAL_INFO(
            new DetectPoint("个人信息删除", "信息\\S*删除|删除\\S*信息"),
            new DetectPoint("个人信息更正", "信息\\S*更正|更正\\S*信息"),
            new DetectPoint("个人信息查询", "信息\\S*查询|查询\\S*信息"),
            new DetectPoint("个人信息注销", "信息\\S*注销|注销\\S*信息")),
    POINT_160301(new DetectPoint("投诉渠道，可包含：电子邮件、传真、客服热线、在线表格", ""));

    public final List<DetectPoint> items;

    DetectPointIdentifyItems(DetectPoint... items) {
        this.items = Collections.unmodifiableList(Arrays.asList(items));
    }

    public static List<DetectPoint> allItems() {
        return Arrays.stream(values()).flatMap(items -> items.items.stream()).collect(Collectors.toList());
    }


    public static final class DetectPoint {
        /**
         * 语义返回的项目名称
         */
        public final String nlpItemName;
        /**
         *  正则匹配规则，在语义识别找不到的情况下用正则匹配找
         */
        public final String regex;

        private DetectPoint(String nlpItem, String regex) {
            this.nlpItemName = nlpItem;
            this.regex = regex;
        }
    }

}
