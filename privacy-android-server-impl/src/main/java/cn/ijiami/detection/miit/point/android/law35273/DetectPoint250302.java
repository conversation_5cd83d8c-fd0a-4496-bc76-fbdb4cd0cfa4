package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.b
 * 应把个人信息主体自主做出的肯定性动作，如主动点击、勾选、填写等，作为产品或服务的特定业务功能的开启条件。
 * 个人信息控制者应仅个人信息主体开启该业务功能后，开始收集个人信息；
 *
 * 判断规则
 * 发现风险：
 * 【有隐私政策】
 * 1、用户首次注册、登陆时，未提供勾选框或默认勾选
 * 【无隐私政策】
 * 存在风险
 */
@EnableDetectPoint
public class DetectPoint250302 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(nonePrivacyDetailConclusion(commonDetectInfo)));
            detectResult.setSuggestion(buildSequenceText(nonePrivacyDetailSuggestion(commonDetectInfo)));
            return detectResult;
        }
        checkPrivacyPolicySelectedByDefault(commonDetectInfo, customDetectInfo, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat("%s在征求用户同意隐私政策环节，设置为默认勾选。", executor(commonDetectInfo)));
            detectResult.setSuggestion(buildSequenceTextFormat("%s在征求用户同意隐私政策环节，提供勾选按钮，不能设置为默认勾选，必须有用户明确点击选择环节。", executor(commonDetectInfo)));
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        }
        return detectResult;
    }
}
