package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20103;

/**
 * 用户注册登录时，APP向用户索取电话/通讯录/定位/短信/录音/相机/存储/日历等权限，用户拒绝授权后，应用无法正常注册或登录。
 * <p>
 * <blockquote><pre>
 *  截图
 *  检测结果会返回判断标识和截图，判断注册登录时，拒绝所有权限，应用是否直接退出，如果应用退出，则合规，否则违规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4020103 extends DetectPoint20103 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
