package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350604.java
 * @Description
 * 其他要求
 * 1. 是否告知并取得用户授权同意后进行剪切板或公共存储区的读取行为
 * 判断规则：
 * b)如存在读取剪切板或公共存储区的行为，应告知用户读取的信息范围和使用目的，不应在未取得用户同意的情况下，收集剪切板中包含的个人信息和公共存储区中的个人信息.
 * 发现风险：
 * APP未告知并取得用户授权同意后进行剪切板或公共存储区的读取行为
 */
@EnableDetectPoint
public class DetectPoint350701 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<ActionAnalyse> allStageActionAnalyses = new ArrayList<>();
        allStageActionAnalyses.addAll(filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        allStageActionAnalyses.addAll(filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        allStageActionAnalyses.addAll(filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        allStageActionAnalyses.addAll(filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
        Set<String> keyWordsList  = new HashSet<>();
        for (ActionAnalyse analyse:allStageActionAnalyses) {
            String keyWords = decideRules.get(analyse.getActionId());
            keyWordsList.add(keyWords);
            if (notInPrivacyPolicy(keyWords, commonDetectInfo)) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        }
        detectResult.setPrivacyPolicyFragment(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), keyWordsList));
        detectResult.setAnalysisResult(allStageActionAnalyses);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat(
                    "%s未告知并取得用户授权同意后进行剪切板或公共存储区的读取行为", executor(commonDetectInfo)));
            detectResult.setSuggestion(buildSequenceTextFormat(
                    "建议%s应告知用户读取的信息范围和使用目的，不应在未取得用户同意的情况下，收集剪切板中包含的个人信息和公共存储区中的个人信息", executor(commonDetectInfo)));
        }
        return detectResult;
    }

}
