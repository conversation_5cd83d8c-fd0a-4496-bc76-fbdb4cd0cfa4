package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 在申请打开可收集个人信息的权限，或申请收集用户身份证号、银行账号、行踪轨迹等个人敏感信息时，未同步告知用户其目的，或者目的不明确、难以理解；
 *
 * 未发现风险：
 * 1、申请系统权限授权前，已检测到对应权限已在权限弹窗前已有相关文字内容描述
 * 2、没有权限申请
 * 发现风险：
 * 1、申请系统权限授权前，未检测到对应权限已在权限弹窗前有相关文字内容描述（存在1个即发现风险）
 */
@EnableDetectPoint
public class DetectPoint120301 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        CheckPermissionPopupResult result = permissionPopupCheck(commonDetectInfo);
        if (result.isNonInvolved()) {
            result.getImageLogsList().forEach(resultDataLogBO -> addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO));
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            result.getImageLogsList().forEach(resultDataLogBO -> addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO));
            detectResult.setConclusion(buildSequenceTextFormat("%s申请%s等个人信息相关权限时，未同步告知使用目的。",
                    executor(commonDetectInfo), String.join("、", result.getPermissionNameList())));
            detectResult.setSuggestion(buildSequenceTextFormat("%s在申请个人信息相关系统权限前，详细说明权限的使用目的。",
                    executor(commonDetectInfo)));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }



}
