package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint130501;

/**
 * 以默认选择同意隐私政策等非明示方式征求用户同意；
 *
 * 未发现风险：
 * 【有隐私政策】
 * 1、隐私政策授权时，提供勾选框，并无默认勾选
 * 发现风险：
 * 1、隐私政策授权时，无提供勾选框或默认勾选
 * 【无隐私政策】
 * 存在风险
 */
@EnableDetectPoint
public class DetectPoint40130501 extends AppletDetectPoint130501 {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
