package cn.ijiami.detection.miit.point.wechat.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint2020107;

/**
 * APP首次打开（或其他时机），未见使用权限对应的相关产品或服务时，提前向用户弹窗申请开启通讯录/定位/短信/录音/相机/XXX等权限。
 * <p>
 * <blockquote><pre>
 * 截图
 * 检测结果会返回判断标识和截图，判断应用静默安装状态下是否直接弹起权限授权窗口，如果是，则违规，否则合规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint2020107 extends AppletDetectPoint2020107 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
