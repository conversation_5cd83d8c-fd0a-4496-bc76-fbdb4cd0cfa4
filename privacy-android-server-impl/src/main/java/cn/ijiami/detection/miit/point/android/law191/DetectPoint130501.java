package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.HashSet;
import java.util.Set;

/**
 * 以默认选择同意隐私政策等非明示方式征求用户同意；
 *
 * 未发现风险：
 * 【有隐私政策】
 * 1、隐私政策授权时，提供勾选框，并无默认勾选
 * 发现风险：
 * 1、隐私政策授权时，无提供勾选框或默认勾选
 * 【无隐私政策】
 * 存在风险
 */
@EnableDetectPoint
public class DetectPoint130501 extends PrivacyUiDetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setSuggestion(buildSequenceTextFormat("%s中隐私政策通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示", executor(commonDetectInfo)));
            return detectResult;
        }
        checkPrivacyPolicySelectedByDefault(commonDetectInfo, customDetectInfo, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat("%s以默认选择同意隐私政策等非明示方式征求用户同意", executor(commonDetectInfo)));
        }
        return detectResult;
    }

}
