package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350602;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350602.java
 * @Description
 * App是否存在违规嵌入使用第三方SDK情况
 * 2. 是否明示三方SDK收集使用个人信息情况
 * 判断规则：
 * d)应向用户告知嵌入的第三方SDK名称、SDK收集的个人信息种类、使用目的及申请的系统权限、申请目的等，并取得用户同意。
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint40350602 extends DetectPoint350602 {


}
