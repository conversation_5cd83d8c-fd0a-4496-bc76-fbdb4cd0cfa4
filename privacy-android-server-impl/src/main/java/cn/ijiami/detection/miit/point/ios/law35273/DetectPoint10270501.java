package cn.ijiami.detection.miit.point.ios.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint270501;
import cn.ijiami.detection.miit.point.android.law35273.Law35273DetectPoint;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 7.5.a
 * a)在向个人信息主体提供业务功能的过程中使用个性化展示的，应显著区分个性化展示的内容和非个性化展示的内容；
 * 注：显著区分的方式包括但不限于：标明“定推”等字样，或通过不同的栏目、版块、页面分别展示等。
 *
 * 判断规则
 * 发现风险
 * 隐私政策中有定向推送、精准营销等功能的描述，界面上没有区分。
 *
 */
@EnableDetectPoint
public class DetectPoint10270501 extends DetectPoint270501 {

}
