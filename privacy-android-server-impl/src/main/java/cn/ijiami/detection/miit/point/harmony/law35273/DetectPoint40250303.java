package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250303;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.d
 * 个人信息主体不同意使用、关闭或退出特定业务功能的，不应频繁征求个人信息主体的同意。
 *
 * 判断规则
 * 发现风险：
 * 1、拒绝权限，App退出，再次打开应用相同功能界面再弹出权限授权窗口（主动点击除外）
 * 2、拒绝权限，之后再次弹出权限
 */
@EnableDetectPoint
public class DetectPoint40250303 extends AppletDetectPoint250303 {

}
