package cn.ijiami.detection.miit.point.android;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawJudgmentHelper.java
 * @Description 法规判断帮助
 * @createTime 2023年10月31日 12:29:00
 */
public class LawJudgmentHelper {

    /**
     * 设置提取所有涉及个人信息的行为
     * @param commonDetectInfo
     * @param customDetectInfo
     */
    public static void setAllPersonalDecideRuleActionIds(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        customDetectInfo.setDecideRuleActionIds(commonDetectInfo.getActionNougatMap()
                .values()
                .stream()
                .filter(actionNougat -> Objects.nonNull(actionNougat) && actionNougat.getPersonal() == PrivacyStatusEnum.YES.getValue())
                .map(TActionNougat::getActionId)
                .collect(Collectors.toSet()));
    }

}
