package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

/**
 * 在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则；
 *
 * 未发现风险：
 * 1、检测到隐私政策
 * 发现风险：
 * 1、未检测到隐私政策
 */
@EnableDetectPoint
public class DetectPoint110201 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonCompliance(commonDetectInfo, customDetectInfo);
        // 不存在隐私政策, 不合规
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setSuggestion(buildSequenceText(getDefaultSuggestion(commonDetectInfo)));
            return detectResult;
        }
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        detectResult.setConclusion(buildSequenceText("隐私政策文本正常"));
        return detectResult;
    }

}
