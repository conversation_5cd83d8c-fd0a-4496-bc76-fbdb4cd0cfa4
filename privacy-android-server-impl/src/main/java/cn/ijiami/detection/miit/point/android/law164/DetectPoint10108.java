package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.HashSet;
import java.util.Set;

/**
 * App在征求用户同意环节，设置为默认勾选。
 * <blockquote><pre>
 * 截图
 *
 * 检测结果会返回判断标识和截图，根据判断标识做判断
 * 判断checkbox=ture
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020-12-18 15:31
 */
@EnableDetectPoint
public class DetectPoint10108 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);

        if (commonDetectInfo.getResultDataLogs() == null) {
            return detectResult;
        }
        checkPrivacyPolicySelectedByDefault(commonDetectInfo, customDetectInfo, detectResult);
        return detectResult;
    }

}
