package cn.ijiami.detection.miit.exception;

import cn.ijiami.detection.analyzer.exception.IParserExceptionEnum;

/**
 * 解析异常
 *
 * <AUTHOR>
 * @date 2020/10/12 10:07
 **/
public class MiitRunException extends RuntimeException {

    private Integer code;

    private String message;

    public MiitRunException(IParserExceptionEnum parserExceptionEnum) {
        this.code = parserExceptionEnum.getCode();
        this.message = parserExceptionEnum.getMessage();
    }

    public MiitRunException(IParserExceptionEnum parserExceptionEnum, String message) {
        this.code = parserExceptionEnum.getCode();
        this.message = message;
    }

    public MiitRunException(IParserExceptionEnum parserExceptionEnum, Throwable throwable) {
        super(throwable);
        this.code = parserExceptionEnum.getCode();
        this.message = throwable.getMessage();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
