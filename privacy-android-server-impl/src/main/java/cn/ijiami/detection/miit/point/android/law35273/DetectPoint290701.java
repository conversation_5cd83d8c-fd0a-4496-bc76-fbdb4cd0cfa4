package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.utils.SdkUtils.actionKeyWordInSdk;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.7.c
 * 应向个人信息主体明确标识产品或服务由第三方提供；
 * 规则
 * 发现风险
 * SDK收集个人信息，但未在隐私政策中声明 应要求第三方根据本标准相关要求向个人信息主体征得收集个人信息同意，必要时核验其实现的方式；
 */
@EnableDetectPoint
public class DetectPoint290701 extends AbstractDetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 各个阶段的行为
        List<ActionAnalyse> allList = new ArrayList<>();
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        List<ActionAnalyse> sdkAnalyseList = allList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                .collect(Collectors.toList());
        return check(commonDetectInfo, customDetectInfo, sdkAnalyseList);
    }


    private DetectResult check(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                               List<ActionAnalyse> allList) {
        List<ActionAnalyse> involvedAnalyseList = new ArrayList<>();
        Set<String> privacyPolicyKeyword = new HashSet<>();
        Set<String> checkListTextKeyword = new HashSet<>();
        Set<String> fragmentList = new HashSet<>();
        Set<String> noMatchNameList = new HashSet<>();
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        for (ActionAnalyse analyse : allList) {
            String[] sdkNames = analyse.getExecutor().split(",");
            Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
            for (String sdkName : sdkNames) {
                // 优先匹配第三方sdk清单的数据
                Optional<CheckList.Row> sdkRow = findCheckListBySdkName(commonDetectInfo, sdkName);
                if (sdkRow.isPresent()) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    // sdk行为没在第三方sdk清单中声明
                    if (actionKeyWordInSdk(keyWord, sdkRow.get())) {
                        // 把第三方sdk转成文字加到隐私片段的展示数据中
                        fragmentList.add(sdkRow.get().privacyPolicyFragment());
                    } else {
                        involvedAnalyseList.add(analyse);
                        noMatchNameList.add(sdkName);
                    }
                } else if (isSdkNameInCheckListText(commonDetectInfo, sdkName)) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        // sdk行为没在隐私政策中
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getThirdPartySharingChecklist().getFullText()).find()) {
                            checkListTextKeyword.add(keyWord);
                        } else {
                            involvedAnalyseList.add(analyse);
                            noMatchNameList.add(sdkName);
                        }
                    }
                } else if (isSdkNameInPolicyContent(commonDetectInfo, sdkName)) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        // sdk行为没在隐私政策中
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find()) {
                            privacyPolicyKeyword.add(keyWord);
                        } else {
                            involvedAnalyseList.add(analyse);
                            noMatchNameList.add(sdkName);
                        }
                    }
                } else {
                    // sdk没在隐私政策中
                    involvedAnalyseList.add(analyse);
                    noMatchNameList.add(sdkName);
                }
            }
        }
        detectResult.setPrivacyPolicyFragment(buildFragment(commonDetectInfo, fragmentList, checkListTextKeyword, privacyPolicyKeyword));
        judgeInvolved(commonDetectInfo, detectResult, noMatchNameList, involvedAnalyseList);
        return detectResult;
    }

    protected void judgeInvolved(CommonDetectInfo commonDetectInfo, DetectResult detectResult,
                                 Set<String> noMatchNameList, List<ActionAnalyse> involvedAnalyseList) {
        if (!noMatchNameList.isEmpty()) {
            String sdkNames = String.join("、", noMatchNameList);
            detectResult.setConclusion(String.format("%s等%s存在收集个人信息行为，未在隐私政策中声明。", sdkNames, pluginName(commonDetectInfo)));
            detectResult.setSuggestion(String.format("请在隐私政策中声明收集个人信息的全部%s。", pluginName(commonDetectInfo)));
            detectResult.setAnalysisResult(involvedAnalyseList);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
    }

}
