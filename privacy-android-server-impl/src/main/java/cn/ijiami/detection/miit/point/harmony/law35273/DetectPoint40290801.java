package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint290801;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.8.a
 * 在中华人民共和国境内运营中收集和产生的个人信息向境外提供的，个人信息控制者应符合国家相关规定和相关标准的要求。
 * 规则
 * 发现风险
 * APP存在跨境传输行为，隐私政策中未说明。
 */
@EnableDetectPoint
public class DetectPoint40290801 extends AppletDetectPoint290801 {

}
