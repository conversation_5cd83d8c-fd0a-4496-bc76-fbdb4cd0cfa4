package cn.ijiami.detection.miit.domain;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;

/**
 * 检测结果
 *
 * <AUTHOR>
 * @date 2020-12-21 11:41
 */
public class DetectResult implements Serializable {

    private static final long serialVersionUID = -8914130497988554322L;

    /**
     * 任务Id
     */
    private Long                 taskId;
    /**
     * 检测点编号
     */
    private String               itemNo;
    /**
     * 合规状态
     */
    private MiitDetectStatusEnum complianceStatus;
    /**
     * 隐私政策文件截图地址
     */
    private String               privacyScreenshot;
    /**
     * 检测截图地址
     */
    private Set<String>          screenshots;
    /**
     * 隐私政策文本内容
     */
    private String               privacyPolicyFragment;
    /**
     * 总计触发次数
     */
    private Integer              count;
    /**
     * 行为分析后数据集合
     */
    private List<ActionAnalyse>  analysisResult;

    /**
     * 传输个人信息数据集合
     */
    private List<ActionNetwork> sensitiveWordResult;

    /**
     * 储存个人信息数据集合
     */
    private List<ActionSharedPerfs> sensitivePrefsResult;

    /**
     * 境外请求数据集合
     */
    private List<ActionOutSide>     outsideAddressResult;

    /**
     * 整改建议
     */
    private String              suggestion;

    /**
     * 检测结论
     */
    private String              conclusion;

    /**
     * 索取的权限名字
     */
    private Set<String>          requestPermissionNames;

    /**
     * 检测截图内容md5
     */
    private Set<String>          screenshotMd5s;
    
	public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public MiitDetectStatusEnum getComplianceStatus() {
        return complianceStatus;
    }

    public void setComplianceStatus(MiitDetectStatusEnum complianceStatus) {
        this.complianceStatus = complianceStatus;
    }

    public String getPrivacyScreenshot() {
        return privacyScreenshot;
    }

    public void setPrivacyScreenshot(String privacyScreenshot) {
        this.privacyScreenshot = privacyScreenshot;
    }

    public Set<String> getScreenshots() {
        return screenshots;
    }

    public void setScreenshots(Set<String> screenshots) {
        this.screenshots = screenshots;
    }

    public String getPrivacyPolicyFragment() {
        return privacyPolicyFragment;
    }

    public void setPrivacyPolicyFragment(String privacyPolicyFragment) {
        this.privacyPolicyFragment = privacyPolicyFragment;
    }

    public List<ActionAnalyse> getAnalysisResult() {
        return analysisResult;
    }

    public void setAnalysisResult(List<ActionAnalyse> analysisResult) {
        this.analysisResult = analysisResult;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Set<String> getScreenshotMd5s() {
        return screenshotMd5s;
    }

    public void setScreenshotMd5s(Set<String> screenshotMd5s) {
        this.screenshotMd5s = screenshotMd5s;
    }

    public Set<String> getRequestPermissionNames() {
        return requestPermissionNames;
    }

    public void setRequestPermissionNames(Set<String> requestPermissionNames) {
        this.requestPermissionNames = requestPermissionNames;
    }

    public List<ActionNetwork> getSensitiveWordResult() {
        return sensitiveWordResult;
    }

    public void setSensitiveWordResult(List<ActionNetwork> sensitiveWordResult) {
        this.sensitiveWordResult = sensitiveWordResult;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public List<ActionSharedPerfs> getSensitivePrefsResult() {
        return sensitivePrefsResult;
    }

    public void setSensitivePrefsResult(List<ActionSharedPerfs> sensitivePrefsResult) {
        this.sensitivePrefsResult = sensitivePrefsResult;
    }

    public List<ActionOutSide> getOutsideAddressResult() {
        return outsideAddressResult;
    }

    public void setOutsideAddressResult(List<ActionOutSide> outsideAddressResult) {
        this.outsideAddressResult = outsideAddressResult;
    }
}
