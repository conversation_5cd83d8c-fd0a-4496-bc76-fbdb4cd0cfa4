package cn.ijiami.detection.miit.point.ios.law191;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120101;

import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_120101;

/**
 * 未逐一列出App(包括委托的第三方或嵌入的第三方代码、插件)收集使用个人信息的目的、方式、范围等；
 * <p>
 * 未发现风险：
 * 1、隐私政策中不包含（等、例如、设备信息）
 * 2、触发个人信息行为的SDK全部在隐私政策中说明
 * 发现风险：
 * 1、隐私政策中包含（等、例如、设备信息）
 * 2、触发个人信息行为的SDK存在1个及以上未在隐私政策说明
 */
@EnableDetectPoint
public class DetectPoint10120101 extends DetectPoint120101 {

}
