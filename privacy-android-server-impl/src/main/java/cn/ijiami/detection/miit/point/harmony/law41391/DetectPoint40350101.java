package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350101;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350101.java
 * @Description
 * App收集的个人信息是否满足最小必要性
 * 2. 收集的个人信息是否具有明确、合理、具体的个人信息处理目的
 * 判断规则
 * a)收集的个人信息应具有明确、合理、具体的个人信息处理目的；
 * 发现风险
 * 无隐私政策：
 * 1、在APP中通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示隐私政策。
 * 2、请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。
 * 有隐私政策：
 * 建议根据APP实际情况增加隐私政策，并在隐私政策中清晰明示【取表：行为-关键词中个人信息列的名称，多项时，用“、”隔开】等APP收集的个人信息具有明确、合理、具体的个人信息处理目的。
 */
@EnableDetectPoint
public class DetectPoint40350101 extends DetectPoint350101 {

}
