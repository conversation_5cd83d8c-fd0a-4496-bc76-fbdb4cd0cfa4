package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20104;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * APP运行时，向用户索取当前服务场景未使用到的电话/通讯录/定位/短信/录音/相机/存储/日历等权限，且用户拒绝授权后，应用退出或关闭（应用陷入弹窗循环，无法正常使用）。
 * <p>
 * <blockquote><pre>
 * 截图
 * 检测结果会返回判断标识和截图，判断启动应用后，拒绝所有权限授权弹窗，应用是否退出，如果应用退出，则不合规，否则不涉及
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint1020104 extends DetectPoint20104 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
