package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350202;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350202.java
 * @Description
 * App收集的个人信息是否满足告知同意要求
 * 2. App是否存在用户拒绝或撤回同意收集非必要个人信息时而拒绝用户使用该App的基本业务情况
 * 判断规则：
 * d)当用户同意收集App必要个人信息时，应保障用户可拒绝或撤回同意收集非必要个人信息且不应因用户拒绝或撤回同意提供非必要个人信息，而拒绝用户使用该App的基本业务功能；
 * 注2：例如提供“退出”“上一步”“关闭”“取消”的按钮等方式供用户拒绝个人信息收集。
 * 发现风险：
 * 建议用户拒绝或撤回同意收集非必要个人信息时或打开非必要权限仍提供App的基础业务功能。
 */
@EnableDetectPoint
public class DetectPoint40350202 extends DetectPoint350202 {

}
