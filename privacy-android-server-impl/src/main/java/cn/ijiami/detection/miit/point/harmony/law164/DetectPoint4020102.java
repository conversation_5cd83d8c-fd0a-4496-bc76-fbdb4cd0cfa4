package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20102;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * APP运行时，未向用户告知XXX权限的目的，向用户索取当前服务场景未使用到的通讯录、定位、短信、录音、相机、日历等权限，且用户拒绝授权后，应用退出或关闭相关功能，无法正常使用。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、截图
 * 1、隐私政策截图
 * 2、隐私政策中检测到「通讯录、定位、短信、录音、相机、日历」等关键词语句
 * 3、截图
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断隐私政策，无隐私政策
 * 2、根据检测结果返回的标识，判断应用是否退出，如果不退出，则违规，退出了，则合规
 * 二、有隐私政策
 * 1、判断隐私政策、有隐私政策
 * 2、根据关键字，匹配隐私政策中，是否包含通讯录、定位、短信、录音、相机、日历等权限的说明，如无，则违规，有则继续判断第三个条件
 * 3、根据检测结果返回的标识，判断应用是否退出，如果不退出，则违规，退出了，则合规
 * 核对判断依据：
 * 1、拒绝授权退出，不涉及
 * 2、拒绝授权不退出，判断隐私政策是否有说明，有，则不涉及，否则不合规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4020102 extends DetectPoint20102 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
