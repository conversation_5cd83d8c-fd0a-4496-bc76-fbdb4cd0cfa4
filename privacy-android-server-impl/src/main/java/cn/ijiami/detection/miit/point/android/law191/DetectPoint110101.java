package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.utils.ConstantsUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 在App中没有隐私政策，或者隐私政策中没有收集使用个人信息规则；
 *
 * 未发现风险：
 * a.检测到隐私政策
 * b.获取到隐私政策文本内容字符串大于500字符
 * 发现风险：
 * 1、a.检测到隐私政策；b.隐私政策文本内容字符串小于等于500字符
 * 2、未检测到隐私政策
 */
@EnableDetectPoint
public class DetectPoint110101 extends AbstractDetectPoint {


    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 不存在隐私政策, 不合规
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setSuggestion(getDefaultSuggestion(commonDetectInfo));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            return detectResult;
        }
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        if (StringUtils.length(commonDetectInfo.getPrivacyPolicyContent()) > ConstantsUtils.LAW_191_PRIVACY_POLICY_MIN_LENGTH) {
            detectResult.setConclusion(buildSequenceText("隐私政策文本正常"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText("检测到隐私政策，但隐私政策文本获取存在问题"));
            detectResult.setSuggestion(getDefaultSuggestion(commonDetectInfo));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }


}
