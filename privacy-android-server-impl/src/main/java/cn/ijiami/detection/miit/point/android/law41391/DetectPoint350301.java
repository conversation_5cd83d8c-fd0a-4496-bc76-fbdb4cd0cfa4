package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint250402;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * 敏感个人信息告知同意
 * 1. 收集敏感个人信息时，是否同步告知用户收集使用目的并取得用户单独同意
 * 判断规则：与35273检测 5.4.a 一致
 */
@EnableDetectPoint
public class DetectPoint350301 extends DetectPoint250402 {

    @Override
    protected String permissionNameConclusion(CommonDetectInfo commonDetectInfo, CheckPermissionPopupResult result) {
        return String.format("%s收集敏感个人信息时，未同步告知用户收集使用目的并取得用户单独同意。", executor(commonDetectInfo));
    }
}
