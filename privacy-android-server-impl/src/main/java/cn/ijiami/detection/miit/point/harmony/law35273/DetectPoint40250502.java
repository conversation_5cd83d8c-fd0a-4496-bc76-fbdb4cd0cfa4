package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250502;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.5.b
 *个人信息保护政策所告知的信息应真实、准确、完整；
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 APP收集的个人信息与隐私政策不一致
 * 2 SDK收集的个人信息与隐私政策不一致
 * 3 APP实际申请的个人信息权限与隐私政策不一致
 * 【无隐私政策】
 * 有个人信息行为或者权限就存在风险
 */
@EnableDetectPoint
public class DetectPoint40250502 extends AppletDetectPoint250502 {
}