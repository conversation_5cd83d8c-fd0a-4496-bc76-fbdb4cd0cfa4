package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint260301;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 6.3.a
 * a)传输和存储个人敏感信息时，应采用加密等安全措施；
 * 注：采用密码技术时宜遵循密码管理相关国家标准
 *
 * 判断规则
 * 1 明文传输个人信息
 * 2 明文存储个人信息
 */
@EnableDetectPoint
public class DetectPoint40260301 extends AppletDetectPoint260301 {

}