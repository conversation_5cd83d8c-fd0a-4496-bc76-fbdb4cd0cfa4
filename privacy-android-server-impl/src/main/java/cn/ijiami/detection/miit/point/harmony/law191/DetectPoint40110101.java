package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint110101;

/**
 * 在App中没有隐私政策，或者隐私政策中没有收集使用个人信息规则；
 *
 * 未发现风险：
 * a.检测到隐私政策
 * b.获取到隐私政策文本内容字符串大于500字符
 * 发现风险：
 * 1、a.检测到隐私政策；b.隐私政策文本内容字符串小于等于500字符
 * 2、未检测到隐私政策
 */
@EnableDetectPoint
public class DetectPoint40110101 extends DetectPoint110101 {


    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }


}
