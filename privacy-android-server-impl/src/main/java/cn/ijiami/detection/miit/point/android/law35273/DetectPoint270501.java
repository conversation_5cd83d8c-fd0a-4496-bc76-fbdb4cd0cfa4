package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 7.5.a
 * a)在向个人信息主体提供业务功能的过程中使用个性化展示的，应显著区分个性化展示的内容和非个性化展示的内容；
 * 注：显著区分的方式包括但不限于：标明“定推”等字样，或通过不同的栏目、版块、页面分别展示等。
 *
 * 判断规则
 * 发现风险
 * 隐私政策中有定向推送、精准营销等功能的描述，界面上没有区分。
 *
 */
@EnableDetectPoint
public class DetectPoint270501 extends Law35273DetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        if (checkHaveRecommendContent(commonDetectInfo, customDetectInfo, detectResult)) {
            detectResult.setConclusion(buildSequenceText("隐私政策中有定向推送、精准营销等功能的描述，界面上没有区分。"));
            detectResult.setSuggestion(buildSequenceTextFormat("%s需要有显著区分定向推送或精准营销功能的服务标识。", executor(commonDetectInfo)));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }
}
