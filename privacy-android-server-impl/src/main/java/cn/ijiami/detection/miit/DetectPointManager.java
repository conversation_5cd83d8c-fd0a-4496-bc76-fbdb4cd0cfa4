package cn.ijiami.detection.miit;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-12-18 16:48
 */
public class DetectPointManager implements ApplicationContextAware {

    public static final String DETECT_CLASS_PREFIX = "DetectPoint";

    private ApplicationContext applicationContext;

    private Map<String, AbstractDetectPoint> detectPointMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取类名称
     *
     * @param itemNo
     * @return
     */
    private String getBeanName(String itemNo) {
        return DETECT_CLASS_PREFIX + itemNo;
    }

    public void init() {
        Map<String, Object> detectPoints = applicationContext.getBeansWithAnnotation(EnableDetectPoint.class);
        if (CollectionUtils.isEmpty(detectPoints)) {
            return;
        }
        detectPointMap = new HashMap<>(16);
        detectPoints.forEach((key, value) -> {
            String name = value.getClass().getName();
            detectPointMap.put(name.substring(name.lastIndexOf(".") + 1), (AbstractDetectPoint) value);
        });
    }

    /**
     * 获取检测项实例
     *
     * @param itemNo
     * @return
     */
    public AbstractDetectPoint getInstance(String itemNo) {
        if (CollectionUtils.isEmpty(detectPointMap)) {
            return null;
        }
        return detectPointMap.get(getBeanName(itemNo));
    }
}
