package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350301;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * 敏感个人信息告知同意
 * 1. 收集敏感个人信息时，是否同步告知用户收集使用目的并取得用户单独同意
 * 判断规则：与35273检测 5.4.a 一致
 */
@EnableDetectPoint
public class DetectPoint40350301 extends DetectPoint350301 {

}
