package cn.ijiami.detection.miit.point.android.law191;

import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_160301;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import cn.ijiami.detection.VO.PrivacyDayReplyRegVO;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.utils.PrivacyDayReplyRegUtils;

/**
 * 未建立并公布个人信息安全投诉、举报渠道，或未在承诺时限内（承诺时限不得超过15个工作日，无承诺时限的，以15个工作日为限）受理并处理的。
 *
 * 未发现风险：
 * 1、隐私政策对【投诉、反馈】及渠道进行描述
 */
@EnableDetectPoint
public class DetectPoint160301 extends AbstractDetectPoint {

    private static final String NON_COMPLIANCE_TEXT = "投诉、反馈及相应渠道未在隐私中说明或者承诺时限不得超过15个工作日";
    private static final String NON_COMPLIANCE_SUGGEST = "建议在隐私政策中公布个人信息安全投诉、举报渠道、承诺时限不得超过15个工作日";

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            Set<String> keyWordList = new HashSet<>(Arrays.asList("投诉", "反馈", "渠道"));
            // 先进行语义识别
            if (commonDetectInfo.isNlpSuccess()) {
                if (commonDetectInfo.getNlpResponse().isMatchIdentifyItem(POINT_160301.items)) {
                    detectResult.setPrivacyPolicyFragment(commonDetectInfo.getNlpResponse().getIdentifyFragment(POINT_160301.items));
                    detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
                    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
                } else {
                    detectResult.setConclusion(buildSequenceText(NON_COMPLIANCE_TEXT));
                    detectResult.setSuggestion(buildSequenceText(NON_COMPLIANCE_SUGGEST));
                    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
                }
            } else {
                if (keyWordList.stream()
                        .anyMatch(keyWord -> commonDetectInfo.getPrivacyPolicyContent().contains(keyWord))) {
                    detectResult.setPrivacyPolicyFragment(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), keyWordList));
                    detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
                    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
                } else {
                    detectResult.setConclusion(buildSequenceText(NON_COMPLIANCE_TEXT));
                    detectResult.setSuggestion(buildSequenceText(NON_COMPLIANCE_SUGGEST));
                    detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
                }
            }
            
            //识别隐私政策内容是否存在超过15天的处理
            if(StringUtils.isNoneBlank(commonDetectInfo.getPrivacyPolicyContent())) {
            	PrivacyDayReplyRegVO regVO = PrivacyDayReplyRegUtils.privacyDayReplyRegs(commonDetectInfo.getPrivacyPolicyContent());
            	if(regVO != null) {
            		String policyFragment =  detectResult.getPrivacyPolicyFragment();
            		if(StringUtils.isNoneBlank(policyFragment)){
                        detectResult.setPrivacyPolicyFragment(policyFragment + "\n\t"+ regVO.getContent());
            		}
                    if (regVO.getDay()>15) {
                        detectResult.setConclusion(buildSequenceText(NON_COMPLIANCE_TEXT));
                        detectResult.setSuggestion(buildSequenceText(NON_COMPLIANCE_SUGGEST));
                        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
                    }
            	}
            }
        } else {
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setSuggestion(buildSequenceText(NON_COMPLIANCE_SUGGEST));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }
}