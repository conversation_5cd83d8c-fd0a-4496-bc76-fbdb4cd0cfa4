package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350703;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350703.java
 * @Description
 * 其他要求
 * 3. App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 * 判断规则：
 * e)不应通过积分、奖励、优惠、红包等方式，欺骗误导用户提供与App业务功能无关的个人信息或权限；
 * 发现风险：
 * App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 */
@EnableDetectPoint
public class DetectPoint40350703 extends DetectPoint350703 {

}
