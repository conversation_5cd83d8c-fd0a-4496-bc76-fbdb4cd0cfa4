package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250201;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.2.b
 * 自动采集个人信息的频率应是实现产品或服务的业务功能所必需的最低频率
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 同意隐私政策前，存在收集个人信息行为
 * 2 前台静默状态下收集个人信息。
 * 3 前台运行时，切换功能菜单收集同一个人信息。
 * 4 后台运行时收集个人信息。
 * 5 前台运行时，频率超过1次/秒。
 * 【无隐私政策】
 * 存在收集个人信息行为
 * @createTime 2022年03月24日 17:32:00
 */
@EnableDetectPoint
public class DetectPoint40250201 extends AppletDetectPoint250201 {


}
