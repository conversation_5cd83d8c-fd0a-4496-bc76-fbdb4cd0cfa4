package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350503;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350503.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 3. App是否存在捆绑方式要求用户一次性打开多项业务功能权限
 * 判断规则：
 * d)不应以捆绑方式要求用户一次性同意打开多个系统权限；
 * 注4：安卓App的目标API等级低于23（targetSdkVersion<23)属于捆绑授权的常见情形。
 * 发现风险：
 * 1、请调整targetSdkVersion值大于或等于23。
 * 2、请调整权限申请规则
 */
@EnableDetectPoint
public class DetectPoint40350503 extends DetectPoint350503 {


}
