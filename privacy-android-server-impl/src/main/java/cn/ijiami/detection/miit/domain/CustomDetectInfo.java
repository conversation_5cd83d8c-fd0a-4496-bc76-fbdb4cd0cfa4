package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.entity.TApplyPermission;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 自定义检测信息
 * 包含判断信息、检测结果
 *
 * <AUTHOR>
 * @date 2020-12-21 11:12
 */
public class CustomDetectInfo implements Serializable {

    private static final long serialVersionUID = 411633641934654392L;

    /**
     * 判断点编号
     */
    private String            itemNo;
    /**
     * 判断条件的action Id
     */
    private Set<Long>         decideRuleActionIds;
    /**
     * 判断条件的关键字
     */
    private Set<String>       decideRuleKeys;
    /**
     * 权限对应的关键字正则
     */
    private Map<Long, String> actionWithKeyRegex;
    /**
     * 权限对应的行为类型名
     */
    private Map<Long, String> actionTypeNames;
    /**
     * 其他关键词校验
     */
    private Map<String, String> keyWordRegex;

    /**
     * 云手机内置通讯录
     */
    private Map<String, String> cloudPhoneAddressBook;

    /**
     * 建议模板
     */
    private String            suggestionTemplate;

    public Map<String, String> getKeyWordRegex() {
		return keyWordRegex;
	}

	public void setKeyWordRegex(Map<String, String> keyWordRegex) {
		this.keyWordRegex = keyWordRegex;
	}

	public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public Set<Long> getDecideRuleActionIds() {
        return decideRuleActionIds;
    }

    public void setDecideRuleActionIds(Set<Long> decideRuleActionIds) {
        this.decideRuleActionIds = decideRuleActionIds;
    }

    public Set<String> getDecideRuleKeys() {
        return decideRuleKeys;
    }

    public void setDecideRuleKeys(Set<String> decideRuleKeys) {
        this.decideRuleKeys = decideRuleKeys;
    }

    public Map<Long, String> getActionWithKeyRegex() {
        return actionWithKeyRegex;
    }

    public void setActionWithKeyRegex(Map<Long, String> actionWithKeyRegex) {
        this.actionWithKeyRegex = actionWithKeyRegex;
    }

    public String getSuggestionTemplate() {
        return suggestionTemplate;
    }

    public void setSuggestionTemplate(String suggestionTemplate) {
        this.suggestionTemplate = suggestionTemplate;
    }

    public Map<String, String> getCloudPhoneAddressBook() {
        return cloudPhoneAddressBook;
    }

    public void setCloudPhoneAddressBook(Map<String, String> cloudPhoneAddressBook) {
        this.cloudPhoneAddressBook = cloudPhoneAddressBook;
    }

    public Map<Long, String> getActionTypeNames() {
        return actionTypeNames;
    }

    public void setActionTypeNames(Map<Long, String> actionTypeNames) {
        this.actionTypeNames = actionTypeNames;
    }
}
