package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.nlp.result.ONlpResponse;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.OcrService;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-12-21 11:06
 */
public class CommonDetectInfo {

    /**
     * 任务ID
     */
    private Long                                 taskId;

    /**
     * mongodb文档id
     */
    private String                               apkDetectionDetailId;

    /**
     * 应用包名，后期有其他可换成资产信息
     */
    private String                               apkPackageName;
    /**
     * 应用名，后期有其他可换成资产信息
     */
    private String                               apkName;
    /**
     * 应用包名，后期有其他可换成资产信息
     */
    private int                                  apkTargetSdkVerion;
    /**
     * 文件路径
     */
    private String                               filePath;
    /**
     * 推送类sdkId
     */
    private Set<Long>                            sdkTypePushIds;
    /**
     * 行为对照数据
     */
    private Map<Long, TActionNougat>             actionNougatMap;
    /**
     * sdk别名
     */
    private Map<String, List<String>>            sdkAliasMap;
    /**
     * sdk
     */
    private List<TSdkLibrary>                    sdkList;
    /**
     * 检测数据
     */
    private Map<BehaviorStageEnum, DetectDataBO> detectDataMap;
    /**------------------------------------------- 自定义初始解析的内容 -----------------------------------------**/
    /**
     * 是否有隐私政策
     */
    private boolean                              hasPrivacyPolicy;
    /**
     * 隐私政策截图
     */
    private String                               privacyPolicyImg;
    /**
     * 隐私政策内容
     */
    private String                               privacyPolicyContent;

    /**
     * 隐私政策图片ocr结果
     */
    private List<RecognizeData>                      privacyPolicyImgOcrResult;

    /**
     * 隐私政策最小文字大小
     */
    private int                                  privacyPolicyMinTextSize;

    /**
     * 隐私政策最小文字行间距
     */
    private int                                  privacyPolicyMinTextLineSpacing;

    /**
     * 语义识别服务器url
     */
    private ONlpResponse                         nlpResponse;

    /**
     * 日志文件解析内容
     */
    private List<ResultDataLogBO>                resultDataLogs;

    /**
     * tensorflow导出的checkbox是否存在检测模型路径
     */
    private String tensorflowCheckBoxModelPath;
    /**
     * tensorflow导出的checkbox是否选中检测模型路径
     */
    private String tensorflowCheckedModelPath;

    private TerminalTypeEnum terminalTypeEnum;

    private OcrService ocrService;

    private List<TApplyPermission> applyPermissions;

    /**
     * 检测法规
     */
    private Map<String, LawDetectResultVO> lawsRegulationsMap;

    /**
     * 个人信息共享清单内容
     */
    private CheckList personalInfoCollectionChecklist;

    /**
     * 第三方共享清单内容
     */
    private CheckList thirdPartySharingChecklist;

    private IPrivacyDetectionService privacyDetectionService;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getApkDetectionDetailId() {
        return apkDetectionDetailId;
    }

    public void setApkDetectionDetailId(String apkDetectionDetailId) {
        this.apkDetectionDetailId = apkDetectionDetailId;
    }

    public String getApkPackageName() {
        return apkPackageName;
    }

    public void setApkPackageName(String apkPackageName) {
        this.apkPackageName = apkPackageName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public boolean isHasPrivacyPolicy() {
        return hasPrivacyPolicy;
    }

    public boolean nonHasPrivacyPolicy() {
        return !isHasPrivacyPolicy();
    }

    public void setHasPrivacyPolicy(boolean hasPrivacyPolicy) {
        this.hasPrivacyPolicy = hasPrivacyPolicy;
    }

    public String getPrivacyPolicyImg() {
        return privacyPolicyImg;
    }

    public void setPrivacyPolicyImg(String privacyPolicyImg) {
        this.privacyPolicyImg = privacyPolicyImg;
    }

    public String getPrivacyPolicyContent() {
        return privacyPolicyContent;
    }

    public void setPrivacyPolicyContent(String privacyPolicyContent) {
        this.privacyPolicyContent = privacyPolicyContent;
    }

    public Map<Long, TActionNougat> getActionNougatMap() {
        return actionNougatMap;
    }

    public void setActionNougatMap(Map<Long, TActionNougat> actionNougatMap) {
        this.actionNougatMap = actionNougatMap;
    }

    public Map<BehaviorStageEnum, DetectDataBO> getDetectDataMap() {
        return detectDataMap;
    }

    public void setDetectDataMap(Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        this.detectDataMap = detectDataMap;
    }

    public Set<Long> getSdkTypePushIds() {
        return sdkTypePushIds;
    }

    public void setSdkTypePushIds(Set<Long> sdkTypePushIds) {
        this.sdkTypePushIds = sdkTypePushIds;
    }

    public List<ResultDataLogBO> getResultDataLogs() {
        return resultDataLogs;
    }

    public void setResultDataLogs(List<ResultDataLogBO> resultDataLogs) {
        this.resultDataLogs = resultDataLogs;
    }

    public int getApkTargetSdkVerion() {
        return apkTargetSdkVerion;
    }

    public void setApkTargetSdkVersion(int apkTargetSdkVerion) {
        this.apkTargetSdkVerion = apkTargetSdkVerion;
    }

    public List<RecognizeData> getPrivacyPolicyImgOcrResult() {
        return privacyPolicyImgOcrResult;
    }

    public void setPrivacyPolicyImgOcrResult(List<RecognizeData> privacyPolicyImgOcrResponse) {
        this.privacyPolicyImgOcrResult = privacyPolicyImgOcrResponse;
    }

    public String getApkName() {
        return apkName;
    }

    public void setApkName(String apkName) {
        this.apkName = apkName;
    }

    public int getPrivacyPolicyMinTextSize() {
        return privacyPolicyMinTextSize;
    }

    public void setPrivacyPolicyMinTextSize(int privacyPolicyMinTextSize) {
        this.privacyPolicyMinTextSize = privacyPolicyMinTextSize;
    }

    public int getPrivacyPolicyMinTextLineSpacing() {
        return privacyPolicyMinTextLineSpacing;
    }

    public void setPrivacyPolicyMinTextLineSpacing(int privacyPolicyMinTextLineSpacing) {
        this.privacyPolicyMinTextLineSpacing = privacyPolicyMinTextLineSpacing;
    }

    public ONlpResponse getNlpResponse() {
        return nlpResponse;
    }

    public void setNlpResponse(ONlpResponse nlpResponse) {
        this.nlpResponse = nlpResponse;
    }

    public boolean isNlpSuccess() {
        return getNlpResponse() != null && getNlpResponse().getSuccess();
    }

    public TerminalTypeEnum getTerminalTypeEnum() {
        return terminalTypeEnum;
    }

    public void setTerminalTypeEnum(TerminalTypeEnum terminalTypeEnum) {
        this.terminalTypeEnum = terminalTypeEnum;
    }

    public String getTensorflowCheckBoxModelPath() {
        return tensorflowCheckBoxModelPath;
    }

    public void setTensorflowCheckBoxModelPath(String tensorflowCheckBoxModelPath) {
        this.tensorflowCheckBoxModelPath = tensorflowCheckBoxModelPath;
    }

    public String getTensorflowCheckedModelPath() {
        return tensorflowCheckedModelPath;
    }

    public boolean hasTensorflowModel() {
        return StringUtils.isNotBlank(tensorflowCheckBoxModelPath) && StringUtils.isNotBlank(tensorflowCheckedModelPath);
    }

    public void setTensorflowCheckedModelPath(String tensorflowCheckedModelPath) {
        this.tensorflowCheckedModelPath = tensorflowCheckedModelPath;
    }

    public OcrService getOcrService() {
        return ocrService;
    }

    public void setOcrService(OcrService ocrService) {
        this.ocrService = ocrService;
    }

    public List<TApplyPermission> getApplyPermissions() {
        return applyPermissions;
    }

    public void setApplyPermissions(List<TApplyPermission> applyPermissions) {
        this.applyPermissions = applyPermissions;
    }

    public Map<String, List<String>> getSdkAliasMap() {
        return sdkAliasMap;
    }

    public void setSdkAliasMap(Map<String, List<String>> sdkAliasMap) {
        this.sdkAliasMap = sdkAliasMap;
    }

    public Map<String, LawDetectResultVO> getLawsRegulationsMap() {
        return lawsRegulationsMap;
    }

    public void setLawsRegulationsMap(Map<String, LawDetectResultVO> lawsRegulationsMap) {
        this.lawsRegulationsMap = lawsRegulationsMap;
    }

    public CheckList getPersonalInfoCollectionChecklist() {
        return personalInfoCollectionChecklist;
    }

    public void setPersonalInfoCollectionChecklist(CheckList personalInfoCollectionChecklist) {
        this.personalInfoCollectionChecklist = personalInfoCollectionChecklist;
    }

    public CheckList getThirdPartySharingChecklist() {
        return thirdPartySharingChecklist;
    }

    public void setThirdPartySharingChecklist(CheckList thirdPartySharingChecklist) {
        this.thirdPartySharingChecklist = thirdPartySharingChecklist;
    }

    public IPrivacyDetectionService getPrivacyDetectionService() {
        return privacyDetectionService;
    }

    public void setPrivacyDetectionService(IPrivacyDetectionService privacyDetectionService) {
        this.privacyDetectionService = privacyDetectionService;
    }

    public List<TSdkLibrary> getSdkList() {
        return sdkList;
    }

    public void setSdkList(List<TSdkLibrary> sdkList) {
        this.sdkList = sdkList;
    }
}

