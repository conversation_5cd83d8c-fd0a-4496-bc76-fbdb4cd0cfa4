package cn.ijiami.detection.miit.point.ios.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20203;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350702;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350702.java
 * @Description
 * 其他要求
 * 2. App是否存在自启动关联启动行为
 * 判断规则：
 * d)在非服务所必需或者无合理场景下，不应自启动或者关联启动其他App；
 * 注2：关联启动是指其他程序唤醒的启动。
 * 发现风险：
 * APP未向用户明示未经用户同意，且无合理的使用场景，存在频繁自启动或关联启动的行为。
 */
@EnableDetectPoint
public class DetectPoint10350702 extends DetectPoint350702 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
