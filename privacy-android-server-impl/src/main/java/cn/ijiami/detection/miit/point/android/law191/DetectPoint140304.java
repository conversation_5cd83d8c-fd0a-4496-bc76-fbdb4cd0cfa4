package cn.ijiami.detection.miit.point.android.law191;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

/**
 * 要求用户一次性同意打开多个可收集个人信息的权限，用户不同意则无法使用。
 *
 * 发现风险：
 * 1、target<23
 * 2、启动应用之后，连续弹出权限授权窗口；拒绝权限后，App退出
 */
@EnableDetectPoint
public class DetectPoint140304 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ANDROID &&
                commonDetectInfo.getApkTargetSdkVerion() > 0 && commonDetectInfo.getApkTargetSdkVerion() < 23) {
            conclusionList.add("App targetSdkVersion小于23，存在一揽子授权行为。");
            suggestionList.add("请调整targetSdkVersion值大于或等于23。");
        }
        boolean isQuitApp = !checkRejectPermissionQuitApp(commonDetectInfo).isEmpty();
        List<String> actionNameList = new ArrayList<>();
        boolean isContinuousApply = checkAllBehaviorStageContinuousApplyPermission(commonDetectInfo, actionNameList, detectResult);
        if (isQuitApp || isContinuousApply) {
            if (isContinuousApply) {
                conclusionList.add(String.format("启动%s之后，连续弹出权限授权窗口", executor(commonDetectInfo)));
            }
            // 获取拒绝权限界面
            if (isQuitApp) {
                conclusionList.add(String.format("拒绝权限后，%s退出", executor(commonDetectInfo)));
            }
            suggestionList.add("建议调整权限申请规则");
        }
        setDetectResultByConclusion(commonDetectInfo, detectResult, conclusionList, suggestionList);
        return detectResult;
    }
}
