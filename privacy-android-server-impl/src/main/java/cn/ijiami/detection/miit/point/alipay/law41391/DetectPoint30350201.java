package cn.ijiami.detection.miit.point.alipay.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350201;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350201.java
 * @Description
 * App收集的个人信息是否满足告知同意要求
 * 1. App是否显著告知隐私政策核心内容，并取得用户明示同意
 * 判断规则
 *  a)应采用显著方式（如弹窗、图文、动画等)向用户告知个人信息保护政策的核心内容（如所提供的基本业务功能、必要个人信息等)，提示用户阅读个人信息保护政策，并取得用户明示同意；
 * 发现风险
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、检测到隐私政策，但隐私政策文本获取存在问题。
 */
@EnableDetectPoint
public class DetectPoint30350201 extends DetectPoint350201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
