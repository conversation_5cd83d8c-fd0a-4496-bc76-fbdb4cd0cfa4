package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350203.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 5. App是否存在超频次获取个人信息行为
 * 判断规则：
 * b)权限申请授权后收集个人信息的频率应在实现App业务功能所必需的最低合理频度范围内。
 * 发现风险：
 * 建议在调整非必要收集个人信息场景的收集规则
 */
@EnableDetectPoint
public class DetectPoint350505 extends DetectPoint140401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
