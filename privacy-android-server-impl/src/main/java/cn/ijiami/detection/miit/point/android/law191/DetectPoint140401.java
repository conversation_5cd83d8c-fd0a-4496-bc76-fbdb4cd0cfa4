package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 收集个人信息的频度等超出业务功能实际需要；
 *
 * 发现风险：
 * 1、前台持续、固定频率，同一个主体，触发同一个人信息行为
 * 2、后台、App退出阶段触发个人信息行为（除了定位行为）
 */
@EnableDetectPoint
public class DetectPoint140401 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 前台行为, 是否有固定触发
        List<ActionAnalyse> cycleList = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);

        // 是否有个人隐私触发
        List<ActionAnalyse> groundAndExitActionAnalyseList = new ArrayList<>();
        groundAndExitActionAnalyseList.addAll(filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        groundAndExitActionAnalyseList.addAll(filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 定位权限不在判断范围内
        List<Long> locationActionIdList = commonDetectInfo.getActionNougatMap()
                .values()
                .stream()
                .filter(n -> StringUtils.containsAny(n.getActionName(), "定位", "位置", "纬度", "经度", "多媒体"))
                .map(TActionNougat::getActionId)
                .collect(Collectors.toList());
        List<ActionAnalyse> personalList = groundAndExitActionAnalyseList
                .stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> !locationActionIdList.contains(a.getActionId()))
                .collect(Collectors.toList());
        
        
        //增加多媒体判断
        List<ActionAnalyse> mediaAction = new ArrayList<>();
        List<ActionAnalyse> grantAction = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        mediaAction.addAll(grantAction);
        List<ActionAnalyse> frontAction = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);
        mediaAction.addAll(frontAction);
        List<ActionAnalyse> grountAction = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND);
        mediaAction.addAll(grountAction);
        List<ActionAnalyse> grountExit = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT);
        mediaAction.addAll(grountExit);
        
        //产品要求-存在监听多媒体行为就里面违规 2024-04-25
        List<ActionAnalyse> mediaActionNougats = mediaAction.stream()
                .filter(t -> t.getActionId()==14018)
                .collect(Collectors.toList());
        
        List<String> conclusionList = new ArrayList<>();
        Set<ActionAnalyse> actionAnalyseList = new HashSet<>();
        actionAnalyseList.addAll(personalList);
        actionAnalyseList.addAll(cycleList);
        actionAnalyseList.addAll(mediaActionNougats);
        
        if (!actionAnalyseList.isEmpty()) {
            String actionNames = actionAnalyseList
                    .stream()
                    .map(ActionAnalyse::getActionName)
                    .distinct()
                    .collect(Collectors.joining("、"));
            conclusionList.add(String.format("%s存在以固定频次收集%s", executor(commonDetectInfo), actionNames));
        }
        if (!conclusionList.isEmpty()) {
            detectResult.setAnalysisResult(new ArrayList<>(actionAnalyseList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText("建议在调整非必要收集个人信息场景的收集规则"));
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }
}