package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint290702;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.7.e
 * 应要求第三方根据本标准相关要求向个人信息主体征得收集个人信息同意，必要时核验其实现的方式；
 * 规则
 * 存在风险
 * 1 SDK在隐私政策授权前收集个人信息。
 * 2 SDK收集的个人信息未在隐私政策中声明。
 */
@EnableDetectPoint
public class DetectPoint40290702 extends DetectPoint290702 {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
