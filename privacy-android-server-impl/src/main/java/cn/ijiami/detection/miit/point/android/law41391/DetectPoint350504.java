package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140304;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350504.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 4. App是否提供单次授权选项
 * 判断规则：
 * e)如操作系统支持，申请相机、位置、麦克风等可收集个人信息权限应向用户提供单次授权的选项；
 * 发现风险：
 * 建议APP申请相机、位置、麦克风等可收集个人信息权限应向用户提供单次授权的选项
 */
@EnableDetectPoint
public class DetectPoint350504 extends DetectPoint140304 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        boolean isQuitApp = !checkRejectPermissionQuitApp(commonDetectInfo).isEmpty();
        List<String> actionNameList = new ArrayList<>();
        boolean isContinuousApply = checkAllBehaviorStageContinuousApplyPermission(commonDetectInfo, actionNameList, detectResult);
        if (isQuitApp || isContinuousApply) {
            if (isContinuousApply) {
                conclusionList.add(String.format("启动%s之后，连续弹出权限授权窗口", executor(commonDetectInfo)));
            }
            // 获取拒绝权限界面
            if (isQuitApp) {
                conclusionList.add(String.format("拒绝权限后，%s退出", executor(commonDetectInfo)));
            }
            suggestionList.add("建议调整权限申请规则");
        }
        setDetectResultByConclusion(commonDetectInfo, detectResult, conclusionList, suggestionList);
        return detectResult;
    }

}
