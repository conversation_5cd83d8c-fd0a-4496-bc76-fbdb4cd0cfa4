package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20105;

import static cn.ijiami.detection.helper.DetectPoint20105Helper.judgeCompliance;
import static cn.ijiami.detection.helper.DetectPointCommonHelper.checkRepeatedlyApplyPermission;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350403.java
 * @Description
 * App是否运行用户拒绝或撤回个人信息收集、权限申请或业务功能更的授权同意
 * 3. App拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时，是否频繁申请授权干扰用户正常使用。
 * 判断规则：
 * 当用户拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时，不应频繁申请授权干扰用户正常使用，除非由用户主动触发业务功能，且没有该个人信息或权限参与此业务功能无法实现。“频繁”的形式包括但不限于：
 * 1)单个场景在用户拒绝授权后，48h内弹窗提示用户打开权限的次数超过1次；
 * 2)每当用户重新打开App或使用无关的业务功能时，都会再次向用户索要授权或提示用户缺少相关授权。
 * 发现风险：
 * 建议APP在运行相应服务场景时，用户拒绝授权后，与授权不相关的服务应该不受影响，且用户明确拒绝授权后，建议保留用户拒绝授权记录，不能向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用
 */
@EnableDetectPoint
public class DetectPoint350403 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        // 是否频繁申请权限
        checkRepeatedlyApplyPermission(commonDetectInfo, detectResult);
        judgeCompliance(commonDetectInfo, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            String actionTypeNames = String.join("、", detectResult.getRequestPermissionNames());
            String executorName = executor(commonDetectInfo);
            detectResult.setConclusion(buildSequenceText(
                    String.format("%s运行时，在用户明确拒绝%s等权限申请后，仍向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。",
                            executorName, actionTypeNames)
            ));
            detectResult.setSuggestion(buildSequenceText(
                    String.format("建议%s在运行相应服务场景时，用户拒绝授权后，与授权不相关的服务应该不受影响，且用户明确拒绝授权后，建议保留用户拒绝授权记录，不能向用户频繁弹窗申请开启与当前服务场景无关的权限，影响用户正常使用",
                            executorName)));
            return detectResult;
        }
        return detectResult;
    }

}
