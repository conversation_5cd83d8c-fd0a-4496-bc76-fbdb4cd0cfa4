package cn.ijiami.detection.miit.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * UI数据类型枚举
 *
 * <AUTHOR>
 * @date 2020/12/24 18:41
 **/
/**
 * UI数据类型枚举
 *
 * <AUTHOR>
 * @date 2020/12/24 18:41
 */
public enum MiitDataTypeEnum implements BaseValueEnum {
    /**
     * 应用重启记录
     */
    APP_RESTART(0, "应用重启记录"),
    /**
     * 隐私政策界面
     */
    UI_PRIVACY_INTERFACE(1, "隐私政策界面"),
    /**
     * 权限授权弹窗
     */
    UI_AUTHOR_POPUP(2, "权限授权弹窗"),
    /**
     * 权限授权同意
     */
    UI_AUTHOR_AGREE(3, "权限授权同意"),
    /**
     * 权限授权拒绝
     */
    UI_AUTHOR_DENIED(4, "权限授权拒绝"),
    /**
     * 注册，登录界面
     */
    UI_REGISTER_LOGIN(5, "注册，登录界面"),
    /**
     * 个性化定推
     */
    UI_PERSONAL(6, "个性化定推"),
    /**
     * PDF记录
     */
    UI_PDF(7, "pdf记录"),
    /**
     * 操作事件记录，包括点击和红包诱骗弹窗
     */
    UI_EVENT(8, "操作事件记录"),
    /**
     * 隐私申请弹窗前相关文字内容描述
     */
    UI_BEFORE_AUTHOR_POPUP(9, "隐私申请弹窗前相关文字内容描述"),
    /**
     * 摇一摇动作之前的截图
     */
    UI_BEFORE_SHAKE_THE_PHONE(10, "摇一摇动作之前的截图"),
    /**
     * 摇一摇动作之后的截图或传感器数据
     */
    UI_AFTER_SHAKE_THE_PHONE(13, "摇一摇动作之后的截图或传感器数据"),
    /**
     * 权限授权弹窗（备用）
     */
    UI_AUTHOR_POPUP2(14, "权限授权弹窗");

    private int value;
    private String name;

    /**
     * 构造函数
     *
     * @param value 枚举值
     * @param name  枚举名称
     */
    MiitDataTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据值获取枚举项
     *
     * @param value 枚举值
     * @return 对应的枚举项
     */
    @JsonCreator
    public static MiitDataTypeEnum getItem(int value) {
        for (MiitDataTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public int itemValue() {
        return value;
    }

    /**
     * 获取枚举名称
     *
     * @return 枚举名称
     */
    @Override
    public String itemName() {
        return name;
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    public int getValue() {
        return value;
    }

    /**
     * 设置枚举值
     *
     * @param value 枚举值
     */
    public void setValue(int value) {
        this.value = value;
    }

    /**
     * 获取枚举名称
     *
     * @return 枚举名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置枚举名称
     *
     * @param name 枚举名称
     */
    public void setName(String name) {
        this.name = name;
    }
}