package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250401;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.4.a
 * 收集个人信息，应向个人信息主体告知收集、使用个人信息的目的、方式和范围，并获得个人信息主体的授权同意；
 * 注1：如产品或服务仅提供一项收集、使用个人信息的业务功能时，个人信息控制者可通过隐私政策的形式，实现向个人信息主体的告知；
 * 产品或服务提供多项收集、使用个人信息的业务功能的，除隐私政策外，个人信息控制者宜在实际开始收集特定个人信息时，向个人信息主体提供收集、
 * 使用该个人信息的目的、方式和范围，以便个人信息主体在作出具体的授权同意前，能充分考虑对其的具体影响。
 * 注2：符合本标准5.3和a）要求的实现方法，可参考附录C
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 APP未提供明确同意或者拒绝按钮
 * 2 APP在隐私政策授权前开始收集个人信息。
 * 3 APP在隐私政策授权前使用cookie传输个人信息。
 * 4 APP在隐私政策授权前申请打开个人信息权限。
 * 5 APP或者SDK收集的个人信息与隐私政策不一致。
 * 【没有隐私政策】
 * 存在个人信息行为就有风险
 */
@EnableDetectPoint
public class DetectPoint40250401 extends AppletDetectPoint250401 {

}
