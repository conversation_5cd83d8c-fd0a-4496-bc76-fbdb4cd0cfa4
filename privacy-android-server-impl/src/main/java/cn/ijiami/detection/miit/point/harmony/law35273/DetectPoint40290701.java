package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint290701;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.7.c
 * 应向个人信息主体明确标识产品或服务由第三方提供；
 * 规则
 * 发现风险
 * SDK收集个人信息，但未在隐私政策中声明 应要求第三方根据本标准相关要求向个人信息主体征得收集个人信息同意，必要时核验其实现的方式；
 */
@EnableDetectPoint
public class DetectPoint40290701 extends AppletDetectPoint290701 {
}
