package cn.ijiami.detection.miit.point.harmony.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20201;

/**
 * APP未向用户明示未经用户同意，且无合理的使用场景，存在频繁自启动或关联启动的行为。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 *    启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计行为触发频次，超过1次则为不合规
 * 二、有隐私政策
 *    静默安装前，有启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计触发频次，超过1次则为不合规
 *
 * 涉及行为：21004L,21005L
 *
 * 20210318补充逻辑：涉及点（20201、20202、20203）：
 *      退出阶段：关联启动、自启动都判断
 *      其他阶段：只关心关联启动
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint4020201 extends DetectPoint20201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
