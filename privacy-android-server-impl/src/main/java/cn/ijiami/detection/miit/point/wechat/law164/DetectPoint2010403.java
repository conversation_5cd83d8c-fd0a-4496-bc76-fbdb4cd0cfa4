package cn.ijiami.detection.miit.point.wechat.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;

import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10403;

/**
 * APP隐私政策存在“根据您的偏好进行个性化推荐YYYY”等内容，明示存在定向推送功能，但页面中未见显著区分个性化推送服务，如标明“个性化展示”或“定推”等字样。
 * <p>
 * <blockquote><pre>
 * 1、无截图
 * 2、隐私政策中检测到「定向推送、个性化展示、精准营销」等关键词语句
 *
 * 1、检测结果会返回判断标识和截图，判断是否有「个性化展示”或“定推」字样，如果有，则进一步根据关键字，匹配隐私政策中，是否有定向推送、精准营销等功能的描述，如果没有，则违规，有则合规
 *
 * 1、先找6（个性化定推）截图 : 没有截图就算不合规
 * 2、判断隐私政策关键字（有截图情况下）
 *
 * 禅道：10344 1-4-3改成
 * 1、没有隐私政策就直接不涉及
 * 2、有隐私政策，内容有推送、发现、推荐等关键词，a.界面上发现推送、发现、推荐等关键词截图，则不涉及，b.界面上没发现，则不合规
 * </pre></blockquote>
 * 
 * 
 * 定向推送、个性化展示、精准营销、个人图像、个性化图像、个人肖像、个性化服务、猜你喜欢、可能喜欢、可能还喜欢、为你打造、推荐、发现、为你定制
 * 定向推送|个性化展示|精准营销|个人图像|个性化图像|个人肖像|猜你喜欢|可能喜欢|可能还喜欢|为你打造|推荐|为你定制|用户画像|人群画像|个性化需求|个性化服务
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint2010403 extends DetectPoint10403 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

}
