package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350501;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350501.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 1. App是否提前申请与业务功能无关的个人信息权限
 * 判断规则：
 * b)在用户未使用相关业务功能时，不应提前申请与当前业务功能无关的权限；
 * 1)单个场景在用户拒绝授权后，48h内弹窗提示用户打开权限的次数超过1次；
 * 2)每当用户重新打开App或使用无关的业务功能时，都会再次向用户索要授权或提示用户缺少相关授权。
 * 发现风险：
 * 建议APP在运行相应服务场景时，再向用户申请必要的授权【权限名称，多项时，用“、”隔开】等相关权限。
 */
@EnableDetectPoint
public class DetectPoint40350501 extends DetectPoint350501 {


}
