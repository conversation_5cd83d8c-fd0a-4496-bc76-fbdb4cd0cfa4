package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户明确表示不同意后，仍收集个人信息或打开可收集个人信息的权限，或频繁征求用户同意、干扰用户正常使用；
 *
 * 未发现风险：
 * 1、拒绝权限，能继续正常遍历，并未发现再次授权弹窗
 * 发现风险：
 * 1、拒绝权限，App退出，再次打开应用再弹出权限授权窗口
 * 2、拒绝权限，之再次弹出权限
 */
@EnableDetectPoint
public class DetectPoint130301 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        List<String> conclusionList = new ArrayList<>();
        checkRepeatedlyApplyPermission(commonDetectInfo, detectResult, true);
        boolean repeatedlyApplyPermission = detectResult.getComplianceStatus() != MiitDetectStatusEnum.NON_INVOLVED;
        // 只有拒绝权限和隐私的界面，没有其他界面，说明无法使用
        boolean isAppQuit = false;
        if (repeatedlyApplyPermission) {
            // 遍历界面
            for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
                ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
                if (resultDataLogBO.getUiDumpResult() != null &&
                        resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                    // 拒绝权限后应用推出 退出
                    if (StringUtils.equals(resultDataLogBO.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                        // 获取拒绝权限界面
                        isAppQuit = true;
                        addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    }
                }
            }
        }
        if (repeatedlyApplyPermission) {
            if (isAppQuit) {
                conclusionList.add(String.format("拒绝权限，%s退出，再次打开应用再弹出权限授权窗口", executor(commonDetectInfo)));
            } else {
                conclusionList.add("拒绝权限，再次弹出权限");
            }
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion("建议调整收集个人信息行为或收集个人信息权限规则");
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        }
        return detectResult;
    }

}
