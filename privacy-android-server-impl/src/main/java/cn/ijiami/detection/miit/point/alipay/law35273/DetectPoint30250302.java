package cn.ijiami.detection.miit.point.alipay.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250302;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.b
 * 应把个人信息主体自主做出的肯定性动作，如主动点击、勾选、填写等，作为产品或服务的特定业务功能的开启条件。
 * 个人信息控制者应仅个人信息主体开启该业务功能后，开始收集个人信息；
 *
 * 判断规则
 * 发现风险：
 * 【有隐私政策】
 * 1、用户首次注册、登陆时，未提供勾选框或默认勾选
 * 【无隐私政策】
 * 存在风险
 */
@EnableDetectPoint
public class DetectPoint30250302 extends AppletDetectPoint250302 {
}
