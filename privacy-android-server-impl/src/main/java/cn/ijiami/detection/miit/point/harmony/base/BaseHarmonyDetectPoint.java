package cn.ijiami.detection.miit.point.harmony.base;

import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonyDetectPoint.java
 * @Description 鸿蒙检测基类
 * @createTime 2024年08月01日 14:52:00
 */
public abstract class BaseHarmonyDetectPoint extends AbstractDetectPoint {


    protected DetectResult nonHasPrivacyPolicyNonInvolved(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildEmptyConclusionNonInvolved(commonDetectInfo, customDetectInfo);
        // 不存在隐私政策,不涉及此项
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            return detectResult;
        }
        return detectResult;
    }

}
