package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350402;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350402.java
 * @Description
 * App是否运行用户拒绝或撤回个人信息收集、权限申请或业务功能更的授权同意
 * 2. App拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时，是否拒绝提供拒绝提供App基本业务功能或影响其他无关的业务功能使用。
 * 判断规则：
 * 当用户拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时，不应拒绝提供App基本业务功能或影响其他无关的业务功能使用，除非用户拒绝同意App必要个人信息或基本业务功能。
 * 发现风险：
 * 建议APP明示用到【权限名称，多项时，用“、”隔开】等权限的所有服务场景和必要性。用户拒绝授权后，与授权不相关的服务应该不受影响，不能出现用户拒绝授权应用整体退出或关闭的情况
 */
@EnableDetectPoint
public class DetectPoint40350402 extends DetectPoint350402 {

}
