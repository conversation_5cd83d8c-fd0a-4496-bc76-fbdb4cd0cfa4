<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <!--导入属性配置 -->
    <properties resource="generator/detection-generator.properties" />

    <context id="mysql" targetRuntime="MyBatis3Simple">

        <!-- 生成的Java文件的编码为UTF-8 -->
        <property name="javaFileEncoding" value="UTF-8"/>

        <!-- 指明数据库的用于标记数据库对象名的符号，比如ORACLE就是双引号，MYSQL默认是`反引号； -->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 格式化java代码 -->
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>

        <!-- 格式化XML代码 -->
        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>

        <!--生成Model文件实现可序列化接口，并添加序列化字段-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />

        <!--IDEA这里type会报红线，不影响生成-->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="cn.ijiami.framework.core.mybatis.IjiamiMapper"/>
        </plugin>

        <!--jdbc的数据库连接 -->
        <jdbcConnection driverClass="${spring.datasource.driver-class-name}" connectionURL="${spring.datasource.url}"
                        userId="${spring.datasource.username}" password="${spring.datasource.password}">
        </jdbcConnection>

        <!-- 数据表对应的model层  -->
        <javaModelGenerator targetPackage="cn.ijiami.detection.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- sql mapper 映射配置xml文件 -->
        <sqlMapGenerator targetPackage="cn.ijiami.detection.mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- mybatis3中的mapper接口 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="cn.ijiami.detection.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 数据表进行生成操作 schema:相当于库名; tableName(使用百分号适配所有的表):表名; domainObjectName:对应的DO -->
        <!--自动生成的方法有增加，查询，更新，删除，计数-->
       
        <table schema="detection_new_1.0_test" tableName="t_email_send"
               enableInsert="true"
               enableSelectByPrimaryKey="true"
               enableUpdateByPrimaryKey="true"
               enableDeleteByPrimaryKey="true"
        >
        
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
           
        </table>
    </context>

</generatorConfiguration>