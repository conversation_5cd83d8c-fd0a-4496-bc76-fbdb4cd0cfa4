<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSuspiciousSdkLibraryMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSuspiciousSdkLibrary">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="package_name" property="packageName" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType" jdbcType="BIGINT"/>
        <result column="permission_codes" property="permissionCodes" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findInPackageName" resultMap="BaseResultMap">
        SELECT
        id,
        name,
        package_name,
        terminal_type,
        permission_codes,
        created_time,
        update_time
        FROM t_suspicious_sdk_library
        WHERE package_name in
        <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
            #{packageName,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findInId" resultMap="BaseResultMap">
        SELECT
        id,
        name,
        package_name,
        terminal_type,
        permission_codes,
        created_time,
        update_time
        FROM t_suspicious_sdk_library
        WHERE id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>