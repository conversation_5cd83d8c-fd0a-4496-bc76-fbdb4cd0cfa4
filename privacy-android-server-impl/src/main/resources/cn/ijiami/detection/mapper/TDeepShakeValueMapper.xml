<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TDeepShakeValueMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TDeepShakeValue">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="acceleration_value" property="accelerationValue" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="flag" property="flag" jdbcType="TINYINT"/>
        <result column="opera_time" property="operaTime" jdbcType="TINYINT"/>
        <result column="gyroscope_value" property="gyroscopeValue" jdbcType="VARCHAR"/>
        <result column="is_violation" property="isViolation" jdbcType="TINYINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="BASE_SQL">
        id,task_id,acceleration_value,gyroscope_value,create_time,flag,opera_time,is_violation,description
    </sql>
    <select id="getShakeValueByTaskId" resultMap="BaseResultMap">
        select <include refid="BASE_SQL"/>
        from t_deep_shake_value
        where task_id = #{taskId}
        order by create_time desc
    </select>

    <update id="updateShakeStatusById">
        update t_deep_shake_value set flag = #{flag} where id = #{id}
    </update>

    <update id="updateViolationById">
        update t_deep_shake_value set is_violation = #{isViolation} where id = #{id}
    </update>

    <update id="updateDescribeById">
        update t_deep_shake_value set description = #{description} where id = #{id}
    </update>

    <delete id="deleteAllValueByTaskId">
        delete from t_deep_shake_value where task_id = #{taskId}
    </delete>
</mapper>