<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TReportDesignMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TReportDesign">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="report_title" property="reportTitle"
                jdbcType="VARCHAR"/>
        <result column="report_object" property="reportObject"
                jdbcType="INTEGER"/>
        <result column="company_name" property="companyName"
                jdbcType="VARCHAR"/>
        <result column="company_address" property="companyAddress"
                jdbcType="VARCHAR"/>
        <result column="footer_name" property="footerName"
                jdbcType="VARCHAR"/>
        <result column="is_add_basis" property="isAddBasis"
                jdbcType="INTEGER"/>
        <result column="report_basis_content"
                property="reportBasisContent" jdbcType="VARCHAR"/>
        <result column="is_add_company_introduction"
                property="isAddCompanyIntroduction" jdbcType="INTEGER"/>
        <result column="company_introduction"
                property="companyIntroduction" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="company_logo" property="companyLogo"
                jdbcType="LONGVARCHAR"/>
        <result column="report_logo" property="reportLogo"
                jdbcType="LONGVARCHAR"/>
        <result column="header_logo" property="headerLogo"
                jdbcType="LONGVARCHAR"/>
        <result column="watermark_logo" property="watermarkLogo"
                jdbcType="LONGNVARCHAR"/>
        <result column="template_path" property="templatePath"
                jdbcType="VARCHAR"/>
        <result column="copyright" property="copyright" jdbcType="VARCHAR"/>
        <result column="evaluator" property="evaluator" jdbcType="VARCHAR"/>
        <result property="companyIntroductionDefault" column="company_introduction_default" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findByUserIdAndReportObject" resultMap="BaseResultMap">
        select * from t_report_design
        <where>
            <if test="userId != null">
                create_user_id = #{userId}
            </if>
            <if test="reportObject != null">
                and report_object=#{reportObject}
            </if>
            <if test="terminalType != null">
                and terminal_type = #{terminalType}
            </if>
        </where>
        order by id limit 1
    </select>
    <select id="findByUserIdAndReportObjectAndterminalType" resultMap="BaseResultMap">
        select * from t_report_design
        <where>
            <if test="userId != null">
                create_user_id = #{userId}
            </if>
            <if test="reportObject != null">
                and report_object=#{reportObject}
            </if>
            <if test="terminalType != null">
                and terminal_type=#{terminalType}
            </if>
        </where>
        order by id limit 1
    </select>
</mapper>