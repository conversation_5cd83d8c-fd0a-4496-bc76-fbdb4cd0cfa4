<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTemplateDetectionItemMapper">
	<resultMap id="BaseResultMap"
		type="cn.ijiami.detection.entity.TTemplateDetectionItem">
		<!-- WARNING - @mbg.generated -->
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="template_id" property="templateId" jdbcType="BIGINT" />
		<result column="detection_item_id" property="detectionItemId"
			jdbcType="BIGINT" />
		<result column="platform" property="platform" jdbcType="VARCHAR" />
		<result column="create_user_id" property="createUserId"
			jdbcType="BIGINT" />
		<result column="update_user_id" property="updateUserId"
			jdbcType="BIGINT" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	</resultMap>

	<insert id="insertTemplateDetectionItem" parameterType="java.util.List"
		useGeneratedKeys="true" keyProperty="id">
		insert into t_template_detection_item (id, template_id,
		detection_item_id,platform,create_user_id,update_user_id,create_time,update_time)
		values
		<foreach collection="list" item="item" index="index"
			separator="," close=";">
			(#{item.id},#{item.templateId},#{item.detectionItemId},
			#{item.platform},#{item.createUserId},#{item.updateUserId},#{item.createTime},#{item.updateTime})
		</foreach>
	</insert>
	<delete id="deleteDetectionItemByTemplateId">
		DELETE FROM t_template_detection_item WHERE 1=1
		<if test="templateId !=null">
			template_id = #{templateId}
		</if>
	</delete>
</mapper>