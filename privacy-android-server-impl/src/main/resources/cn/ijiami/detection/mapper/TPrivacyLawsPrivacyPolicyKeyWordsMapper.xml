<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyLawsPrivacyPolicyKeyWordsMapper" >

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyLawsPrivacyPolicyActionKeyWords">
        <result column="action_id" jdbcType="BIGINT" property="actionId"/>
        <result column="item_no" jdbcType="VARCHAR" property="itemNo"/>
        <result column="key_words" jdbcType="VARCHAR" property="keyWords"/>
        <result column="action_type_name" property="actionTypeName"/>
    </resultMap>

    <select id="findKeyWordsInActionId" resultMap="BaseResultMap">
        SELECT p.action_id,p.item_no,k.key_words,k.action_type_name
        FROM t_privacy_laws_action_key_words_rel AS p
        LEFT JOIN t_privacy_laws_privacy_policy_key_words AS k ON p.key_words_id=k.id
        WHERE p.action_id in
        <foreach collection="actionIds" index="index" item="aid"
                 open="(" separator="," close=")">
            #{aid}
        </foreach>
    </select>

    <select id="findPersonalActionKeyWords" resultMap="BaseResultMap">
        SELECT p.action_id,p.item_no,k.key_words,k.action_type_name
        FROM t_privacy_laws_action_key_words_rel AS p
        INNER JOIN t_privacy_laws_privacy_policy_key_words AS k ON p.key_words_id=k.id
        INNER JOIN t_action_nougat AS a ON a.action_id=p.action_id
        WHERE a.is_personal = 1 AND a.active = 1
    </select>

    <select id="findAllActionKeyWords" resultMap="BaseResultMap">
        SELECT p.action_id,p.item_no,k.key_words,k.action_type_name
        FROM t_privacy_laws_action_key_words_rel AS p
        INNER JOIN t_privacy_laws_privacy_policy_key_words AS k ON p.key_words_id=k.id
        INNER JOIN t_action_nougat AS a ON a.action_id=p.action_id
        WHERE a.active = 1
    </select>

    <select id="findKeyWordsByActionId" resultMap="BaseResultMap">
        SELECT p.action_id,p.item_no,k.key_words,k.action_type_name
        FROM t_privacy_laws_action_key_words_rel AS p
        LEFT JOIN t_privacy_laws_privacy_policy_key_words AS k ON p.key_words_id=k.id
        WHERE p.action_id=#{actionId} LIMIT 1
    </select>

    <select id="findKeyWordsByItemNo" resultMap="BaseResultMap">
        SELECT p.action_id,p.item_no,k.key_words,k.action_type_name
        FROM t_privacy_laws_action_key_words_rel AS p
        LEFT JOIN t_privacy_laws_privacy_policy_key_words AS k ON p.key_words_id=k.id
        WHERE p.item_no=#{itemNo} LIMIT 1
    </select>

</mapper>