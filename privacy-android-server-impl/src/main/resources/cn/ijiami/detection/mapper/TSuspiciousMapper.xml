<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSuspiciousSdkMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSuspiciousSdk">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="privacy_action_nougat_id" property="privacyActionNougatId" jdbcType="BIGINT"/>
        <result column="executor_type" property="executorType" jdbcType="INTEGER"/>
        <result column="executor" property="executor" jdbcType="VARCHAR"/>
        <result column="suspicious_sdk_library_id" property="suspiciousSdkLibraryId" jdbcType="BIGINT" />
    </resultMap>

    <resultMap id="BehaviorResultMap" type="cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="executor_type" property="executorType" jdbcType="INTEGER"/>
        <result column="executor" property="executor" jdbcType="VARCHAR"/>
        <result column="package_name" property="packageName" jdbcType="VARCHAR" />
        <result column="suspicious_sdk_id" property="suspiciousSdkId" jdbcType="BIGINT" />
    </resultMap>

    <select id="findBehaviorByTaskId" resultMap="BehaviorResultMap">
        SELECT
        sdk.id,
        sdk.task_id,
        sdk.executor_type,
        sdk.executor,
        IFNULL(lib.package_name, sdk.package_name) AS package_name,
        IFNULL(lib.id, -1) AS suspicious_sdk_id
        FROM t_suspicious_sdk AS sdk
        LEFT JOIN t_suspicious_sdk_library AS lib
        ON sdk.suspicious_sdk_library_id = lib.id
        WHERE sdk.task_id = #{taskId,jdbcType=BIGINT}
        ORDER BY sdk.id ASC
    </select>

    <delete id="deleteByTaskId">
        delete from t_suspicious_sdk where task_id=#{taskId,jdbcType=BIGINT}
    </delete>

    <select id="findAndroidBehaviorByTaskDistinctId" resultMap="BehaviorResultMap">
        SELECT
        sdk.id,
        sdk.task_id,
        sdk.executor_type,
        sdk.executor,
        IFNULL(lib.package_name, sdk.package_name) AS package_name,
        IFNULL(lib.id, -1) AS suspicious_sdk_id
        FROM t_suspicious_sdk AS sdk
        LEFT JOIN t_suspicious_sdk_library AS lib
        ON sdk.suspicious_sdk_library_id = lib.id
        WHERE sdk.task_id = #{taskId,jdbcType=BIGINT}
        GROUP BY package_name
        ORDER BY sdk.id ASC
    </select>
    
    <select id="findIosBehaviorByTaskDistinctId" resultMap="BehaviorResultMap">
        SELECT
        sdk.id,
        sdk.task_id,
        sdk.executor_type,
        sdk.executor,
        IFNULL(lib.package_name, sdk.package_name) AS package_name,
        IFNULL(lib.id, -1) AS suspicious_sdk_id
        FROM t_suspicious_sdk AS sdk
        LEFT JOIN t_suspicious_sdk_library AS lib
        ON sdk.suspicious_sdk_library_id = lib.id
        WHERE sdk.task_id = #{taskId,jdbcType=BIGINT}
        GROUP BY sdk.executor
        ORDER BY sdk.id ASC
    </select>
    
    <select id="findSuspiciousSdkData" resultType="cn.ijiami.detection.VO.SuspiciousSdkDataVO">
    	select a.package_name as packageName,count(task_id) as number,GROUP_CONCAT(task_id) as taskIds,GROUP_CONCAT(permission_codes) as codes 
    	FROM(
		SELECT
	        sdk.id,
	        sdk.task_id,
	        sdk.executor_type,
	        sdk.executor,
	        IFNULL(lib.package_name, sdk.package_name) AS package_name,
	        IFNULL(lib.id, -1) AS suspicious_sdk_id,
			lib.permission_codes AS  permission_codes
	        FROM t_suspicious_sdk AS sdk
	        LEFT JOIN t_suspicious_sdk_library AS lib ON sdk.suspicious_sdk_library_id = lib.id
			LEFT JOIN  t_task as t on t.task_id = sdk.task_id 
			where 1=1 
			<if test="createUserId != 0 and createUserId != null">
	            and t.create_user_id = #{createUserId}
	        </if>
			<if test="startTime != '' and startTime != null">
	            and t.task_starttime &gt; #{startTime}
	        </if>
	        <if test="endTime != '' and endTime != null">
	            and t.task_starttime &lt; #{endTime}
	        </if>
	        GROUP BY package_name,task_id
		) a		
		GROUP BY packageName order by number desc  
    </select>

</mapper>