<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyActionMapper">
    <resultMap id="baseResultMap" type="cn.ijiami.detection.entity.TPrivacyAction">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="actionId" column="action_id"/>
        <result property="imgs" column="imgs"/>
    </resultMap>

    <resultMap id="privacyActionVO" type="cn.ijiami.detection.VO.PrivacyActionVO">
        <result property="actionId" column="action_id"/>
        <result property="actionName" column="action_name"/>
        <result property="actionFunction" column="action_function"/>
        <result property="safeLevel" column="safe_level"/>
        <result property="permissionName" column="permission_name"/>
        <result property="imgs" column="imgs"/>
    </resultMap>

    <select id="findByTaskId" resultMap="privacyActionVO">
        select a.action_id, a.action_name, a.safe_level, a.imgs, b.action_function, c.permission_name
        from t_privacy_action a
                 left join t_action_function b on a.action_id = b.action_id
                 left join t_action_permission c on a.action_id = c.action_id
        where a.task_id = #{taskId,jdbcType=BIGINT}
    </select>


</mapper>