<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TShakeBasicParametesMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TShakeBasicParametes">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="X" property="x" jdbcType="VARCHAR"/>
        <result column="Y" property="y" jdbcType="VARCHAR"/>
        <result column="Z" property="z" jdbcType="VARCHAR"/>
        <result column="RX" property="rx" jdbcType="VARCHAR"/>
        <result column="RY" property="ry" jdbcType="VARCHAR"/>
        <result column="RZ" property="rz" jdbcType="VARCHAR"/>
        <result column="AX" property="ax" jdbcType="VARCHAR"/>
        <result column="AY" property="ay" jdbcType="VARCHAR"/>
        <result column="AZ" property="az" jdbcType="VARCHAR"/>
        <result column="S" property="s" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="base_sql">
        id,type,X,Y,Z,RX,RY,RZ,AX,AY,AZ,S
    </sql>

    <select id="selectShakingParametes" resultMap="BaseResultMap">
        select <include refid="base_sql"/>
        from t_shake_basic_parametes where type = #{type}
    </select>
</mapper>