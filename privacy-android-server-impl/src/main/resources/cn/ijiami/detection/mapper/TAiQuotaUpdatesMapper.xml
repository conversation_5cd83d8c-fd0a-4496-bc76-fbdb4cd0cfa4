<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TAiQuotaUpdatesMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TAiQuotaUpdates">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="auth_code_id" property="authCodeId" jdbcType="VARCHAR"/>
        <result column="update_type" property="updateType" jdbcType="INTEGER"/>
        <result column="updated_user_id" property="updatedUserId" jdbcType="BIGINT"/>
        <result column="quota_change" property="quotaChange" jdbcType="INTEGER"/>
        <result column="previous_quota" property="previousQuota" jdbcType="INTEGER"/>
        <result column="new_quota" property="newQuota" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="BaseColumn">
        id, auth_code_id, update_type, updated_user_id, quota_change, previous_quota, new_quota, reason, update_time
    </sql>

    <select id="findByAuthCodeId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/>
        FROM t_ai_quota_updates
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByAuthCodeId">
        DELETE FROM t_ai_quota_updates
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </delete>

</mapper>