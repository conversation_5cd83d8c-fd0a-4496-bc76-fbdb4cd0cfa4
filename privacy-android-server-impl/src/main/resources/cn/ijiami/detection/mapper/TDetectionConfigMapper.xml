<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TDetectionConfigMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TDetectionConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="ios_devices_limit" property="iosDevicesLimit" jdbcType="INTEGER"/>
        <result column="android_devices_limit" property="androidDevicesLimit" jdbcType="INTEGER"/>
        <result column="harmony_devices_limit" property="harmonyDevicesLimit" jdbcType="INTEGER"/>
        <result column="ai_usage_limit" property="aiUsageLimit" jdbcType="INTEGER"/>
        <result column="ai_usage_limit_type" property="aiUsageLimitType" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="static_job_ids" property="staticJobIds" jdbcType="VARCHAR"/>
        <result column="dynamic_job_ids" property="dynamicJobIds" jdbcType="VARCHAR"/>
        <result column="android_device_ips" property="androidDeviceIps" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="cn.ijiami.detection.VO.DetectionConfigItem">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="ios_devices_limit" property="iosDevicesLimit" jdbcType="INTEGER"/>
        <result column="android_devices_limit" property="androidDevicesLimit" jdbcType="INTEGER"/>
        <result column="effective_end_date" property="effectiveEndDate" jdbcType="TIMESTAMP"/>
        <result column="harmony_devices_limit" property="harmonyDevicesLimit" jdbcType="INTEGER"/>
        <result column="ai_usage_limit" property="aiUsageLimit" jdbcType="INTEGER"/>
        <result column="ai_usage_limit_type" property="aiUsageLimitType" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="BASE_SQL">
        d.id,u.user_name,r.role_name,d.user_id,d.role_id,d.start_date,d.end_date,d.description,d.create_time,
        d.ios_devices_limit,d.android_devices_limit,d.harmony_devices_limit,d.ai_usage_limit,d.ai_usage_limit_type,
        d.static_job_ids,dynamic_job_ids,android_device_ips
    </sql>

    <select id="findList" resultMap="BaseResultMap2">
        SELECT
        <include refid="BASE_SQL"/>, MAX(dt.end_date) AS effective_end_date
        FROM t_detection_config AS d
        LEFT JOIN tsys_user AS u ON u.user_id=d.user_id
        LEFT JOIN tsys_role AS r ON r.role_id=d.role_id
        LEFT JOIN t_detectable_terminal AS dt ON dt.config_id=d.id
        where r.`status`!=3 OR u.`status`!=3
        <if test="name !=null and name != ''">
            AND u.user_name LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%') OR r.role_name LIKE
            CONCAT('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY d.id
        <if test="sortType == null or sortType==1">
            ORDER BY d.create_time
        </if>
        <if test="sortType != null and sortType==2">
            ORDER BY effective_end_date
        </if>
        <if test="sortOrder==null or sortOrder==1">
            ASC
        </if>
        <if test="sortOrder!=null and sortOrder==2">
            DESC
        </if>
    </select>
    
    <select id="findJobIdConfig" resultType="cn.ijiami.detection.VO.TDetectionConfigVO">
    	select user_id as userId,static_job_ids as staticJobIds,dynamic_job_ids as dynamicJobIds ,ios_devices_limit as iosDevicesLimit ,android_devices_limit as androidDevicesLimit,
    	android_device_ips as androidDeviceIps 
    	from t_detection_config where ((static_job_ids is not null and static_job_ids !=null)  or (dynamic_job_ids is not null and dynamic_job_ids !='') or (android_device_ips is not null and android_device_ips !=''))
    </select>
    
    
    <select id="getAllUserAppointDevice" resultType="java.lang.String">
    	select android_device_ips as andoridDeviceIps from t_detection_config where android_device_ips is not null and android_device_ips!='' and user_id != #{createUserId}
    </select>
</mapper>