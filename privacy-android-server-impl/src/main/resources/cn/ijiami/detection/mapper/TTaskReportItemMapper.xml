<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskReportItemMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTaskReportItem">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name"/>
        <result column="type_id" property="typeId"/>
        <result column="item_no" property="itemNo"/>
        <result column="status" property="status"/>
    </resultMap>
</mapper>