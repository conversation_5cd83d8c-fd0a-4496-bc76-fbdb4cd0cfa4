<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPermissionMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TPermission">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="alias_name" property="aliasName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result property="harm" column="harm"/>
        <result property="permissionCode" column="permission_code"/>
        <result property="isGoogle" column="is_google"/>
        <result property="isSystem" column="is_system"/>
        <result property="isPrivacy" column="is_isPrivacy"/>
        <result property="protectionLevel" column="protection_level"/>
    </resultMap>

    <resultMap id="BaseUserPermissionVOMap"
               type="cn.ijiami.detection.VO.UserPermissionVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="alias_name" property="aliasName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="permission_id" property="permissionId"
                jdbcType="BIGINT"/>
        <result property="signaTure" column="signa_ture"/>
        <result property="permissionCode" column="permission_code"/>
        <result property="harm" column="harm"/>
        <result property="isGoogle" column="is_google"/>
        <result property="isSystem" column="is_system"/>
        <result property="isPrivacy" column="is_isPrivacy"/>
        <result property="protectionLevel" column="protection_level"/>
    </resultMap>

    <!-- 查询全部用户敏感权限、权限特征码 -->
    <resultMap type="cn.ijiami.detection.VO.UserPermissionVO"
               id="BaseUserPermissionResultMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="alias_name" property="aliasName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="permission_id" property="permissionId"
                jdbcType="BIGINT"/>
        <result column="signature" property="signaTure"
                jdbcType="VARCHAR"/>
        <result property="permissionCode" column="permission_code"/>
        <result property="harm" column="harm"/>
        <result property="protectionLevel" column="protection_level"/>
    </resultMap>

    <sql id="Base_Left_Join">
            tp.id,
            tp.group_id,
            tp.name,
            tp.alias_name,
            tp.permission_code,
            tp.remark,
            tp.grade,
            tp.harm,
            tp.type,
            tp.create_user_id,
            tp.update_user_id,
            tp.create_time,
            tp.update_time
    </sql>

    <!-- 权限字典返回值 （包含权限特征码） -->
    <sql id="Base_all_permission_sql">
            tp.id,
            tp.group_id,
            tp.name,
            tp.alias_name,
            tp.permission_code,
            tp.remark,
            tp.grade,
            tp.harm,
            tp.type,
            tp.create_user_id,
            tp.update_user_id,
            tp.create_time,
            tp.update_time,
            tsign.signature
    </sql>
    <select id="selectPermissionByPage"
            parameterType="cn.ijiami.detection.entity.TPermission"
            resultMap="BaseUserPermissionVOMap">
        SELECT
        <include refid="Base_Left_Join"/>
        FROM t_permission tp WHERE 1=1
        <if test="name != null and name != ''">
            and tp.name LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="grade !=null">
            and tp.grade =#{grade}
        </if>
        <if test="type !=null">
            and tp.type =#{type}
        </if>
        <if test="terminalType !=null">
            AND tp.terminal_type = #{terminalType.value}
        </if>
    </select>

    <!-- 查询所有权限字典 -->
    <select id="selectAllPermission"
            parameterType="cn.ijiami.detection.entity.TPermission"
            resultMap="BaseUserPermissionResultMap">
        select
        <include refid="Base_all_permission_sql"/>
        from t_permission tp LEFT JOIN t_permission_signature tsign
        on
        tp.id=tsign.permission_id
    </select>

    <!-- 根据权限编码查询权限信息 -->
    <select id="findPermissionByPermissionCodes" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT a.name,
        a.alias_name AS aliasName,
        a.grade,
        a.type,
        a.remark,
        a.harm,
        a.is_privacy AS isPrivacy,
        a.permission_code as permissionCode,
        b.name AS groupName,
        b.remark AS groupRemark,
        a.protection_level AS protectionLevel
        FROM t_permission a LEFT JOIN
        t_permisson_group b
        ON a.group_id = b.id
        WHERE a.permission_code in
        <foreach collection="permissionCodes.split(',')" item="pc" open="(" separator="," close=")">
            #{pc}
        </foreach>
        ORDER BY a.is_privacy DESC
    </select>

    <!-- 根据权限编码查询权限信息 -->
    <select id="findByPermissionCodeList" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT a.name,
        a.alias_name AS aliasName,
        a.grade,
        a.type,
        a.remark,
        a.harm,
        a.is_privacy AS isPrivacy,
        a.permission_code as permissionCode,
        a.protection_level AS protectionLevel
        FROM t_permission a
        WHERE a.permission_code in
        <foreach collection="permissionCodes" item="pc" open="(" separator="," close=")">
            #{pc}
        </foreach>
        ORDER BY a.is_privacy DESC
    </select>
    
    <!-- 根据权限编码查询权限信息 -并统计SDK数量-->
    <select id="findPermissionByPermissionCodesSdk" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT aa.name,
        aa.alias_name AS aliasName,
        aa.grade,
        aa.type,
        aa.remark,
        aa.harm,
        aa.is_privacy AS isPrivacy,
        aa.permission_code as permissionCode,
        bb.name AS groupName,
        bb.remark AS groupRemark,
        aa.protection_level AS protectionLevel,
        IFNULL((SELECT SUM(case when a.executor_type = 2 then 1 else 0 end) as sdkCount
            FROM t_privacy_action_nougat a
            LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
            LEFT JOIN t_permission c ON b.action_permission = c.`name`
            WHERE a.task_id = #{taskId} and a.executor_type=2 AND c.`name` = aa.`name`
            AND a.action_id != 28001
            <if test="sdkId != null and sdkId != ''">
                AND a.sdk_ids LIKE CONCAT('%',#{sdkId},'%')
            </if>), 0) AS sdkCount
        FROM t_permission aa
        LEFT JOIN t_permisson_group bb ON aa.group_id = bb.id
        WHERE aa.permission_code in
        <foreach collection="permissionCodes.split(',')" item="pc" open="(" separator="," close=")">
            #{pc}
        </foreach>
        ORDER BY aa.is_privacy DESC
    </select>

    <select id="findAll" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT a.name,
        a.alias_name AS aliasName,
        a.grade,
        a.type,
        a.remark,
        a.harm,
        a.is_privacy AS isPrivacy,
        b.name AS groupName,
        b.remark AS groupRemark,
        n.action_api AS actionApi,
        a.protection_level AS protectionLevel
        FROM t_permission a
        LEFT JOIN
        t_permisson_group b
        ON a.group_id = b.id
        LEFT JOIN
        t_action_nougat n
        ON a.`name`=n.`action_permission`
        where a.terminal_type = #{terminalType}
        ORDER BY a.is_privacy DESC, a.grade DESC
    </select>
    <select id="findByTerminalType" resultMap="BaseResultMap">
        select * from  t_permission
        <where>
            <if test="name!=null and name !=''">
                name = #{name}
            </if>
            <if test="terminalType !=null">
                and terminal_type = #{terminalType}
            </if>
        </where>
    </select>
    
    <select id="getPermissionCodes" resultType="java.lang.String">
        SELECT p.permission_code from t_permission AS p LEFT JOIN t_action_nougat AS a ON p.`name`=a.`action_permission` WHERE a.action_id IN
        <foreach collection="actionIdList" item="actionId" open="(" separator="," close=")">
            #{actionId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="getPermissionByPermissionNames" resultType="cn.ijiami.detection.entity.TPermission">
        SELECT
        a.name,a.id,
        a.remark AS remark,
        a.type,
        is_privacy as isPrivacy,
        relate_api as relateApi
        FROM t_permission a
        <where>
            <if test="nameList!=null and nameList.size()>0">
                and a.name in
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="terminalType !=null">
                and terminal_type = #{terminalType}
            </if>
        </where>

    </select>
</mapper>