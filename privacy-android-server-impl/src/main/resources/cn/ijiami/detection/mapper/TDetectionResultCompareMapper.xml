<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TDetectionResultCompareMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.query.DetectionSummary" >
        <!--
          WARNING - @mbg.generated
        -->
        <result column="task_id" property="taskId" jdbcType="BIGINT" />
        <result column="assets_name" property="assetsName" jdbcType="VARCHAR" />
        <result column="document_id" property="documentId" jdbcType="VARCHAR" />
        <result column="assets_package" property="assetsPackage" jdbcType="VARCHAR" />
        <result column="version" property="version" jdbcType="VARCHAR" />
        <result column="environment" property="environment" jdbcType="VARCHAR" />
        <result column="md5" property="md5" jdbcType="VARCHAR" />
    </resultMap>


    <resultMap id="BaseResultMap2" type="cn.ijiami.detection.entity.TDetectionResultCompare" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id"/>
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="BaseResultMap3" type="cn.ijiami.detection.VO.DetectionResultCompareDetailVO" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id"/>
        <collection property="taskList" ofType="cn.ijiami.detection.entity.TTask" >
            <result column="task_id" property="taskId" jdbcType="BIGINT" />
            <result column="apk_detection_detail_id" property="apkDetectionDetailId" jdbcType="VARCHAR" />
        </collection>
    </resultMap>

    <select id="findDetectionSummaryById" resultMap="BaseResultMap">
        SELECT
        t.`task_id`,
        t.`apk_detection_detail_id` AS document_id,
        a.`name` AS assets_name,
        a.`pakage` AS assets_package,
        a.`version`,
        a.`MD5`,
        CONCAT('手机：', e.`model`, '\n系统：', e.`version`) AS environment
        FROM t_detection_result_compare AS d
        JOIN t_detection_result_compare_task AS dt ON dt.report_id=d.id
        JOIN t_task AS t ON t.task_id=dt.task_id
        JOIN t_assets AS a ON a.id=t.assets_id
        JOIN t_task_extend AS e ON e.task_id=t.task_id
        WHERE d.id=#{id} GROUP BY t.task_id
    </select>

    <select id="findByPage" resultMap="BaseResultMap2">
        SELECT
        d.`id`,
        d.`terminal_type`,
        d.`create_user_id`,
        d.`status`,
        d.`create_time`,
        COUNT(t.task_id) AS task_count
        FROM t_detection_result_compare AS d
        JOIN t_detection_result_compare_task AS dt ON dt.report_id=d.id
        JOIN t_task AS t ON dt.task_id=t.task_id
        <if test="assetsName !=null and assetsName != ''">
        JOIN (
            SELECT
            d.`id`
            FROM t_detection_result_compare AS d
            JOIN t_detection_result_compare_task AS dt ON dt.report_id=d.id
            JOIN t_task AS t ON dt.task_id=t.task_id
            JOIN t_assets AS a ON a.id=t.assets_id
            WHERE a.`name` LIKE CONCAT('%',#{assetsName,jdbcType=VARCHAR},'%')
            GROUP BY d.`id`
        ) AS s ON s.`id` = d.`id`
        </if>
        WHERE d.terminal_type = #{terminalType} AND status=0
        <if test="createUserId !=null">
            AND d.create_user_id = #{createUserId}
        </if>
        GROUP BY d.id
        HAVING task_count>1
        ORDER BY d.create_time DESC
    </select>


    <select id="findById" resultMap="BaseResultMap3">
        SELECT
        d.id,
        t.task_id,
        t.apk_detection_detail_id
        FROM t_detection_result_compare AS d
        JOIN t_detection_result_compare_task AS dt ON dt.report_id=d.id
        JOIN t_task AS t ON dt.task_id=t.task_id
        WHERE d.`id` = #{id}
        GROUP BY d.id, t.task_id
    </select>

</mapper>