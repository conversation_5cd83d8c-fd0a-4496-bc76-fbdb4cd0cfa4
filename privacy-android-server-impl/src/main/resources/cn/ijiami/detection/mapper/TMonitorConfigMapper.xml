<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TMonitorConfigMapper">
	<resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TMonitorConfigEntity">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="code" property="code" />
		<result column="name" property="name" />
		<result column="value" property="value" />
	</resultMap>

	<select id="selectCleanShow" resultMap="BaseResultMap">
		select id,code,name,value from t_monitor_config where group_id=1 and is_show=1
	</select>

</mapper>