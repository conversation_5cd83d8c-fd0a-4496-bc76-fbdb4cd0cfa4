<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TTaskAutoReportMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTaskAutoReport">
    </resultMap>

    <update id="deleteByTaskUser" >
        UPDATE t_task_auto_report AS r
        JOIN t_task AS t ON r.task_id = t.task_id
        SET r.is_delete = 1
        WHERE t.create_user_id = #{userId} AND t.terminal_type=#{terminalType};
    </update>
</mapper>