<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionNougatMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TActionNougat">
        <id column="id" property="id"/>
        <result column="action_id" property="actionId"/>
        <result column="action_name" property="actionName"/>
        <result column="action_permission" property="actionPermission"/>
        <result column="action_permission_alias" property="actionPermissionAlias"/>
        <result column="sensitive" property="sensitive"/>
        <result column="is_personal" property="personal"/>
        <result column="action_api" property="actionApi"/>
    </resultMap>

    <select id="findByTerminalTypeAndActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id = #{actionId} and terminal_type = #{TerminalType} limit 1
    </select>

    <select id="findByTerminalType" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where terminal_type = #{TerminalType}
    </select>

    <select id="findInTerminalTypeAndActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id in
        <foreach collection="actionIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and terminal_type = #{terminalType}
    </select>


    <select id="findInActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id in
        <foreach collection="actionIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findActionIdByNames" resultType="java.lang.Long">
        SELECT action_id FROM t_action_nougat
        WHERE action_name IN
        <foreach collection="actionNameList" item="action_name" open="(" separator="," close=")">
            #{action_name}
        </foreach>
        AND terminal_type = #{terminalType}
        GROUP BY action_id
    </select>

    <select id="selectPersonalAction" resultType="cn.ijiami.detection.VO.PersonalActionVO">
        select action_id as actionId,action_name as actionName from t_action_nougat where terminal_type = #{terminalType} and is_personal =1
    </select>
</mapper>
