<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TBigdataFunctionalMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TBigdataFunctional">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="name_path" property="namePath"/>
        <result column="code" property="code"/>
        <result column="keyword" property="keyword"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_SQL">
       id,
       name,
       parent_id,
       level,
       name_path,
       code,
       keyword,
       create_time,
       update_time
    </sql>
    <select id="getBigDataFunctionalById" resultMap="BaseResultMap">
        select <include refid="Base_SQL"/>
        from t_bigdata_functional
        <where>
            1=1
            <if test="ids!=null ">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
</mapper>