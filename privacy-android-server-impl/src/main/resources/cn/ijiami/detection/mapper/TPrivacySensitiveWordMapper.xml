<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacySensitiveWord">
        <id property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="taskId" column="task_id"/>
        <result property="sensitiveWord" column="sensitive_word"/>
        <result property="method" column="method"/>
        <result property="host" column="host"/>
        <result property="port" column="port"/>
        <result property="cookie" column="cookie"/>
        <result property="protocol" column="protocol"/>
        <result property="url" column="url"/>
        <result property="address" column="address"/>
        <result property="code" column="code"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="suggestion" column="suggestion"/>
        <result property="fileKey" column="file_key"/>
        <result property="sdkId" column="sdk_id"/>
        <result property="sdkName" column="sdk_name"/>
        <result column="name" property="name"/>
        <result column="executor_type" property="executorType"/>
        <result column="executor" property="executor"/>
        <result column="stack_info" property="stackInfo"/>
        <result column="details_data" property="detailsData"/>
        <result column="response_data" property="responseData"/>
        <result column="behavior_stage" property="behaviorStage"/>
        <result column="action_time" property="actionTime"/>
        <result column="sdk_ids" property="sdkIds"/>
        <result column="cookie_mark" property="cookieMark"/>
        <result column="plaintext_transmission" property="plaintextTransmission"/>
    </resultMap>

    <resultMap id="TPrivacySensitiveWordResultMap" type="cn.ijiami.detection.entity.TPrivacySensitiveWord" extends="BaseResultMap">
        <result column="typeName" property="typeName"/>
        <result column="executorTypeString" property="executorTypeString"/>
        <result column="attributively" property="attributively"/>
        <result column="ip" property="ip"/>
        <result column="package_name" property="packageName"/>
        <result column="cookie_mark" property="cookieMark"/>
    </resultMap>

    <resultMap id="PersonalMessageAndTypeVO" type="cn.ijiami.detection.VO.PersonalMessageAndType">
        <result column="task_id" property="taskId"/>
        <result column="type_id" property="typeId"/>
        <result column="name" property="name"/>
        <result column="type_name" property="typeName"/>
    </resultMap>

    <sql id="base_column_list">
        id,
            task_id,
            type_id,
            name,
            sensitive_word,
            method,
            host,
            port,
            cookie,
            protocol,
            url,
            address,
            code,
            risk_level,
            suggestion,
            file_key,
            sdk_id,
            sdk_name,
            executor_type,
            executor,
            stack_info,
            details_data,
            response_data,
            behavior_stage,
            action_time,
            sdk_ids,
            cookie_mark,
            plaintext_transmission
    </sql>

    <select id="findByTypeIdAndTaskId" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word where type_id = #{typeId,jdbcType=BIGINT} and task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
    </select>

    <select id="findByTaskIdAndMethod" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word where task_id=#{taskId,jdbcType=BIGINT}
        <if test="method != null and method != ''">
            and method = #{method,jdbcType=VARCHAR}
        </if>
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and result_status !=2
    </select>
    <select id="findByTaskIdAndSdkId" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word
        where task_id=#{taskId}
        and sdk_id=#{sdkId}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
    </select>
    <select id="findByTaskId" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word
        where task_id=#{taskId}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
    </select>
    <select id="findBySdk" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word
        where sdk_id IS NOT NULL
    </select>

    <select id="countSensitiveTypeByTaskId" resultType="cn.ijiami.detection.VO.CountSensitiveTypeVO">
        <!-- select task_id                                                     as taskId,
               type_id                                                     as typeId,
               (select type_name from t_sensitive_type where id = type_id) as typeName,
               count(type_id)                                              as typeCount
        from t_privacy_sensitive_word
        where task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        group by type_id -->
        select task_id  as taskId,
               type_id  as typeId,
               (select type_name from t_sensitive_type where id = type_id) as typeName,
			   `name`, 
               count(type_id)  as typeCount,
			   sum(case when executor_type=1 then 1 ELSE 0 end)  as appCount,
			   sum(case when executor_type=2 then 1 ELSE 0 end)   as sdkCount
        from t_privacy_sensitive_word
        where task_id =  #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        group by type_id,`name`
    </select>

    <select id="countSensitiveNameByTaskId" resultType="cn.ijiami.detection.VO.CountSensitiveNameVO">
        SELECT task_id                                  as taskId,
               type_id                                  as typeId,
               `name`                                   as name,
               sensitive_word                           as sensitiveWord,
               COUNT(IF(executor_type = 1, TRUE, NULL)) AS appCount,
               COUNT(IF(executor_type = 2, TRUE, NULL)) AS sdkCount
        FROM t_privacy_sensitive_word
        WHERE task_id = #{taskId,jdbcType=BIGINT}
          AND type_id = #{typeId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        GROUP BY `name`
    </select>

    <select id="findByTaskIdAndTypeIdAndName" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from t_privacy_sensitive_word
        where task_id=#{taskId,jdbcType=BIGINT}
        and type_id=#{typeId,jdbcType=BIGINT}
        and name=#{name,jdbcType=VARCHAR}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
    </select>

    <delete id="deleteByTaskId">
        delete from t_privacy_sensitive_word where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="findByTaskIdAndAllQuerys" resultMap="TPrivacySensitiveWordResultMap">
        select * from (
            select
                tps.id,
                tps.task_id,
                tps.type_id,
                tps.name,
                tps.sensitive_word,
                tps.method,
                tps.host,
                tps.port,
                tps.cookie,
                tps.protocol,
                tps.url,
                tps.address,
                tps.code,
                tps.risk_level,
                tps.suggestion,
                tps.file_key,
                tps.sdk_id,
        tps.sdk_name,
        tps.executor_type,
        tps.executor,
        tps.stack_info,
        tps.details_data,
        tps.response_data,
        tps.behavior_stage,
        tps.action_time,
        tps.sdk_ids,
        tst.type_name AS typeName,
        <choose>
            <when test="terminalType != null and (terminalType == 4 or terminalType == 5)">
                (case tps.executor_type when 1 then '小程序' when 2 then 'SDK' end) AS executorTypeString,
            </when>
            <otherwise>
                (case tps.executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
            </otherwise>
        </choose>
        tps.attributively,
        tps.ip,
        tps.package_name,
        tps.cookie_mark
        from t_privacy_sensitive_word tps left join t_sensitive_type tst on tps.type_id=tst.id
        where tps.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tps.behavior_stage = #{behaviorStage}
        </if>
        <if test="cookieMark != null">
            and tps.cookie_mark = #{cookieMark}
        </if>
        <if test="plaintextTransmission != null">
            and tps.plaintext_transmission = #{plaintextTransmission}
        </if>
        ) a
      <where>
          1=1
          <if test="executors != null and executors != '' ">
              AND (
              <foreach collection="executors.split(';')" item="executor" index="num">
                  <if test="executors.split(';').length > num+1">
                      a.executor like CONCAT('%',#{executor},'%') or
                  </if>
                  <if test="executors.split(';').length==num+1">
                      a.executor like CONCAT('%',#{executor},'%')
                  </if>
              </foreach>
              )
          </if>
          <if test="executorType != null and executorType != '' ">
              AND a.executorTypeString in
              <foreach collection="executorType.split(',')" item="type" open="(" separator="," close=")">
                  #{type}
              </foreach>
          </if>
          <if test="typeName !=null and typeName !='' ">
              AND a.typeName in
              <foreach collection="typeName.split(',')" item="t" open="(" separator="," close=")">
                  #{t}
              </foreach>
          </if>
          <if test="personalName !=null and personalName !='' ">
              AND a.name in
              <foreach collection="personalName.split(';')" item="name" open="(" separator="," close=")">
                  #{name}
              </foreach>
          </if>

          <if test="behaviorStage != 0 and behaviorStage != null">
              AND a.behavior_stage = #{behaviorStage}
          </if>
          AND a.task_id=#{taskId,jdbcType=BIGINT}
      </where>

        <if test="sortType == null or sortType == 0">
            order by a.id desc
        </if>
        <if test="sortType != null and sortType != 0">
            order by
            <if test="sortType == 1">
                a.action_time
            </if>
            <if test="sortType == 2">
                a.executorTypeString
            </if>
            <if test="sortType == 3">
                a.executor
            </if>
            <if test="sortType == 8">
                a.typeName
            </if>
            <if test="sortType == 9">
                a.name
            </if>
            <if test="sortOrder == 1">
                asc
            </if>
            <if test="sortOrder == 2">
                desc
            </if>
        </if>

    </select>

    <!--获取触发主体筛选条件-->
    <select id="getBehaviorTransmission" resultType="java.lang.String">
        select DISTINCT (executor)
        from t_privacy_sensitive_word
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and executor_type=2
        and executor is not null
        and executor &lt;&gt; ''
    </select>

    <!--获取信息分类筛选条件-->
    <select id="getBehaviorBehaviorMessageType" resultType="java.lang.String">
        select DISTINCT (tst.type_name)
        from t_privacy_sensitive_word tps left join t_sensitive_type tst on tps.type_id=tst.id
        where tps.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tps.behavior_stage = #{behaviorStage}
        </if>
        and tst.type_name is not null
        and tst.type_name &lt;&gt; ''
    </select>

    <!--获取个人信息筛选条件-->
    <select id="getBehaviorPersonalMessage" resultType="java.lang.String">
        select DISTINCT (name)
        from t_privacy_sensitive_word
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and name is not null
        and name &lt;&gt; ''
    </select>

    <select id="getBehaviorPersonalList" resultMap="PersonalMessageAndTypeVO">
        select DISTINCT (tps.name),(tps.task_id),(tps.type_id),(tst.type_name)
        from t_privacy_sensitive_word tps left join t_sensitive_type tst on tps.type_id=tst.id
        where tps.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tps.behavior_stage = #{behaviorStage}
        </if>
        and tps.name is not null
        and tps.name &lt;&gt; ''
    </select>

    <select id="getBehaviorMessageTypeList" resultMap="PersonalMessageAndTypeVO">
        select DISTINCT (tst.type_name),(tps.type_id),(tps.task_id),(tps.name)
        from t_privacy_sensitive_word tps left join t_sensitive_type tst on tps.type_id=tst.id
        where tps.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tps.behavior_stage = #{behaviorStage}
        </if>
        and tst.type_name is not null
        and tst.type_name &lt;&gt; ''
    </select>
    
    <select id="findMisjudgmentData" resultType="cn.ijiami.detection.VO.SensitiveWordExcelReportVO">
    	select 
		w.task_id as taskId,
		a.`name` as appName,
		a.pakage,
		a.shell_ipa_path AS path,
		w.behavior_stage as behaviorStage,
		w.action_time as actionTime,
		w.executor_type executorType,
		w.executor,
		type.type_name as typeName,
		w.`name`,
		w.`port`,
		w.`host`,
		w.protocol,
		w.method,
		w.url,
		w.sensitive_word as sensitiveWord,
		w.`code`,
		w.details_data as detailsData,
        w.response_data as responseData,
		w.attributively as address,
        w.ip,
        w.package_name as actionPackageName,
		w.stack_info as stackInfo from t_privacy_sensitive_word as w 
		LEFT JOIN t_task as t on t.task_id = w.task_id
		LEFT JOIN t_assets as a on t.assets_id = a.id
		LEFT JOIN t_sensitive_type as type on w.type_id = type.id
		where w.result_status=2
		AND t.task_id in(
		select word.task_id from t_privacy_result_mark as mark
		LEFT JOIN t_privacy_sensitive_word as word on mark.b_id=word.id
		where mark.result_type=1 
		<if test="startTime != '' and startTime != null">
            and mark.create_time &gt; #{startTime}
        </if>
        <if test="endTime != '' and endTime != null">
            and mark.create_time &lt; #{endTime}
        </if>
		GROUP BY word.task_id)
		<if test="createUserId != 0 and createUserId != null">
            and t.create_user_id = #{createUserId}
        </if>
    </select>

    <select id="selectDataById" resultMap="TPrivacySensitiveWordResultMap">
        select
                tps.id,
                tps.task_id,
                tps.type_id,
                tps.name,
                tps.sensitive_word,
                tps.method,
                tps.host,
                tps.port,
                tps.cookie,
                tps.protocol,
                tps.url,
                tps.address,
                tps.code,
                tps.risk_level,
                tps.suggestion,
                tps.file_key,
                tps.sdk_id,
                tps.sdk_name,
                tps.executor_type,
                tps.executor,
                tps.stack_info,
                tps.details_data,
                tps.response_data,
                tps.behavior_stage,
                tps.action_time,
                tps.sdk_ids,
                tst.type_name AS typeName,
                (case tps.executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
                tps.attributively,
                tps.ip,
                tps.package_name,
                tps.cookie_mark
            from t_privacy_sensitive_word tps left join t_sensitive_type tst on tps.type_id=tst.id
            where tps.id=#{id}
    </select>
</mapper>