<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.THardwareSensorShakeValueMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.THardwareSensorShakeValue">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="trigger_value" property="triggerValue" jdbcType="VARCHAR"/>
        <result column="shake_page_site" property="shakePageSite" jdbcType="VARCHAR"/>
        <result column="page_jump" property="pageJump" jdbcType="TINYINT"/>
        <result column="non_compliance" property="nonCompliance" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="BASE_SQL">
        id,task_id,trigger_value,shake_page_site,page_jump,non_compliance,create_time
    </sql>
    <select id="getShakeValueByTaskId" resultMap="BaseResultMap">
        select <include refid="BASE_SQL"/>
        from t_hardware_sensor_shake_value
        where task_id = #{taskId}
        order by create_time desc
    </select>

    <delete id="deleteAllValueByTaskId">
        delete from t_hardware_sensor_shake_value where task_id = #{taskId}
    </delete>
</mapper>