<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TCcrcTaskMapper">
    <resultMap id="BaseResult" type="cn.ijiami.detection.entity.TCcrcTask">
        <id column="id" property="id"/>
        <result column="apk_id" property="apkId"/>
        <result column="categories" property="categories"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="callback_url" property="callbackUrl"/>
    </resultMap>
</mapper>
