<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskQueueMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTaskQueue">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="bussiness_id" property="bussinessId"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="callback_url" property="callbackUrl"/>
        <result column="queue_status" property="queueStatus"/>
        <result column="message" property="message"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="app_id" property="appId"/>
        
    </resultMap>
    

    <!-- 分页查询任务 -->
    <sql id="Base_sql">
    	id,bussiness_id,app_id,download_url,callback_url,queue_status,message,create_time
    </sql>
    
    <select id="waitQueueList" resultMap="BaseResultMap">
        select <include refid="Base_sql"/> from t_task_queue where queue_status=1
        <if test="dataSources != null">
            and data_sources = #{dataSources}
        </if>
        <if test="size != null and strategy != null and strategy == 1">
            and size &lt; #{size}
        </if>
        <if test="size != null and strategy != null and strategy == 2">
            and size &gt; #{size}
        </if>
         limit 10
    </select>
    
    <select id="findQueueByBussinessId" resultMap="BaseResultMap">
        select <include refid="Base_sql"/> from t_task_queue where bussiness_id=#{bussinessId} order by create_time desc limit 1
    </select>
</mapper>