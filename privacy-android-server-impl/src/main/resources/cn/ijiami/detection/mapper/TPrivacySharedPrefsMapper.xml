<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacySharedPrefs">
        <!--@mbg.generated-->
        <!--@Table t_privacy_shared_prefs-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="sensitive_id" jdbcType="BIGINT" property="sensitiveId"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="file_key" jdbcType="BIGINT" property="fileKey"/>
        <result column="executor_type" jdbcType="BOOLEAN" property="executorType"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="type_id" property="typeId"/>
        <result column="name" property="name"/>
        <result column="sensitive_word" property="sensitiveWord"/>
        <result column="behavior_stage" property="behaviorStage"/>
        <result column="stack_info" property="stackInfo"/>
        <result column="action_time" property="actionTime"/>
        <result column="sdk_ids" property="sdkIds"/>
    </resultMap>

    <resultMap id="TPrivacySharedPrefsResultMap" type="cn.ijiami.detection.entity.TPrivacySharedPrefs" extends="BaseResultMap">
        <result column="typeName" property="typeName"/>
        <result column="executorTypeString" property="executorTypeString"/>
        <result column="package_name" property="packageName"/>
    </resultMap>

    <resultMap id="StoragePersonalMessageAndTypeVO" type="cn.ijiami.detection.VO.StoragePersonalMessageAndType">
        <result column="task_id" property="taskId"/>
        <result column="type_id" property="typeId"/>
        <result column="name" property="name"/>
        <result column="type_name" property="typeName"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_id,type_id,name,sensitive_word, sensitive_id, `path`, content, file_key, executor_type, executor, behavior_stage,
        stack_info,action_time,sdk_ids
    </sql>

    <select id="countSharedPrefsTypeByTaskId" resultType="cn.ijiami.detection.VO.CountSharedPrefsTypeVO">
        select task_id                                                     as taskId,
               type_id                                                     as typeId,
               (select type_name from t_sensitive_type where id = type_id) as typeName,
               `name`,
               count(type_id)                                              as typeCount,
               sum(case when executor_type=1 then 1 ELSE 0 end)  as appCount,
			   sum(case when executor_type=2 then 1 ELSE 0 end)   as sdkCount
        from t_privacy_shared_prefs
        where task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        group by type_id,`name`
    </select>

    <select id="countSharedPrefsNameByTaskId" resultType="cn.ijiami.detection.VO.CountSharedPrefsNameVO">
        SELECT task_id                                  as taskId,
               type_id                                  as typeId,
               `name`                                   as name,
               sensitive_word                           as sensitiveWord,
               COUNT(IF(executor_type = 1, TRUE, NULL)) AS appCount,
               COUNT(IF(executor_type = 2, TRUE, NULL)) AS sdkCount
        FROM t_privacy_shared_prefs
        WHERE task_id = #{taskId,jdbcType=BIGINT}
          AND type_id = #{typeId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        GROUP BY `name`
    </select>

    <select id="findByTaskIdAndTypeIdAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_privacy_shared_prefs
        where task_id=#{taskId,jdbcType=BIGINT}
        and type_id=#{typeId,jdbcType=BIGINT}
        and name=#{name,jdbcType=VARCHAR}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
    </select>

    <delete id="deleteByTaskId">
        delete
        from t_privacy_shared_prefs
        where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="findByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_privacy_shared_prefs where task_id=#{taskId,jdbcType=BIGINT} 
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and result_status != 2
    </select>

    <!-- 查询存储个人信息平铺展示 -->
    <select id="findByTaskIdAndBehaviorStageAndQuerys" resultMap="TPrivacySharedPrefsResultMap">
        select * from (
            select
                tpsp.id,
                tpsp.task_id,
                tpsp.type_id,
                tpsp.name,
                tpsp.sensitive_word,
                tpsp.sensitive_id,
        tpsp.path,
        tpsp.content,
        tpsp.file_key,
        tpsp.executor_type,
        tpsp.executor,
        tpsp.behavior_stage,
        tpsp.stack_info,
        tpsp.action_time,
        tpsp.sdk_ids,
        tst.type_name AS typeName,
        <choose>
            <when test="terminalType != null and (terminalType == 4 or terminalType == 5)">
                (case tpsp.executor_type when 1 then '小程序' when 2 then 'SDK' end) AS executorTypeString,
            </when>
            <otherwise>
                (case tpsp.executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
            </otherwise>
        </choose>
        tpsp.package_name
        from t_privacy_shared_prefs tpsp left join t_sensitive_type tst on tpsp.type_id=tst.id
        where tpsp.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tpsp.behavior_stage = #{behaviorStage}
        </if>
        ) a
        <where>
            1=1
            <if test="executors !=null and executors!='' ">
                AND (
                <foreach collection="executors.split(';')" item="executor" index="num">
                    <if test="executors.split(';').length > num+1">
                        a.executor like CONCAT('%',#{executor},'%') or
                    </if>
                    <if test="executors.split(';').length==num+1">
                        a.executor like CONCAT('%',#{executor},'%')
                    </if>
                </foreach>
                )
            </if>
            <if test="executorType !=null and executorType!='' ">
                AND a.executorTypeString in
                <foreach collection="executorType.split(',')" item="e" open="(" separator="," close=")">
                    #{e}
                </foreach>
            </if>
            <if test="typeName !=null and typeName!='' ">
                AND a.typeName in
                <foreach collection="typeName.split(',')" item="t" open="(" separator="," close=")">
                    #{t}
                </foreach>
            </if>
            <if test="personalName !=null and personalName!='' ">
                AND a.name in
                <foreach collection="personalName.split(';')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>


            <if test="behaviorStage != 0 and behaviorStage != null">
                AND a.behavior_stage = #{behaviorStage}
            </if>
            AND a.task_id=#{taskId,jdbcType=BIGINT}
        </where>

        <if test="sortType == null or sortType == 0">
            order by a.id desc
        </if>
        <if test="sortType != null and sortType != 0">
            order by
            <if test="sortType == 1">
                a.action_time
            </if>
            <if test="sortType == 2">
                a.executorTypeString
            </if>
            <if test="sortType == 3">
                a.executor
            </if>
            <if test="sortType == 8">
                a.typeName
            </if>
            <if test="sortType == 9">
                a.name
            </if>
            <if test="sortOrder == 1">
                asc
            </if>
            <if test="sortOrder == 2">
                desc
            </if>
        </if>

    </select>

    <select id="getBehaviorStorage" resultType="java.lang.String">
        select DISTINCT(executor)
        from t_privacy_shared_prefs
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and executor_type=2
        and executor is not null
        and executor &lt;&gt; ''
    </select>

    <select id="getBehaviorStorageMessageType" resultType="java.lang.String">
        select DISTINCT(tst.type_name)
        from t_privacy_shared_prefs tpsp left join t_sensitive_type tst on tpsp.type_id=tst.id
        where tpsp.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tpsp.behavior_stage = #{behaviorStage}
        </if>
        and tst.type_name is not null
        and tst.type_name &lt;&gt; ''
    </select>

    <select id="getBehaviorStoragePersonalMessage" resultType="java.lang.String">
        select DISTINCT(name)
        from t_privacy_shared_prefs
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        and name is not null
        and name &lt;&gt; ''
    </select>

    <select id="getStorageMessageTypeList" resultMap="StoragePersonalMessageAndTypeVO">
        select DISTINCT(tst.type_name),(tpsp.task_id),(tpsp.type_id),(tpsp.name)
        from t_privacy_shared_prefs tpsp left join t_sensitive_type tst on tpsp.type_id=tst.id
        where tpsp.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tpsp.behavior_stage = #{behaviorStage}
        </if>
        and tst.type_name is not null
        and tst.type_name &lt;&gt; ''
    </select>

    <select id="getStoragePersonalMessageList" resultMap="StoragePersonalMessageAndTypeVO">
        select DISTINCT(tpsp.name),(tst.type_name),(tpsp.task_id),(tpsp.type_id)
        from t_privacy_shared_prefs tpsp left join t_sensitive_type tst on tpsp.type_id=tst.id
        where tpsp.task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and tpsp.behavior_stage = #{behaviorStage}
        </if>
        and tpsp.name is not null
        and tpsp.name &lt;&gt; ''
    </select>
    
    <select id="findMisjudgmentData" resultType="cn.ijiami.detection.VO.SharedPrefsExcelReportVO">
  	  select 
		w.task_id as taskId,
		a.`name` as appName,
		a.pakage,
		a.shell_ipa_path AS appPath,
		w.behavior_stage as behaviorStage,
		w.action_time as actionTime,
		w.executor_type executorType,
		w.executor,
		type.type_name as typeName,
		w.`name`,
		w.sensitive_word as sensitiveWord,
		w.path,
		w.`content`,
		w.package_name as actionPackageName,
		w.stack_info as stackInfo from t_privacy_shared_prefs as w 
		LEFT JOIN t_task as t on t.task_id = w.task_id
		LEFT JOIN t_assets as a on t.assets_id = a.id
		LEFT JOIN t_sensitive_type as type on w.type_id = type.id
		where w.result_status=2
		AND t.task_id in(
		select word.task_id from t_privacy_result_mark as mark
		LEFT JOIN t_privacy_shared_prefs as word on mark.b_id=word.id
		where mark.result_type=2
		<if test="startTime != '' and startTime != null">
            and mark.create_time &gt; #{startTime}
        </if>
        <if test="endTime != '' and endTime != null">
            and mark.create_time &lt; #{endTime}
        </if>
		GROUP BY word.task_id)
		<if test="createUserId != 0 and createUserId != null">
            and t.create_user_id = #{createUserId}
        </if>
    </select>

    <select id="selectDataById" resultMap="TPrivacySharedPrefsResultMap">
        select
                tpsp.id,
                tpsp.task_id,
                tpsp.type_id,
                tpsp.name,
                tpsp.sensitive_word,
                tpsp.sensitive_id,
                tpsp.path,
                tpsp.content,
                tpsp.file_key,
                tpsp.executor_type,
                tpsp.executor,
                tpsp.behavior_stage,
                tpsp.stack_info,
                tpsp.action_time,
                tpsp.sdk_ids,
                tst.type_name AS typeName,
                (case tpsp.executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
                tpsp.package_name
            from t_privacy_shared_prefs tpsp left join t_sensitive_type tst on tpsp.type_id=tst.id
            where tpsp.id=#{id,jdbcType=BIGINT}
    </select>

</mapper>