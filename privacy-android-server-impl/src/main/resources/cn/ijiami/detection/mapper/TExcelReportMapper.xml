<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TExcelReportMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TExcelReport">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <id column="file_name" property="fileName" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <update id="updateFileName">
        update t_excel_report set file_name = #{fileName},update_time=NOW() where file_name like '%IDB%'
    </update>
</mapper>