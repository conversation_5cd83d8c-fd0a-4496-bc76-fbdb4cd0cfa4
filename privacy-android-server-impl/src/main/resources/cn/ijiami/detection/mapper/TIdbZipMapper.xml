<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TIdbZipMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TIdbZip">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="idb_file_name" property="idbFileName" />
        <result column="idb_file_url" property="idbFileUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <insert id="insertIdb">
        insert into t_idb_zip (idb_file_name,idb_file_url,create_time,update_time) value (#{fileName},#{fileUrl},now(),now())
    </insert>

    <update id="updateIdb">
        update t_idb_zip set idb_file_name=#{fileName},idb_file_url=#{fileUrl},update_time=now() where id=#{id}
    </update>
</mapper>