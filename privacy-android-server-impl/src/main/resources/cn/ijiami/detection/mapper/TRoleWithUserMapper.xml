<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.RoleWithUserMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.VO.RoleUserResultVO">
        <result column="role_id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="name" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="INTEGER"/>
        <collection property="childList" ofType="cn.ijiami.detection.VO.RoleUserResultVO" columnPrefix="u_">
            <result column="user_id" property="id" jdbcType="BIGINT"/>
            <result column="user_name" property="name" jdbcType="VARCHAR"/>
            <result column="category" property="category" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <sql id="BASE_SQL">
        r.role_name, r.role_id, 1 AS category, u.user_name AS u_user_name, u.user_id AS u_user_id, IF(u.user_id IS NULL,
        NULL, 2) AS u_category
    </sql>


    <select id="findByName" parameterType="cn.ijiami.detection.entity.TActionFilterRegex"
            resultMap="BaseResultMap">
        SELECT
        <include refid="BASE_SQL"/>
        FROM tsys_role AS r LEFT JOIN tsys_user_role AS ur ON ur.role_id=r.role_id LEFT JOIN tsys_user AS u ON
        u.user_id=ur.user_id
        WHERE 1=1
        <if test="name !=null and name != ''">
            AND u.user_name LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%') OR r.role_name LIKE
            CONCAT('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY r.role_id,u.user_id
    </select>
</mapper>