<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TComplianceAppletPluginsMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TComplianceAppletPlugins">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="plugin_name" property="pluginName" jdbcType="VARCHAR"/>
        <result column="developer" property="developer" jdbcType="VARCHAR"/>
        <result column="plugin_appid" property="pluginAppid" jdbcType="VARCHAR"/>
        <result column="service_category" property="serviceCategory" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="BASE_SQL">
        id,task_id,plugin_name,developer,plugin_appid,service_category
    </sql>

    <select id="selectAllPluginsByTaskId" resultMap="BaseResultMap">
        select <include refid="BASE_SQL"/>
        from t_compliance_applet_plugins
        where task_id = #{taskId}
    </select>
</mapper>