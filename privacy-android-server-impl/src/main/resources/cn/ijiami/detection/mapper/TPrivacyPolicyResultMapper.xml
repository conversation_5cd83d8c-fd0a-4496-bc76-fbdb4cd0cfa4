<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyPolicyResultMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TPrivacyPolicyResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="policy_item_id" property="policyItemId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="category" property="category" jdbcType="INTEGER"/>
        <result column="behavior_stage" property="behaviorStage" jdbcType="INTEGER"/>
        <result column="detail_result" property="detailResult" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="standards" property="standards" jdbcType="VARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="item_no" property="itemNo" jdbcType="VARCHAR"/>
        <result column="file_key" property="fileKey" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="risk_description" property="riskDescription" jdbcType="VARCHAR"/>
        <result column="safe_description" property="safeDescription" jdbcType="VARCHAR"/>
        <result column="stack_info" property="stackInfo" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteByTaskId">
        delete
        from t_privacy_policy_result
        where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT
        i.item_no,
        i.content,
        i.risk_description,
        i.safe_description,
        i.name,
        r.*
        FROM t_privacy_policy_result r
        LEFT JOIN t_privacy_policy_item i
        ON i.id = r.policy_item_id
        WHERE r.task_id = #{taskId,jdbcType=BIGINT} AND r.category=1
        ORDER BY i.id ASC
    </select>

    <select id="findAppStoreByTaskId" resultMap="BaseResultMap">
        SELECT
        i.item_no,
        i.content,
        i.risk_description,
        i.safe_description,
        i.standards,
        i.name,
        r.*
        FROM t_privacy_policy_result r
        LEFT JOIN t_privacy_policy_item i
        ON i.id = r.policy_item_id
        WHERE r.task_id = #{taskId,jdbcType=BIGINT} AND r.category=2
        ORDER BY r.id ASC
    </select>
</mapper>