<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TIosIdbDetectionMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TIosIdbDetection">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="cmd_type" property="cmdType" jdbcType="INTEGER"/>
        <result column="msg" property="msg" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>