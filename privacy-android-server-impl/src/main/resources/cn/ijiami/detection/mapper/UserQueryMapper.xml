<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.UserQueryMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.manager.user.entity.User">
        <!-- WARNING - @mbg.generated -->
        <id column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="sex" property="sex" jdbcType="INTEGER" />
        <result column="email" property="email" jdbcType="VARCHAR" />
        <result column="idcard" property="idcard" jdbcType="VARCHAR" />
        <result column="mobilephone" property="mobilephone" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="permission_code" property="permissionCode" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="valid_start_date" property="validStartDate" jdbcType="DATE" />
        <result column="valid_end_date" property="validEndDate" jdbcType="DATE" />
        <result column="created_user_id" property="createdUserId" jdbcType="BIGINT" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
        <result column="updated_user_id" property="updatedUserId" jdbcType="BIGINT" />
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
        <result column="lastest_login_time" property="lastestLoginTime" jdbcType="TIMESTAMP" />
        <result column="is_default" property="isDefault" jdbcType="INTEGER" />
        <result column="fails" property="fails" jdbcType="INTEGER" />
        <result column="locked_time" property="lockedTime" jdbcType="TIMESTAMP" />
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    </resultMap>

    <sql id="Base_Column_List">
        user_id,user_name,password,real_name,sex,email,idcard,mobilephone,status,permission_code,remark,
        valid_start_date,valid_end_date,created_user_id,created_time,updated_user_id,updated_time,lastest_login_time,is_default,fails,locked_time,tenant_id
    </sql>

    <select id="selectUsersByUserName" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tsys_user where status!=3 and user_name = #{userName}
        <if test="email !=null and email!='' ">
            and email = #{email}
        </if>
        LIMIT 1
    </select>

</mapper>