<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkDectDetailMapper" >

    <resultMap id="SdkResultMap" type="cn.ijiami.detection.VO.statistics.SdkUsageResult" >
        <result column="usage_count" property="usageCount"/>
        <result column="sdk_id" property="sdkId"/>
        <result column="sdk_name" property="sdkName"/>
        <result column="sdk_package" property="sdkPackage"/>
    </resultMap>

    <select id="findSdkUsageStatistics" resultMap="SdkResultMap">
        SELECT
        s.`sdk_id`,
        s.`sdk_name`,
        s.`sdk_package`,
        COUNT(s.`task_id`) AS usage_count
        FROM t_sdk_dect_detail AS s
        INNER JOIN t_assets AS a ON a.id=s.assets_id
        INNER JOIN t_task AS t ON t.task_id=s.task_id
        INNER JOIN (
            SELECT a.`name`, MAX(t.`task_starttime`) AS `task_starttime`
            FROM t_assets AS a
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE a.is_delete=0 AND t.`create_user_id`=#{userId} AND t.`dynamic_status` = 6
            <if test="assetsName != null and assetsName != ''">
                AND a.`name` LIKE CONCAT('%',#{assetsName},'%')
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`
        ) AS ts ON ts.`name`=a.`name`AND ts.`task_starttime`=t.`task_starttime`
        WHERE s.`terminal_type`=#{terminalType} AND s.`user_id`=#{userId}
        GROUP BY s.`sdk_name`
        ORDER BY usage_count DESC
        LIMIT #{limit}
    </select>

    <delete id="deleteByTaskId">
        delete from t_sdk_dect_detail where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByAssetsId">
        delete from t_sdk_dect_detail where assets_id = #{assetsId,jdbcType=BIGINT}
    </delete>
</mapper>