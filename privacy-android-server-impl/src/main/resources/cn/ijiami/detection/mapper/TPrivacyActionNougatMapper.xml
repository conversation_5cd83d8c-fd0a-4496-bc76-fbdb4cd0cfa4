<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyActionNougatMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyActionNougat">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="action_id" property="actionId"/>
        <result column="stack_info" property="stackInfo"/>
        <result column="details_data" property="detailsData"/>
        <result column="action_time" property="actionTime"/>
        <result column="action_time_stamp" property="actionTimeStamp"/>
        <result column="type" property="type"/>
        <result column="executor" property="executor"/>
        <result column="package_name" property="packageName"/>
        <result column="executor_type" property="executorType"/>
        <result column="behavior_stage" property="behaviorStage"/>
        <result column="sdk_ids" property="sdkIds"/>
    </resultMap>

    <resultMap id="TPrivacyActionNougatVO" type="cn.ijiami.detection.entity.TPrivacyActionNougat"
               extends="BaseResultMap">
        <result column="task_id" property="taskId"/>
        <result column="action_id" property="actionId"/>
        <result column="action_name" property="actionName"/>
        <result column="action_permission" property="actionPermission"/>
        <result column="action_permission_alias" property="actionPermissionAlias"/>
        <result column="executor" property="executor"/>
        <result column="counter" property="counter"/>
        <result column="sensitive" property="sensitive"/>
        <result column="executor_type" property="executorType"/>
        <result column="package_name" property="packageName"/>
    </resultMap>

    <resultMap id="TPrivacyActionNougatMap" type="cn.ijiami.detection.entity.TPrivacyActionNougat" extends="TPrivacyActionNougatVO">
        <result column="is_personal" property="isPersonal"/>
        <result column="number_action" property="numberAction"/>
        <result column="trigger_cycle_time" property="triggerCycleTime"/>
        <result column="executorTypeString" property="executorTypeString"/>
        <result column="jni_stack_info" property="jniStackInfo"/>
        <result column="api_name" property="apiName"/>
    </resultMap>
    
    <resultMap id="TPrivacyActionNougatVO1" type="cn.ijiami.detection.entity.TPrivacyActionNougat" extends="TPrivacyActionNougatVO">
        <result column="is_personal" property="isPersonal"/>
    </resultMap>

    <resultMap id="ActionNameAndPermissionVO" type="cn.ijiami.detection.VO.ActionNameAndPermission">
        <result column="task_id" property="taskId"/>
        <result column="action_id" property="actionId"/>
        <result column="action_name" property="actionName"/>
        <result column="action_permission_alias" property="actionPermissionAlias"/>
    </resultMap>

    <resultMap id="AppStoreBehaviorApiVO" type="cn.ijiami.detection.VO.AppStorePrivacyApiVO">
        <result column="permission_name" property="permissionName"/>
        <result column="api_name" property="apiName"/>
        <result column="api_type" property="apiType"/>
        <result column="permission_desc" property="permissionDesc"/>
        <result column="executor" property="executor"/>
        <result column="protection_level" property="protectionLevel"/>
        <result column="type" property="type"/>
        <result column="is_privacy" property="isPrivacy"/>
        <result column="use_count" property="useCount"/>
    </resultMap>

    <!-- 【4/21 产品说只用统计不是应用前行为】 -->
    <select id="countByTaskId" resultMap="TPrivacyActionNougatVO">
        SELECT a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        count(a.action_id) AS 'counter',
        b.sensitive,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND a.type = 0
        GROUP BY a.action_id
        ORDER BY `sensitive` DESC,
        counter DESC
    </select>

    <select id="findById" resultMap="TPrivacyActionNougatMap">
        SELECT
        a.id,
        a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        b.sensitive,
        b.is_personal,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.sdk_ids,
        a.number_action,
        a.trigger_cycle_time,
        a.behavior_stage,
        nougat_e.jni_stack_info AS jni_stack_info,
        nougat_e.api_name AS api_name
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=a.id
        <where>
            a.id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="findByTaskId" resultMap="TPrivacyActionNougatMap">
        SELECT
        a.id,
        a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        b.sensitive,
        b.is_personal,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.sdk_ids,
        a.number_action,
        a.trigger_cycle_time,
        a.behavior_stage,
        nougat_e.jni_stack_info AS jni_stack_info,
        nougat_e.api_name AS api_name
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=a.id
        <where>
            a.task_id = #{taskId,jdbcType=BIGINT}
            <if test="actionIdList != null and actionIdList.size() > 0">
                AND b.`action_id` IN
                <foreach collection="actionIdList" item="actionId" open="(" separator="," close=")">
                    #{actionId}
                </foreach>
            </if>
            <if test="isPersonalList != null and isPersonalList.size() > 0">
                AND b.`is_personal` IN
                <foreach collection="isPersonalList" item="isPersonal" open="(" separator="," close=")">
                    #{isPersonal}
                </foreach>
            </if>
            <if test="behaviorStageList != null and behaviorStageList.size() > 0">
                AND a.`behavior_stage` IN
                <foreach collection="behaviorStageList" item="behaviorStage" open="(" separator="," close=")">
                    #{behaviorStage}
                </foreach>
            </if>
        </where>
        ORDER BY action_time_stamp DESC
    </select>

    <select id="findByTaskIdAndActionId" resultMap="TPrivacyActionNougatVO">
        SELECT
        action_id,
        stack_info,
        details_data,
        action_time,
        action_time_stamp,
        executor_type,
        executor,
        package_name
        FROM
        t_privacy_action_nougat
        <where>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT}
            </if>
            <if test="actionId != null">
                and action_id = #{actionId,jdbcType=BIGINT}
            </if>
            AND type = 0
        </where>
        order by executor_type
    </select>

    <select id="countActionPermission" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT c.`name`,
        c.alias_name AS aliasName,
        c.remark,
        c.type,
        c.grade,
        c.is_privacy AS isPrivacy,
        c.protection_level AS protectionLevel,
        COUNT(a.action_id) AS useCount,
        SUM(case when a.executor_type = 1 then 1 else 0 end) as appCount,
        SUM(case when a.executor_type = 2 then 1 else 0 end) as sdkCount,
        SUM(case when a.executor_type = 1 AND a.behavior_stage=1  then 1 else 0 end) as appB1Count,
        SUM(case when a.executor_type = 2 AND a.behavior_stage=1 then 1 else 0 end) as sdkB1Count,
        
        <if test="behaviorStage != 0 and behaviorStage != null">
			SUM(case when a.executor_type = 1 AND a.behavior_stage=2  then 1 else 0 end) as appB2Count,
			SUM(case when a.executor_type = 2 AND a.behavior_stage=2 then 1 else 0 end) as sdkB2Count,
		</if>
		<if test="behaviorStage == 0 or behaviorStage == null">
			SUM(case when a.executor_type = 1  then 1 else 0 end) as appB2Count,
			SUM(case when a.executor_type = 2  then 1 else 0 end) as sdkB2Count,
		</if>
        
		SUM(case when a.executor_type = 1 AND a.behavior_stage=3  then 1 else 0 end) as appB3Count,
        SUM(case when a.executor_type = 2 AND a.behavior_stage=3 then 1 else 0 end) as sdkB3Count,
		SUM(case when a.executor_type = 1 AND a.behavior_stage=4  then 1 else 0 end) as appB4Count,
        SUM(case when a.executor_type = 2 AND a.behavior_stage=4 then 1 else 0 end) as sdkB4Count,
        SUM(case when a.executor_type = 1 AND a.behavior_stage=5  then 1 else 0 end) as appB5Count,
        SUM(case when a.executor_type = 2 AND a.behavior_stage=5 then 1 else 0 end) as sdkB5Count
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_permission c ON b.action_permission = c.`name`
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND `name` IS NOT NULL
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        GROUP BY c.`name`
        ORDER BY type DESC,
        is_privacy DESC,
        useCount DESC
    </select>

    <select id="countSdkPermission" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT c.`name`,
        c.alias_name AS aliasName,
        c.remark,
        c.type,
        c.grade,
        c.is_privacy AS isPrivacy,
        COUNT(a.action_id) AS useCount
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_permission c ON b.action_permission = c.`name`
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.type = 0
        AND a.action_id != 28001
        AND `name` IS NOT NULL
        AND a.executor_type = 2
        AND a.package_name LIKE CONCAT('%', #{packageName,jdbcType=VARCHAR}, '%')
        GROUP BY c.`name`
        ORDER BY type DESC,
        is_privacy DESC,
        useCount DESC
    </select>

    <select id="countActionByTaskId" resultMap="TPrivacyActionNougatVO1">
        SELECT a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        b.`sensitive`,
        a.executor_type,
        a.executor,
        count(a.action_id) AS counter,
        a.package_name,
        a.sdk_ids,
		b.is_personal
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        <if test="behaviorStage != 0 and behaviorStage != null">
        	and a.behavior_stage=#{behaviorStage}
        </if>
        GROUP BY a.action_id,
        a.executor_type, a.package_name
        ORDER BY b.`sensitive` DESC,
        executor_type,
        counter DESC
    </select>

    <!-- 不统计应用前行为 -->
    <select id="countActionPermissionByTaskId" resultType="cn.ijiami.detection.VO.PermissionVO">
        SELECT c.name,
        c.alias_name AS aliasName,
        c.remark,
        c.type,
        c.is_privacy as isPrivacy,
        a.executor_type as executorType,
        count(a.action_id) AS useCount,
        SUM(case when a.executor_type = 1 then 1 else 0 end) as appCount,
        SUM(case when a.executor_type = 2 then 1 else 0 end) as sdkCount
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_permission c ON b.action_permission = c.`name`
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND b.action_permission IS NOT NULL
        AND a.action_id != 28001
        GROUP BY b.action_permission,
        a.executor_type
        ORDER BY c.type DESC,
        c.is_privacy DESC,
        useCount DESC
    </select>

    <delete id="deleteByTaskId">
        delete
        from t_privacy_action_nougat
        where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteNotPersonalByTaskId">
        DELETE t_privacy_action_nougat
        FROM t_privacy_action_nougat
        JOIN t_action_nougat ON t_privacy_action_nougat.action_id = t_action_nougat.action_id
        WHERE t_privacy_action_nougat.task_id = #{taskId,jdbcType=BIGINT} AND t_action_nougat.is_personal=0
    </delete>

    <select id="countActionNougatByTaskId" resultType="cn.ijiami.detection.VO.PrivacyActionNougatVO">
        SELECT a.action_id AS actionId,
        b.action_name AS actionName,
        b.action_permission_alias AS actionPermissionAlias,
        b.`sensitive`,
        COUNT(IF(executor_type = 1, TRUE, NULL)) AS appCount,
        COUNT(IF(executor_type = 2, TRUE, NULL)) AS sdkCount
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND a.type = 0 
        <if test="behaviorStage != 0 and behaviorStage != null">
        and a.behavior_stage=#{behaviorStage}
        </if>
        GROUP BY a.action_id
        ORDER BY `sensitive` DESC
    </select>
    <select id="findByTaskIdExcel" resultType="cn.ijiami.detection.entity.TPrivacyActionNougat">
       SELECT a.action_id AS actionId,
        a.behavior_stage as behaviorStage,
        b.action_name AS actionName,
        b.action_permission_alias AS actionPermissionAlias,
        b.`sensitive`,
				a.executor_type as executorType,
				a.executor,
				a.package_name as packageName,
				a.action_time as  actionTime,
				a.action_time_stamp as actionTimeStamp,
				a.stack_info as stackInfo,
				a.details_data as detailsData
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId}
        AND a.action_id != 28001
        ORDER BY a.action_id DESC
    </select>
    <select id="countBehaviorsCategoryByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT(action_id)) as num FROM t_privacy_action_nougat WHERE task_id = #{taskId} AND action_id != 28001;
    </select>

    <select id="countBehaviorsByTaskIdAndStage" resultMap="TPrivacyActionNougatVO">
        SELECT a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        count(a.action_id) AS 'counter',
        b.sensitive,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.behavior_stage
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        <if test="behaviorStage != 0 and behaviorStage != null">
        and a.behavior_stage=#{behaviorStage}
        </if>
        GROUP BY a.action_id
        ORDER BY `sensitive` DESC,
        counter DESC
    </select>

    <select id="countBehaviorsByTaskId" resultMap="TPrivacyActionNougatVO">
        SELECT a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        count(a.action_id) AS 'counter',
        b.sensitive,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.behavior_stage
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id not in(28001,32001)
        GROUP BY a.action_id
        ORDER BY `sensitive` DESC,
        counter DESC
    </select>
    <select id="countBehaviorsByTaskIdAndExecutorAndStage" resultType="java.lang.Integer">
        SELECT COUNT(1) AS NUM
        FROM t_privacy_action_nougat a
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND a.behavior_stage = #{behaviorStage}
    </select>
    <select id="findByTaskIdAndBehaviorStage" resultMap="TPrivacyActionNougatVO">
        SELECT a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission,
        b.action_permission_alias,
        count(a.action_id) AS 'counter',
        b.sensitive,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.sdk_ids
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        GROUP BY a.action_id,a.executor
        ORDER BY `sensitive` DESC,
        counter DESC
    </select>
    
    
    <select id="findByTaskIdAndBehaviorStageNew" resultMap="TPrivacyActionNougatMap">
        SELECT a.*,
		count(action_id) AS 'counter'
		FROM (
		SELECT a.task_id,
		        a.action_id,
		        b.action_name,
		        b.action_permission,
		        b.action_permission_alias,
		        b.sensitive,
		        a.package_name,
		        a.stack_info,
		        a.details_data,
		        a.action_time,
		        a.action_time_stamp,
		        a.executor,
		        a.executor_type,
		        a.sdk_ids,
                b.is_personal
		        FROM t_privacy_action_nougat a
		        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
		        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
		        AND a.action_id != 28001
		        <if test="behaviorStage != 0 and behaviorStage != null">
		            AND a.behavior_stage = #{behaviorStage}
		        </if>
		        AND  (a.action_id!=21004 AND a.action_id!=21005)
		        
		UNION ALL
		
		SELECT a.task_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN a.action_id 
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE 121005 END action_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN b.action_name
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE '应用关联启动' END action_name,
		        b.action_permission,
		        b.action_permission_alias,
		        b.sensitive,
		        a.package_name,
		        a.stack_info,
		        a.details_data,
		        a.action_time,
		        a.action_time_stamp,
		        a.executor,
		        a.executor_type,
		        a.sdk_ids,
                b.is_personal
		        FROM t_privacy_action_nougat a
		        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
		        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
		        <if test="behaviorStage != 0 and behaviorStage != null">
		            AND a.behavior_stage = #{behaviorStage}
		        </if>
				AND a.action_id =21004
		UNION ALL
		SELECT a.task_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN a.action_id 
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE 121005 END action_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN b.action_name
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE '应用关联启动' END action_name,
		        b.action_permission,
		        b.action_permission_alias,
		        b.sensitive,
		        a.package_name,
		        a.stack_info,
		        a.details_data,
		        a.action_time,
		        a.action_time_stamp,
		        a.executor,
		        a.executor_type,
		        a.sdk_ids,
                b.is_personal
		        FROM t_privacy_action_nougat a
		        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
		        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
		        <if test="behaviorStage != 0 and behaviorStage != null">
		            AND a.behavior_stage = #{behaviorStage}
		        </if>
				AND a.action_id=21005
		) a
		GROUP BY action_id,executor
		ORDER BY `sensitive` DESC,counter DESC
    </select>

    <select id="findByTaskIdAndActionIdAndExecutors" resultMap="TPrivacyActionNougatMap">
        SELECT
        action_id,
        stack_info,
        details_data,
        action_time,
        action_time_stamp,
        executor_type,
        executor,
        package_name
        FROM
        t_privacy_action_nougat
        <where>
            1 = 1
            <if test="taskId != null">
                AND task_id = #{taskId,jdbcType=BIGINT}
            </if>
            <if test="actionId != null">
                AND action_id = #{actionId,jdbcType=BIGINT}
            </if>
            <if test="executors != null and executors != '' ">
                AND executor in
                <foreach collection="executors.split(',')" item="executor" open="(" separator="," close=")">
                    #{executor}
                </foreach>
            </if>
            <if test="behaviorStage != 0 and behaviorStage != null">
                AND behavior_stage = #{behaviorStage}
            </if>
        </where>
        order by executor_type
    </select>

    <select id="getByTaskIdAndActionId" resultMap="TPrivacyActionNougatMap">
        SELECT
        a.action_id,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor_type,
        a.executor,
        a.package_name,
        b.is_personal
        FROM
        t_privacy_action_nougat as a
        left join t_action_nougat as b on a.action_id=b.action_id
        <where>
            1 = 1
            <if test="taskId != null">
                AND a.task_id = #{taskId,jdbcType=BIGINT}
            </if>
            <if test="actionId != null">
                AND a.action_id = #{actionId,jdbcType=BIGINT}
            </if>
            <if test="executors != null and executors != '' ">
                AND a.executor in
                <foreach collection="executors.split(',')" item="executor" open="(" separator="," close=")">
                    #{executor}
                </foreach>
            </if>
            <if test="behaviorStage != 0 and behaviorStage != null">
                AND a.behavior_stage = #{behaviorStage}
            </if>
        </where>
        order by a.executor_type
    </select>
    
    <select id="findByTaskIdAndActionIdAndExecutorsNew" resultMap="TPrivacyActionNougatMap">
		SELECT A.* from (
		SELECT a.task_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN a.action_id 
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE 121005 END action_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN b.action_name
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE '应用关联启动' END action_name,
		        b.action_permission,
		        b.action_permission_alias,
		        b.sensitive,
		        a.package_name,
		        a.stack_info,
		        a.details_data,
		        a.action_time,
		        a.action_time_stamp,
		        a.executor,
		        a.executor_type,
		        a.sdk_ids,
                b.is_personal,
                ex.jni_stack_info,
				(case a.number_action when null then 1 when '' then 1 else a.number_action end) AS number_action,
        		a.trigger_cycle_time
		        FROM t_privacy_action_nougat a
		        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
		        LEFT JOIN t_privacy_action_nougat_extend as ex on a.id = ex.nougat_id
		        WHERE 1=1
		        <if test="taskId != null">
                	AND a.task_id = #{taskId,jdbcType=BIGINT}
           		 </if>
		        <if test="behaviorStage != 0 and behaviorStage != null">
               		 AND a.behavior_stage = #{behaviorStage}
           		</if>
				AND a.action_id =21004
		
		UNION ALL
		SELECT a.task_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN a.action_id 
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE 121005 END action_id,
		        CASE WHEN a.details_data LIKE CONCAT('%',#{packageName},'%') THEN b.action_name
		       WHEN a.details_data = '/' THEN b.action_name
		       ELSE '应用关联启动' END action_name,
		        b.action_permission,
		        b.action_permission_alias,
		        b.sensitive,
		        a.package_name,
		        a.stack_info,
		        a.details_data,
		        a.action_time,
		        a.action_time_stamp,
		        a.executor,
		        a.executor_type,
		        a.sdk_ids,
                b.is_personal,
                ex.jni_stack_info,
				(case a.number_action when null then 1 when '' then 1 else a.number_action end) AS number_action,
        		a.trigger_cycle_time
		        FROM t_privacy_action_nougat a
		        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
		        LEFT JOIN t_privacy_action_nougat_extend as ex on a.id = ex.nougat_id
		        WHERE 1=1
		        <if test="taskId != null">
                	AND a.task_id = #{taskId,jdbcType=BIGINT}
           		 </if>
		        <if test="behaviorStage != 0 and behaviorStage != null">
               		 AND a.behavior_stage = #{behaviorStage}
           		</if>
				AND a.action_id=21005
		) A WHERE action_id=#{actionId,jdbcType=BIGINT}
     </select>
    
    <select id="getBehaviorSdk" resultType="java.lang.String">
        SELECT DISTINCT (executor)
        FROM t_privacy_action_nougat a
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND a.executor_type = 2
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>

    </select>
    <select id="getBehaviorSdkPkg" resultType="java.lang.String">
        SELECT DISTINCT (package_name)
        FROM t_privacy_action_nougat a
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        AND a.executor_type = 2
    </select>

    <!--行为权限查询-->
    <select id="getBehaviorPermission" resultType="String">
        SELECT DISTINCT b.action_permission_alias
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b on a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        <if test="behaviorStage != 4">
            AND a.action_id not in (32001)
        </if>
        AND a.action_id not in (28001)
        AND b.action_permission_alias is not null
        AND b.action_permission_alias &lt;&gt; ''
    </select>

    <!--行为权限查询-->
    <select id="getAppStorePrivacyApiList" resultMap="AppStoreBehaviorApiVO">
        SELECT
        c.remark AS permission_name,
        b.action_api AS api_type,
        e.api_name AS api_name,
        c.harm AS permission_desc,
        a.executor,
        c.type,
        c.is_privacy AS is_privacy,
        c.protection_level AS protection_level,
        COUNT(a.action_id) AS use_count
        FROM t_privacy_action_nougat a
        INNER JOIN t_privacy_action_nougat_extend e ON e.nougat_id = a.id
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_permission c ON b.action_permission = c.`name`
        WHERE a.task_id=#{taskId,jdbcType=BIGINT} AND a.action_id IN (110070, 110071, 110072, 110073, 110074, 110075)
        GROUP BY b.action_api,e.api_name
        ORDER BY use_count DESC
    </select>

    <!--应用行为名称查询-->
    <select id="getBehaviorApplyName" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT b.action_name,b.action_id
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b on a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        AND a.action_id not in (28001)
        AND b.action_name is not null
        AND b.action_name &lt;&gt; ''
    </select>

    <!--应用行为名称查询-->
    <select id="getLawDetailBehaviorApplyName" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT b.action_name,b.action_id
        FROM t_privacy_laws_detail AS tpld
        INNER JOIN t_action_nougat b on tpld.action_id = b.action_id
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.item_no = #{itemNo}
        AND tpld.data_type=#{dataType,jdbcType=INTEGER}
        AND b.action_name is not null
        AND b.action_name &lt;&gt; ''
    </select>

    <select id="getLawDetailBehaviorPermissionList" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT (b.action_permission_alias),(tpld.task_id),(b.action_id),(b.action_name)
        FROM t_privacy_laws_detail AS tpld
        INNER JOIN t_action_nougat b on tpld.action_id = b.action_id
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type = #{dataType,jdbcType=INTEGER}
        AND tpld.item_no = #{itemNo}
        AND b.action_permission_alias is not null
        AND b.action_permission_alias &lt;&gt; ''
    </select>

    <select id="getLawDetailBehaviorActionNameList" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT (tpld.task_id),(tpld.action_id),(b.action_name),(b.action_permission_alias)
        FROM t_privacy_laws_detail AS tpld
        INNER JOIN t_action_nougat b on tpld.action_id = b.action_id
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type = #{dataType,jdbcType=INTEGER}
        AND tpld.item_no = #{itemNo}
        AND b.action_name is not null
        AND b.action_name &lt;&gt; ''
    </select>

    <select id="getLawDetailBehaviorPermission" resultType="String">
        SELECT DISTINCT b.action_permission_alias
        FROM t_privacy_laws_detail AS tpld
        INNER JOIN t_action_nougat b on tpld.action_id = b.action_id
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type = #{dataType,jdbcType=INTEGER}
        AND tpld.item_no = #{itemNo}
        AND b.action_permission_alias is not null
        AND b.action_permission_alias &lt;&gt; ''
    </select>

    <select id="getLawDetailBehaviorSdk" resultType="java.lang.String">
        SELECT DISTINCT (executor)
        FROM t_privacy_laws_detail tpld
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type = #{dataType,jdbcType=INTEGER}
        AND tpld.item_no = #{itemNo}
    </select>

    <!--查询应用行为数据（平铺展示）2021/6/2-->
    <select id="findByTaskIdAndActionIdAndQuerys" resultMap="TPrivacyActionNougatMap">
        select s.*
        FROM
        (
            SELECT
            a.id,
            a.task_id,
            a.action_id,
            b.action_name,
        b.action_permission,
        b.action_permission_alias,
        b.sensitive,
        a.package_name,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.behavior_stage,
        <choose>
            <when test="terminalType != null and (terminalType == 4 or terminalType == 5)">
                (case a.executor_type when 1 then '小程序' when 2 then 'SDK' end) AS executorTypeString,
            </when>
            <otherwise>
                (case a.executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
            </otherwise>
        </choose>
        a.sdk_ids,
        (case b.is_personal when 1 then '是' when 0 then '否' end) AS is_personal,
        (case a.number_action when null then 1 when '' then 1 else a.number_action end) AS number_action,
        a.trigger_cycle_time,
        IFNULL(e.api_name, "--") AS api_name
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_privacy_action_nougat_extend e ON a.id = e.nougat_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
            AND a.action_id not in (28001)
            ORDER BY action_time_stamp desc
        ) s

        <where>
            1 = 1
            <if test="executors != null and executors != '' ">
                AND (
                <foreach collection="executors.split(';')" item="executor" index="num">
                    <if test="executors.split(';').length > num+1">
                        executor like CONCAT('%',#{executor},'%') or
                    </if>
                    <if test="executors.split(';').length==num+1">
                        executor like CONCAT('%',#{executor},'%')
                    </if>
                </foreach>
                )
            </if>
            <if test="actionPermissionAliases != null and actionPermissionAliases != '' ">
                AND action_permission_alias in
                <foreach collection="actionPermissionAliases.split(';')" item="actionPermissionAliase" open="(" separator="," close=")">
                    #{actionPermissionAliase}
                </foreach>
            </if>
            <if test="actionIds != null and actionIds != '' ">
                AND action_id in
                <foreach collection="actionIds.split(',')" item="actionId" open="(" separator="," close=")">
                    #{actionId}
                </foreach>
            </if>
            <if test="executorTypeString != null and executorTypeString != '' ">
                AND executorTypeString in
                <foreach collection="executorTypeString.split(',')" item="executorType" open="(" separator="," close=")">
                    #{executorType}
                </foreach>
            </if>
            <if test="isPersonal != null and isPersonal != '' ">
                AND is_personal in
                <foreach collection="isPersonal.split(',')" item="p" open="(" separator="," close=")">
                    #{p}
                </foreach>
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId,jdbcType=BIGINT}
            </if>
            <if test="behaviorStage != 0 and behaviorStage != null">
                AND behavior_stage = #{behaviorStage}
            </if>
        </where>

        <if test="sortType == null or sortType == 0">
            order by action_time_stamp desc
        </if>
        <if test="sortType != null and sortType != 0">
            order by
            <if test="sortType == 1">
                action_time
            </if>
            <if test="sortType == 2">
                executorTypeString
            </if>
            <if test="sortType == 3">
                executor
            </if>
            <if test="sortType == 4">
                action_permission_alias
            </if>
            <if test="sortType == 5">
                is_personal
            </if>
            <if test="sortOrder == 1">
                asc
            </if>
            <if test="sortOrder == 2">
                desc
            </if>
        </if>

    </select>


    <select id="getBehaviorActionNameList" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT (a.task_id),(a.action_id),(b.action_name),(b.action_permission_alias)
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b on a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        AND a.action_id != 28001
        AND b.action_name is not null
        AND b.action_name &lt;&gt; ''
    </select>

    <select id="getBehaviorPermissionList" resultMap="ActionNameAndPermissionVO">
        SELECT DISTINCT (b.action_permission_alias),(a.task_id),(a.action_id),(b.action_name)
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b on a.action_id = b.action_id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND a.behavior_stage = #{behaviorStage}
        </if>
        AND a.action_id != 28001
        AND b.action_permission_alias is not null
        AND b.action_permission_alias &lt;&gt; ''
    </select>

    <select id="getResultByIdAndTaskId" resultMap="TPrivacyActionNougatMap">
        select tpt.id,
               tpt.task_id,
               tpt.action_id,
               ta.action_name
        from t_privacy_action_nougat tpt
        left join t_action_nougat ta on tpt.action_id=ta.action_id
        where tpt.id=#{id,jdbcType=BIGINT}
    </select>
    
    <select id="selectAllActionData" resultMap="TPrivacyActionNougatMap">
    	<if test="terminalType != null and terminalType==1">
	    	SELECT a.task_id,
	        a.action_id,
	        b.action_name,
	        b.action_permission,
	        b.action_permission_alias,
	        b.sensitive,
	        b.is_personal,
            a.package_name,
            a.stack_info,
            a.details_data,
            a.action_time,
            a.action_time_stamp,
            a.executor,
            a.executor_type,
            a.sdk_ids,
            a.number_action,
            a.trigger_cycle_time,
            a.behavior_stage,
            nougat_e.jni_stack_info AS jni_stack_info,
            nougat_e.api_name AS api_name
            FROM t_privacy_action_nougat a
            LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
            LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=a.id
            WHERE a.task_id = #{taskId,jdbcType=BIGINT}
            AND a.action_id != 28001
            AND a.behavior_stage in(1,2,3,5)
            AND a.action_id != 32001
            <if test="isPersonal != null">
                AND b.is_personal = #{isPersonal}
            </if>
			UNION ALL
		</if>   
		<if test="terminalType != null and terminalType==1">
	    	SELECT a.task_id,
	        a.action_id,
	        b.action_name,
	        b.action_permission,
	        b.action_permission_alias,
	        b.sensitive,
	        b.is_personal,
            a.package_name,
            a.stack_info,
            a.details_data,
            a.action_time,
            a.action_time_stamp,
            a.executor,
            a.executor_type,
            a.sdk_ids,
            a.number_action,
            a.trigger_cycle_time,
            a.behavior_stage,
            nougat_e.jni_stack_info AS jni_stack_info,
            nougat_e.api_name AS api_name
            FROM t_privacy_action_nougat a
            LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
            LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=a.id
            WHERE a.task_id = #{taskId,jdbcType=BIGINT}
            AND a.behavior_stage in(1,2,3,4)
            AND a.action_id = 32001

            UNION ALL
        </if>
		SELECT a.task_id,
	        a.action_id,
	        b.action_name,
	        b.action_permission,
	        b.action_permission_alias,
	        b.sensitive,
	        b.is_personal,
        a.package_name,
        a.stack_info,
        a.details_data,
        a.action_time,
        a.action_time_stamp,
        a.executor,
        a.executor_type,
        a.sdk_ids,
        a.number_action,
        a.trigger_cycle_time,
        a.behavior_stage,
        nougat_e.jni_stack_info AS jni_stack_info,
        nougat_e.api_name AS api_name
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=a.id
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001 AND a.action_id != 32001
        <if test="terminalType != null and terminalType==1">
	        	AND a.behavior_stage =4
	       </if>
           <if test="isPersonal != null">
                AND b.is_personal = #{isPersonal}
           </if>
    </select>

    <!--获取操作应用详情页面数据-->
    <select id="selectDataById" resultMap="TPrivacyActionNougatMap">
        SELECT
        a.id,
        a.task_id,
        a.action_id,
        b.action_name,
        b.action_permission_alias,
        b.sensitive,
        a.package_name,
        a.details_data,
        a.action_time,
        a.executor,
        a.stack_info,
        IFNULL(e.jni_stack_info, "--") AS jni_stack_info,
        IFNULL(e.api_name, "--") AS api_name
        FROM t_privacy_action_nougat a
        LEFT JOIN t_privacy_action_nougat_extend e ON e.nougat_id=a.id
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.id=#{id}
        <if test="packageName != null">
            and a.package_name LIKE CONCAT('%', #{packageName,jdbcType=VARCHAR}, '%')
        </if>
        AND a.action_id not in (28001)
    </select>

    <select id="countActionNougatByTaskIdAndStage" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM t_privacy_action_nougat a
        WHERE a.task_id = #{taskId,jdbcType=BIGINT}
        AND a.action_id != 28001
        <if test="behaviorStage != 0 and behaviorStage != null">
            and a.behavior_stage=#{behaviorStage}
        </if>
    </select>
    
    <select id="getActionBehaviorByTaskId" resultMap="TPrivacyActionNougatMap">
        SELECT
            a.id,
            a.task_id,
            a.action_id,
            b.action_name,
            a.package_name,
            a.action_time,
            a.executor,
        a.sdk_ids,
        a.behavior_stage
        FROM t_privacy_action_nougat a
        LEFT JOIN t_action_nougat b ON a.action_id = b.action_id
        WHERE a.task_id=#{taskId}
        AND b.is_personal=1 and executor_type=2
        GROUP BY action_id,package_name
        order by action_id,action_time asc;
    </select>


    <update id="updateCycleTrigger">
        UPDATE t_privacy_action_nougat SET number_action=#{numberAction},trigger_cycle_time=#{triggerCycleTime} WHERE
        id=#{id}
    </update>

</mapper>
