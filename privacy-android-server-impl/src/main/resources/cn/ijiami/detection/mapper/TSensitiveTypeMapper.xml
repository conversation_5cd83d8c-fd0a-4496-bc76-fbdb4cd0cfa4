<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSensitiveTypeMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSensitiveType">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="sensitive_type" property="sensitiveType"
                jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="BaseResultSensitiveMap" type="cn.ijiami.detection.entity.TSensitiveType">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="sensitive_type" property="sensitiveType"
                jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <association property="createUserName" column="create_user_id" select="selectUserName"/>
    </resultMap>

    <resultMap id="sensitiveTypeVO" type="cn.ijiami.detection.VO.SensitiveTypeVO">
        <id property="id" column="id"/>
        <result property="typeName" column="type_name"/>
        <collection property="privacySensitiveWords" column="{typeId=id,taskId=taskId}"
                    select="cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper.findByTypeIdAndTaskId"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            type_name ,
            platform,
            terminal_type,
            sensitive_type ,
            create_user_id,
            update_user_id,
            create_time,
            update_time
    </sql>

    <select id="selectUserName" resultType="string">
        select user_name as createUserName
        from tsys_user
        where user_id = #{create_user_id}
    </select>

    <select id="selectSensitiveTypeList" parameterType="cn.ijiami.detection.entity.TSensitiveType"
            resultMap="BaseResultSensitiveMap">
        select
        id,
        type_name ,
        (select count(type_id) from t_sensitive_word where type_id = a.id) as count,
        platform,
        terminal_type,
        sensitive_type ,
        create_user_id,
        update_user_id,
        create_time,
        update_time
        from t_sensitive_type a
        WHERE 1 = 1
        <if test="typeName != null">
            AND a.type_name like concat('%',#{typeName},'%')
        </if>
        <if test="terminalType !=null">
            AND a.terminal_type = #{terminalType.value}
        </if>
        order by a.id desc
    </select>

    <select id="findByTaskId" resultMap="sensitiveTypeVO">
        select id, type_name, #{taskId} as taskId
        from t_sensitive_type
    </select>
</mapper>