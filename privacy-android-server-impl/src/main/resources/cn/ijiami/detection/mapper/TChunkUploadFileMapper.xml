<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TChunkUploadFileMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TChunkUploadFile" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="file_md5" property="fileMd5" jdbcType="VARCHAR" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="file_bucket_name" property="fileBucketName" jdbcType="VARCHAR" />
        <result column="file_path_prefix" property="filePathPrefix" jdbcType="VARCHAR" />
        <result column="dfs_path" property="dfsPath" jdbcType="VARCHAR" />
        <result column="file_size" property="fileSize" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="chunk_total" property="chunkTotal" jdbcType="INTEGER" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="BaseColumn">
        id,user_id,file_md5,file_name,file_bucket_name,file_path_prefix,dfs_path,file_size,status,chunk_total,update_time,create_time
    </sql>

    <select id="findOneByUserIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_chunk_upload_file
        WHERE user_id=#{userId} AND file_md5=#{fileMd5}
        AND status IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status,jdbcType=INTEGER}
        </foreach>
        ORDER BY id DESC LIMIT 1;
    </select>

    <select id="findByStatusAndTimeout" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_chunk_upload_file WHERE 1=1
        <if test="hour !=null">
            AND update_time &lt; date_sub(NOW(), interval #{hour} hour)
        </if>
        <if test="statusList !=null">
            AND status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <update id="updateFileStatus" >
        UPDATE  t_chunk_upload_file SET status=#{updateStatus,jdbcType=INTEGER} WHERE dfs_path=#{dfsPath,jdbcType=VARCHAR}
        <if test="userId !=null">
            and user_id=#{userId}
        </if>
        AND status IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status,jdbcType=INTEGER}
        </foreach>

    </update>

    <select id="findOneByFileNameOrMd5" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_chunk_upload_file
        WHERE user_id=#{userId}
        <if test="fileMd5 != null">
            AND file_md5=#{fileMd5}
        </if>
        <if test="fileName != null">
            AND file_name=#{fileName}
        </if>
        <if test="statusList !=null">
            AND status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status,jdbcType=INTEGER}
            </foreach>
        </if>
        ORDER BY id DESC LIMIT 1;
    </select>

    <select id="queryAnalysisQueue" resultMap="BaseResultMap">
        SELECT id,file_md5,file_name,file_size,status,message,terminal_type,progress,analysis_result
        FROM t_chunk_upload_file
        WHERE user_id=#{userId}
        <if test="statusList !=null">
            and status IN
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="terminalType !=null">
            AND terminal_type=#{terminalType}
        </if>
    </select>

    <update id="updateFileStatusFrom" >
        UPDATE  t_chunk_upload_file SET status=#{updateStatus} WHERE id=#{id}
        <if test="statusList !=null">
            and status IN
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>