<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSystemUpdateMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TSystemUpdate">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="release_notes" property="releaseNotes" jdbcType="VARCHAR"/>
        <result column="data_path" property="dataPath" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ItemPageResultMap"
               type="cn.ijiami.detection.VO.SystemUpdateItemVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="release_notes" property="releaseNotes" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="DetailPageResultMap"
               type="cn.ijiami.detection.VO.SystemUpdateDetailVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="release_notes" property="releaseNotes" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="BaseColumn">

    </sql>

    <select id="findItemByPage" resultMap="ItemPageResultMap">
        SELECT id, name, version, release_notes, type, update_time
        FROM t_system_update
        WHERE (name, id) IN (
            SELECT name, MIN(id)
            FROM (
                SELECT id, type, name, status
                FROM t_system_update
                WHERE status IN (3, 1) AND is_delete=0
                ORDER BY CASE
                    WHEN status = 3 THEN 1
                    WHEN status = 1 THEN 2
                    ELSE 3
                END
            ) AS subquery
            GROUP BY name,type
        )
        ORDER BY create_time DESC
    </select>


    <select id="findDetailByPage" resultMap="DetailPageResultMap">
        SELECT id,name,version,release_notes,type,update_time,status FROM t_system_update
        WHERE `name`=#{name} AND type=#{type} AND is_delete=0
        ORDER BY create_time DESC
    </select>

    <update id="updateFinishById">
        UPDATE t_system_update SET `status`=#{updateStatus},`previous_version_id`=#{previousVersionId},
        `update_user_id`=#{updateUserId} WHERE `id`=#{id}
    </update>

    <update id="updateStatusById">
        UPDATE t_system_update SET `status`=#{updateStatus},`update_user_id`=#{updateUserId} WHERE `id`=#{id}
    </update>

    <update id="deleteUpdate">
        UPDATE t_system_update SET `is_delete`=1, `update_user_id`=#{updateUserId} WHERE `id`=#{id}
    </update>

    <select id="getNameList" resultType="java.lang.String">
        SELECT name
        FROM t_system_update
        WHERE is_delete=0
        GROUP BY name
    </select>
</mapper>