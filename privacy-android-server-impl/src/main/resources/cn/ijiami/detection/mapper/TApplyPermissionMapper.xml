<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TApplyPermissionMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TApplyPermission" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="apply_name" property="applyName" jdbcType="VARCHAR" />
        <result column="action_permission" property="actionPermission" jdbcType="VARCHAR" />
        <result column="regex" property="regex" jdbcType="VARCHAR" />
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
    </resultMap>

    <select id="findByTerminalType"  resultMap="BaseResultMap">
        SELECT id,apply_name,action_permission,regex,terminal_type FROM t_apply_permission WHERE terminal_type=#{terminalType}
    </select>
</mapper>