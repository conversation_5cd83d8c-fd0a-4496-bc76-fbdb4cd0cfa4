<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TDetectionItemTypeMapper" >
  <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TDetectionItemType" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type_name" property="typeName" jdbcType="VARCHAR" />
    <result column="detection_item_count" property="detectionItemCount" jdbcType="INTEGER" />
    <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="update_user_id" property="updateUserId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
</mapper>