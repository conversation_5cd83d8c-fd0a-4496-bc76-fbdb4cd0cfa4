<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TUserSdkAliasMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TUserSdkAlias" >
        <id column="sdk_id" property="sdkId" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="sdk_alias" property="sdkAlias" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="BaseSdkAliasMap" type="cn.ijiami.detection.VO.UserSdkAliasVO" >
        <id column="sdk_id" property="sdkId" jdbcType="BIGINT" />
        <result column="sdk_name" property="sdkName" jdbcType="VARCHAR" />
        <result column="sdk_alias" property="sdkAlias" jdbcType="VARCHAR" />
    </resultMap>

    <select id="findLawSdkAlias" resultMap="BaseSdkAliasMap">
        SELECT s.id AS sdk_id, s.`name` AS sdk_name, u.sdk_alias AS sdk_alias
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p ON s.id=p.sdk_id
        LEFT JOIN t_user_sdk_alias AS u ON s.id=u.sdk_id
        LEFT JOIN tsys_user_role AS ru ON ru.user_id=u.user_id
        LEFT JOIN tsys_role AS r ON r.role_id=ru.role_id
        WHERE s.first_party_library = 0 AND (u.user_id = #{userId} OR r.role_code IN ('admin', 'manager')) AND u.sdk_alias IS NOT NULL AND u.sdk_alias!=''
        GROUP BY sdk_name, sdk_alias
    </select>

    <select id="findUserSdkAlias" resultMap="BaseSdkAliasMap">
        SELECT MAX(s.id) AS sdk_id, s.`name` AS sdk_name,
        (
            SELECT u.sdk_alias
            FROM t_user_sdk_alias AS u
            WHERE u.sdk_id = MAX(s.id)
            AND u.user_id IN
            <foreach collection="userIdList" item="item"
                    index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            LIMIT 1
        ) AS sdk_alias
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p ON s.id=p.sdk_id
        LEFT JOIN t_user_sdk_alias AS u ON s.id=u.sdk_id AND u.user_id IN
        <foreach collection="userIdList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        WHERE s.first_party_library = 0
        <if test="packageName != null and packageName != ''">
            AND p.`package_name` LIKE concat('%',#{packageName},'%')
        </if>
        <if test="name != null and name != ''">
            AND (s.`name` LIKE concat('%',#{name},'%') OR u.sdk_alias LIKE concat('%',#{name},'%'))
        </if>
        GROUP BY s.`name`
        ORDER BY u.sdk_alias IS NOT NULL AND u.sdk_alias!='' DESC
    </select>
</mapper>