<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkLibraryMapper">

    <resultMap id="BaseSdkInfoMap" type="cn.ijiami.detection.entity.TSdkLibrary">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="alias" property="alias" jdbcType="VARCHAR"/>
        <result column="package_name" property="packageName" jdbcType="VARCHAR"/>
        <result column="sdk_describe" property="describe" jdbcType="VARCHAR"/>
        <result column="sdk_manufacturer" property="manufacturer" jdbcType="VARCHAR"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="created_user_id" property="createdUserId" jdbcType="BIGINT"/>
        <result column="updated_user_id" property="updatedUserId" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result property="sdkMd5" column="sdk_md5"/>
        <result property="url" column="url"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="permissionCodes" column="permission_codes"/>
        <result property="minSdkVersion" column="min_sdk_version"/>
        <result property="targetSdkVersion" column="target_sdk_version"/>
        <result property="compileSdkVersion" column="compile_sdk_version"/>
        <result property="suggestion" column="suggestion"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="data" column="data"/>
        <result property="hotfix" column="hotfix"/>
        <result property="version" column="version"/>
        <result property="groupId" column="group_id"/>
        <result property="artifactId" column="artifact_id"/>
        <result property="newVersionFeature" column="new_version_feature"/>
        <result property="appKey" column="app_key"/>
        <result property="apiUrl" column="api_url"/>
        <result property="host" column="host"/>
        <result property="badLabelId" column="bad_label_id"/>
        <result property="chargeType" column="charge_type"/>
        <result property="source" column="source"/>
        <result property="sdkPath" column="sdk_path"/>
        <result property="websiteUrl" column="website_url"/>
        <result property="privacyUrl" column="privacy_url"/>
        <result property="uniqueNo" column="unique_no"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="sdkManufacturerWebsiteUrl" column="sdk_manufacturer_website_url"/>
        <result property="sdkManufacturerAddress" column="sdk_manufacturer_address"/>
        <result property="firstPartyLibrary" column="first_party_library"/>
        <result property="coordinate" column="coordinate"/>
        <result property="iosHeaderFileName" column="ios_header_file_name"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSdkLibrary" extends="BaseSdkInfoMap">
        <collection property="packageList" ofType="cn.ijiami.detection.entity.TSdkLibraryPackage">
            <id column="p_id" property="id" jdbcType="BIGINT" />
            <result column="p_sdk_id" property="sdkId" jdbcType="BIGINT" />
            <result column="p_package_name" property="packageName" jdbcType="VARCHAR" />
            <result column="p_created_user_id" property="createdUserId" jdbcType="BIGINT"/>
            <result column="p_update_user_id" property="updateUserId" jdbcType="BIGINT"/>
            <result column="p_created_time" property="createdTime" jdbcType="TIMESTAMP"/>
            <result column="p_update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <resultMap id="sdkVO" type="cn.ijiami.detection.VO.detection.SdkVO" extends="BaseResultMap">
        <collection property="permissions" column="{packageName=package_name,taskId=task_id}"
                    select="cn.ijiami.detection.mapper.TPrivacyActionNougatMapper.countSdkPermission"/>
    </resultMap>

    <resultMap id="customSdkMap" type="cn.ijiami.detection.entity.TSdkLibrary" extends="BaseSdkInfoMap">
        <collection property="packageList" column="{sdkId=id}"
                    select="cn.ijiami.detection.mapper.TSdkLibraryPackageMapper.findBySdkId"/>
        <collection property="classFunctionList" column="{sdkId=id}"
                    select="cn.ijiami.detection.mapper.TSdkLibraryClassMapper.findBySdkId"/>
    </resultMap>

    <sql id="sdk_sql">
        s.id,
        s.name,
        s.package_name,
        s.sdk_md5,
        s.url,
        s.data,
        s.min_sdk_version,
        s.target_sdk_version,
        s.compile_sdk_version,
        s.sdk_manufacturer,
        s.sdk_describe,
        s.type_name,
        s.terminal_type,
        s.ip_address,
        s.permission_codes,
        s.risk_level,
        s.hotfix,
        s.suggestion,
        s.created_user_id,
        s.updated_user_id,
        s.created_time,
        s.update_time,
        s.version,
        s.group_id,
        s.artifact_id,
        s.new_version_feature,
        s.app_key,
        s.api_url,
        s.host,
        s.bad_label_id,
        s.source,
        s.sdk_path,
        s.website_url,
        s.privacy_url,
        s.sdk_manufacturer_website_url,
        s.sdk_manufacturer_address,
        s.unique_no,
        s.download_url,
        s.charge_type,
        s.alias,
        s.first_party_library,
        s.coordinate,
        s.ios_header_file_name
    </sql>

    <sql id="package_sql">
        p.id AS p_id,
        p.sdk_id AS p_sdk_id,
        IF(p.package_name IS NULL or p.package_name = '', s.package_name, p.package_name) AS p_package_name,
        p.created_user_id AS p_created_user_id,
        p.update_user_id AS p_update_user_id,
        p.created_time AS p_created_time,
        p.update_time AS p_update_time
    </sql>

    <select id="findByPackageNameAndTaskId" resultMap="sdkVO">
        SELECT
        <include refid="sdk_sql" /> ,
        #{taskId,jdbcType=BIGINT} as task_id,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where p.package_name=#{packagesName,jdbcType=VARCHAR}  ORDER BY s.update_time,s.id DESC LIMIT 1
    </select>

    <select id="findInPackageName" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" /> ,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.first_party_library=0 AND p.package_name IN
        <foreach collection="packagesNameList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findInPackageNameSource" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" /> ,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.first_party_library=0
        <if test="source != null">
            AND s.source=#{source}
        </if>
        <if test="terminalType != null">
            AND s.terminal_type=#{terminalType}
        </if>
        AND p.package_name IN
        <foreach collection="packagesNameList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findInSdkNameSource" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" /> ,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.first_party_library=0
        <if test="source != null">
            AND s.source=#{source}
        </if>
        <if test="terminalType != null">
            AND s.terminal_type=#{terminalType}
        </if>
        AND s.name IN
        <foreach collection="sdkNameList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findByNameAndTaskId" resultMap="sdkVO">
        SELECT
        <include refid="sdk_sql" /> ,
        #{taskId,jdbcType=BIGINT} as task_id,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.id = (SELECT id FROM t_sdk_library WHERE `name`=#{sdkName,jdbcType=VARCHAR} ORDER BY update_time DESC LIMIT 1)
    </select>

    <select id="findByTypeName" resultType="java.lang.Long">
        select id
        from t_sdk_library
        where type_name = #{typeName,jdbcType=VARCHAR} and terminal_type = 1
    </select>
    
    <select id="findTSdkLibraryByPackage" resultMap="BaseResultMap">
        select  <include refid="sdk_sql" />
        from t_sdk_library AS s
        where s.package_name = #{packageName,jdbcType=VARCHAR} and s.terminal_type = 1 limit 1
    </select>

    <select id="findByNameAndVersion" resultMap="BaseResultMap">
        select  <include refid="sdk_sql" />
        from t_sdk_library AS s
        where s.name = #{sdkName,jdbcType=VARCHAR} and s.version = #{version,jdbcType=VARCHAR} limit 1
    </select>

    <update id="updatePermissionCodesById">
        UPDATE t_sdk_library
        SET permission_codes = #{permissionCodes,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <select id="findAll" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.id>0
    </select>

    <select id="findByTerminalType" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.terminal_type=#{terminalType} and s.first_party_library=0
    </select>

    <select id="findFirstPartyByTerminalType" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.terminal_type=#{terminalType} and s.first_party_library=1
    </select>

    <select id="findInSdkName" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" /> ,
        <include refid="package_sql" />
        from t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        WHERE s.name IN
        <foreach collection="sdkNameList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findInMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        from t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p ON s.id = p.sdk_id
        WHERE s.sdk_md5 IN
        <foreach collection="md5List" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    
    <select id="findBaseSdkInPackage" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        from t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p ON s.id = p.sdk_id
        WHERE s.source=1 AND s.package_name IN
        <foreach collection="packageList" item="item"
                 index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findByMd5AndVersion" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        from t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p ON s.id = p.sdk_id
        WHERE s.sdk_md5=#{md5} AND s.version=#{version} LIMIT 1
    </select>

    <select id="findCustomSdkByPage" resultMap="customSdkMap">
        SELECT
        <include refid="sdk_sql" />
        from t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        WHERE s.source in(2,3)
        <if test="query.name != null and query.name != ''">
            AND s.name LIKE CONCAT('%',#{query.name},'%')
        </if>
        <if test="query.sdkPackage != null and query.sdkPackage != ''">
            AND p.package_name LIKE CONCAT('%',#{query.sdkPackage},'%')
        </if>
        <if test="query.terminalType != null">
            AND s.terminal_type=#{query.terminalType}
        </if>
        GROUP BY s.id
        ORDER BY s.created_time DESC
    </select>

    <select id="findHaveAliasByTerminalType" resultMap="BaseResultMap">
        SELECT
        <include refid="sdk_sql" />,
        <include refid="package_sql" />
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.terminal_type=#{terminalType} AND s.alias IS NOT NULL AND s.alias != ''
    </select>

    <select id="countByNameAndVersion" resultType="long">
        SELECT
        COUNT(DISTINCT(s.id))
        FROM t_sdk_library AS s
        LEFT JOIN t_sdk_library_package AS p
        ON s.id=p.sdk_id
        where s.`version`=#{version} AND s.terminal_type=#{terminalType} AND s.`name`=#{sdkName}
        <if test="packagesNameList != null and packagesNameList.size() != 0">
            AND p.package_name IN
            <foreach collection="packagesNameList" item="item"
                     index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    
    <select id="getIosPrivacySdkList" resultType="cn.ijiami.detection.VO.sdk.IosPrivacySdk">
    	SELECT id,sdk_name as sdkName,type from t_ios_privacy_sdk where type=1
    </select>
</mapper>