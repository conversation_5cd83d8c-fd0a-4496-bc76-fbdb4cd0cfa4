<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TUserUseDeviceMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TUserUseDevice" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="device_serial" property="deviceSerial" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="business_id" property="businessId" jdbcType="BIGINT" />
        <result column="preempt_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="applet_status" property="appletStatus" jdbcType="INTEGER" />
    </resultMap>

    <sql id="BaseColumn">
        id,user_id,device_serial,status,terminal_type,business_type,business_id,preempt_time,start_time,end_time,applet_status
    </sql>

    <select id="findUsingCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_user_use_device WHERE user_id=#{userId} AND terminal_type=#{terminalType}
        AND status=1 AND start_time > DATE_SUB(NOW(), INTERVAL '120' MINUTE)
    </select>

    <select id="findPreemptCount" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_user_use_device WHERE user_id=#{userId} AND terminal_type=#{terminalType}
        AND ((status=0 AND preempt_time > #{preemptTimeout})
        OR (status=1 AND start_time > #{startTimeout}))
    </select>

    <select id="findUserDevices" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_user_use_device WHERE user_id=#{userId} AND business_id=#{businessId}
        AND (preempt_time > #{preemptTimeout}
        OR start_time > #{startTimeout}) ORDER BY preempt_time DESC LIMIT 1;
    </select>
    
    <select id="findAppletUseDevicesTimeOut" resultMap="BaseResultMap">
        SELECT
		t1.id,t1.user_id,t1.device_serial,t1.status,t1.terminal_type,t1.business_type,t1.business_id,t1.preempt_time,
        t1.start_time,t1.end_time,t1.applet_status
		FROM
		t_user_use_device t1
		INNER JOIN ( SELECT MAX( id ) AS id FROM t_user_use_device where terminal_type=#{terminalType}
        AND applet_status IN
        <foreach collection="appletStatusList" item="appletStatus" open="(" separator="," close=")">
            #{appletStatus}
        </foreach>
        GROUP BY device_serial) t2 ON t1.id = t2.id
		where t1.end_time &lt; DATE_SUB(NOW(), INTERVAL '60' MINUTE) AND t1.end_time > DATE_SUB(NOW(), INTERVAL '7' DAY)
        
    </select>
    
    <select id="findAppletUseDevices" resultMap="BaseResultMap">
        SELECT * from ( SELECT id,user_id,device_serial,status,terminal_type,business_type,business_id,preempt_time,
		 (case when end_time is null then start_time else end_time END) as start_time ,
		 end_time,applet_status FROM t_user_use_device
        WHERE terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
        AND applet_status IN
        <foreach collection="appletStatusList" item="appletStatus" open="(" separator="," close=")">
            #{appletStatus}
        </foreach>)a  where start_time > DATE_SUB(NOW(), INTERVAL '60' MINUTE) <!-- 达梦数据库必须要加单引号 #{interval} -->
    </select>
    
    <select id="findAppletUseDevicesByUserId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_user_use_device WHERE terminal_type=#{terminalType} AND 
        applet_status IN
        <foreach collection="appletStatusList" item="appletStatus" open="(" separator="," close=")">
            #{appletStatus}
        </foreach>
        AND user_id=#{userId} AND start_time > DATE_SUB(NOW(), INTERVAL '60' MINUTE) order by id desc limit 1
    </select>


    <select id="findLastUseDeviceByDeviceSerial" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_user_use_device WHERE device_serial=#{deviceSerial}
        AND business_id=#{businessId}
        AND terminal_type=#{terminalType} order by id desc limit 1
    </select>
    
</mapper>