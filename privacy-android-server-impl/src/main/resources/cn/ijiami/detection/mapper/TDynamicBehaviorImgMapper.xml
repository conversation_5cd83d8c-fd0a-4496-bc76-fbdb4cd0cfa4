<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TDynamicBehaviorImgMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TDynamicBehaviorImg">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="file_key" property="fileKey"/>
        <result column="action_id" property="actionId"/>
        <result column="business_id" property="businessId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remarks" property="remarks"/>
        <result column="picture_time_stamp" property="pictureTimeStamp"/>
        <result column="business_type" property="businessType"/>
    </resultMap>

    <sql id="Base_Column_SQL">
        id,
        task_id,
        file_key,
        action_id,
        business_id,
        create_time,
        update_time,
        remarks,
        max(picture_time_stamp) picture_time_stamp,
        business_type
    </sql>

    <!-- 只获取每条行为最新的截图，展示一条 -->
    <select id="findByActionNougatId" resultMap="BaseResultMap">
        select <include refid="Base_Column_SQL"/>
        from t_dynamic_behavior_img
        where
            <if test="taskId!=null and taskId!=0 ">
                task_id=#{taskId}
            </if>
            <if test="actionId!=null and actionId!=0 ">
                and action_id=#{actionId}
            </if>
            <if test="businessId!=null and businessId!=0 ">
                and business_id=#{businessId}
            </if>
            <if test="businessType!=null and businessType!=0 ">
                and business_type=#{businessType}
            </if>
        group by task_id,business_id
    </select>
</mapper>