<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TStaticFunctionsMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TStaticFunctions">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="action_id" property="actionId" jdbcType="BIGINT"/>
        <result column="function" property="function" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="fragment" property="fragment" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="executor_type" property="executorType" jdbcType="INTEGER"/>
        <result column="executor" property="executor" jdbcType="VARCHAR"/>
        <result column="package_name" property="packageName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="BehaviorMap"
               type="cn.ijiami.detection.VO.StaticFunctionBehaviorVO">
        <!-- WARNING - @mbg.generated -->
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="action_id" property="actionId" jdbcType="BIGINT"/>
        <result column="action_name" property="actionName" jdbcType="BIGINT"/>
        <result column="function_count" property="functionCount" jdbcType="INTEGER"/>
        <result column="is_privacy" property="isPrivacy" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="DetailMap"
               type="cn.ijiami.detection.VO.StaticFunctionDetailVO">
        <!-- WARNING - @mbg.generated -->
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="action_id" property="actionId" jdbcType="BIGINT"/>
        <result column="function" property="function" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="fragment" property="fragment" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        `task_id`,
        `action_id`,
        `function`,
        `location`,
        `fragment`,
        `terminal_type`,
        `executor_type`,
        `executor`,
        `package_name`,
        `create_time`,
        `update_time`
    </sql>

    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_static_functions
        WHERE task_id = #{taskId,jdbcType=BIGINT}
    </select>

    <select id="findBehaviorByTaskId" resultMap="BehaviorMap">
        SELECT
        s.task_id,
        s.action_id,
        a.action_name,
        COUNT(s.id) AS function_count,
        IFNULL(a.is_personal, 0) AS is_privacy
        FROM t_static_functions AS s
        LEFT JOIN t_action_nougat AS a
        ON s.action_id=a.action_id
        WHERE s.task_id = #{taskId,jdbcType=BIGINT}
        AND a.is_personal IN
        <foreach collection="privacyList" item="privacy" open="(" separator="," close=")">
            #{privacy,jdbcType=INTEGER}
        </foreach>
        GROUP BY s.action_id
    </select>

    <select id="findDetailByTaskIdAndActionId" resultMap="DetailMap">
        SELECT
        `task_id`,
        `action_id`,
        `function`,
        `location`,
        `fragment`
        FROM t_static_functions
        WHERE task_id = #{taskId,jdbcType=BIGINT} AND action_id=#{actionId,jdbcType=BIGINT}
    </select>

</mapper>