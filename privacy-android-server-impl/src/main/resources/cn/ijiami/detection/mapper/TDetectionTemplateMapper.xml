<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="cn.ijiami.detection.mapper.TDetectionTemplateMapper">
	<resultMap id="BaseResultMap"
		type="cn.ijiami.detection.entity.TDetectionTemplate">
		<!-- WARNING - @mbg.generated -->
		<id column="template_id" property="templateId" jdbcType="BIGINT" />
		<result column="template_name" property="templateName"
			jdbcType="VARCHAR" />
		<result column="template_type" property="templateType"
			jdbcType="INTEGER" />
		<result column="detection_item_count"
			property="detectionItemCount" jdbcType="BIGINT" />
		<result column="use_count" property="useCount"
			jdbcType="BIGINT" />
		<result column="terminal_type" property="terminalType"
			jdbcType="INTEGER" />
		<result column="sensitive_type" property="sensitiveType"
			jdbcType="INTEGER" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="platform" property="platform"
			jdbcType="VARCHAR" />
		<result column="create_user_id" property="createUserId"
			jdbcType="BIGINT" />
		<result column="update_user_id" property="updateUserId"
			jdbcType="BIGINT" />
		<result column="create_time" property="createTime"
			jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime"
			jdbcType="TIMESTAMP" />
	</resultMap>

	<resultMap id="BaseResultMap1"
		type="cn.ijiami.detection.VO.DetectionTemplateTypeVO">
		<!-- WARNING - @mbg.generated -->
		<id column="template_id" property="templateId" jdbcType="BIGINT" />
		<result column="template_name" property="templateName"
			jdbcType="VARCHAR" />
		<result column="template_type" property="templateType"
			jdbcType="INTEGER" />
		<result column="detection_item_count"
			property="detectionItemCount" jdbcType="BIGINT" />
		<result column="use_count" property="useCount"
			jdbcType="BIGINT" />
		<result column="terminal_type" property="terminalType"
			jdbcType="INTEGER" />
		<result column="sensitive_type" property="sensitiveType"
			jdbcType="INTEGER" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="platform" property="platform"
			jdbcType="VARCHAR" />
		<result column="create_user_id" property="createUserId"
			jdbcType="BIGINT" />
		<result column="update_user_id" property="updateUserId"
			jdbcType="BIGINT" />
		<result column="create_time" property="createTime"
			jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime"
			jdbcType="TIMESTAMP" />
		<result column="user_name" property="userName"
			jdbcType="VARCHAR" />
	</resultMap>

	<sql id="Base_Column_List">
		tem.`template_id`, tem.`template_name`,
		tem.`template_type`, tem.`detection_item_count`,
		tem.`use_count`,
		tem.`terminal_type`, tem.`remark`, tem.`platform`,
		tem.`create_user_id`,
		tem.`update_user_id`, tem.`create_time`,
		tem.`update_time`
	</sql>
	<sql id="Base_Column_List1">
		tem.`template_id`, tem.`template_name`,
		tem.`template_type`, tem.`detection_item_count`,
		tem.`use_count`,
		tem.`terminal_type`, tem.`remark`, tem.`platform`,
		tem.`create_user_id`,
		tem.`update_user_id`, tem.`create_time`,
		tem.`update_time`,user.`user_name`
	</sql>
	<select id="selectDetectionTemplateList"
		parameterType="cn.ijiami.detection.query.DetectionTemplateQuery"
		resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM t_detection_template as tem WHERE 1=1
		<if test="terminalType != null">
			and tem.terminal_type = #{terminalType.value}
		</if>
		<if test="createUserId != null">
			and (tem.sensitive_type =1
			or tem.create_user_id = #{createUserId})
		</if>
		<if test="templateName != null">
			and tem.template_name = #{templateName}
		</if>
		<if test="countSortEnum !=null and countSortEnum.getValue()==1">
			ORDER BY tem.use_count DESC
		</if>
		<if test="countSortEnum !=null and countSortEnum.getValue()==2">
			ORDER BY tem.use_count ASC
		</if>
		order by tem.create_time desc
	</select>
	<select id="selectDetectionTemplateList1"
		parameterType="cn.ijiami.detection.query.DetectionTemplateQuery"
		resultMap="BaseResultMap1">
		SELECT
		<include refid="Base_Column_List1" />
		FROM t_detection_template as tem LEFT JOIN tsys_user as user ON
		user.user_id = tem.create_user_id WHERE 1=1
		<if test="terminalType != null">
			and tem.terminal_type = #{terminalType.value}
		</if>
		<if test="createUserId != null">
			and (tem.sensitive_type =1 or tem.create_user_id =
			#{createUserId})
		</if>
		<if test="templateName != null and templateName != ''">
			and tem.template_name = #{templateName}
		</if>
		<if test="countSortEnum !=null and countSortEnum.getValue()==1">
			ORDER BY tem.use_count DESC
		</if>
		<if test="countSortEnum !=null and countSortEnum.getValue()==2">
			ORDER BY tem.use_count ASC
		</if>
		order by tem.create_time desc
	</select>
</mapper>