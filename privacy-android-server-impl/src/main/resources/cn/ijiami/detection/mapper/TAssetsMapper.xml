<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TAssetsMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TAssets">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="pakage" property="pakage" jdbcType="VARCHAR"/>
        <result column="MD5" property="md5" jdbcType="VARCHAR"/>
        <result column="sign_md5" property="signMd5" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="BIGINT"/>
        <result column="size" property="size" jdbcType="VARCHAR"/>
        <result column="detection_count" property="detectionCount"
                jdbcType="BIGINT"/>
        <result column="detection_score" property="detectionScore"
                jdbcType="VARCHAR"/>
        <result column="risk_level" property="riskLevel"
                jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="logo" property="logo" jdbcType="LONGVARCHAR"/>
        <result property="lastDetectionTime" column="last_detection_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="shellIpaPath" column="shell_ipa_path"/>
        <result property="appId" column="app_id"/>
        <result property="isHavePacker" column="is_have_packer"/>
        <result property="dumpZipUrl" column="dump_zip_url"/>
        <result property="sourceFileName" column="source_file_name"/>
        <result property="thirdPartyShareListPath" column="third_party_share_list_path"/>
        <result property="permissions" column="permissions"/>
        <result property="minSdkVersion" column="min_sdk_version"/>
        <result property="targetSdkVersion" column="target_sdk_version"/>
        <result property="encryptCompany" column="encrypt_company"/>
        <result property="signature" column="signature"/>
    </resultMap>
    <resultMap id="BaseResultMapOne"
               type="cn.ijiami.detection.VO.AssetsListVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="pakage" property="pakage" jdbcType="VARCHAR"/>
        <result column="MD5" property="md5" jdbcType="VARCHAR"/>
        <result column="sign_md5" property="signMd5" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="BIGINT"/>
        <result column="size" property="size" jdbcType="VARCHAR"/>
        <result column="detection_count" property="detectionCount"
                jdbcType="BIGINT"/>
        <result column="detection_score" property="detectionScore"
                jdbcType="VARCHAR"/>
        <result column="risk_level" property="riskLevel"
                jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="logo" property="logo" jdbcType="LONGVARCHAR"/>
        <result column="real_name" property="userName"
                jdbcType="VARCHAR"/>
        <result property="lastDetectionTime" column="last_detection_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="shellIpaPath" column="shell_ipa_path"/>
        <result property="appId" column="app_id"/>
        <result property="isHavePacker" column="is_have_packer"/>
        <result property="dumpZipUrl" column="dump_zip_url"/>
        <result property="sourceFileName" column="source_file_name"/>
        <result property="assetsFunctionType" column="assets_function_type"/>
        <result property="privacyPolicyPath" column="privacy_policy_path"/>
        <result property="thirdPartyShareListPath" column="third_party_share_list_path"/>
        <result property="permissions" column="permissions"/>
        <result property="minSdkVersion" column="min_sdk_version"/>
        <result property="targetSdkVersion" column="target_sdk_version"/>
        <result property="encryptCompany" column="encrypt_company"/>
        <result property="signature" column="signature"/>
        <result property="assetsFunctionType" column="assets_function_type"/>
        <result property="obbDataPath" column="obb_data_path"/>
        <result property="obbDevicePath" column="obb_device_path"/>
        <result property="qrcodePath" column="qrcode_path"/>
        <result property="shareUrl" column="share_url"/>
    </resultMap>
    <resultMap id="detectionStatistics" type="cn.ijiami.detection.entity.AssetsDetectionStatistics">
        <result column="id" property="assetsId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="detection_count" property="detectionCount" jdbcType="INTEGER"/>
        <result column="fast_detection_count" property="fastDetectionCount" jdbcType="INTEGER"/>
        <result column="deep_detection_count" property="deepDetectionCount" jdbcType="INTEGER"/>
        <result column="fast_detection_completions" property="fastDetectionCompletions" jdbcType="INTEGER"/>
        <result column="deep_detection_completions" property="deepDetectionCompletions" jdbcType="INTEGER"/>
        <result column="fast_detection_failures" property="fastDetectionFailures" jdbcType="INTEGER"/>
        <result column="deep_detection_failures" property="deepDetectionFailures" jdbcType="INTEGER"/>
        <result column="last_detection_time" property="lastDetectionTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap id="assetsTask" type="cn.ijiami.detection.VO.statistics.AssetsTask">
        <!-- WARNING - @mbg.generated -->
        <result column="name" property="assetsName" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="pakage" property="packageName" jdbcType="VARCHAR"/>
        <result column="size" property="assetsSize" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName"
                jdbcType="BIGINT"/>
        <result column="assets_create_time" property="assetsCreateTime"
                jdbcType="TIMESTAMP"/>
        <result column="detection_time" property="detectionTime"
                jdbcType="TIMESTAMP"/>
        <result column="task_tatus" property="staticStatus"/>
        <result column="dynamic_law_status" property="lawStatus"/>
        <result column="dynamic_status" property="dynamicStatus"/>
        <result column="detection_type" property="detectionType"/>
    </resultMap>
    <!-- 资产信息sql -->
    <sql id="base_colunm_sql">
        assets.id,
        assets.name,
        assets.version,
        assets.pakage,
        assets.MD5,
        assets.sign_md5,
        assets.address,
        assets.category,
            assets.size,
            assets.detection_count,
            assets.detection_score,
            assets.risk_level,
            assets.last_detection_time,
            assets.terminal_type,
            assets.platform,
            assets.create_user_id,
            assets.update_user_id,
            assets.create_time,
            assets.update_time,
            assets.logo,
            assets.shell_ipa_path,
            assets.dump_zip_url,
            assets.privacy_policy_path,
            assets.app_id,
            assets.third_party_share_list_path,
            assets.permissions,
            assets.min_sdk_version,
            assets.target_sdk_version,
            assets.encrypt_company,
            assets.signature,
        assets.assets_function_type,
        assets.obb_data_path,
        assets.obb_device_path,
        assets.qrcode_path,
        assets.share_url,
        user.real_name
    </sql>

    <!-- 资产数据统计返回值 -->
    <resultMap type="cn.ijiami.detection.entity.TAssetsCount" id="assetsCount">
        <result column="android_size" property="androidSize"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="all_size" property="allSize"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="ios_size" property="iosSize"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="wechat_applet_size" property="wechatAppletSize"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="alipay_applet_size" property="alipayAppletSize"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="harmony_size" property="harmonySize"
                jdbcType="INTEGER" javaType="INTEGER"/>
    </resultMap>

    <resultMap id="assetsDaily" type="cn.ijiami.detection.VO.statistics.HomePageDailyStatistics">
        <!-- WARNING - @mbg.generated -->
        <result column="detectionTotalCount" property="detectionTotalCount" jdbcType="INTEGER"/>
        <result column="fastDetectionCount" property="fastDetectionCount" jdbcType="INTEGER"/>
        <result column="deepDetectionCount" property="deepDetectionCount" jdbcType="INTEGER"/>
        <result column="nonComplianceTotalCount" property="nonComplianceTotalCount" jdbcType="INTEGER"/>
        <result column="detectionDate" property="detectionDate" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="assetsTerminalTypeCount"
            parameterType="cn.ijiami.detection.entity.TAssets"
            resultMap="assetsCount">
        SELECT
        sum( case
        when assets.terminal_type = 1 then 1
        else 0
        end
        ) as
        android_size,
        sum( case
        when assets.terminal_type = 2 then 1
        else 0
        end
        ) as ios_size,
        sum( case
        when assets.terminal_type = 4 then 1
        else 0
        end
        ) as
        wechat_applet_size,
        sum( case
        when assets.terminal_type = 5 then 1
        else 0
        end
        ) as
        alipay_applet_size,
        sum( case
        when assets.terminal_type = 6 then 1
        else 0
        end
        ) as
        harmony_size,
        count(assets.id) as all_size,
        assets._user_id
        FROM
        (select
        assets.id,
        assets.terminal_type,
        assets.`md5`,
        assets.create_user_id AS _user_id
        from t_assets as assets left join tsys_user as user on user.user_id = assets.create_user_id where
        assets.is_delete = 0
        <if test="name != null and name != ''">
            and (assets.name like concat('%',#{name},'%')
            or user.real_name like concat('%',#{name},'%')
            or assets.md5 like concat('%',#{name},'%'))
        </if>
        GROUP BY assets.create_user_id,assets.`md5`) AS assets GROUP BY assets._user_id
    </select>

    <!-- 资产列表 -->
    <select id="findAssetsList"
            parameterType="cn.ijiami.detection.entity.TAssets"
            resultMap="BaseResultMapOne">
        SELECT
        a.id,
        a.name,
        a.version,
        a.pakage,
        a.MD5,
        a.sign_md5,
        a.address,
        a.category,
        a.size,
        SUM(a.detection_count) AS detection_count,
        a.detection_score,
        a.risk_level,
        a.last_detection_time,
        a.terminal_type,
        a.platform,
        a.create_user_id,
        a.update_user_id,
        a.create_time,
        a.update_time,
        a.logo,
        a.shell_ipa_path,
        a.dump_zip_url,
        a.real_name,
        a.privacy_policy_path,
        a.app_id,
        a.third_party_share_list_path,
        a.permissions,
        a.min_sdk_version,
        a.target_sdk_version,
        a.encrypt_company,
        a.signature,
        a.create_user_id as _user_id,
        a.assets_function_type,
        a.obb_data_path,
        a.obb_device_path,
        a.qrcode_path,
        a.share_url
        FROM (SELECT
        <include refid="base_colunm_sql"/>
        FROM t_assets AS assets LEFT JOIN tsys_user AS user ON user.user_id
        = assets.create_user_id WHERE assets.is_delete = 0
        <if test="terminalType != null">
            AND assets.terminal_type = #{terminalType}
        </if>
        <if test="name != null">
            AND (assets.name LIKE concat('%',#{name},'%')
            OR user.real_name LIKE concat('%',#{name},'%')
            OR assets.md5 LIKE concat('%',#{name},'%'))
        </if>
         <if test="createUserId != null">
            and assets.create_user_id = #{createUserId}
        </if>
        ORDER BY assets.create_time DESC) AS a
        GROUP BY a.create_user_id,a.`md5` ORDER BY a.update_time DESC
    </select>

    <select id="findAssetsList1" parameterType="map" resultType="map">

        select
        assets.id,
        assets.name,
        assets.version,
        assets.size,
        assets.detection_count detectionCount,
        assets.detection_score detectionScore,
        assets.last_detection_time,
        assets.risk_level riskLevel,
        assets.terminal_type terminalType,
        assets.pakage,
        DATE_FORMAT(assets.create_time,'%Y-%m-%d %H:%i:%s') createTime,
        assets.platform
        from
        t_assets as assets left join tsys_user as user
        on user.user_id = assets.create_user_id
        where assets.is_delete = 0
        <if test="assertId != null">
            and assets.id = #{assertId}
        </if>
        <if test="terminalType != null">
            and assets.terminal_type = #{terminalType}
        </if>
        <if test="name != null">
            and assets.name like concat('%',#{name},'%')
        </if>
        <if test="createUserId != null">
            and assets.create_user_id = #{createUserId}
        </if>
        order by assets.create_time desc

    </select>

    <select id="selectAssetByTaskId" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.name,
            t.version,
            t.pakage,
            t.MD5,
            t.terminal_type,
            t.app_id,
            t.is_have_packer,
            t.shell_ipa_path,
            t.size
        FROM t_assets AS t WHERE t.id = (SELECT assets_id FROM t_task WHERE task_id = #{taskId,jdbcType=BIGINT})
    </select>

    <select id="selectAssetByDocumentId" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.size
        FROM t_assets AS t WHERE t.id = (SELECT assets_id FROM t_task WHERE apk_detection_detail_id = #{documentId})
    </select>

    <select id="findOneByMd5" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.address,
        t.privacy_policy_path,
        t.third_party_share_list_path,
        t.permissions,
        t.min_sdk_version,
        t.target_sdk_version,
        t.encrypt_company,
        t.signature
        FROM t_assets AS t
        where
        t.MD5 = #{md5}
        and t.is_delete = 0
        ORDER BY t.id ASC
        LIMIT 1
    </select>

    <select id="findOneByUserAndMd5" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.name,
            t.version,
            t.pakage,
            t.MD5,
            t.terminal_type,
            t.app_id,
            t.is_have_packer,
            t.shell_ipa_path,
            t.address,
            t.privacy_policy_path,
            t.third_party_share_list_path,
            t.permissions,
            t.min_sdk_version,
            t.target_sdk_version,
            t.encrypt_company,
            t.signature
        FROM t_assets AS t
        where
        t.create_user_id = #{userId}
        and t.MD5 = #{md5}
        and t.is_delete = 0
        ORDER BY t.id ASC
        LIMIT 1
    </select>

    <select id="findOneByUserAndName" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.address,
        t.privacy_policy_path,
        t.third_party_share_list_path
        FROM t_assets AS t
        where
        t.create_user_id = #{userId}
        and t.name = #{name}
        and t.terminal_type = #{terminalType}
        and t.is_delete = 0
        ORDER BY t.id ASC
        LIMIT 1
    </select>

    <select id="findOneByUserAndAppId" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.address,
        t.privacy_policy_path,
        t.third_party_share_list_path
        FROM t_assets AS t
        where
        t.create_user_id = #{userId}
        and t.app_id = #{appId}
        and t.terminal_type = #{terminalType}
        and t.is_delete = 0
        ORDER BY t.id ASC
        LIMIT 1
    </select>

    <select id="findByUserAndMd5" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.address,
        t.privacy_policy_path,
        t.third_party_share_list_path,
        t.permissions,
        t.min_sdk_version,
        t.target_sdk_version,
        t.encrypt_company,
        t.signature
        FROM t_assets AS t
        where t.MD5 = #{md5} and t.is_delete = 0
        <if test="userId != null">
            and t.create_user_id = #{userId}
        </if>

    </select>

    <update id="updateDumpZipUrlById">
        update t_assets set dump_zip_url=#{dumpZipUrl},is_have_packer = #{isHavePacker} where id = #{id}
    </update>

    <select id="findAssetsListTest"
            parameterType="cn.ijiami.detection.entity.TAssets"
            resultMap="BaseResultMapOne">
        select
        <include refid="base_colunm_sql"/>
        from t_assets as assets left join tsys_user as user on user.user_id
        = assets.create_user_id where assets.is_delete = 0
        <![CDATA[and assets.terminal_type = #{terminalType} and assets.id<#{startId}
        order by assets.create_time desc limit #{num}]]>
    </select>

    <select id="getAssetByTaskId" resultMap="BaseResultMapOne">
        select
            a.id,
            a.name,
            a.version,
            a.pakage,
            a.MD5,
            a.sign_md5,
            a.address,
            a.category,
            a.size,
            a.detection_count,
            a.detection_score,
            a.risk_level,
            a.last_detection_time,
            a.terminal_type,
            a.platform,
            a.create_user_id,
            a.update_user_id,
            a.create_time,
            a.update_time,
            a.logo,
            a.shell_ipa_path,
            a.dump_zip_url,
            a.source_file_name,
            a.assets_function_type,
            a.app_id,
            a.privacy_policy_path,
            a.third_party_share_list_path,
            a.permissions,
            a.min_sdk_version,
            a.target_sdk_version,
            a.encrypt_company,
            a.signature
         from t_assets a left join t_task b on a.id=b.assets_id  where b.task_id=#{taskId,jdbcType=BIGINT} and a.is_delete=0
    </select>

    <select id="getAssetIncludeDeletedByTaskId" resultMap="BaseResultMapOne">
        select
        a.id,
        a.name,
        a.version,
        a.pakage,
        a.MD5,
        a.sign_md5,
        a.address,
        a.category,
        a.size,
        a.detection_count,
        a.detection_score,
        a.risk_level,
        a.last_detection_time,
        a.terminal_type,
        a.platform,
        a.create_user_id,
        a.update_user_id,
        a.create_time,
        a.update_time,
        a.logo,
        a.shell_ipa_path,
        a.dump_zip_url,
        a.source_file_name,
        a.assets_function_type,
        a.privacy_policy_path,
        a.third_party_share_list_path,
        a.permissions,
        a.min_sdk_version,
        a.target_sdk_version,
        a.encrypt_company,
        a.signature
        from t_assets a left join t_task b on a.id=b.assets_id  where b.task_id=#{taskId,jdbcType=BIGINT}
    </select>

    <update id="updateFunctionTypeById">
        update t_assets set assets_function_type=#{functionType} where id=#{id}
    </update>

    <select id="detectionCountRank" resultMap="BaseResultMap">
        SELECT
        a.id,
        a.`name`,
        MAX(r.`create_time`) AS `last_detection_time`,
        a.`version`,
        COUNT(r.`task_id`) AS `detection_count`
        FROM t_task AS r
        INNER JOIN t_assets AS a ON r.assets_id=a.id
        WHERE a.`is_delete`=0 AND a.`terminal_type`=#{terminalType} AND r.`create_user_id` = #{userId}
        <if test="startDate != null">
            AND r.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND r.`task_starttime` &lt; #{endDate}
        </if>
        <if test="detectionType != null">
            AND r.detection_type = #{detectionType}
        </if>
        <if test="assetsName != null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName},'%')
        </if>
        <if test="detectionStatus != null">
            <if test="detectionStatus == 1">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6 AND (r.`dynamic_law_status`= 4 OR r.`dynamic_law_status`= 1)
            </if>
            <if test="detectionStatus == 2">
                AND r.`task_tatus` = 3 AND r.`dynamic_status` = 5 AND (r.`dynamic_law_status`= 3 OR r.`dynamic_law_status`= 1)
            </if>
            <if test="detectionStatus == 3">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` != 6 AND r.`dynamic_law_status`!= 4
            </if>
            <if test="detectionStatus == 4">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6
            </if>
        </if>
        GROUP BY a.`name`, a.`version`
        ORDER BY `detection_count` DESC
        LIMIT #{limit}
    </select>

    <select id="assetsDetectionCountRank" resultMap="BaseResultMap">
        SELECT
        a.`name`
        FROM t_task AS r
        INNER JOIN t_assets AS a ON r.assets_id=a.id
        WHERE a.`is_delete`=0 AND a.`terminal_type`=#{terminalType} AND r.`create_user_id` = #{userId}
        <if test="assetsName != null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName},'%')
        </if>
        <if test="startDate != null">
            AND r.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND r.`task_starttime` &lt; #{endDate}
        </if>
        <if test="detectionType != null">
            AND r.detection_type = #{detectionType}
        </if>
        <if test="detectionStatus != null">
            <if test="detectionStatus == 1">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6 AND (r.`dynamic_law_status`= 4 OR r.`dynamic_law_status`= 1)
            </if>
            <if test="detectionStatus == 2">
                AND r.`task_tatus` = 3 AND r.`dynamic_status` = 5 AND (r.`dynamic_law_status`= 3 OR r.`dynamic_law_status`= 1)
            </if>
            <if test="detectionStatus == 3">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` != 6 AND r.`dynamic_law_status`!= 4
            </if>
            <if test="detectionStatus == 4">
                AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6
            </if>
        </if>
        GROUP BY a.`name`
        ORDER BY MAX(a.`create_time`) DESC
    </select>

    <select id="assetsTask" resultMap="assetsTask">
        SELECT
        a.`name`,
        a.`version`,
        a.`pakage`,
        a.`size`,
        u.`user_name` AS `create_user_name`,
        a.`create_time` AS `assets_create_time`,
        r.`task_starttime` AS `detection_time`,
        r.`task_tatus`,
        r.`dynamic_status`,
        r.`dynamic_law_status`,
        r.`detection_type`
        FROM t_task AS r
        INNER JOIN t_assets AS a ON r.assets_id=a.id
        LEFT JOIN tsys_user AS u ON u.user_id=r.create_user_id
        WHERE a.`is_delete`=0 AND a.`terminal_type`=#{terminalType} AND r.`create_user_id` = #{userId}
        <if test="assetsName != null and assetsName != ''">
            AND a.`name` LIKE CONCAT('%',#{assetsName},'%')
        </if>
        <if test="startDate != null">
            AND r.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND r.`task_starttime` &lt; #{endDate}
        </if>
        <if test="detectionType != null">
            AND r.`detection_type` = #{detectionType}
        </if>
        ORDER BY a.`create_time` DESC
    </select>

    <select id="findSimpleByName" resultMap="detectionStatistics">
        SELECT
        a.name,
        a.version,
        a.id,
        COUNT(r.task_id) AS detection_count,
        COUNT(IF(r.detection_type=1, 1, NULL)) AS fast_detection_count,
        COUNT(IF(r.detection_type=2, 1, NULL)) AS deep_detection_count,
        COUNT(IF(r.detection_type=1 AND r.dynamic_status=6, 1, NULL)) AS fast_detection_completions,
        COUNT(IF(r.detection_type=2 AND r.dynamic_status=6, 1, NULL)) AS deep_detection_completions,
        COUNT(IF(r.detection_type=1 AND r.dynamic_status=5, 1, NULL)) AS fast_detection_failures,
        COUNT(IF(r.detection_type=2 AND r.dynamic_status=5, 1, NULL)) AS deep_detection_failures,
        a.last_detection_time
        FROM t_assets a
        INNER JOIN (
            SELECT t1.*
            FROM t_task t1
            INNER JOIN (
                SELECT a.name, a.version, MAX(t.task_id) AS max_task_id
                FROM t_task t
                INNER JOIN t_assets a ON t.assets_id = a.id
                WHERE a.is_delete = 0 AND a.`terminal_type`=#{terminalType} AND t.`create_user_id` = #{userId}
                <if test="startDate != null">
                    AND r.`task_starttime` &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    AND r.`task_starttime` &lt; #{endDate}
                </if>
                <if test="detectionType != null">
                    AND r.detection_type = #{detectionType}
                </if>
                <if test="detectionStatus != null">
                    <if test="detectionStatus == 1">
                        AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6 AND (r.`dynamic_law_status`= 4 OR r.`dynamic_law_status`= 1)
                    </if>
                    <if test="detectionStatus == 2">
                        AND r.`task_tatus` = 3 AND r.`dynamic_status` = 5 AND (r.`dynamic_law_status`= 3 OR r.`dynamic_law_status`= 1)
                    </if>
                    <if test="detectionStatus == 3">
                        AND r.`task_tatus` = 4 AND r.`dynamic_status` != 6 AND r.`dynamic_law_status`!= 4
                    </if>
                    <if test="detectionStatus == 4">
                        AND r.`task_tatus` = 4 AND r.`dynamic_status` = 6
                    </if>
                </if>
                AND a.`name` IN
                <foreach collection="nameList" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
                GROUP BY a.name, a.version
            ) t2 ON t1.task_id = t2.max_task_id
        ) r ON a.id = r.assets_id
        GROUP BY a.name, a.version
    </select>

    <select id="findSameAssetsIdById" resultType="java.lang.Long">
        SELECT a.`id` FROM t_assets AS a
        INNER JOIN (SELECT `name`, `version`, `terminal_type` FROM t_assets WHERE id=#{id}) AS b
        ON a.`name`=b.`name` AND a.`version`=b.`version` AND a.`terminal_type`=b.`terminal_type`
    </select>

    <select id="selectByDate" resultMap="BaseResultMap">
        select id,address,shell_ipa_path,dump_zip_url,privacy_policy_path,third_party_share_list_path,MD5,create_user_id from t_assets where is_delete =0
        <if test="startDate != null">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            and create_time &lt;= #{endDate}
        </if>
    </select>

    <select id="assetsDetectionCount" resultType="java.lang.Integer">
        SELECT
        COUNT(a.`id`)
        FROM t_task AS r
        INNER JOIN t_assets AS a ON r.assets_id=a.id
        WHERE a.`is_delete`=0 AND r.dynamic_status=6 AND a.`terminal_type`=#{terminalType} AND r.`create_user_id` = #{userId}
        <if test="packageNameList != null and packageNameList.size() > 0">
            AND a.`pakage` IN
            <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                #{packageName}
            </foreach>
        </if>
        <if test="startDate != null">
            AND r.`task_starttime` &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND r.`task_starttime` &lt; #{endDate}
        </if>
        <if test="detectionType != null">
            AND r.detection_type = #{detectionType}
        </if>
    </select>

    <select id="findByMd5AndPackage" resultMap="BaseResultMap">
        SELECT
        t.id,
        t.name,
        t.version,
        t.pakage,
        t.MD5,
        t.terminal_type,
        t.app_id,
        t.is_have_packer,
        t.shell_ipa_path,
        t.address,
        t.privacy_policy_path,
        t.third_party_share_list_path
        FROM t_assets AS t
        where
        t.is_delete = 0
        <if test="packageNameList != null and packageNameList.size() > 0">
            AND t.`pakage` IN
            <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                #{packageName}
            </foreach>
        </if>
        ORDER BY t.id ASC
    </select>

    <select id="assetsDailyDetection" resultMap="assetsDaily">
        SELECT
            COUNT(a.detectionAssets) AS detectionTotalCount,
            dc.fastDetectionCount,
            dc.deepDetectionCount,
            IF(laws.nonComplianceCount IS NULL, 0, laws.nonComplianceCount) AS nonComplianceTotalCount,
            a.detectionDate
        FROM (SELECT
            COUNT(a.`id`) AS detectionAssets,
            DATE_FORMAT(t.task_starttime, "%Y-%m-%d") AS detectionDate
            FROM t_assets AS a
            INNER JOIN t_task AS t ON t.assets_id=a.id
            WHERE a.`is_delete`=0 AND t.dynamic_status=6 AND a.`terminal_type`=#{terminalType} AND t.`create_user_id` = #{userId}
            <if test="packageNameList != null and packageNameList.size() > 0">
                AND a.`pakage` IN
                <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                    #{packageName}
                </foreach>
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY a.`name`, a.`version`
        ) AS a
        LEFT JOIN (
            SELECT
            COUNT(if(t.detection_type=1, 1, NULL)) AS fastDetectionCount,
            COUNT(if(t.detection_type=2, 1, null)) AS deepDetectionCount,
            DATE_FORMAT(t.task_starttime, "%Y-%m-%d") AS detectionDate
            FROM t_assets AS a
            INNER JOIN t_task AS t ON a.id=t.assets_id
            WHERE a.`is_delete`=0 AND t.dynamic_status=6 AND a.`terminal_type`=#{terminalType} AND t.`create_user_id` = #{userId}
            <if test="packageNameList != null and packageNameList.size() > 0">
                AND a.`pakage` IN
                <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                    #{packageName}
                </foreach>
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY detectionDate
        ) AS dc ON dc.detectionDate=a.detectionDate
        LEFT JOIN (
            SELECT
            COUNT(1) AS nonComplianceCount,
            DATE_FORMAT(t.task_starttime, "%Y-%m-%d") AS detectionDate
            FROM t_app_laws_dect_detail AS d
            INNER JOIN t_assets AS a ON a.id=d.assets_id
            INNER JOIN t_task AS t ON t.task_id=d.task_id
            WHERE a.is_delete=0 AND t.dynamic_status=6 AND a.`terminal_type`=#{terminalType} AND t.`create_user_id` = #{userId}
            <if test="packageNameList != null and packageNameList.size() > 0">
                AND a.`pakage` IN
                <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                    #{packageName}
                </foreach>
            </if>
            <if test="startDate != null">
                AND t.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND t.`task_starttime` &lt; #{endDate}
            </if>
            GROUP BY detectionDate
        ) AS laws ON laws.detectionDate=a.detectionDate
        GROUP BY a.detectionDate
        ORDER BY a.detectionDate DESC
    </select>

    <select id="detectionAssetsCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (SELECT
            COUNT(a.`id`)
            FROM t_assets AS a
            INNER JOIN t_task AS r ON r.assets_id=a.id
            WHERE a.`is_delete`=0 AND r.dynamic_status=6 AND a.`terminal_type`=#{terminalType} AND r.`create_user_id` = #{userId}
            <if test="packageNameList != null and packageNameList.size() > 0">
                AND a.`pakage` IN
                <foreach collection="packageNameList" item="packageName" open="(" separator="," close=")">
                    #{packageName}
                </foreach>
            </if>
            <if test="startDate != null">
                AND r.`task_starttime` &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND r.`task_starttime` &lt; #{endDate}
            </if>
        GROUP BY a.`name`, a.`version`) AS a
    </select>
</mapper>