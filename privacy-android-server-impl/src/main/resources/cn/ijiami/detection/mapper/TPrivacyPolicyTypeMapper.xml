<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyPolicyTypeMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyPolicyType">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result column="law_name" property="lawName"/>
        <result column="terminal_type" property="terminalType"/>
        <result column="is_custom" property="isCustom"/>
        <result column="status" property="status"/>
        <result column="is_del" property="isDel"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
    </resultMap>


    <select id="findByTaskIdAndType" resultType="cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO">
        SELECT
            t2.type_id AS typeId,
            t1.type,
            t2.type_name AS typeName,
            t2.id,
            t2.check_name AS checkName,
            t2.notes,
            t2.reference,
            t2.test_point AS testPoint,
            t2.test_method AS testMethod,
            t2.test_result AS testResult,
            IFNULL( t3.result, t2.result ) AS result,
            IFNULL( t3.suggestion, t2.suggestion ) AS suggestion,
            t2.type_sort AS typeSort,
            t2.check_sort AS checkSort
        FROM
            t_privacy_policy_type t1,
            t_privacy_check t2,
            t_privacy_policy t3
        WHERE
            t1.id = t2.type_id
          AND t3.type = t1.type
          AND t3.privacy_id = t2.id
          AND t3.task_id = #{taskId}
          AND t1.type = #{type}
          <if test="terminalType !=null">
              AND t1.terminal_type =#{terminalType}
          </if>
        ORDER BY
            t2.type_sort,
            t2.check_sort ASC
    </select>

    <select id="countLawNumber" resultType="int">
        SELECT
            COUNT( DISTINCT t1.law_name )
        FROM
            t_privacy_policy_type t1,
            t_privacy_check t2
        WHERE
            t1.is_del = 0
          AND t1.STATUS = 2
          AND t1.terminal_type = #{terminalType}
    </select>

    <select id="selectLaws" parameterType="cn.ijiami.detection.query.LawQuery" resultType="cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO">
        SELECT
            A.id,
            A.law_name AS lawName,
            A.terminal_type AS terminalType,
            t2.user_name AS updateUser,
            A.update_time AS updateTime,
            A.STATUS AS status,
            count( t1.id ) AS policyPointNum,
            A.is_custom AS IsCustom,
            A.type,
            A.create_time AS createTime,
            A.create_user_id AS createUserId
        FROM
           ( SELECT DISTINCT * FROM t_privacy_policy_type ORDER BY update_time DESC ) AS A
            LEFT JOIN t_privacy_check t1 ON A.id = t1.type_id AND A.terminal_type = t1.terminal_type AND t1.is_del=0 and t1.is_show=1
            LEFT JOIN tsys_user t2 ON t2.user_id = A.update_user_id
        WHERE
            A.is_del = 0
          AND A.terminal_type = #{terminalType}
        <if test="lawName !=null and lawName !='' ">
            AND A.law_name like CONCAT('%',#{lawName},'%')
        </if>
        GROUP BY
            A.law_name
       <if test="sortType !=null and sortType!=0 ">
           ORDER BY
               <if test="sortType == 1">
                   updateTime
               </if>
               <if test="sortType == 2">
                   status
               </if>
               <if test="sortOrder == 1">
                   asc
               </if>
               <if test="sortOrder == 2">
                   desc
               </if>
       </if>
    </select>

    <select id="selectMaxType" resultType="int">
        SELECT
            max( type )
        FROM
            t_privacy_policy_type
        WHERE
            is_del = 0
          AND terminal_type = #{terminalType}
    </select>

    <select id="selectPolicyByIdAndTerminalType" resultMap="BaseResultMap">
        select *
            from t_privacy_policy_type
            where id = #{id}
            <if test="terminalType !=null and terminalType !=0 ">
                and terminal_type = #{terminalType}
            </if>
    </select>

    <update id="deleteById">
        update t_privacy_policy_type set update_user_id =#{userId},update_time =NOW(),is_del = 1 where id =#{id}
    </update>

    <select id="selectByLawName" resultMap="BaseResultMap">
        select * from t_privacy_policy_type where terminal_type=#{terminalType} and law_name =#{lawName} and is_del=0
    </select>

</mapper>