<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyLawsDetail">
		<id column="id" jdbcType="BIGINT" property="id"/>
		<result column="task_id" jdbcType="BIGINT" property="taskId"/>
		<result column="data_id" jdbcType="BIGINT" property="dataId"/>
		<result column="item_no" jdbcType="VARCHAR" property="itemNo"/>
		<result column="action_id" jdbcType="BIGINT" property="actionId"/>
		<result column="action_alias" jdbcType="VARCHAR" property="actionAlias"/>
		<result column="trigger_num" jdbcType="INTEGER" property="triggerNum"/>
		<result column="old_policy_snippet" jdbcType="VARCHAR" property="oldPolicySnippet"/>
		<result column="privacy_policy_snippet" jdbcType="VARCHAR" property="privacyPolicySnippet"/>
		<result column="file_key" jdbcType="VARCHAR" property="fileKey"/>
		<result column="data_type" jdbcType="INTEGER" property="dataType"/>
		<result column="behavior_stage" jdbcType="INTEGER" property="behaviorStage"/>
		<result column="executor_type" jdbcType="INTEGER" property="executorType"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="sdk_ids" jdbcType="VARCHAR" property="sdkIds"/>
        <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        
    </resultMap>

    <sql id="Base_Column_List">
            id,
            task_id,
            item_no,
            action_id,
            action_alias,
            trigger_num,
            old_policy_snippet,
            privacy_policy_snippet,
            file_key,
            data_type,
            behavior_stage,
            executor_type,
            executor,
            package_name,
            sdk_ids,
            trigger_time,
            create_time,
            update_time
    </sql>
    <delete id="deleteByTaskId">
        delete from t_privacy_laws_detail where task_id = #{taskId,jdbcType=BIGINT}
    </delete>

    <select id="selectOtherDetailByDataType" resultMap="BaseResultMap">
        select old_policy_snippet,
        privacy_policy_snippet,
        file_key
        from t_privacy_laws_detail as tpld
        where tpld.task_id = #{taskId,jdbcType=BIGINT}
        and tpld.item_no = #{itemNo,jdbcType=VARCHAR}
        and tpld.data_type = 1
    </select>

    <select id="selectActionDetailByTaskIdAndItemNo" resultType="cn.ijiami.detection.VO.LawActionDetailVO">
				SELECT tpld.id,
		        tpld.item_no AS itemNo,
		        tpld.action_id AS actionId,
		        tpld.action_alias AS actionAlias,
		        tpld.trigger_num AS triggerNum,
		        tpld.executor_type AS executorType,
		        tpld.executor,
		        tpld.interval_time AS intervalTime,
		        tpld.trigger_time AS triggerTime,
		        tan.is_personal AS personal,
		        tan.action_permission AS permission,
		        tan.action_permission_alias AS permissionAlias,
				tpld.behavior_stage AS behaviorStage,
				tpld.attributively,
				tpld.ip,
				tpld.code,
				tpld.host,
				tpld.port,
				tpld.cookie,
				tpld.outside,
				word.key_words as keyWords
		        FROM t_privacy_laws_detail AS tpld
		        LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
				LEFT JOIN t_privacy_laws_action_key_words_rel AS wordRel ON wordRel.action_id=tpld.action_id
				LEFT JOIN t_privacy_laws_privacy_policy_key_words AS word ON word.id=wordRel.key_words_id
		        WHERE tpld.task_id =  #{taskId,jdbcType=BIGINT}
		        AND tpld.item_no = #{itemNo,jdbcType=VARCHAR}
		        AND tpld.data_type = #{dataType,jdbcType=BIGINT} 
				AND tpld.action_id != 32001
				AND tpld.action_id != 28001
		        <if test="actionPermissionAliases != null and actionPermissionAliases.size() > 0">
					AND tan.action_permission_alias IN
					<foreach collection="actionPermissionAliases" item="alias" open="(" separator="," close=")">
						#{alias}
					</foreach>
				</if>
				<if test="actionIds != null and actionIds.size() > 0">
					AND tpld.action_id IN
					<foreach collection="actionIds" item="actionId" open="(" separator="," close=")">
						#{actionId}
					</foreach>
				</if>
				<if test="executors != null and executors.size() > 0">
					AND (
					<foreach collection="executors" item="executor" index="num">
						<if test="executors.size() > num+1">
							tpld.executor like CONCAT('%',#{executor},'%') or
						</if>
						<if test="executors.size()==num+1">
							tpld.executor like CONCAT('%',#{executor},'%')
						</if>
					</foreach>
					)
				</if>
		  UNION ALL	 			
				SELECT tpld.id,
		        tpld.item_no AS itemNo,
		        tpld.action_id AS actionId,
		        tpld.action_alias AS actionAlias,
		        tpld.trigger_num AS triggerNum,
		        tpld.executor_type AS executorType,
		        tpld.executor,
		        tpld.interval_time AS intervalTime,
		        tpld.trigger_time AS triggerTime,
		        tan.is_personal AS personal,
		        tan.action_permission AS permission,
		        tan.action_permission_alias AS permissionAlias,
				tpld.behavior_stage AS behaviorStage,
				tpld.attributively,
				tpld.ip,
				tpld.code,
				tpld.host,
				tpld.port,
				tpld.cookie,
				tpld.outside,
				word.key_words as keyWords
		        FROM t_privacy_laws_detail AS tpld
		        LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
				LEFT JOIN t_privacy_laws_action_key_words_rel AS wordRel ON wordRel.action_id=tpld.action_id
				LEFT JOIN t_privacy_laws_privacy_policy_key_words AS word ON word.id=wordRel.key_words_id
		        WHERE tpld.task_id =  #{taskId,jdbcType=BIGINT}
		        AND tpld.item_no = #{itemNo,jdbcType=VARCHAR}
		        AND tpld.data_type = #{dataType,jdbcType=BIGINT}
				AND tpld.behavior_stage=4
				AND tpld.action_id=32001
				<if test="actionPermissionAliases != null and actionPermissionAliases.size() > 0">
					AND tan.action_permission_alias IN
					<foreach collection="actionPermissionAliases" item="alias" open="(" separator="," close=")">
						#{alias}
					</foreach>
				</if>
				<if test="actionIds != null and actionIds.size() > 0">
					AND tpld.action_id IN
					<foreach collection="actionIds" item="actionId" open="(" separator="," close=")">
						#{actionId}
					</foreach>
				</if>
				<if test="executors != null and executors.size() > 0">
					AND (
					<foreach collection="executors" item="executor" index="num">
						<if test="executors.size() > num+1">
							tpld.executor like CONCAT('%',#{executor},'%') or
						</if>
						<if test="executors.size()==num+1">
							tpld.executor like CONCAT('%',#{executor},'%')
						</if>
					</foreach>
					)
				</if>
    </select>

    <select id="selectAllItemDetailByTaskId" resultType="cn.ijiami.detection.VO.LawActionDetailVO">
        <!-- SELECT tpld.id,
        tpld.item_no AS itemNo,
        tpld.action_id AS actionId,
        tpld.action_alias AS actionAlias,
        tpld.trigger_num AS triggerNum,
        tpld.executor_type AS executorType,
        tpld.executor,
        tpld.trigger_time AS triggerTime,
        tan.is_personal AS personal,
        tan.action_permission AS permission,
        tan.action_permission_alias AS permissionAlias,
        tpld.interval_time AS intervalTime,
        base.key_words as keyWords,
        tpld.behavior_stage AS behaviorStage
        FROM t_privacy_laws_detail AS tpld
        LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
        LEFT JOIN t_privacy_laws_basis_v3 AS base ON base.action_id=tpld.action_id AND base.item_no=tpld.item_no
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type = 2 -->
        <!-- SELECT tpld.id,
        tpld.item_no AS itemNo,
        tpld.action_id AS actionId,
        tpld.action_alias AS actionAlias,
        tpld.trigger_num AS triggerNum,
        tpld.executor_type AS executorType,
        tpld.executor,
        tpld.trigger_time AS triggerTime,
        tan.is_personal AS personal,
        tan.action_permission AS permission,
        tan.action_permission_alias AS permissionAlias,
        tpld.interval_time AS intervalTime,
        tpld.attributively,
		tpld.ip,
		tpld.code,
		tpld.host,
		tpld.port,
		tpld.cookie,
		tpld.data_type AS dataType,
        base.key_words as keyWords,
        tpld.behavior_stage AS behaviorStage
        FROM t_privacy_laws_detail AS tpld
        LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
        LEFT JOIN t_privacy_laws_basis_v3 AS base ON base.action_id=tpld.action_id AND base.item_no=tpld.item_no
		LEFT JOIN t_privacy_laws_result AS res ON tpld.task_id=res.task_id AND res.item_no=tpld.item_no
        WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
        AND tpld.data_type=#{dataType,jdbcType=BIGINT} and res.law_id=#{lawId,jdbcType=BIGINT}
		GROUP BY id -->


		SELECT a.*,max(a.intervalTime) FROM (
		SELECT tpld.id,
		tpld.item_no AS itemNo,
		tpld.action_id AS actionId,
		tpld.action_alias AS actionAlias,
		tan.action_name AS actionName,
		tpld.trigger_num AS triggerNum,
		tpld.executor_type AS executorType,
		tpld.executor,
		tpld.trigger_time AS triggerTime,
		tpld.package_name AS packageName,
		tpld.details_data AS detailsData,
		tpld.stack_info AS stackInfo,
		nougat_e.jni_stack_info AS jniStackInfo,
		nougat_e.api_name AS apiName,
		tan.is_personal AS personal,
		tan.action_permission AS permission,
		tan.action_permission_alias AS permissionAlias,
		tpld.interval_time AS intervalTime,
		tpld.attributively,
		tpld.ip,
		tpld.code,
		tpld.host,
		tpld.port,
		tpld.cookie,
		tpld.data_type AS dataType,
		word.key_words as keyWords,
		tpld.behavior_stage AS behaviorStage
		FROM t_privacy_laws_detail AS tpld
		LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
		LEFT JOIN t_privacy_laws_action_key_words_rel AS wordRel ON wordRel.action_id=tpld.action_id
		LEFT JOIN t_privacy_laws_privacy_policy_key_words AS word ON word.id=wordRel.key_words_id
		LEFT JOIN t_privacy_laws_result AS res ON tpld.task_id=res.task_id AND res.item_no=tpld.item_no
		LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=tpld.data_id
		WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
		AND tpld.data_type=#{dataType,jdbcType=BIGINT} and res.law_id=#{lawId,jdbcType=BIGINT}
		AND tpld.action_id != 32001
		GROUP BY id

		UNION ALL

		SELECT tpld.id,
		tpld.item_no AS itemNo,
		tpld.action_id AS actionId,
		tpld.action_alias AS actionAlias,
		tan.action_name AS actionName,
		tpld.trigger_num AS triggerNum,
		tpld.executor_type AS executorType,
		tpld.executor,
		tpld.trigger_time AS triggerTime,
		tpld.package_name AS packageName,
		tpld.details_data AS detailsData,
		tpld.stack_info AS stackInfo,
		nougat_e.jni_stack_info AS jniStackInfo,
		nougat_e.api_name AS apiName,
		tan.is_personal AS personal,
		tan.action_permission AS permission,
		tan.action_permission_alias AS permissionAlias,
		tpld.interval_time AS intervalTime,
		tpld.attributively,
		tpld.ip,
		tpld.code,
		tpld.host,
		tpld.port,
		tpld.cookie,
		tpld.data_type AS dataType,
		word.key_words as keyWords,
		tpld.behavior_stage AS behaviorStage
		FROM t_privacy_laws_detail AS tpld
		LEFT JOIN t_action_nougat AS tan ON tpld.action_id = tan.action_id
		LEFT JOIN t_privacy_laws_action_key_words_rel AS wordRel ON wordRel.action_id=tpld.action_id
		LEFT JOIN t_privacy_laws_privacy_policy_key_words AS word ON word.id=wordRel.key_words_id
		LEFT JOIN t_privacy_laws_result AS res ON tpld.task_id=res.task_id AND res.item_no=tpld.item_no
		LEFT JOIN t_privacy_action_nougat_extend AS nougat_e ON nougat_e.nougat_id=tpld.data_id
		WHERE tpld.task_id = #{taskId,jdbcType=BIGINT}
		AND tpld.data_type=#{dataType,jdbcType=BIGINT} and res.law_id=#{lawId,jdbcType=BIGINT}
		AND tpld.behavior_stage=4
		AND tpld.action_id=32001
		) a GROUP BY itemNo,behaviorStage,executor,actionName
	</select>

    <select id="selectAllItemOtherDetailByDataType" resultMap="BaseResultMap">
        <!-- select
        item_no,
        old_policy_snippet,
        privacy_policy_snippet,
        file_key
        from t_privacy_laws_detail as tpld
        where tpld.task_id = #{taskId,jdbcType=BIGINT}
        and tpld.data_type = 1 -->
        select
        tpld.item_no,
        tpld.old_policy_snippet,
        tpld.privacy_policy_snippet,
        tpld.file_key
        from t_privacy_laws_detail as tpld
		LEFT JOIN t_privacy_laws_result AS res ON tpld.task_id=res.task_id AND res.item_no=tpld.item_no
        where tpld.task_id = #{taskId,jdbcType=BIGINT}
        and tpld.data_type = 1 and res.law_id=#{lawId,jdbcType=BIGINT}
    </select>
</mapper>
