<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.ISysLogMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.manager.syslog.entity.TsysOperateLog">
        <result column="log_id" jdbcType="INTEGER" property="logId" />
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="operate_name" jdbcType="VARCHAR" property="operateName"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="created_user_id" jdbcType="INTEGER" property="createdUserId" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    </resultMap>

    <sql id="baseSQL">
        log_id,module_name,operate_name,user_name,real_name,ip,content,status,created_user_id,created_time
    </sql>

    <select id="selectSysLogByQuery" resultMap="BaseResultMap">
        select
        <include refid="baseSQL"/>
        from tsys_operate_log
        <where>
            <if test="status!=null and status!='' ">
                AND status=#{status}
            </if>
            
             <if test="username!=null and username!='' ">
                AND user_name=#{username}
            </if>
            
            <if test="realName!=null and realName!='' ">
                AND real_name=#{realName}
            </if>
            
            <if test="ip!=null and ip!='' ">
                AND ip=#{ip}
            </if>
            
            <if test="startTime!=null and startTime!='' ">
                AND created_time &gt;= #{startTime} AND created_time &lt;= #{endTime}
            </if>
            
            <if test="keyword!=null and keyword!='' ">
                AND content LIKE CONCAT('%',#{keyword},'%')
            </if>
            
            <if test="!export">
                AND created_time >= DATE_SUB(NOW(), INTERVAL 180 DAY)
            </if>
            
        </where>
        <choose>
            <when test="sortType != null and sortType==1">
                ORDER BY created_time ASC
            </when>
            <otherwise>
                ORDER BY created_time DESC
            </otherwise>
        </choose>

    </select>

</mapper>