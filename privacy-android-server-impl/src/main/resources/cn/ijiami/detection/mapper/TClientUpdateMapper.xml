<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TClientUpdateMapper">
    <select id="findNewVersion" resultMap="BaseResultMap">
        select id, version_name, version_code, base_url, file_list, description, update_time
        from t_client_update
        <if test="currentVersion != null">
            where version_code > #{currentVersion} / 1000
        </if>
        order by update_time
    </select>

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TClientUpdate">
        <id property="id" column="id"/>
        <result property="versionName" column="version_name"/>
        <result property="versionCode" column="version_code"/>
        <result property="baseUrl" column="base_url"/>
        <result property="fileList" column="file_list"/>
        <result property="description" column="description"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>