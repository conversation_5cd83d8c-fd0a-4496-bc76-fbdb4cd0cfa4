<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TSensitiveWordMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TSensitiveWord">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="sensitive_type" property="sensitiveType"
                jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result property="sensitiveWords" column="sensitive_words"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="suggestion" column="suggestion"/>
        <result property="regex" column="regex"/>
    </resultMap>
    <resultMap type="cn.ijiami.detection.VO.SensitiveAndTypeVO"
               id="SensitiveWordAndTypeResultMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="sensitive_type" property="sensitiveType"
                jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="type_name" property="typeName"/>
        <result property="sensitiveWords" column="sensitive_words"/>
        <result property="regex" column="regex"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            type_id,
            name,
            sensitive_words,
            regex,
            risk_level,
            terminal_type,
            platform,
            sensitive_type,
            create_user_id,
            update_user_id,
            create_time,
            update_time
    </sql>
    <!-- 敏感词、敏感词分类 -->
    <sql id="Base_Column_find_sql">
            word.id,
            word.type_id,
            word.name,
            word.sensitive_words,
            word.regex,
            word.risk_level,
            word.terminal_type,
            word.platform,
            word.sensitive_type,
            word.create_user_id,
            word.update_user_id,
            word.create_time,
            word.update_time,
            type.type_name
    </sql>
    <insert id="batchAddSensitiveWord"
            parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_sensitive_word (id, type_id, name,sensitive_words,risk_level, terminal_type,
        platform,
        sensitive_type, create_user_id, update_user_id, create_time,
        update_time)
        values
        <foreach collection="sensitiveWordList" item="item"
                 index="index" separator=",">
            (#{item.id},#{item.typeId},#{item.name},#{item.sensitiveWords},#{item.riskLevel}
            ,#{item.terminalType},#{item.platform},#{item.sensitiveType},
            #{item.createUserId},#{item.updateUserId},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>
    <select id="selectSensitiveWordByPage"
            parameterType="cn.ijiami.detection.entity.TSensitiveWord"
            resultMap="BaseResultMap">
        SELECT * FROM t_sensitive_word where 1=1
        <if test="typeId != null">
            and type_id=#{typeId}
        </if>
        <if test="name != null and name != ''">
            and name LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="terminalType !=null">
            AND terminal_type = #{terminalType.value}
        </if>
        order by create_time desc
    </select>

    <!-- 查询敏感词汇 -->
    <select id="findSensitiveWord" parameterType="cn.ijiami.detection.entity.TSensitiveWord"
            resultMap="BaseResultMap">
        SELECT * FROM t_sensitive_word where 1=1
        <if test="typeId != null">
            and type_id=#{typeId}
        </if>
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
        <if test="terminalType !=null">
            AND terminal_type = #{terminalType.value}
        </if>
        order by create_time desc
    </select>

    <!-- 查询敏感词 -->
    <select id="findSensitiveWordByQuery"
            parameterType="cn.ijiami.detection.query.SensitiveWordQuery"
            resultMap="SensitiveWordAndTypeResultMap">
        select
        <include refid="Base_Column_find_sql"/>
        from t_sensitive_word word left join t_sensitive_type type on type.id
        = word.type_id
        where 1=1
        <if test="name != null">
            and word.name = #{name}
        </if>
        <if test="terminalType !=null">
            AND a.terminal_type = #{terminalType}
        </if>
    </select>
    <select id="findByTerminalType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_sensitive_word where terminal_type = #{terminalType} AND risk_level=1;
    </select>
</mapper>