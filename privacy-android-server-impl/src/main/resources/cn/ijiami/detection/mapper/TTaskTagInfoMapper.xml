<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskTagInfoMapper">
    <resultMap id="BaseResult"
               type="cn.ijiami.detection.VO.ApiTaskTagInfoVO">
        <id column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="assets_name" property="assetsName" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="download_url" property="downloadUrl" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <collection property="tagList" javaType="list"
                    ofType="string">
            <result column="tag_name" />
        </collection>
    </resultMap>

    <select id="findInfoByPage" resultMap="BaseResult">
        SELECT info.`task_id`, info.`description`, info.`create_time`,dict.`tag_name`, a.`name` AS `assets_name`,
        a.`version`, a.`shell_ipa_path` AS `download_url`
        FROM t_task_tag_info AS info
        LEFT JOIN t_task_tag_list AS tag ON info.task_id=tag.task_id
        LEFT JOIN t_task_tag_dict AS dict ON dict.tag_id=tag.tag_id
        INNER JOIN t_task AS t ON t.task_id=tag.task_id
        INNER JOIN t_assets AS a ON a.id=t.assets_id
        WHERE info.`task_id`>0
        <if test="terminalType != null">
            AND t.`terminal_type`=#{terminalType}
        </if>
        <if test="tagIdList != null and tagIdList.size() != 0">
            AND tag.`tag_id` in
            <foreach collection="tagIdList" item="tagId" index="index"
                     open="(" close=")" separator=",">
                #{tagId}
            </foreach>
        </if>
    </select>

</mapper>
