<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTask">
        <!-- WARNING - @mbg.generated -->
        <id column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="thread_id" property="threadId" jdbcType="VARCHAR"/>
        <result column="apk_detection_detail_id" property="apkDetectionDetailId" jdbcType="BIGINT"/>
        <result column="assets_id" property="assetsId"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType" jdbcType="BIGINT"/>
        <result column="task_tatus" property="taskTatus" jdbcType="INTEGER"/>
        <result column="dynamic_status" property="dynamicStatus" jdbcType="INTEGER"/>
        <result column="dynamic_sub_status" property="dynamicSubStatus" jdbcType="INTEGER"/>
        <result column="dynamic_manual_status" property="dynamicManualStatus" jdbcType="INTEGER"/>
        <result column="dynamic_law_status" property="dynamicLawStatus" jdbcType="INTEGER"/>
        <result column="dynamic_law_sub_status" property="dynamicLawSubStatus" jdbcType="INTEGER"/>
        <result column="review_status" property="reviewStatus" jdbcType="INTEGER"/>
        <result column="detect_complete" property="detectComplete" jdbcType="INTEGER"/>
        <result column="task_starttime" property="taskStarttime" jdbcType="TIMESTAMP"/>
        <result column="task_endtime" property="taskEndtime"
                jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="is_safe" property="isSafe"/>
        <result column="detection_type" property="detectionType"/>
        <result column="data_path" property="dataPath"/>
        <result column="dynamic_starttime" property="dynamicStarttime"/>
        <result column="description" property="description"/>
        <result column="static_task_sort" property="staticTaskSort"/>
        <result column="dynamic_task_sort" property="dynamicTaskSort"/>
        <result column="dynamic_device_type" property="dynamicDeviceType"/>
        <result column="device_serial" property="deviceSerial"/>
        <result column="device_hardware_serial" property="deviceHardwareSerial"/>
        <result column="device_remote_connect_url" property="deviceRemoteConnectUrl"/>
        <result column="stf_token" property="stfToken"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="detect_time_length" property="detectTimeLength" jdbcType="VARCHAR"/>
        <result column="is_api" property="isApi" jdbcType="TINYINT"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 任务数据统计返回值 -->
    <resultMap type="cn.ijiami.detection.entity.TTaskTypeCount" id="taskTypeCount">
        <result column="fastTaskCount" property="fastTaskCount"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="deepTaskCount" property="deepTaskCount"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="aiTaskCount" property="aiTaskCount"
                jdbcType="INTEGER" javaType="INTEGER"/>
    </resultMap>

    <!-- 分页查询任务 -->
    <sql id="Base_sql">
            task_id,
            thread_id,
            assets_id,
            apk_detection_detail_id,
            terminal_type,
            platform,
            task_tatus,
            dynamic_status,
            dynamic_sub_status,
            dynamic_manual_status,
            dynamic_law_status,
            dynamic_law_sub_status,
            review_status,
            detect_complete,
            is_safe,
            task_starttime,
            task_endtime,
            create_user_id,
            is_delete,
            data_path,
        dynamic_starttime,
        description,
        law_starttime,
        static_task_sort,
        dynamic_task_sort,
        dynamic_device_type,
        device_serial,
        device_remote_connect_url,
        detection_type,
        stf_token,
        version,
        device_hardware_serial
    </sql>

    <sql id="taskPageWhere">
    	<if test="userId != null">
            and a.create_user_id = #{userId}
        </if>
        <if test="taskType != null">
            <if test="taskType == 2">
                and (te.task_id is null or (te.action_filter_group_id = 0 and te.custom_laws_group_id=0))
            </if>
            <if test="taskType == 3">
                and te.action_filter_group_id > 0
            </if>
            <if test="taskType == 4">
                and te.custom_laws_group_id > 0
            </if>
        </if>
        <if test="terminalTypeEnum != null">
            and a.terminal_type = #{terminalTypeEnum}
        </if>
        <if test="appName != null and appName != ''">
            and (b.name LIKE CONCAT('%',#{appName,jdbcType=VARCHAR},'%')
            or u.real_name LIKE CONCAT('%',#{appName,jdbcType=VARCHAR},'%')
            or b.pakage LIKE CONCAT('%',#{appName,jdbcType=VARCHAR},'%'))
        </if>
        <if test="ids != null and ids.size() > 0">
            and a.apk_detection_detail_id in
            <foreach collection="ids" index="index" item="detail_id"
                     open="(" separator="," close=")">
                #{detail_id}
            </foreach>
        </if>
        <if test="status != null">
            <if test="status == 1">
                and (a.dynamic_status != 6 or a.task_tatus != 4)
            </if>
            <if test="status == 2">
                and a.dynamic_status = 6 and a.task_tatus = 4
            </if>
        </if>

        <if test="staticStatus != null and staticStatus != 0">
            and a.task_tatus = #{staticStatus}
        </if>

        <if test="dynamicStatus != null and dynamicStatus != 0">
            <if test="dynamicStatus == 1">
                and a.dynamic_status = 1
            </if>
            <if test="dynamicStatus == 2">
                and a.dynamic_status in (2, 4, 7, 8, 9)
            </if>
            <if test="dynamicStatus == 3">
                and a.dynamic_status in (3, 5)
            </if>
            <if test="dynamicStatus == 4">
                and a.dynamic_status = 6
            </if>
        </if>

        <if test="lawStatus != null and lawStatus != 0">
            <if test="lawStatus == 1">
                and a.dynamic_law_status = 1
            </if>
            <if test="lawStatus == 2">
                and a.dynamic_law_status in (2, 5)
            </if>
            <if test="lawStatus == 3">
                and a.dynamic_law_status = 3
            </if>
            <if test="lawStatus == 4">
                and a.dynamic_law_status = 4
            </if>
        </if>
    </sql>

    <select id="findTaskByPage" parameterType="cn.ijiami.detection.query.task.TaskQuery" resultMap="BaseResultMap">
        select a.*,
        a.create_user_id AS _user_id
        <if test="sortType != null">
         , case when a.task_tatus = 1 then 1
        when a.task_tatus = 2 then 2
        when a.task_tatus = 3 then 3
        when a.task_tatus = 4 then 4  else 0 end task_tatus_sort
         , case when a.dynamic_status = 1 then 1
        when a.dynamic_status in (2, 4, 7, 8, 9) then 2
        when a.dynamic_status in (3, 5) then 3
        when a.dynamic_status = 6 then 4 else 0  end dynamic_status_sort
         , case when a.dynamic_law_status = 1 then 1
        when a.dynamic_law_status in (2, 5) then 2
        when a.dynamic_law_status = 3 then 3
        when a.dynamic_law_status = 4 then 4 else 0 end dynamic_law_status_sort
        </if>
        from t_task a
        left join t_assets b on a.assets_id=b.id
        left join tsys_user u on a.create_user_id = u.user_id
        left join t_task_extend te on a.task_id = te.task_id
        where a.is_delete = 0

        <if test="detectionType !=null">
            and a.detection_type = #{detectionType}
        </if>

        <include refid="taskPageWhere"/>

        <if test="sortType == null or sortType == 0">
        order by a.task_starttime desc
        </if>
        <if test="sortType != null and sortType != 0">
        order by
            <if test="sortType == 1">
                task_tatus_sort
            </if>
            <if test="sortType == 2">
                dynamic_status_sort
            </if>
            <if test="sortType == 3">
                dynamic_law_status_sort
            </if>
            <if test="sortOrder == 1">
                asc
            </if>
            <if test="sortOrder == 2">
                desc
            </if>
        </if>
    </select>

    <select id="taskTypeCount" parameterType="cn.ijiami.detection.query.task.TaskQuery"
            resultMap="taskTypeCount">
        SELECT
        sum( case when (a.detection_type = 2 and a.detection_type is NOT NULL) then 1 else 0 end ) as deepTaskCount,
        sum( case when (a.detection_type = 1 and a.detection_type is NOT NULL) then 1 else 0 end ) as fastTaskCount,
        sum( case when (a.detection_type = 5 and a.detection_type is NOT NULL) then 1 else 0 end ) as aiTaskCount,
        a.create_user_id as _user_id
        FROM
        t_task a
        LEFT JOIN t_assets b on a.assets_id=b.id
        LEFT JOIN tsys_user u on a.create_user_id = u.user_id
        LEFT JOIN t_task_extend te on a.task_id = te.task_id
        WHERE
        a.is_delete = 0
        <include refid="taskPageWhere"/>
        GROUP BY a.create_user_id
    </select>

    <!-- 通过文档id 查询任务状态   deng -->
    <select id="queryTaskMessageByDocumentId" parameterType="string" resultType="map">
        select task_id,
        task_tatus,
        is_safe,
        apk_detection_detail_id,
        terminal_type
        from t_task
        where apk_detection_detail_id = #{documentId}
    </select>

    <update id="deleteByApkDetectionDetailId">
        update t_task set is_delete = 1
        where apk_detection_detail_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="findDetectedMD5" resultType="cn.ijiami.detection.VO.BigDataVO">
        select a.task_endtime as detectionTime, b.MD5 as md5
        from t_task a
                 left join t_assets b on a.assets_id = b.id
        where a.detect_complete = 1
          and a.is_delete = 0
    </select>

    <select id="findByMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_sql"/>
        FROM t_task
        WHERE assets_id = (SELECT b.id FROM t_assets b WHERE b.MD5 = #{md5,jdbcType=VARCHAR} LIMIT 1)
        AND detect_complete = 1
        AND is_delete = 0
        ORDER BY task_endtime DESC
        LIMIT 1
    </select>

    <select id="findByDeviceSerial" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_sql"/>
        FROM t_task
        WHERE device_serial = #{deviceSerial}
        ORDER BY task_starttime DESC
        LIMIT 1
    </select>

    <select id="findByDeviceHardwareSerial" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_sql"/>
        FROM t_task
        WHERE device_hardware_serial = #{deviceHardwareSerial} AND (dynamic_status=4 or dynamic_manual_status=2 or
        dynamic_law_status=2)
        ORDER BY task_starttime DESC
        LIMIT 1
    </select>

    <select id="findByTaskIdForUpdate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_sql"/>
        FROM t_task WHERE task_id=#{taskId} FOR UPDATE
    </select>

    <select id="findByDocumentId" resultMap="BaseResultMap">
        select
        <include refid="Base_sql"/>
        from t_task where apk_detection_detail_id=#{documentId,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    
    <select id="findStaticDataTimeOut" resultMap="BaseResultMap">
	     SELECT  <include refid="Base_sql"/> FROM t_task  WHERE task_tatus=2
		 AND task_starttime &lt;= DATE_SUB(NOW(), INTERVAL '2' HOUR) and terminal_type = #{terminalType}
		 ORDER BY  task_starttime desc limit 0,10
    </select>
    
    <select id="findDynamicDataDTTimeOut" resultMap="BaseResultMap">
		 SELECT  <include refid="Base_sql"/> FROM t_task  WHERE (task_tatus=1 or dynamic_status in(1,2,4,7,8,9))
		 AND task_starttime  &lt;= DATE_SUB(NOW(), INTERVAL '24' HOUR) and terminal_type = 1
		 ORDER BY  task_starttime desc limit 0,10
    </select>

    <select id="findNeedToCheckDynamicTask" resultMap="BaseResultMap">
        SELECT  <include refid="Base_sql"/> FROM t_task  WHERE  dynamic_status in (4) AND dynamic_starttime is not null
        AND dynamic_starttime &lt;= DATE_SUB(NOW(), INTERVAL '60' MINUTE)
        AND dynamic_starttime > DATE_SUB(NOW(), INTERVAL '90' MINUTE)
        AND terminal_type = #{terminalType}
        ORDER BY  task_starttime desc limit 0,10
    </select>

    <select id="findDynamicDataTimeOut" resultMap="BaseResultMap">
        SELECT  <include refid="Base_sql"/> FROM t_task  WHERE  dynamic_status in (4,9,10) AND dynamic_starttime is not null
        AND dynamic_starttime  &lt;= #{timeout} and terminal_type = #{terminalType}
        ORDER BY  task_starttime desc limit 0,10
    </select>

    <select id="findDynamicDataWaitTimeOut" resultMap="BaseResultMap">
        SELECT  <include refid="Base_sql"/> FROM t_task  WHERE  dynamic_status = 1 AND dynamic_starttime is not null
        AND dynamic_starttime  &lt;= #{timeout} and terminal_type = #{terminalType}
        ORDER BY  dynamic_starttime desc limit 0,10
    </select>

    <select id="findLawDataTimeOut" resultMap="BaseResultMap">
        SELECT  <include refid="Base_sql"/> FROM t_task  WHERE  dynamic_law_status in (2,5) AND law_starttime is not null
        AND law_starttime  &lt;= DATE_SUB(NOW(), INTERVAL '2' HOUR) and terminal_type = #{terminalType}
        ORDER BY  task_starttime desc limit 0,10
    </select>

    <select id="findReviewDataTimeOut" resultMap="BaseResultMap">
        SELECT  <include refid="Base_sql"/> FROM t_task  WHERE  review_status in (1,2,5) AND review_starttime is not null
        AND review_starttime  &lt;= DATE_SUB(NOW(), INTERVAL '2' HOUR) and terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
        ORDER BY  review_starttime desc limit 0,10
    </select>

    <select id="selectDocumentIdByTime" resultType="java.lang.String">
        SELECT  apk_detection_detail_id FROM t_task WHERE 1=1
        AND is_delete = 0
        AND task_tatus = 4
        AND dynamic_status = 6
        <if test="begin != null">
            AND task_endtime &gt;= #{begin,jdbcType=TIMESTAMP}
        </if>
        <if test="end != null">
            AND task_endtime &lt;= #{end,jdbcType=TIMESTAMP}
        </if>
        ORDER BY task_id
    </select>

    <select id="getDynamicTaskCount" resultType="java.lang.Integer">
        select count(1) from t_task WHERE is_delete = 0 and t_task.terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
        and (dynamic_status in (4,7,8,9) or dynamic_law_status in (2,5))  and create_user_id = #{userId}
        <if test="detectionType != null">
            and detection_type = #{detectionType}
        </if>
    </select>

    <select id="getDynamicTaskList"  resultMap="BaseResultMap">
        select a.* from t_task a left join t_assets b on a.assets_id=b.id where a.is_delete = 0
        and a.terminal_type = #{terminalType}
        and (a.dynamic_status in (4,7,8,9) or a.dynamic_law_status in (2,5))
        <if test="detectionType != null">
            and a.detection_type = #{detectionType}
        </if>
        and a.create_user_id = #{userId}
        order by a.task_starttime desc
    </select>
    <select id="selectDynamicTaskByUserId" resultType="java.lang.Integer">
        select count(1)
        from  t_task where is_delete = 0
        and  terminal_type = #{terminalType}
        and (dynamic_status in (4,7,8,9) or dynamic_law_status in (2,5) )
        <if test="detectionType != null">
            and detection_type = #{detectionType}
        </if>
        and create_user_id = #{userId};
    </select>
    <select id="getMinStaticWaitSort" resultType="java.lang.Integer">
        select min(static_task_sort) from t_task where is_delete = 0 and terminal_type = #{terminalType} and task_tatus = 1  and detection_type = 1;
    </select>
    <select id="getMinDynamicWaitSort" resultType="java.lang.Integer">
        select min(dynamic_task_sort) from t_task where is_delete = 0 and terminal_type = #{terminalType} and dynamic_status = 1  and detection_type = 1;
    </select>

    <select id="getMaxStaticWaitSort" resultType="java.lang.Integer">
        select max(static_task_sort) from t_task where is_delete = 0 and terminal_type = #{terminalType} and task_tatus = 1  and detection_type = 1;
    </select>
    <select id="getMaxDynamicWaitSort" resultType="java.lang.Integer">
        select max(dynamic_task_sort) from t_task where is_delete = 0 and terminal_type = #{terminalType} and dynamic_status in (1,7,8,9) and detection_type = 1;
    </select>

    <select id="getStaticWaitSortList" resultMap="BaseResultMap">
        select task_id,static_task_sort from t_task where is_delete = 0 and terminal_type = #{terminalType}
        and task_tatus = 1  and detection_type = 1
        <if test="userId != null">
        and create_user_id = #{userId}
        </if>
    </select>

    <select id="getAiDetectWaitingForLogin" resultMap="BaseResultMap">
        SELECT t.task_id
        FROM t_task AS t join t_task_extend AS e ON t.task_id=e.task_id
        WHERE t.is_delete = 0 AND t.terminal_type = #{terminalType}
        AND t.dynamic_status = 4 AND t.detection_type = 5
        AND e.ai_detect_login_status=#{aiDetectLoginStatus} AND t.create_user_id = #{userId}
    </select>

    <select id="getDynamicWaitSortList" resultMap="BaseResultMap">
        select task_id,dynamic_task_sort from t_task where is_delete = 0 and terminal_type = #{terminalType}
        and dynamic_status = 1 and detection_type = 1
        <if test="userId != null">
            and create_user_id = #{userId}
        </if>
    </select>
    <select id="countUserSandyBoxTask" resultType="java.lang.Integer">
        select count(1) from t_task where is_delete = 0 and terminal_type = 1 and dynamic_device_type = 2 and
        (dynamic_status in (4,7,8,9) or dynamic_law_status in (2,5) ) and create_user_id = #{userId};
    </select>

    <select id="countActiveTask" resultType="java.lang.Integer">
        select count(1) from t_task where is_delete = 0
        and (
            dynamic_status in (4,7,8,9)
            or dynamic_law_status in (2,5)
            <!-- 小程序静态检测需要用到设备，需要计算在内-->
            <if test="terminalType == 4 or terminalType == 5">
                or task_tatus = 2
            </if>
            )
        and create_user_id = #{userId} and detection_type = #{detectionType} and terminal_type = #{terminalType}
    </select>

    <select id="countStaticTask" resultType="java.lang.Integer">
        select count(1) from t_task where is_delete = 0 and task_tatus = 1
        and create_user_id = #{userId} and detection_type = #{detectionType} and terminal_type = #{terminalType}
    </select>

    <select id="getDynamicTaskDeviceCount" resultType="java.lang.Integer">
        select count(1) from t_task WHERE is_delete = 0 and t_task.terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
        and (dynamic_status in (4,7,8,9) or dynamic_law_status in (2,5)) and create_user_id = #{userId} and
        dynamic_device_type = #{dynamicDeviceType} and detection_type = 2;
    </select>

    <select id="getStaticWaitTask" resultMap="BaseResultMap">
        SELECT * FROM t_task WHERE is_delete = 0 AND task_tatus = 1 AND terminal_type IN (1, 2, 6) ORDER BY
        create_time ASC;
    </select>
    <select id="getAndroidDynamicWaitTask" resultMap="BaseResultMap">
        SELECT * FROM t_task  WHERE is_delete = 0 AND dynamic_status = 1 AND detection_type IN (1, 5)  AND terminal_type = 1 ORDER BY create_time ASC;
    </select>
    <select id="getDynamicWaitTask" resultMap="BaseResultMap">
        SELECT * FROM t_task  WHERE is_delete = 0 AND dynamic_status = 1 AND detection_type = 1  AND terminal_type = #{terminalType} ORDER BY create_time ASC;
    </select>
    <select id="getIosDynamicWaitTask" resultMap="BaseResultMap">
        SELECT * FROM t_task  WHERE is_delete = 0 AND dynamic_status in (1, 9) AND detection_type = 1  AND terminal_type = 2 ORDER BY create_time ASC;
    </select>
    <select id="getAppletWaitTask" resultMap="BaseResultMap">
        SELECT * FROM t_task  WHERE is_delete = 0
        AND (dynamic_status = 1 OR task_tatus = 1)
        AND terminal_type IN (4, 5)
        ORDER BY create_time ASC;
    </select>
    <select id="getStaticCheckTask" resultType="java.lang.Integer">
        SELECT count(1) FROM t_task  WHERE is_delete = 0 AND task_tatus = 2
        AND terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
    </select>

    <select id="getSpeedCheckTask" resultType="java.lang.Integer">
        SELECT count(1) FROM t_task  WHERE is_delete = 0 AND dynamic_status in (4,7,8,9) AND detection_type = 1 AND terminal_type IN
        <foreach collection="terminalTypeList" item="terminalType" open="(" separator="," close=")">
            #{terminalType}
        </foreach>
    </select>

    <select id="getFastDynamicTask" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_sql"/>
        FROM t_task WHERE is_delete = 0 AND dynamic_status in (4,7,8) AND detection_type = 1 AND terminal_type = #{terminalType}
        AND dynamic_device_type=1;
    </select>
    
    
    <select id="findDetectionCompleteByMd5" parameterType="string" resultMap="BaseResultMap">
        select a.name,t.apk_detection_detail_id,a.MD5 as md5,t.task_tatus,t.dynamic_status from t_assets as a 
        RIGHT JOIN t_task as t ON a.id = t.assets_id
 		WHERE md5=#{md5} and 
 		t.dynamic_status=6 ORDER BY t.task_starttime desc limit 1
    </select>
    
    <select id="find164DetectionResultList" resultType="cn.ijiami.detection.VO.Privacy164ResultVO">
    	select tk.task_starttime as taskStarttime, tk.task_id as taskId, tk.apk_detection_detail_id as documentId,ta.`name` ,ta.version ,ta.pakage ,ta.size, ta.MD5,
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10101'  and r.task_id=tk.task_id)  as 'R10101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10102'  and r.task_id=tk.task_id)  as 'R10102',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10103'  and r.task_id=tk.task_id)  as 'R10103',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10104'  and r.task_id=tk.task_id)  as 'R10104',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10105'  and r.task_id=tk.task_id)  as 'R10105',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10106'  and r.task_id=tk.task_id)  as 'R10106',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10107'  and r.task_id=tk.task_id)  as 'R10107',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10108'  and r.task_id=tk.task_id)  as 'R10108',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10201'  and r.task_id=tk.task_id)  as 'R10201',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10202'  and r.task_id=tk.task_id)  as 'R10202',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10203'  and r.task_id=tk.task_id)  as 'R10203',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10204'  and r.task_id=tk.task_id)  as 'R10204',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10205'  and r.task_id=tk.task_id)  as 'R10205',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10206'  and r.task_id=tk.task_id)  as 'R10206',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10207'  and r.task_id=tk.task_id)  as 'R10207',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10208'  and r.task_id=tk.task_id)  as 'R10208',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10301'  and r.task_id=tk.task_id)  as 'R10301',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10302'  and r.task_id=tk.task_id)  as 'R10302',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10401'  and r.task_id=tk.task_id)  as 'R10401',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='10403'  and r.task_id=tk.task_id)  as 'R10403',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20101'  and r.task_id=tk.task_id)  as 'R20101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20102'  and r.task_id=tk.task_id)  as 'R20102',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20103'  and r.task_id=tk.task_id)  as 'R20103',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20104'  and r.task_id=tk.task_id)  as 'R20104',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20105'  and r.task_id=tk.task_id)  as 'R20105',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20106'  and r.task_id=tk.task_id)  as 'R20106',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20107'  and r.task_id=tk.task_id)  as 'R20107',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20201'  and r.task_id=tk.task_id)  as 'R20201',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20202'  and r.task_id=tk.task_id)  as 'R20202',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='20203'  and r.task_id=tk.task_id)  as 'R20203',
		 
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='110101'  and r.task_id=tk.task_id limit 1)  as 'R110101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='110201'  and r.task_id=tk.task_id limit 1)  as 'R110201',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='110401'  and r.task_id=tk.task_id limit 1)  as 'R110401',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='120101'  and r.task_id=tk.task_id limit 1)  as 'R120101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='120301'  and r.task_id=tk.task_id limit 1)  as 'R120301',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='130101'  and r.task_id=tk.task_id limit 1)  as 'R130101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='130301'  and r.task_id=tk.task_id limit 1)  as 'R130301',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='130401'  and r.task_id=tk.task_id limit 1)  as 'R130401',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='130501'  and r.task_id=tk.task_id limit 1)  as 'R130501',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='130801'  and r.task_id=tk.task_id limit 1)  as 'R130801', 
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='140101'  and r.task_id=tk.task_id limit 1)  as 'R140101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='140201'  and r.task_id=tk.task_id limit 1)  as 'R140201',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='140401'  and r.task_id=tk.task_id limit 1)  as 'R140401',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='140303'  and r.task_id=tk.task_id limit 1)  as 'R140303',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='140304'  and r.task_id=tk.task_id limit 1)  as 'R140304',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='150101'  and r.task_id=tk.task_id limit 1)  as 'R150101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='160101'  and r.task_id=tk.task_id limit 1)  as 'R160101',
		 (select r.result_status from t_privacy_laws_result as r where r.item_no='160301'  and r.task_id=tk.task_id limit 1)  as 'R160301'
		 FROM t_task tk 
		 LEFT JOIN t_privacy_laws_result ts on ts.task_id = tk.task_id
		 LEFT JOIN t_assets ta on ta.id = tk.assets_id
		 where tk.dynamic_status=6
		 <if test="resultStatus != null and resultStatus != 0">
            and ts.result_status = #{resultStatus}
         </if>
		 AND tk.task_id in
		 <foreach collection="taskIds" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
         GROUP BY tk.task_id
		 ORDER BY tk.task_starttime
    </select>

    <select id="find164DetectionResultListNew" resultType="cn.ijiami.detection.VO.Privacy164ResultVO">
        select tk.task_starttime as taskStarttime, tk.task_id as taskId, tk.apk_detection_detail_id as documentId,ta.`name` ,ta.version ,ta.pakage ,ta.size, ta.MD5
        FROM t_task tk
        LEFT JOIN t_privacy_laws_result ts on ts.task_id = tk.task_id
        LEFT JOIN t_assets ta on ta.id = tk.assets_id
        where tk.dynamic_status=6
        <if test="resultStatus != null and resultStatus != 0">
            and ts.result_status = #{resultStatus}
        </if>
        AND tk.task_id in
        <foreach collection="taskIds" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        GROUP BY tk.task_id
        ORDER BY tk.task_starttime
    </select>

    <select id="countWaitingOrDetectionInByAssetsId" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_task
        WHERE is_delete = 0 AND (task_tatus IN (1,2) OR dynamic_status IN (1,4,8,9,10))
        AND assets_id=#{assetsId}
        <if test="createUserId != null">
            AND create_user_id=#{createUserId}
        </if>
    </select>

    <select id="countWaitingOrDetectionInByMd5" resultType="java.lang.Long">
        SELECT COUNT(1) FROM t_assets AS a JOIN t_task AS t ON a.id=t.assets_id
        WHERE t.is_delete = 0 AND (t.task_tatus IN (1,2) OR t.dynamic_status IN (1,4,8,9,10))
        AND a.MD5=#{md5}
        AND a.create_user_id=#{createUserId}
    </select>

    <select id="findAssetsLastTaskDynamicDetectDuration" resultType="java.lang.Long">
        SELECT
        dynamic_detect_duration
        FROM t_task
        WHERE assets_id = #{assetsId} AND is_delete = 0 AND dynamic_status=6 AND dynamic_detect_duration > 120000 AND detection_type=1
        ORDER BY task_id DESC
        LIMIT 1
    </select>

    <select id="findAssetsLastTaskStaticDetectDuration" resultType="java.lang.Long">
        SELECT
        static_detect_duration
        FROM t_task
        WHERE assets_id = #{assetsId} AND is_delete = 0 AND task_tatus=4 AND dynamic_detect_duration > 120000
        ORDER BY task_id DESC
        LIMIT 1
    </select>

    <select id="findAssetsStaticDetectDuration" resultType="java.lang.Long">
        SELECT
        MAX(static_detect_duration)
        FROM t_task
        WHERE assets_id = #{assetsId} AND is_delete = 0 AND task_tatus=4 AND static_detect_duration > 120000
    </select>
</mapper>