<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionMapper">
	<resultMap id="BaseResultMap"
		type="cn.ijiami.detection.entity.TAction">
		<!-- WARNING - @mbg.generated -->
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="name" property="name" jdbcType="VARCHAR" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="grade" property="grade" jdbcType="VARCHAR" />
		<result column="platform" property="platform"
			jdbcType="VARCHAR" />
		<result column="create_user_id" property="createUserId"
			jdbcType="BIGINT" />
		<result column="update_user_id" property="updateUserId"
			jdbcType="BIGINT" />
		<result column="create_time" property="createTime"
			jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime"
			jdbcType="TIMESTAMP" />
	</resultMap>

	<resultMap type="cn.ijiami.detection.VO.ActionVO"
		id="ActionFunctionResultMap">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="name" property="name" jdbcType="VARCHAR" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="grade" property="grade" jdbcType="VARCHAR" />
		<result column="platform" property="platform"
			jdbcType="VARCHAR" />
		<result column="create_user_id" property="createUserId"
			jdbcType="BIGINT" />
		<result column="update_user_id" property="updateUserId"
			jdbcType="BIGINT" />
		<result column="create_time" property="createTime"
			jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime"
			jdbcType="TIMESTAMP" />
		<result column="action_function" property="actionFunction"
			jdbcType="VARCHAR" />
	</resultMap>

	<sql id="base_sql">
		a.id,a.name,a.remark,a.grade,a.platform,a.create_user_id,a.update_user_id,a.create_time,a.update_time,f.action_function
	</sql>
	<!-- 查询行为、行为函数 -->
	<select id="findActionList"
		parameterType="cn.ijiami.detection.query.ActionQuery"
		resultMap="ActionFunctionResultMap">
		select
		<include refid="base_sql" />
		from t_action as a inner join t_action_function as f on a.id =
		action_id
	</select>

	<!-- 查询行为、行为函数 -->
	<select id="findActionByFunction"
		parameterType="java.lang.String" resultMap="ActionFunctionResultMap">
		select
		<include refid="base_sql" />
		from t_action as a inner join t_action_function as f on a.id =
		action_id where f.action_function = #{function}
	</select>
</mapper>