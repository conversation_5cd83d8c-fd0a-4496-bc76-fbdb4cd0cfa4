<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyPolicyItemMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TPrivacyPolicyItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="item_no" property="item_no" jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType" jdbcType="BIGINT"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="risk_description" property="riskDescription" jdbcType="VARCHAR"/>
        <result column="safe_description" property="safeDescription" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Base_Column_List">
        id,
        name,
        item_no,
        terminal_type,
        content,
        risk_description,
        safe_description,
        create_time,
        update_time
    </sql>


    <select id="findAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM t_privacy_policy_item ORDER BY id asc;
    </select>

    <select id="findByTerminalType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM t_privacy_policy_item WHERE terminal_type=#{terminalType} ORDER BY id asc;
    </select>

</mapper>