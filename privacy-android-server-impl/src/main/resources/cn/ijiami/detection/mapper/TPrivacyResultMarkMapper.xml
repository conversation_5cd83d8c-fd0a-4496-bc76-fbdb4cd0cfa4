<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyResultMarkMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyResultMark">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="b_id" property="bId" jdbcType="BIGINT" />
        <result column="result_type" property="resultType" jdbcType="BIGINT" />
        <result column="result_status" property="resultStatus" jdbcType="BIGINT" />
        <result column="risk_level" property="riskLevel" jdbcType="BIGINT" />
        <result column="suggestion" property="suggestion" jdbcType="VARCHAR" />
        <result column="conclusion" property="conclusion" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <collection property="screenshotFileKeyList" ofType="string">
            <result column="file_key" />
        </collection>

    </resultMap>

    <sql id="Base_Column_List">
        id, b_id, result_type, result_status, risk_level, suggestion, conclusion, description, create_time
    </sql>



    <select id="findLatestMarkDesc" resultType="java.lang.String">
        SELECT `description` FROM t_privacy_result_mark WHERE b_id=#{bId} ORDER BY id DESC LIMIT 1;
    </select>

    <select id="findByResultType" resultMap="BaseResultMap">
        SELECT
        m.id, m.b_id, m.result_type, m.result_status, m.risk_level, m.suggestion, m.conclusion, m.description, m.create_time, i.file_key
        FROM t_privacy_result_mark AS m LEFT JOIN t_privacy_result_mark_image AS i ON m.id=i.mark_id
        WHERE m.b_id=#{bId} AND m.result_type=#{resultType}
        ORDER BY create_time DESC;
    </select>

    <select id="findLatestMarkByResultType" resultMap="BaseResultMap">
        SELECT
        m.id, m.b_id, m.result_type, m.result_status, m.risk_level, m.suggestion, m.conclusion, m.description, m.create_time, i.file_key
        FROM t_privacy_result_mark AS m LEFT JOIN t_privacy_result_mark_image AS i ON m.id=i.mark_id
        WHERE m.b_id=#{bId} AND m.result_type=#{resultType} ORDER BY id DESC LIMIT 1;
    </select>
</mapper>