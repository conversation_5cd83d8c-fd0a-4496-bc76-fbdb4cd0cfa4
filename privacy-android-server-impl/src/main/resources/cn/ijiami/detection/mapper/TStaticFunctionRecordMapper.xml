<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TStaticFunctionRecordMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TStaticFunctionRecord">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="asset_id" property="assetId" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="http_code" property="httpCode" jdbcType="INTEGER"/>
        <result column="http_result" property="httpResult" jdbcType="VARCHAR"/>
        <result column="descp" property="descp" jdbcType="VARCHAR"/>
        <result column="request_param" property="requestParam" jdbcType="VARCHAR"/>
        <result column="result_json" property="resultJson" jdbcType="VARCHAR"/>
        <result column="shell_count" property="shellCount" jdbcType="INTEGER"/>
        <result column="progress" property="progress"
                jdbcType="INTEGER"/>
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        task_id,
        asset_id,
        request_id,
        status,
        http_code,
        http_result,
        descp,
        request_param,
        result_json,
        shell_count,
        progress,
        terminal_type,
        create_time,
        update_time
    </sql>

    <update id="updateProgress">
        UPDATE t_static_function_record SET progress=#{progress},status=#{status},update_time=NOW() WHERE id=#{id}
    </update>


    <update id="updateStatus">
        UPDATE t_static_function_record SET status=#{status},update_time=NOW() WHERE id=#{id}
    </update>

    <update id="incrementShellCount">
        UPDATE t_static_function_record SET shell_count=shell_count+1,update_time=#{updateTime} WHERE id=#{id}
    </update>

    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_static_function_record
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="findSuccessByAssetsId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_static_function_record
        WHERE asset_id = #{assetsId,jdbcType=BIGINT} AND status=1
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="findByRequestId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_static_function_record
        WHERE request_id = #{requestId,jdbcType=BIGINT}
        LIMIT 1
    </select>

    <select id="findByTimeout" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_static_function_record
        <![CDATA[ WHERE update_time < date_sub(NOW(), interval '5' MINUTE) AND status IN (0, 3)
        ORDER BY update_time DESC ]]>
    </select><!-- #{timeoutMinute,jdbcType=BIGINT} -->
</mapper>