<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyCheckMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyCheck">
        <id property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="checkName" column="check_name"/>
        <result property="typeName" column="type_name"/>
        <result property="notes" column="notes"/>
        <result property="result" column="result"/>
        <result property="suggestion" column="suggestion"/>
        <result property="reference" column="reference"/>
        <result property="testPoint" column="test_point"/>
        <result property="testMethod" column="test_method"/>
        <result property="testResult" column="test_result"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="keyWord" column="key_word"/>
        <result property="status" column="status"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="typeSort" column="type_sort"/>
        <result property="checkSort" column="check_sort"/>
        <result property="isCache" column="is_cache"/>
        <result property="oldPrivacyId" column="old_privacy_id"/>
        <result property="isShow" column="is_show"/>
    </resultMap>

    <resultMap id="privacyCheckVO" type="cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO">
        <result property="id" column="id"/>
        <result property="typeId" column="type_id"/>
        <result property="typeName" column="type_name"/>
        <result property="checkName" column="check_name"/>
        <result property="notes" column="notes"/>
        <result property="result" column="result"/>
        <result property="suggestion" column="suggestion"/>
        <result property="reference" column="reference"/>
        <result property="testPoint" column="test_point"/>
        <result property="testMethod" column="test_method"/>
        <result property="testResult" column="test_result"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="status" column="status"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="baseColumn">
            id,
            type_id,
            check_name,
            notes,
            result,
            suggestion,
            reference,
            test_point,
            test_method,
            test_result
    </sql>
    <select id="findByIds" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"/>
        from t_privacy_check where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPrivacyByTypeId" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.type_id,
            t.type_name,
            t.check_name,
            t.notes,
            t.result,
            t.suggestion,
            t.reference,
            t.test_point,
            t.test_method,
            t.test_result,
            t.terminal_type,
            t.key_word,
            t.STATUS,
            t.is_del,
            t.create_time,
            t.create_user_id,
            t.update_time,
            t.update_user_id,
            t.type_sort,
            t.check_sort,
            t.is_cache,
            t.old_privacy_id
        FROM
            t_privacy_check t
        WHERE
            type_id = #{typeId}
          AND is_del = 0
          AND is_show = 1
          <if test="terminalType">
              AND terminal_type = #{terminalType}
          </if>
        ORDER BY
            t.type_sort,
            t.check_sort
    </select>

    <update id="updatePrivacyCheckById">
        update t_privacy_check
        set status = #{status},
            update_time = #{updateTime},
            update_user_id = #{updateUserId}
        where id = #{id}
    </update>

    <select id="selectByTypeIdAndTerminalType" resultMap="BaseResultMap">
        select
            id,
            type_id,
            type_name,
            check_name,
            notes,
            result,
            suggestion,
            reference,
            test_point,
            test_method,
            test_result,
            terminal_type,
            key_word,
            STATUS,
            is_del,
            create_time,
            create_user_id,
            update_time,
            update_user_id,
            type_sort,
            check_sort,
            is_cache,
            old_privacy_id,
            is_show
        from t_privacy_check
        where is_del = 0
          and type_id=#{typeId}
          <if test="terminalType !=null">
              and terminal_type=#{terminalType}
          </if>
          and is_show = 1
    </select>

    <update id="deleteById">
        update t_privacy_check set update_user_id=#{userId},update_time=NOW(),is_del=1 where id=#{id}
    </update>

    <select id="selectMaxSort" resultType="map">
        SELECT
        max( type_sort ) AS typeSort,
        max( check_sort ) AS checkSort
        FROM
        t_privacy_check
        WHERE
        type_id = #{typeId}
        <if test="typeName !=null and typeName !=''">
            AND type_name = #{typeName}
        </if>
        AND is_del=0
        AND is_show=1
    </select>

    <select id="findAllCachePrivacyByTypeId" resultMap="BaseResultMap">
        select * from t_privacy_check where type_id=#{typeId} and terminal_type=#{terminalType} and is_cache=1 and is_show=1 and is_del=0
    </select>

    <select id="findCheckByTypeName" resultMap="BaseResultMap">
        select * from t_privacy_check where type_id = #{typeId} and type_name = #{typeName} and is_del=0
    </select>

    <select id="findAllNoCachePrivacyByTypeId" resultMap="BaseResultMap">
        select * from t_privacy_check where type_id=#{typeId} and terminal_type=#{terminalType} and is_cache=0 and is_del=0 and is_show=1
    </select>
</mapper>