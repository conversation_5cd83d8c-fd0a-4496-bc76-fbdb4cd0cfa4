<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyCategoryMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyCategory">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result property="taskId" column="task_id"/>
        <result property="categoryId" column="category_id"/>
    </resultMap>
    <delete id="deleteByTaskId">
        delete from t_privacy_category where task_id=#{taskId}
    </delete>

    <select id="findByTaskId" resultMap="BaseResultMap">
        select * from t_privacy_category where task_id=#{taskId}
    </select>

    <select id="findCategoryByTaskId" resultType="java.lang.String">
        SELECT b.category_name
        FROM t_privacy_category a
                 LEFT JOIN t_apk_category b ON a.category_id = b.category_id
        WHERE a.task_id = #{taskId}
    </select>
</mapper>