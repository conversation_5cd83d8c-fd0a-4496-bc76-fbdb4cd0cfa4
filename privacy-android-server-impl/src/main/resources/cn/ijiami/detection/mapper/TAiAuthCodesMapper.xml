<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TAiAuthCodesMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TAiAuthCodes">
        <id column="auth_code_id" property="authCodeId" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="BIGINT"/>
        <result column="start_date" property="startDate" jdbcType="TIMESTAMP"/>
        <result column="end_date" property="endDate" jdbcType="TIMESTAMP"/>
        <result column="total_quota" property="totalQuota" jdbcType="INTEGER"/>
        <result column="remaining_quota" property="remainingQuota" jdbcType="INTEGER"/>
        <result column="max_concurrent_calls" property="maxConcurrentCalls" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="BaseColumn">
        auth_code_id, client_id, start_date, end_date, total_quota, remaining_quota, max_concurrent_calls, update_time, create_time, is_delete
    </sql>

    <select id="findByClientId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/>
        FROM t_ai_auth_codes
        WHERE is_delete = 0 AND client_id = #{clientId,jdbcType=BIGINT}
    </select>

    <select id="findByAuthCodeId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/>
        FROM t_ai_auth_codes
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </select>

    <update id="updateRemainingQuota">
        UPDATE t_ai_auth_codes
        SET remaining_quota = #{remainingQuota,jdbcType=INTEGER}
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </update>

    <update id="deleteByAuthCodeId">
        UPDATE t_ai_auth_codes
        SET is_delete = 1
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </update>

</mapper>