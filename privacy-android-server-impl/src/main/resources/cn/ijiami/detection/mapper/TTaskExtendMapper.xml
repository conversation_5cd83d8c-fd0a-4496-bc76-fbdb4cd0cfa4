<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskExtendMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTaskExtend">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <id column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="bussiness_id" property="bussinessId"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="callback_url" property="callbackUrl"/>
        <result column="push_status" property="pushStatus"/>
        <result column="push_count" property="pushCount"/>
        <result column="message" property="message"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="detection_laws" property="detectionLaws"/>
        <result column="model" property="model"/>
    </resultMap>
    
    <resultMap id="BaseResultMapVO"
               type="cn.ijiami.detection.VO.TTaskExtendVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <id column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="bussiness_id" property="bussinessId"/>
        <result column="download_url" property="downloadUrl"/>
        <result column="callback_url" property="callbackUrl"/>
        <result column="push_status" property="pushStatus"/>
        <result column="push_count" property="pushCount"/>
        <result column="message" property="message"/>
        <result column="task_tatus" property="taskTatus"/>
        <result column="dynamic_status" property="dynamicStatus"/>
        <result column="version" property="version"/>
        <result column="detection_laws" property="detectionLaws"/>
        <result column="model" property="model"/>
        <result column="ai_detect_login_status" property="aiDetectLoginStatus"/>
    </resultMap>
    

    <!-- 分页查询任务 -->
    <sql id="Base_sql">
    		ex.*,t.task_tatus,t.dynamic_status
    </sql>

    <select id="findTaskByTaskId" parameterType="long" resultMap="BaseResultMapVO">
        select 
		<include refid="Base_sql"/>
		from  t_task as t, t_task_extend as ex where ex.task_id=t.task_id
 		and ex.task_id=#{taskId} 
 		ORDER BY ex.create_time desc limit 1
    </select>

    <select id="findTaskByTaskIds" resultMap="BaseResultMapVO">
        select
        <include refid="Base_sql"/>
        from  t_task as t, t_task_extend as ex where ex.task_id=t.task_id
        and ex.task_id IN
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId,jdbcType=BIGINT}
        </foreach>
        ORDER BY ex.create_time desc
    </select>

    <select id="waitPushList" resultMap="BaseResultMapVO">
        <!-- select task_id from t_task_extend where push_status=0 or  (push_status=2 and push_count &lt;3) -->
         select e.task_id from t_task_extend as e
		LEFT JOIN t_task as t on e.task_id=t.task_id
		where (e.push_status=0 or  (e.push_status=2 and e.push_count &lt;3)) and t.task_tatus not in(1,2) and t.dynamic_status not in(1,4,10) ORDER BY t.task_id desc
    	limit 40;
    </select>

    <select id="findTaskByBussinessId" resultType="cn.ijiami.detection.entity.TTask">
        select
        tt.task_tatus as taskTatus,
        tt.dynamic_status as dynamicStatus,
        tt.apk_detection_detail_id as apkDetectionDetailId,
        tt.assets_id as assetsId,
        tt.task_id as taskId,
        tt.task_starttime as taskStarttime,
        tt.task_endtime as taskEndtime,
        tt.create_user_id as createUserId,
        tt.terminal_type as terminalType
        from t_task_extend as tte
        join t_task as tt
        on tte.task_id = tt.task_id
        WHERE tte.bussiness_id=#{bussinessId,jdbcType=VARCHAR}
        ORDER BY tte.create_time desc limit 1
    </select>
    
    
    <select id="findTaskByBussinessIdDynamicDesc" resultType="cn.ijiami.detection.entity.TTask">
        select
        tt.task_tatus as taskTatus,
        tt.dynamic_status as dynamicStatus,
        tt.apk_detection_detail_id as apkDetectionDetailId,
        tt.assets_id as assetsId,
        tt.task_id as taskId,
        tt.task_starttime as taskStarttime,
        tt.task_endtime as taskEndtime,
        tt.terminal_type as terminalType
        from t_task_extend as tte
        join t_task as tt
        on tte.task_id = tt.task_id
        WHERE tte.bussiness_id=#{bussinessId,jdbcType=VARCHAR}
        ORDER BY dynamic_status desc, tte.create_time desc limit 1
    </select>
    
    <select id="findPushlawRole" resultType="string">
    	select res.resource_name from tsys_user_role as uro LEFT JOIN tsys_role_resource as rs on uro.role_id=rs.role_id
		LEFT JOIN tsys_resource as res on res.resource_id = rs.resource_id
		where res.resource_name in(164,191,35273) and uro.user_id=${userId}
    </select>
    
</mapper>