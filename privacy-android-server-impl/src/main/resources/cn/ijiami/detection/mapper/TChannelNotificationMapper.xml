<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TChannelNotificationMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TChannelNotification">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="bussiness_subject_id" property="bussinessSubjectId"/>
        <result column="bussiness_subject_name" property="bussinessSubjectName"/>
        <result column="channel_name" property="channelName"/>
        <result column="app_type" property="channelTypeEnum" jdbcType="INTEGER"/>
        <result column="problem" property="problem"/>
        <result column="bulletin_organ" property="bulletinOrgan"/>
        <result column="bulletin_batch_number" property="bulletinBatchNumber"/>
        <result column="bulletin_page_url" property="bulletinPageUrl"/>
        <result column="bulletin_app_time" property="bulletinAppTime"/>
        <result column="package_name" property="packageName"/>
        <result column="package_md5" property="packageMd5"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="base_sql">
        id,name,version,bussiness_subject_id,bussiness_subject_name,channel_name,app_type,problem,bulletin_organ,
            bulletin_batch_number,bulletin_page_url,bulletin_app_time,package_name,package_md5,create_time,update_time
    </sql>

    <select id="findChannelByQuery" resultMap="BaseResultMap">
        select <include refid="base_sql"/> from t_channel_notification
        <if test=" name != null">
            where name like concat('%',#{name},'%')
        </if>
        <if test="channelTypeList != null and channelTypeList.size() > 0 ">
            and app_type in
              <foreach collection="channelTypeList" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
        </if>
        <if test="problemList != null and problemList.size() > 0">
            and problem in
              <foreach collection="problemList" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
        </if>
        <if test="bulletinOrganList != null and bulletinOrganList.size() > 0">
            and bulletin_organ in
              <foreach collection="bulletinOrganList" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
        </if>
        <if test="(ascList != null and ascList.size() > 0) or (descList != null and descList.size() > 0)">
            order by
        </if>
        <if test="(ascList != null and ascList.size() > 0) and (descList == null or descList.size() == 0)">
            <foreach collection="ascList" item="item" separator=",">
                #{item} asc
            </foreach>
        </if>
        <if test="(descList != null and descList.size() > 0) and (ascList == null or ascList.size() == 0)">
            <foreach collection="descList" item="item" separator=",">
                #{item} desc
            </foreach>
        </if>
        <if test="(ascList != null and ascList.size() > 0) and (descList != null and descList.size() > 0)">
            <foreach collection="ascList" item="item" separator=",">
                #{item} asc
            </foreach>
            <foreach collection="descList" item="item" open="," separator=",">
                #{item} desc
            </foreach>
        </if>
    </select>
</mapper>