<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionFilterRegexMapper" >

  <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TActionFilterRegex" >

    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_id" property="actionId" jdbcType="BIGINT" />
    <result column="action_filter_regex" property="actionFilterRegex" jdbcType="VARCHAR" />
    <result column="action_filter_field" property="actionFilterField" jdbcType="BIGINT" />
    <result column="action_filter_mode" property="actionFilterMode" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="BIGINT" />
    <result column="terminal_type" property="terminalType" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
   
  </resultMap>
  
  
  <select id="findFilterList"  parameterType="cn.ijiami.detection.entity.TActionFilterRegex"
            resultMap="BaseResultMap">
        select id,action_id,action_filter_regex,action_filter_field,action_filter_mode,terminal_type from t_action_filter_regex where `status`=1
       <if test="terminalType != null">
        and terminal_type = #{terminalType}
       </if>
  </select>
</mapper>