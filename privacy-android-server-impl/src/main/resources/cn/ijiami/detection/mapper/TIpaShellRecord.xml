<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TIpaShellRecordMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TIpaShellRecord">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="asset_id" property="assetId" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="http_code" property="httpCode" jdbcType="INTEGER"/>
        <result column="http_result" property="httpResult" jdbcType="VARCHAR"/>
        <result column="shell_count" property="shellCount" jdbcType="INTEGER"/>
        <result column="descp" property="descp" jdbcType="VARCHAR"/>
        <result column="request_param" property="requestParam" jdbcType="VARCHAR"/>
        <result column="result_json" property="resultJson" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="app_size" property="appSize" jdbcType="BIGINT"/>
        <result column="progress" property="progress"
                jdbcType="INTEGER"/>
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="is_need_remind" property="isNeedRemind"/>
    </resultMap>

    <sql id="BaseColumn">
        id,asset_id,task_id,status,http_code,http_result,device_id,app_size,shell_count,descp,request_param,result_json,create_time,update_time,start_time,is_need_remind
    </sql>

    <select id="selectOneByAssetId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE asset_id = #{assetId,jdbcType=BIGINT}
    </select>

    <select id="selectOneByTaskId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE task_id = #{taskId,jdbcType=BIGINT}
    </select>

    <select id="selectByStatus" resultType="java.lang.Long">
        SELECT id FROM t_ipa_shell_record WHERE status = #{status,jdbcType=INTEGER} AND shell_count &lt; #{maxShellCount} ORDER BY update_time ASC
    </select>

    <update id="updateStatusById">
         update t_ipa_shell_record set status=#{newStatus}  where id=#{shellId,jdbcType=BIGINT}  and status=#{oldStatus}
    </update>

    <select id="selectAssetByStatus" resultType="java.lang.Long">
        SELECT asset_id FROM t_ipa_shell_record WHERE status = #{status,jdbcType=INTEGER} AND shell_count &lt; #{maxShellCount} ORDER BY update_time ASC
    </select>

    <select id="selectTaskByStatus" resultType="java.lang.Long">
        SELECT task_id FROM t_ipa_shell_record WHERE status = #{status,jdbcType=INTEGER} AND shell_count &lt; #{maxShellCount} ORDER BY update_time ASC
    </select>

    <select id="selectFailRecordByTime" resultMap="BaseResultMap">
        <!-- SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE status = 3 AND update_time &lt; DATE_SUB(NOW(),  INTERVAL '1800'  SECOND); --> <!-- 达梦数据库必须要加单引号 <![CDATA['#{second}']]>#{second} -->
   SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE status = 3 AND start_time &lt; DATE_SUB(NOW(),  INTERVAL '1800'  SECOND);
    </select>

    <select id="selectWaitRecordOverShellCount" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE status = 0 AND shell_count >= #{maxShellCount};
    </select>

    <select id="selectShellRecordByStatus" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_ipa_shell_record WHERE status = #{status,jdbcType=INTEGER} ORDER BY start_time ASC
    </select>

    <select id="selectAllShellRecordByStatus" resultMap="BaseResultMap">
        SELECT
            t1.id,
            t1.asset_id,
            t1.task_id,
            t1.STATUS,
            t1.http_code,
            t1.http_result,
            t1.device_id,
            t1.app_size,
            t1.shell_count,
            t1.descp,
            t1.request_param,
            t1.result_json,
            t1.create_time,
            t1.update_time,
            t1.start_time,
            t1.is_need_remind
        FROM t_ipa_shell_record t1,t_task t2 WHERE t1.task_id = t2.task_id and t2.dynamic_status in (1,7,8,9) and t2.is_delete=0 and t2.detection_type=2 and t1.status in (0,3) ORDER BY t1.start_time ASC
    </select>

    <update id="updateTimeByTaskId">
        update t_ipa_shell_record set start_time=now(),update_time=now(),is_need_remind=0 where task_id=#{taskId}
    </update>
    
    <select id="ipaShellRecordTimeOut" resultMap="BaseResultMap">
    	SELECT 
		    id,
		    task_id,
		    asset_id,
		    http_code,
		    http_result,
		    status,
		    descp,
		    request_param,
		    result_json,
		    shell_count,
		    create_time,
		    update_time,
		    terminal_type,
		    progress,
		    desca,
		    device_id,
		    app_size,
		    start_time,
		    is_need_remind,
		    TIMESTAMPDIFF(MINUTE, start_time, NOW()) AS duration_minutes
		FROM 
		    t_ipa_shell_record
		WHERE 
		    status IN (0, 3)
		    AND TIMESTAMPDIFF(MINUTE, start_time, NOW()) > 30
    </select>
</mapper>