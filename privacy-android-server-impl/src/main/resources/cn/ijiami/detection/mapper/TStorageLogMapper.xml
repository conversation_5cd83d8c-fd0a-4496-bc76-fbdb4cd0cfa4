<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TStorageLogMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TStorageLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="assets_id" jdbcType="BIGINT" property="assetsId"/>
        <result column="association_key" jdbcType="VARCHAR" property="associationKey"/>
        <result column="storage_address" jdbcType="VARCHAR" property="storageAddress"/>
        <result column="file_type" jdbcType="INTEGER" property="fileType"/>
        <result column="storage_type" jdbcType="INTEGER" property="storageType"/>
        <result column="is_delete" jdbcType="BOOLEAN" property="delete"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, assets_id, storage_address, file_type, storage_type, is_delete, create_user_id, gmt_create, gmt_modified, association_key
    </sql>

    <update id="updateByStorageAddress">
        UPDATE t_storage_log
        SET assets_id = #{assetsId,jdbcType=BIGINT},gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},is_delete=0
        WHERE is_delete = 3
        AND storage_address = #{storageAddress,jdbcType=VARCHAR}
        AND create_user_id = #{createUserId,jdbcType=BIGINT}
        AND assets_id IS NULL
    </update>

    <update id="updateByAssociationKey">
        UPDATE t_storage_log
        SET gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},is_delete=0
        WHERE is_delete = 3
        AND association_key = #{associationKey,jdbcType=VARCHAR}
        AND create_user_id = #{createUserId,jdbcType=BIGINT}
    </update>

    <select id="selectUnusedStorage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_storage_log
        WHERE (is_delete in (0, 3) AND assets_id IS NULL AND file_type !=6 AND gmt_create &lt;= date_sub(NOW(), interval '240' MINUTE)) OR (is_delete = 2)
    </select>

    <select id="findNullAssetsIdByAddress" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_storage_log
        WHERE is_delete = 3
        AND assets_id IS NULL
        AND storage_address = #{storageAddress,jdbcType=VARCHAR}
        AND create_user_id = #{createUserId,jdbcType=BIGINT}
    </select>

    <select id="findOneByAssociationKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_storage_log
        WHERE is_delete = 3
        AND association_key = #{associationKey,jdbcType=VARCHAR}
        AND create_user_id = #{createUserId,jdbcType=BIGINT}
        <if test="fileTypeList != null">
            AND file_type IN
            <foreach collection="fileTypeList" item="fileType" index="index"
                     open="(" close=")" separator=",">
                #{fileType}
            </foreach>
        </if>
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="findAllByAddress" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_storage_log
        WHERE storage_address = #{storageAddress,jdbcType=VARCHAR}
    </select>

    <select id="countAssetsIdNotNullByAddress" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_storage_log
        WHERE is_delete = 0
        AND assets_id IS NOT NULL
        AND storage_address = #{storageAddress,jdbcType=VARCHAR}
    </select>

    <select id="countTaskFileByTaskId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_storage_log
        WHERE is_delete = 0
        AND association_key = #{associationKey,jdbcType=VARCHAR}
        AND storage_type=#{storageType,jdbcType=INTEGER}
    </select>


    <update id="markWaitDeleteByStorageAddress">
        UPDATE t_storage_log
        SET is_delete = 2, gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        WHERE is_delete = 0 AND storage_address = #{storageAddress,jdbcType=VARCHAR} AND assets_id = #{assetsId,jdbcType=BIGINT}
    </update>

    <update id="markWaitDeleteByAssociationKey">
        UPDATE t_storage_log
        SET is_delete = 2, gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        WHERE is_delete = 0
        AND association_key = #{associationKey,jdbcType=VARCHAR}
        AND storage_type=#{storageType,jdbcType=INTEGER}
    </update>

    <update id="updateDeleteByStorageAddress">
        UPDATE t_storage_log
        SET is_delete = 1, gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        WHERE is_delete = 0 AND storage_address = #{storageAddress,jdbcType=VARCHAR} AND assets_id = #{assetsId,jdbcType=BIGINT}
    </update>

</mapper>