<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TOdpCompanyProductMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TOdpCompanyProduct">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="product_website" property="productWebsite" jdbcType="VARCHAR"/>
        <result column="product_english_name" property="productEnglishName" jdbcType="VARCHAR"/>
        <result column="product_abbreviation" property="productAbbreviation" jdbcType="VARCHAR"/>
        <result column="create_usename" property="createUsename" jdbcType="VARCHAR"/>
        <result column="update_usename" property="updateUsename" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>