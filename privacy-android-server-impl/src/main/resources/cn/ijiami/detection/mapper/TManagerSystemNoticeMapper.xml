<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TManagerSystemNoticeMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TManagerSystemNotice">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="INTEGER"/>
        <result column="send_status" property="sendStatus" jdbcType="INTEGER"/>
        <result column="validity_period" property="validityPeriod" jdbcType="INTEGER"/>
        <result column="frequency" property="frequency" jdbcType="INTEGER"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="PageResultMap"
               type="cn.ijiami.detection.VO.ManagerSystemNoticeVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="noticeId" jdbcType="BIGINT"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="regards" property="regards" jdbcType="VARCHAR"/>
        <result column="send_status" property="sendStatus" jdbcType="INTEGER"/>
        <result column="validity_period" property="validityPeriod" jdbcType="INTEGER"/>
        <result column="frequency" property="frequency" jdbcType="INTEGER"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <association property="template" javaType="cn.ijiami.detection.VO.SystemNoticeTemplateVO">
            <result column="template_id"  property="id" jdbcType="BIGINT" />
            <result column="template_name"  property="name" jdbcType="VARCHAR" />
        </association>
    </resultMap>

    <sql id="BaseColumn">
        n.id,n.content,n.regards,n.send_status,n.validity_period,n.frequency,n.`type`,n.`priority`,
        IFNULL(u.user_name, '未知') AS create_user_name,n.create_time,t.id AS template_id,t.`name` AS template_name
    </sql>

    <select id="findByPage" resultMap="PageResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_manager_system_notice AS n
        LEFT JOIN t_system_notice_template AS t ON t.id=n.template_id
        LEFT JOIN tsys_user AS u ON u.user_id=n.create_user_id
        WHERE n.id > 0
        <if test="type != null">
            AND n.type = #{type,jdbcType=INTEGER}
        </if>
        <if test="templateId != null">
            AND n.template_id = #{templateId,jdbcType=BIGINT}
        </if>
        <if test="validityPeriod != null">
            AND n.validity_period = #{validityPeriod,jdbcType=INTEGER}
        </if>
        <if test="sendStatus != null">
            AND n.send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
        ORDER BY n.create_time
        <if test="sortOrder == 1">
            ASC
        </if>
        <if test="sortOrder == 2">
            DESC
        </if>
    </select>

</mapper>