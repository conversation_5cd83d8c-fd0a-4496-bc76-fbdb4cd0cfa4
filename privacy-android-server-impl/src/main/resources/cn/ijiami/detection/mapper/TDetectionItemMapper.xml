<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
        namespace="cn.ijiami.detection.mapper.TDetectionItemMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TDetectionItem">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="purpose" property="purpose" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="VARCHAR"/>
        <result column="harm" property="harm" jdbcType="VARCHAR"/>
        <result column="solution" property="solution"
                jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="detection_type" property="detectionType"
                jdbcType="INTEGER"/>
        <result column="detection_item_type"
                property="detectionItemType" jdbcType="INTEGER"/>
        <result column="item_no" property="itemNo" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="detection_item_image"
                property="detectionItemImage" jdbcType="LONGVARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="BaseResultItemMap"
               type="cn.ijiami.detection.VO.DetectionItemVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="purpose" property="purpose" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="harm" property="harm" jdbcType="VARCHAR"/>
        <result column="solution" property="solution"
                jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="detection_type" property="detectionType"
                jdbcType="INTEGER"/>
        <result column="detection_item_type"
                property="detectionItemType" jdbcType="INTEGER"/>
        <result column="item_no" property="itemNo" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="detection_item_image"
                property="detectionItemImage" jdbcType="LONGVARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>
    <!-- 资产数据统计返回值 -->
    <resultMap type="HashMap" id="countResultMap">
        <result column="android_size" property="android_size"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="ios_size" property="ios_size"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="ios_size" property="wechat_size"
                jdbcType="INTEGER" javaType="INTEGER"/>
        <result column="all_size" property="all_size"
                jdbcType="INTEGER" javaType="INTEGER"/>
    </resultMap>

    <sql id="Base_Detection_Item">
            di.id,
            di.type_id,
            di.name,
            di.purpose,
            di.grade,
            di.harm,
            di.solution,
            di.terminal_type,
            di.detection_type,
            di.detection_item_type,
            di.item_no,
            di.order_no,
            di.platform,
            di.detection_item_image,
            di.create_user_id,
            di.update_user_id,
            di.create_time,
            di.update_time
    </sql>
    <sql id="Base_Column_List_Item">
            t.id,
            td.type_name as detectionItemName,
            t.type_id,
            t.name,
            t.purpose,
            t.grade,
            t.harm,
            t.solution,
            t.terminal_type,
            t.detection_type,
            t.detection_item_type,
            t.item_no,
            t.order_no,
            t.platform,
            t.detection_item_image,
            t.create_user_id,
            t.update_user_id,
            t.create_time,
            t.update_time
    </sql>
    <!-- 用户检测项 -->
    <sql id="Base_UserDetection_sql">
            t.id,
            td.type_name as detectionItemName,
            t.type_id,
            t.name,
            t.purpose,
            t.grade,
            t.harm,
            t.solution,
            t.terminal_type,
            t.detection_type,
            t.detection_item_type,
            t.order_no,
            t.platform,
            t.detection_item_image,
            t.create_user_id,
            t.update_user_id,
            t.create_time,
            t.update_time,
            ts.signature
    </sql>
    <resultMap id="BaseUserDetectionResultMap"
               type="cn.ijiami.detection.VO.DetectionItemVO">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_id" property="typeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="purpose" property="purpose" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="harm" property="harm" jdbcType="VARCHAR"/>
        <result column="solution" property="solution"
                jdbcType="VARCHAR"/>
        <result column="terminal_type" property="terminalType"
                jdbcType="INTEGER"/>
        <result column="detection_type" property="detectionType"
                jdbcType="INTEGER"/>
        <result column="detection_item_type"
                property="detectionItemType" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="INTEGER"/>
        <result column="platform" property="platform"
                jdbcType="VARCHAR"/>
        <result column="detection_item_image"
                property="detectionItemImage" jdbcType="LONGVARCHAR"/>
        <result column="create_user_id" property="createUserId"
                jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId"
                jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="signature" property="signaTure"
                jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectDetectionItemByQuery"
            parameterType="cn.ijiami.detection.query.TemplateDetectionItemQuery"
            resultMap="BaseResultItemMap">
        SELECT
        <include refid="Base_Column_List_Item"/>
        FROM t_template_detection_item tdi left join
        t_detection_item
        t on
        tdi.detection_item_id = t.id left join t_detection_item_type td
        on
        t.type_id = td.id where tdi.template_id =
        #{templateId}
        <if test="detectionTypeId != null">
            and t.type_id = #{detectionTypeId}
        </if>
        order by t.type_id asc
    </select>
    <select id="selectDetectionItemListVO"
            parameterType="cn.ijiami.detection.entity.TDetectionItem"
            resultMap="BaseResultItemMap">
        SELECT
        <include refid="Base_Column_List_Item"/>
        FROM t_detection_item t LEFT JOIN t_detection_item_type td
        on(t.type_id =td.id) where 1=1 and t.grade != 0
        <if test="terminalType != null ">
            and t.terminal_type = #{terminalType}
        </if>
        <if test="name != null and name != ''">
            and t.name LIKE CONCAT('%',#{name},'%')
        </if>
    </select>

    <!-- 统计android、ios 检测项数量 -->
    <select id="detectionItemCountSize"
            parameterType="cn.ijiami.detection.entity.TDetectionItem"
            resultMap="countResultMap">
        select
        sum( case
        when t_detection_item.terminal_type = 1 then 1
        else 0
        end
        ) as
        android_size,
        sum( case
        when t_detection_item.terminal_type = 2 then 1
        else 0
        end
        ) as ios_size,
        sum( case
        when t_detection_item.terminal_type = 3 then 1
        else 0
        end
        ) as wechat_size,
        count(t_detection_item.id) as all_size
        from t_detection_item where 1=1
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="createUserId != null">
            and create_user_id = #{createUserId}
        </if>
    </select>
    <select id="selectDetectionItemByPage"
            parameterType="cn.ijiami.detection.entity.TDetectionItem"
            resultMap="BaseResultItemMap">
        SELECT
        <include refid="Base_Column_List_Item"/>
        FROM t_detection_item t LEFT JOIN t_detection_item_type td
        on(t.type_id =td.id) where 1=1
        <if test="terminalType != null ">
            and t.terminal_type = #{terminalType}
        </if>
        <if test="typeId != null">
            and t.type_id =#{typeId}
        </if>
        <if test="id != null">
            and t.id = #{id}
        </if>
        <if test="itemNo != null">
            and t.item_no = #{itemNo}
        </if>
    </select>

    <!-- 根据模板id查询用户检测项 -->
    <select id="setectUserDetectionByTemplateId"
            parameterType="java.lang.Long" resultMap="BaseUserDetectionResultMap">
        select
        <include refid="Base_UserDetection_sql"/>
        from t_detection_item t LEFT JOIN t_detection_item_type td
        on t.type_id
        =td.id LEFT JOIN t_detection_item_signature ts
        on ts.item_id = t.id
        LEFT JOIN t_template_detection_item ti
        on t.id = ti.detection_item_id
        where ti.template_id =#{templateId}
    </select>

    <select id="findByTerminalType" resultMap="BaseResultMap">
        select
        <include refid="Base_Detection_Item"/>
        from t_detection_item di
        where di.terminal_type = #{terminalType}
    </select>

    <select id="selectByItemNo"
            resultMap="BaseResultMap">
        SELECT * FROM t_detection_item WHERE item_no = #{itemNo} and terminal_type = #{terminalType}
    </select>
    
    <select id="selectByItemNoVO"
            resultMap="BaseUserDetectionResultMap">
         SELECT
        <include refid="Base_Column_List_Item"/>
        FROM t_detection_item t LEFT JOIN t_detection_item_type td
        on(t.type_id =td.id) where 1=1 and t.grade != 0 and
        t.item_no = #{itemNo} and t.terminal_type = #{terminalType}
    </select>
    
    
    <select id="findDetectionItemInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Detection_Item"/>
        from t_detection_item di
        where 1 = 1
        <if test="terminalType != null">
            and di.terminal_type = #{terminalType}
        </if>
        <if test="itemNo != null">
            and di.item_no = #{itemNo}
        </if> 
        limit 1
    </select>
</mapper>