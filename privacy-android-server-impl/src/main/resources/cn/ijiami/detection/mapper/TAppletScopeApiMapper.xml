<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TAppletScopeApiMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TAppletScopeApi">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="api" property="api" jdbcType="VARCHAR"/>
        <result column="scope" property="scope" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findInApi" resultType="java.lang.String">
        SELECT DISTINCT(`scope`) FROM t_applet_scope_api WHERE api in
        <foreach collection="apiList" item="api" open="(" separator="," close=")">
            #{api}
        </foreach>
    </select>
</mapper>