<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkLibraryClassMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSdkLibraryClass" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="sdk_id" property="sdkId" jdbcType="BIGINT" />
        <result column="class" property="clazz" jdbcType="VARCHAR" />
        <result column="function" property="function" jdbcType="VARCHAR" />
        <result column="created_user_id" property="createdUserId" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findBySdkId" resultMap="BaseResultMap">
        SELECT
        id,
        sdk_id,
        class,
        function,
        created_user_id,
        update_user_id,
        created_time,
        update_time
        FROM t_sdk_library_class
        WHERE sdk_id=#{sdkId}
    </select>

</mapper>