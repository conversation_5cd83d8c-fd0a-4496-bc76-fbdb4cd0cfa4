<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkLibraryUpdateMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSdkLibraryUpdate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="identification" property="identification" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findByIdForUpdate" resultMap="BaseResultMap">
        SELECT id,identification,version,update_time
        FROM t_sdk_library_update
        WHERE id = #{id} FOR UPDATE
    </select>
</mapper>