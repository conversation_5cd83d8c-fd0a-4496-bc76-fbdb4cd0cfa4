<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TReportStoreMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TReportStore">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name"/>
        <result column="document_id" property="documentId"/>
        <result column="path" property="path"/>
        <result column="type" property="type"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="terminal_type" property="terminalType"/>
    </resultMap>
</mapper>