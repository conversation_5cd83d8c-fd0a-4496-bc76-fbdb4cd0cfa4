<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkWhitelistRuleMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSdkWhitelistRule" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="pattern" property="pattern" jdbcType="VARCHAR" />
        <result column="package_name" property="packageName" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findPackageNameByType" resultType="java.lang.String">
        SELECT package_name FROM t_sdk_whitelist_rule where type=#{type}
    </select>

    <select id="findPatternByType" resultType="java.lang.String">
        SELECT pattern FROM t_sdk_whitelist_rule where type=#{type}
    </select>
</mapper>