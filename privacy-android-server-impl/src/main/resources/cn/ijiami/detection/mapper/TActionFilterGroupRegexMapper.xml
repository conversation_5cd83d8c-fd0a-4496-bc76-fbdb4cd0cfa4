<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionFilterGroupRegexMapper" >

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TActionFilterGroupRegex" >

        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="action_id" property="actionId" jdbcType="BIGINT" />
        <result column="group_id" property="groupId" jdbcType="BIGINT" />
        <result column="action_filter_regex" property="actionFilterRegex" jdbcType="VARCHAR" />
        <result column="action_filter_field" property="actionFilterField" jdbcType="BIGINT" />
        <result column="action_filter_mode" property="actionFilterMode" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="BIGINT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="action_type" property="actionType" jdbcType="BIGINT" />
    </resultMap>


    <select id="findUserFilterList"  parameterType="cn.ijiami.detection.entity.TActionFilterGroupRegex"
            resultMap="BaseResultMap">
        SELECT r.id,r.group_id,r.action_id,r.action_filter_regex,r.action_filter_field,r.action_filter_mode,r.action_type
        from t_action_filter_group AS g JOIN t_action_filter_group_regex AS r ON g.id=r.group_id
        WHERE g.`create_user_id`=#{createUserId} AND g.`category`=2 AND g.`terminal_type`=#{terminalType}
    </select>

    <select id="findUserFilterListByGroupId"  parameterType="cn.ijiami.detection.entity.TActionFilterGroupRegex"
            resultMap="BaseResultMap">
        SELECT r.id,r.group_id,r.action_id,r.action_filter_regex,r.action_filter_field,r.action_filter_mode,r.action_type
        FROM t_action_filter_group AS g JOIN t_action_filter_group_regex AS r ON g.id=r.group_id
        WHERE g.`id`=#{groupId} AND g.`category`=2
    </select>

    <select id="findMainFilterList"  parameterType="cn.ijiami.detection.entity.TActionFilterGroupRegex"
            resultMap="BaseResultMap">
        SELECT r.id,r.group_id,r.action_id,r.action_filter_regex,r.action_filter_field,r.action_filter_mode,r.action_type
        FROM t_action_filter_group AS g JOIN t_action_filter_group_regex AS r ON g.id=r.group_id
        WHERE g.`status`=2 AND g.`category`=1 AND g.`terminal_type`=#{terminalType}
    </select>
</mapper>