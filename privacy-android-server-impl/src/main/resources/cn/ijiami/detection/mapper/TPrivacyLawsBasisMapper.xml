<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyLawsBasisMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyLawsBasis">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="item_no" jdbcType="VARCHAR" property="itemNo"/>
        <result column="action_id" jdbcType="BIGINT" property="actionId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="category" jdbcType="INTEGER"  property="category"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            item_no,
            action_id,
            category,
            create_time,
            update_time
    </sql>

    <select id="selectItemNoInfo" resultMap="BaseResultMap">
        SELECT tplr.item_no,
               tplb.`category`
        FROM t_privacy_laws_regulations AS tplr
                 LEFT JOIN t_privacy_laws_basis_v3 AS tplb on tplr.item_no = tplb.item_no
        WHERE tplr.is_check = 1
    </select>

    <select id="selectItemNoInfoByLawId" resultMap="BaseResultMap">
        SELECT
        tplr.id,
        tplr.item_no,
        tplb.action_id,
        tplb.`category`
        FROM t_privacy_laws_regulations AS tplr
        LEFT JOIN t_privacy_laws_basis_v3 AS tplb on tplr.item_no = tplb.item_no
        WHERE tplr.is_check = 1 AND tplr.law_id=#{lawId}
    </select>
    
    <select id="findByItemNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from t_privacy_laws_basis_v3
        where item_no=#{itemNo}
    </select>
</mapper>