<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TEmailSendMapper">
	<resultMap id="BaseResultMap"
		type="cn.ijiami.detection.entity.TEmailSend">
		<!-- WARNING - @mbg.generated -->
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="theme_type" property="themeType"
			jdbcType="INTEGER" />
		<result column="is_send" property="isSend" jdbcType="INTEGER" />
		<result column="email_address" property="emailAddress"
			jdbcType="VARCHAR" />
		<result column="created_user_id" property="createdUserId"
			jdbcType="BIGINT" />
		<result column="created_time" property="createdTime"
			jdbcType="TIMESTAMP" />
		<result column="updated_user_id" property="updatedUserId"
			jdbcType="BIGINT" />
		<result column="update_time" property="updateTime"
			jdbcType="TIMESTAMP" />
		<result column="mail_content" property="mailContent"
			jdbcType="LONGVARCHAR" />
	</resultMap>

	<sql id="selectEmail_cloumn">
		id,theme_type,is_send,email_address,created_user_id,created_time,updated_user_id,update_time,mail_content
	</sql>
	<select id="findEmailSendList" resultMap="BaseResultMap"
		parameterType="cn.ijiami.detection.entity.TEmailSend">
		select
		<include refid="selectEmail_cloumn" />
		from t_email_send where 1=1
		<if test="themeType != null">
			and theme_type = #{themeType}
		</if>
		<if test="isSend != null">
			and is_send = #{isSend}
		</if>
		<if test="mailContent != null">
			and mail_content = #{mailContent}
		</if>
	</select>
</mapper>