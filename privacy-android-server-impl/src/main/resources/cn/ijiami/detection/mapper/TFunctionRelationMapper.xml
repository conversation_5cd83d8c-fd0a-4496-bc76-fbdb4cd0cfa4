<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.FunctionRelationMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TFunctionRelation">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId"/>
        <result column="function_id" property="functionId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_SQL">
        id,
        category_id,
        function_id,
        create_time,
        update_time
    </sql>

    <select id="getFunctionRelationByFunctionId" resultMap="BaseResultMap">
        select <include refid="Base_SQL"/>
        from t_function_relation
        <where>
            1=1
            <if test="functionIds !=null ">
                and function_id in
                <foreach collection="functionIds" item="funcitonId" open="(" separator="," close=")">
                    #{funcitonId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
</mapper>