<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyOutsideAddress">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="ip" property="ip"/>
        <result column="host" property="host"/>
        <result column="port" property="port"/>
        <result column="cookie" property="cookie"/>
        <result column="protocol" property="protocol"/>
        <result column="address" property="address"/>
        <result column="outside" property="outside"/>
        <result column="counter" property="counter"/>
        <result column="executor_type" property="executorType"/>
        <result column="executor" property="executor"/>
        <result column="stack_info" property="stackInfo"/>
        <result column="details_data" property="detailsData"/>
        <result column="response_data" property="responseData"/>
        <result column="str_time" property="strTime"/>
        <result column="behavior_stage" property="behaviorStage"/>
        <result column="sdk_ids" property="sdkIds"/>
    </resultMap>

    <resultMap id="TPrivacyOutsideAddressBaseMap" type="cn.ijiami.detection.entity.TPrivacyOutsideAddress" extends="BaseResultMap">
        <result column="sdk_id" property="sdkId"/>
        <result column="action_time" property="actionTime"/>
        <result column="outsideString" property="outsideString"/>
        <result column="request_method" property="requestMethod"/>
        <result column="url" property="url"/>
        <result column="package_name" property="packageName"/>
        <result column="cookie_mark" property="cookieMark"/>
    </resultMap>

    <sql id="Base_Column_SQL">
        id,
        task_id,
        ip,
        host,
        port,
        cookie,
        protocol,
        address,
        counter,
        outside,
        executor_type,
        executor,
        stack_info,
        details_data,
        response_data,
        str_time,
        behavior_stage
    </sql>

    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT id,
        task_id,
        ip,
        `host`,
        port,
        cookie,
        protocol,
        address,
        outside,
        stack_info,
        details_data,
        response_data,
        count(*) AS counter,
        sdk_ids
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            and behavior_stage = #{behaviorStage}
        </if>
        GROUP BY ip,`host`
        ORDER BY outside DESC,
                 counter DESC
    </select>

    <select id="countByOutside" resultType="cn.ijiami.detection.VO.CountOutsideTypeVO">
        select taskId ,type, COUNT(taskId) as counter,outside FROM
        (SELECT task_id   AS taskId,
               CASE outside
                   WHEN 0 THEN '境内IP'
                   ELSE '境外IP'
                   END   AS type,
               outside
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        GROUP BY ip,`host`,outside
        ORDER BY outside DESC)
        AS tmp GROUP BY type
    </select>

    <select id="findByTaskIdAndOutside" resultType="cn.ijiami.detection.entity.TPrivacyOutsideAddress">
        SELECT
        task_id AS taskId,
        ip,
        `host`,
        port,
        cookie,
        protocol,
        address,
        outside,
        executor_type AS executorType,
        stack_info AS stackInfo,
        SUM(case when executor_type = 1 then 1 else 0 end) as appCount,
        SUM(case when executor_type = 2 then 1 else 0 end) as sdkCount,
        COUNT(*) AS counter,
        package_name AS packageName,
        url
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        AND outside = #{outside,jdbcType=INTEGER}
        GROUP BY ip,`host`
        ORDER BY counter DESC
    </select>

    <select id="findByTaskIdAndOutsideIos" resultMap="TPrivacyOutsideAddressBaseMap">
        SELECT id,
               task_id,
               ip,
               `host`,
        address,
        port,
        cookie,
        protocol,
        outside,
        executor_type,
        executor,
        stack_info,
        details_data,
        response_data,
        count(ip) as counter,
        package_name,
        url
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        AND outside = #{outside,jdbcType=INTEGER}
        GROUP BY ip,`host`
        ORDER BY counter DESC
    </select>
    <select id="findByTaskIdAndOutsideStackInfo" resultMap="TPrivacyOutsideAddressBaseMap">
        SELECT id,
               task_id,
               ip,
               `host`,
        address,
        port,
        cookie,
        protocol,
        outside,
        executor_type,
        executor,
        stack_info,
        details_data,
        response_data,
        str_time,
        package_name,
        cookie_mark,
        url
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        <if test="host != null and host != ''">
            AND host = #{host}
        </if>
        <if test="ip != null and ip != ''">
            AND ip = #{ip}
        </if>
    </select>

    <select id="getOutSideAddressExecutor" resultType="java.lang.String">
        SELECT DISTINCT(executor) FROM t_privacy_outside_address WHERE task_id = #{taskId,jdbcType=BIGINT}
    </select>


    <delete id="deleteByTaskId">
        delete from t_privacy_outside_address where task_id=#{taskId,jdbcType=BIGINT}
    </delete>

    <select id="findOutsideDataByTaskId" resultMap="TPrivacyOutsideAddressBaseMap" >
        SELECT * FROM (
            SELECT
                id,
                task_id,
                port,
                cookie,
                protocol,
                ip,
                host,
                address,
                counter,
                outside,
        executor_type,
        executor,
        sdk_id,
        stack_info,
        details_data,
        response_data,
        str_time,
        behavior_stage,
        action_time,
        sdk_ids,
        <choose>
            <when test="terminalType != null and (terminalType == 4 or terminalType == 5)">
                (case executor_type when 1 then '小程序' when 2 then 'SDK' end) AS executorTypeString,
            </when>
            <otherwise>
                (case executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
            </otherwise>
        </choose>
        (case outside when 0 then '境内' when 1 then '境外' end) AS outsideString,
        request_method,
        url,
        package_name,
        cookie_mark
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="behaviorStage != 0 and behaviorStage != null">
            AND behavior_stage = #{behaviorStage}
        </if>
        <if test="cookieMark != null">
            AND cookie_mark = #{cookieMark}
        </if>
        ) a
        <where>
            1=1
            <if test="executors != null and executors != '' ">
                AND (
                <foreach collection="executors.split(';')" item="executor" index="num">
                    <if test="executors.split(';').length > num+1">
                        a.executor like CONCAT('%',#{executor},'%') or
                    </if>
                    <if test="executors.split(';').length==num+1">
                        a.executor like CONCAT('%',#{executor},'%')
                    </if>
                </foreach>
                )
            </if>
            <if test="executorTypeString != null and executorTypeString != '' ">
                AND a.executorTypeString in
                <foreach collection="executorTypeString.split(',')" item="executorType" open="(" separator="," close=")">
                    #{executorType}
                </foreach>
            </if>
            <if test="outsideString != null and outsideString != '' ">
                AND a.outsideString in
                <foreach collection="outsideString.split(',')" item="outside" open="(" separator="," close=")">
                    #{outside}
                </foreach>
            </if>
            <if test="protocol != null and protocol != '' ">
                AND a.protocol in
                <foreach collection="protocol.split(',')" item="p" open="(" separator="," close=")">
                    #{p}
                </foreach>
            </if>

            <if test="behaviorStage != 0 and behaviorStage != null">
                AND a.behavior_stage = #{behaviorStage}
            </if>
            AND a.task_id = #{taskId,jdbcType=BIGINT}
        </where>

        <if test="sortType == null or sortType == 0">
            order by a.action_time desc
        </if>
        <if test="sortType != null and sortType != 0">
            order by
            <if test="sortType == 1">
                a.action_time
            </if>
            <if test="sortType == 2">
                a.executorTypeString
            </if>
            <if test="sortType == 3">
                a.executor
            </if>
            <if test="sortType == 6">
                a.outsideString
            </if>
            <if test="sortType == 7">
                a.protocol
            </if>
            <if test="sortOrder == 1">
                asc
            </if>
            <if test="sortOrder == 2">
                desc
            </if>
        </if>

    </select>

    <!--通信行为触发主体查询-->
    <select id="getBehaviorNoun" resultType="java.lang.String">
        select DISTINCT (executor)
        from t_privacy_outside_address
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage !=null and behaviorStage!=0 ">
            and behavior_stage=#{behaviorStage}
        </if>
        and executor_type=2
        and (executor is not null and executor &lt;&gt;'')
    </select>

    <!--通信行为协议类型查询-->
    <select id="getBehaviorNounOfProtocol" resultType="java.lang.String">
        select DISTINCT (protocol)
        from t_privacy_outside_address
        where task_id=#{taskId,jdbcType=BIGINT}
        <if test="behaviorStage !=null and behaviorStage!=0 ">
            and behavior_stage=#{behaviorStage}
        </if>
        and (protocol is not null and protocol &lt;&gt;'')
    </select>
    
    
    <select id="findByTaskIdAndOutsideAndSdkId" resultType="cn.ijiami.detection.entity.TPrivacyOutsideAddress">
        SELECT
        task_id AS taskId,
        ip,
        `host`,
        port,
        cookie,
        protocol,
        address,
        outside,
        executor_type AS executorType,
        stack_info AS stackInfo,
        SUM(case when executor_type = 1 then 1 else 0 end) as appCount,
        SUM(case when executor_type = 2 then 1 else 0 end) as sdkCount,
        COUNT(*) AS counter,
        sdk_ids as sdkIds
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="sdkId != null and sdkId != '' ">
            AND sdk_ids like CONCAT('%',#{sdkId,jdbcType=VARCHAR},'%')
        </if>
        <if test="outside != null">
     	  	AND outside = #{outside,jdbcType=INTEGER}
        </if>
        GROUP BY ip,`host`
        ORDER BY counter DESC
    </select>
    
     <select id="findByTaskIdAndOutsideAndSdkName" resultType="cn.ijiami.detection.entity.TPrivacyOutsideAddress">
        SELECT
        task_id AS taskId,
        ip,
        `host`,
        port,
        cookie,
        protocol,
        executor_type,
        executor,
        address,
        outside,
        executor_type AS executorType,
        stack_info AS stackInfo,
        SUM(case when executor_type = 1 then 1 else 0 end) as appCount,
        SUM(case when executor_type = 2 then 1 else 0 end) as sdkCount,
        COUNT(*) AS counter,
        sdk_ids as sdkIds
        FROM t_privacy_outside_address
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        <if test="sdkName != null and sdkName != '' ">
            AND executor like CONCAT('%',#{sdkName,jdbcType=VARCHAR},'%')
        </if>
        <if test="outside != null">
     	  	AND outside = #{outside,jdbcType=INTEGER}
        </if>
        GROUP BY ip,`host`
        ORDER BY counter DESC
    </select>

    <select id="selectDetailDataByOutsideId" resultMap="TPrivacyOutsideAddressBaseMap">
        SELECT
                id,
                task_id,
                port,
                cookie,
                protocol,
                ip,
                host,
                address,
                counter,
                outside,
                executor_type,
                executor,
                sdk_id,
                stack_info,
                details_data,
                response_data,
                str_time,
                behavior_stage,
                action_time,
                sdk_ids,
                (case executor_type when 1 then 'APP' when 2 then 'SDK' end) AS executorTypeString,
                (case outside when 0 then '境内' when 1 then '境外' end) AS outsideString,
                request_method,
                url,
                package_name,
                cookie_mark
            FROM t_privacy_outside_address
            WHERE id = #{id,jdbcType=BIGINT}
    </select>
</mapper>