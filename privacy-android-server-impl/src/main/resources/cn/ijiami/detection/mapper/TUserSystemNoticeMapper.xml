<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TUserSystemNoticeMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TUserSystemNotice">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="manager_system_notice_id" property="managerSystemNoticeId" jdbcType="BIGINT"/>
        <result column="read_status" property="readStatus" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="read_time" property="readTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="PageResultMap"
               type="cn.ijiami.detection.VO.UserSystemNoticeVO">
        <!-- WARNING - @mbg.generated -->
        <id column="notice_id" property="noticeId" jdbcType="BIGINT"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="regards" property="regards" jdbcType="VARCHAR"/>
        <result column="read_status" property="readStatus" jdbcType="INTEGER"/>
        <result column="validity_period" property="validityPeriod" jdbcType="INTEGER"/>
        <result column="frequency" property="frequency" jdbcType="INTEGER"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="read_time" property="readTime" jdbcType="TIMESTAMP"/>
        <association property="template" javaType="cn.ijiami.detection.VO.SystemNoticeTemplateVO">
            <result column="template_id"  property="id" jdbcType="BIGINT" />
            <result column="template_name"  property="name" jdbcType="VARCHAR" />
        </association>
    </resultMap>

    <sql id="BaseColumn">
        mn.id AS notice_id,un.read_status,un.read_time,mn.content,mn.regards,mn.validity_period,mn.frequency,mn.`type`,mn.`priority`,
        IFNULL(u.user_name, '未知') AS create_user_name,mn.create_time,un.read_time,t.id AS template_id, t.`name` AS template_name
    </sql>

    <select id="findByPage" resultMap="PageResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_user_system_notice AS un
        INNER JOIN t_manager_system_notice AS mn ON un.manager_system_notice_id=mn.id
        INNER JOIN t_system_notice_template AS t ON t.id=mn.template_id
        LEFT JOIN tsys_user AS u ON u.user_id=mn.create_user_id
        WHERE un.user_id=#{userId,jdbcType=BIGINT}
        <if test="sendStatus != null">
            AND mn.send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
        <if test="readStatus != null">
            AND un.read_status = #{readStatus,jdbcType=INTEGER}
        </if>
        ORDER BY un.create_time DESC
    </select>

    <select id="findGteCreateTime" resultMap="PageResultMap">
        SELECT <include refid="BaseColumn"/> FROM t_user_system_notice AS un
        INNER JOIN t_manager_system_notice AS mn ON un.manager_system_notice_id=mn.id
        INNER JOIN t_system_notice_template AS t ON t.id=mn.template_id
        LEFT JOIN tsys_user AS u ON u.user_id=mn.create_user_id
        WHERE un.create_time >= #{createTime,jdbcType=TIMESTAMP} AND un.user_id=#{userId,jdbcType=BIGINT}
        <if test="sendStatus != null">
            AND mn.send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
        ORDER BY un.create_time DESC
    </select>

</mapper>