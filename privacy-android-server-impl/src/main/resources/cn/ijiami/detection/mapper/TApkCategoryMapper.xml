<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TApkCategoryMapper" >
  <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TApkCategory" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="category_id" property="categoryId" jdbcType="BIGINT" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="update_user_id" property="updateUserId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <select id="apkCategoryNames"  resultType="java.lang.String">
  		select category_name from t_apk_category where category_id in
  		<foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
  </select>
</mapper>