<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionPermissionMapper">
	<resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TActionPermission">
		<result property="actionId" column="action_id"/>
		<result property="actionName" column="action_name"/>
		<result property="permissionRemark" column="permission_remark"/>
		<result property="permissionCode" column="permission_code"/>
		<result property="permissionName" column="permission_name"/>
	</resultMap>


	<select id="findCodeInName" resultType="java.lang.String">
		SELECT permission_code FROM t_action_permission
		WHERE permission_name IN
		<foreach collection="nameList" item="name" index="index"
				 open="(" close=")" separator=",">
			#{name}
		</foreach>
		GROUP BY permission_code
	</select>
</mapper>