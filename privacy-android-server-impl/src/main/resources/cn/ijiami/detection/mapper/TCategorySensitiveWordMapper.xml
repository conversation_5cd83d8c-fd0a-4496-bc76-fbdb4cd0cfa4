<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TCategorySensitiveWordMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TCategorySensitiveWord">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="sensitive_id" property="sensitiveId"/>
    </resultMap>

    <select id="findSensitiveIdsByCategoryIds" resultType="java.lang.Long">
        select category_id from t_category_sensitive_word
        <where>
            <if test="categoryIds != null and categoryIds.size() != 0">
                category_id in
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
</mapper>