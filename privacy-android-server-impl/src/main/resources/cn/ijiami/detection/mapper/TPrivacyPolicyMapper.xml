<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyPolicyMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TPrivacyPolicy">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="privacyId" column="privacy_id"/>
        <result property="result" column="result"/>
        <result property="suggestion" column="suggestion"/>
        <result property="type" column="type"/>
    </resultMap>

    <resultMap id="privacyPolicyVo" type="cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyVO">
        <result property="result" column="result"/>
        <result property="suggestion" column="suggestion"/>
    </resultMap>
    <delete id="deleteByTaskIdAndType">
        delete from t_privacy_policy
        where task_id = #{taskId,jdbcType=BIGINT} and type = #{type,jdbcType=BIGINT}
    </delete>

    <select id="findByPrivacyIdAndTaskId" resultMap="privacyPolicyVo">
        select result, suggestion
        from t_privacy_policy
        where privacy_id = #{privacyId,jdbcType=BIGINT}
        and task_id = #{taskId,jdbcType=BIGINT} limit 1
    </select>

    <select id="selectPrivacyPolicy" resultType="cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO">
        SELECT
        a.privacy_id AS id,
        b.type_name AS typeName,
        b.check_name AS checkName,
        a.result,
        a.suggestion,
        a.type,
        b.test_result AS testResult
        FROM
        t_privacy_policy a
        LEFT JOIN t_privacy_check b ON a.privacy_id = b.id
        LEFT JOIN t_privacy_policy_type c ON b.type_id = c.id
        WHERE
        a.task_id = #{taskId}
        and c.terminal_type = #{terminalType}
        <if test="type != null">
            and a.type=#{type,jdbcType=INTEGER}
        </if>
    </select>

    <select id="distinctTypeByTaskId" resultType="java.lang.Integer">
        SELECT DISTINCT type FROM t_privacy_policy WHERE task_id=#{taskId}
    </select>

    <select id="countLawByTaskId" resultType="cn.ijiami.detection.entity.TPrivacyPolicyType">
        SELECT
        b.id,
        a.type,
        b.law_name AS lawName
        FROM
        t_privacy_policy a
        LEFT JOIN t_privacy_policy_type b ON a.type = b.type
        WHERE
        a.task_id = #{taskId,jdbcType=BIGINT}
        and b.terminal_type = #{terminalType}
        GROUP BY
        type
    </select>

    <select id="countLawByTaskIdAndType" resultType="cn.ijiami.detection.entity.TPrivacyPolicyType">
        SELECT
        a.type,
        b.law_name AS lawName
        FROM
        t_privacy_policy a
        LEFT JOIN t_privacy_policy_type b ON a.type = b.type
        WHERE
        a.task_id = #{taskId,jdbcType=BIGINT} AND a.type=#{type,jdbcType=INTEGER}
        AND b.terminal_type=#{terminalType}
        GROUP BY
        type
    </select>

    <select id="countLawByDocumentId" resultType="cn.ijiami.detection.entity.TPrivacyPolicyType">
        SELECT
        a.type,
        b.law_name AS lawName
        FROM
        t_task t
        LEFT JOIN t_privacy_policy a ON t.task_id = a.task_id
        LEFT JOIN t_privacy_policy_type b ON a.type = b.type
        WHERE
        t.apk_detection_detail_id = #{documentId,jdbcType=VARCHAR}
        GROUP BY
        type
    </select>
    <select id="findByTypeVo" resultType="cn.ijiami.detection.entity.TPrivacyCheck">
        SELECT
        p.check_name as checkName,
        p.test_point as testPoint,
        p.test_method as testMethod,
        p.test_result as testResult,
        p.*
        FROM
        t_privacy_check p
        WHERE 1 = 1
        <if test="type != null">
            and p.type=#{type,jdbcType=INTEGER}
        </if>
        <if test="terminalType != null">
            and p.terminal_type=#{terminalType,jdbcType=INTEGER}
        </if>
    </select>
    <select id="findByPrivacyByType" resultMap="BaseResultMap">
        select * from t_privacy_policy_type where type = #{lawType} group by type
    </select>

    <select id="selectPrivacyCheckByTerminalAndLawType" resultType="cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO">
        SELECT
            p.id,
            p.type_id AS typeId,
            t.type AS type,
            p.check_name AS checkName,
            p.type_name AS typeName,
            p.notes,
            p.result,
            p.suggestion,
            p.reference,
            p.test_point AS testPoint,
            p.test_method AS testMethod,
            p.test_result AS testResult,
            p.type_sort AS typeSort,
            p.check_sort AS checkSort
        FROM
            t_privacy_check p,
            t_privacy_policy_type t
        WHERE
            p.type_id = t.id
          AND t.is_del = 0
          AND t.STATUS = 2
          AND p.is_del = 0
          AND p.STATUS = 2
          AND p.is_show = 1
          AND t.type = #{lawType}
          AND t.terminal_type = #{terminalType}
        ORDER BY
            p.type_sort,
            p.check_sort
    </select>
</mapper>