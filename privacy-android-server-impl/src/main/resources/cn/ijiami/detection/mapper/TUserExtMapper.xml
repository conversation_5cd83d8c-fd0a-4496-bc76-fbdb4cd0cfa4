<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TUserExtMapper" >
  <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TUserExt" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="expiration_time" property="expirationTime" jdbcType="TIMESTAMP" />
    <result column="detection_count" property="detectionCount" jdbcType="INTEGER" />
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT" />
    <result column="update_user_id" property="updateUserId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
</mapper>