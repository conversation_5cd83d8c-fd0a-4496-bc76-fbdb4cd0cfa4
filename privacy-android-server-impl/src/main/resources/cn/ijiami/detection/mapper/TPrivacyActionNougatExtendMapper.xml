<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TPrivacyActionNougatExtendMapper">


    <delete id="deleteByTaskId">
        DELETE e.*
        FROM t_privacy_action_nougat_extend AS e
        INNER JOIN t_privacy_action_nougat AS a ON a.id=e.nougat_id
        where a.task_id = #{taskId,jdbcType=BIGINT}
    </delete>

</mapper>