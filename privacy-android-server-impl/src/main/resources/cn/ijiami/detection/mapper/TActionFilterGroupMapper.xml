<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionFilterGroupMapper" >

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.query.ActionFilterGroupVO" >

        <id column="id" property="groupId" jdbcType="BIGINT" />
        <result column="name" property="groupName" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="group_user_names" property="userNames" jdbcType="VARCHAR" />
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
        <result column="is_main" property="isMain" jdbcType="BOOLEAN" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />

    </resultMap>


    <select id="findSpecificUserGroupList" resultMap="BaseResultMap">
        SELECT g.`id`, g.`name`, g.`description`, GROUP_CONCAT(u2.user_name SEPARATOR ';') AS group_user_names,u.user_name AS create_user_name,g.terminal_type, IF(g.`status`=2, TRUE, FALSE) AS is_main, g.create_time
        FROM t_action_filter_group_user AS gu
        JOIN (SELECT g.* FROM t_action_filter_group_user AS gu JOIN t_action_filter_group AS g ON g.id=gu.group_id
            WHERE g.`category` = 2 AND g.`status` != 3
            <if test="userId!=null">
                AND gu.user_id = #{userId}
            </if>
            GROUP BY gu.group_id
        ) AS g ON g.id=gu.group_id
        LEFT JOIN tsys_user AS u ON g.create_user_id=u.user_id
        LEFT JOIN tsys_user AS u2 ON u2.user_id=gu.user_id
        WHERE 1=1
        <if test="terminalType!=null">
            AND g.`terminal_type` = #{terminalType}
        </if>
        GROUP BY g.`id`
    </select>

    <select id="findAllUserGroupList" resultMap="BaseResultMap">
        SELECT g.`id`, g.`name`, g.`description`, '' AS group_user_names, u.user_name AS create_user_name,g.terminal_type, IF(g.`status`=2, TRUE, FALSE) AS is_main, g.create_time
        FROM t_action_filter_group AS g
        LEFT JOIN tsys_user AS u ON g.create_user_id=u.user_id
        WHERE g.`category` = 1 AND g.`status` != 3
        <if test="terminalType!=null">
            AND g.`terminal_type` = #{terminalType}
        </if>
    </select>

    <select id="countGroup" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_action_filter_group AS g
        LEFT JOIN t_action_filter_group_user AS gu ON g.id=gu.group_id
        WHERE g.`id` = #{groupId}
        AND g.`terminal_type`=#{terminalType}
        AND ((gu.user_id = #{userId} AND g.`status` = 1 AND g.`category`=1) OR (g.`status` != 3 AND g.`category`=2))
    </select>

    <select id="findGroupList" resultMap="BaseResultMap">
        SELECT g.`id`, g.`name`, g.`description`, GROUP_CONCAT(u2.user_name SEPARATOR ';') AS group_user_names, u.user_name AS create_user_name,g.terminal_type, IF(g.`status`=2, TRUE, FALSE) AS is_main, g.create_time
        FROM t_action_filter_group AS g
        LEFT JOIN t_action_filter_group_user AS gu ON g.id=gu.group_id
        LEFT JOIN tsys_user AS u ON g.create_user_id=u.user_id
        LEFT JOIN tsys_user AS u2 ON u2.user_id=gu.user_id
        WHERE g.`status` != 3
        GROUP BY g.`id`
    </select>
</mapper>