<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TTaskReportTypeMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TTaskReportType">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="type_name" property="typeName"/>
        <result column="type" property="type"/>
        <result column="terminal_type" property="terminalType"/>
        <result column="sort_num" property="sortNum"/>
    </resultMap>
    
    
    <select id="selectReprotTypeBySort" resultMap="BaseResultMap">
    	select id,type_name,type,terminal_type,sort_num from t_task_report_type order by sort_num
    </select>
</mapper>