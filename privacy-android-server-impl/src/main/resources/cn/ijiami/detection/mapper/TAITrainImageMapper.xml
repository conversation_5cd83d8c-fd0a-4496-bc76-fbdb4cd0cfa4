<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TAITrainImageMapper">
	<resultMap id="BaseResultMap" type="cn.ijiami.detection.query.AITrainImageVo">
		<id property="id" column="id"/>
		<result property="imageUrl" column="image_path"/>
		<result property="createUserName" column="create_user_name"/>
		<result property="category" column="category"/>
		<result property="status" column="status"/>
		<result property="createTime" column="create_time"/>
	</resultMap>


	<select id="findImage" resultMap="BaseResultMap">
		SELECT a.id, a.image_path, u.user_name AS create_user_name, a.category, a.status, a.create_time
		FROM `t_ai_train_image` AS a LEFT JOIN tsys_user AS u ON a.create_user_id = u.user_id
		WHERE a.status IN
		<foreach collection="statusList" item="status" open="(" separator="," close=")">
			#{status}
		</foreach>
		ORDER BY id DESC
	</select>
</mapper>