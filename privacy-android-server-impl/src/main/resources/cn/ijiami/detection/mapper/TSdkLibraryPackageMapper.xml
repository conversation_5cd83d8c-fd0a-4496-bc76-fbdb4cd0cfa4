<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TSdkLibraryPackageMapper" >
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TSdkLibraryPackage" >
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="sdk_id" property="sdkId" jdbcType="BIGINT" />
        <result column="package_name" property="packageName" jdbcType="VARCHAR" />
        <result column="created_user_id" property="createdUserId" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findBySdkId" resultMap="BaseResultMap">
        SELECT
        id,
        sdk_id,
        package_name,
        created_user_id,
        update_user_id,
        created_time,
        update_time
        FROM t_sdk_library_package
        WHERE sdk_id=#{sdkId} AND package_name IS NOT NULL
    </select>

</mapper>